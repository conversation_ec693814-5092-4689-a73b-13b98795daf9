<!DOCTYPE html>
<html lang="en" id="html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="page.title">Sanzi Universe Technology - Innovative Technology Solutions Provider</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <script>
        // Tailwind Configuration
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#722ED1',
                        dark: '#0F172A',
                        light: '#F8FAFC',
                        accent: '#06B6D4'
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }

        // Internationalization Configuration
        const i18nData = {
            en: {
                'page.title': 'Sanzi Universe Technology - Innovative Technology Solutions Provider',
                'nav.company': 'Sanzi Universe Technology',
                'nav.home': 'Home',
                'nav.services': 'Services',
                'nav.cases': 'Cases',
                'nav.about': 'About Us',
                'nav.contact': 'Contact Us',
                'nav.consult': 'Get Consultation',
                'hero.title1': 'Innovative Technology',
                'hero.title2': 'Reshaping the Future',
                'hero.description': 'Sanzi Universe Technology focuses on software development, APP development, and electronic product maintenance, providing comprehensive digital solutions for enterprises to help them achieve technological innovation and business growth.',
                'hero.cta1': 'Consult Solutions',
                'hero.cta2': 'Learn About Our Services',
                'hero.trust': 'Trusted by industry-leading companies',
                'hero.completion': 'Project Completion Rate',
                'services.title': 'Our Core Services',
                'services.description': 'With our professional technical team and rich industry experience, we provide customized technology solutions for clients to meet diverse needs across different industries.',
                'services.software.title': 'Software Development',
                'services.software.description': 'Providing customized software development services, including enterprise management systems, customer relationship management systems, e-commerce platforms, etc., to meet enterprise digital transformation needs.',
                'services.software.feature1': 'Customized System Development',
                'services.software.feature2': 'Enterprise Application Development',
                'services.software.feature3': 'System Integration & Optimization',
                'services.app.title': 'APP Development',
                'services.app.description': 'Focusing on iOS and Android platform mobile application development, providing full-process services from requirement analysis to product launch, creating mobile products with excellent user experience.',
                'services.app.feature1': 'iOS Application Development',
                'services.app.feature2': 'Android Application Development',
                'services.app.feature3': 'Cross-platform Application Development',
                'services.maintenance.title': 'Electronic Product Maintenance',
                'services.maintenance.description': 'Providing professional electronic product maintenance and technical support services, including hardware repair, system upgrades, fault diagnosis, etc., ensuring stable equipment operation.',
                'services.maintenance.feature1': 'Hardware Repair & Maintenance',
                'services.maintenance.feature2': 'System Upgrade & Optimization',
                'services.maintenance.feature3': 'Technical Support & Consulting',
                'services.cloud.title': 'Cloud Computing Services',
                'services.cloud.description': 'Providing cloud computing solutions, including cloud server deployment, cloud storage services, cloud application development, etc., helping enterprises reduce IT costs and improve operational efficiency.',
                'services.cloud.feature1': 'Cloud Server Deployment',
                'services.cloud.feature2': 'Cloud Storage Solutions',
                'services.cloud.feature3': 'Cloud Application Development',
                'services.security.title': 'Network Security Services',
                'services.security.description': 'Providing comprehensive network security solutions, including vulnerability detection, data encryption, firewall configuration, etc., ensuring enterprise information security and stable business operations.',
                'services.security.feature1': 'Vulnerability Detection & Repair',
                'services.security.feature2': 'Data Encryption & Protection',
                'services.security.feature3': 'Security Consulting & Training',
                'services.data.title': 'Data Analysis Services',
                'services.data.description': 'Providing professional data analysis and visualization services, helping enterprises mine data value, make data-driven decisions, and enhance business competitiveness.',
                'services.data.feature1': 'Data Collection & Processing',
                'services.data.feature2': 'Data Visualization Analysis',
                'services.data.feature3': 'Data Mining & Prediction',
                'services.learn_more': 'Learn More',
                'stats.projects': 'Successful Projects',
                'stats.clients': 'Satisfied Clients',
                'stats.engineers': 'Professional Engineers',
                'stats.experience': 'Years of Experience',
                'cases.title': 'Success Case Studies',
                'cases.description': 'We have provided quality technical solutions for clients across multiple industries, helping them achieve business growth and digital transformation.',
                'cases.filter.all': 'All Cases',
                'cases.filter.software': 'Software Development',
                'cases.filter.app': 'APP Development',
                'cases.filter.maintenance': 'Maintenance Services',
                'cases.view_details': 'View Details',
                'cases.view_more': 'View More Cases',
                'about.title': 'About Sanzi Universe Technology',
                'about.description1': 'Shenzhen Sanzi Universe Technology Co., Ltd. was established in October 2021. It is an enterprise focused on technological innovation, committed to providing high-quality software development, APP development, and electronic product maintenance services for clients.',
                'about.description2': 'We have a professional team with rich experience and excellent technical skills. Although our team size is less than 50 people, each member has solid technical foundation and rich project experience. We always adhere to customer needs as the guide and technological innovation as the driving force, providing customized solutions for clients to help them maintain competitive advantages in the digital age.',
                'about.advantage1.title': 'Professional Technical Team',
                'about.advantage1.description': 'Technical experts with years of industry experience, ensuring project quality',
                'about.advantage2.title': 'Customized Solutions',
                'about.advantage2.description': 'Providing personalized technical solutions based on customer needs',
                'about.advantage3.title': 'Quality Customer Service',
                'about.advantage3.description': 'Providing 7x24 technical support, ensuring service quality',
                'about.advantage4.title': 'Reasonable Pricing System',
                'about.advantage4.description': 'Providing cost-effective services, helping customers control costs',
                'about.chart.title': 'Project Completion Status',
                'about.learn_more': 'Learn More About Us',
                'team.title': 'Meet Our Team',
                'team.description': 'Our team consists of a group of passionate and technically skilled professionals, committed to providing the highest quality technical solutions for clients.',
                'testimonials.title': 'What Our Clients Say',
                'testimonials.description': 'Listen to what our clients say - their success is our greatest motivation.',
                'contact.title': 'Contact Us',
                'contact.description': 'Whether you have any questions or needs, our team is always ready to provide help and support. Fill out the form below or contact us through the following methods, and we will reply to you as soon as possible.',
                'contact.address.title': 'Company Address',
                'contact.address.content': 'Room 505, Building 16, District 2, Yucui Community, Longhua Street, Longhua District, Shenzhen',
                'contact.email.title': 'Email',
                'contact.hours.title': 'Business Hours',
                'contact.hours.content': 'Monday to Friday: 9:00 - 18:00<br>Saturday to Sunday: Closed',
                'contact.follow': 'Follow Us',
                'contact.form.title': 'Send Inquiry',
                'contact.form.name': 'Name',
                'contact.form.name.placeholder': 'Please enter your name',
                'contact.form.email': 'Email',
                'contact.form.email.placeholder': 'Please enter your email',
                'contact.form.company': 'Company Name',
                'contact.form.company.placeholder': 'Please enter your company name',
                'contact.form.service': 'Interested Service',
                'contact.form.service.placeholder': 'Please select service type',
                'contact.form.service.software': 'Software Development',
                'contact.form.service.app': 'APP Development',
                'contact.form.service.maintenance': 'Electronic Product Maintenance',
                'contact.form.service.cloud': 'Cloud Computing Services',
                'contact.form.service.security': 'Network Security Services',
                'contact.form.service.data': 'Data Analysis Services',
                'contact.form.service.other': 'Other Services',
                'contact.form.message': 'Inquiry Content',
                'contact.form.message.placeholder': 'Please describe your needs or questions in detail',
                'contact.form.submit': 'Send Inquiry',
                'footer.description': 'Focusing on software development, APP development, and electronic product maintenance, providing comprehensive digital solutions for enterprises.',
                'footer.quick_links': 'Quick Links',
                'footer.services_title': 'Services',
                'footer.contact_title': 'Contact Information',
                'footer.copyright': '© 2024 Sanzi Universe Technology Co., Ltd. All rights reserved.',
                'footer.icp': 'ICP Registration: 粤ICP备2021XXXXXX号'
            },
            zh: {
                'page.title': '三子宇宙科技 - 创新科技解决方案提供商',
                'nav.company': '三子宇宙科技',
                'nav.home': '首页',
                'nav.services': '服务',
                'nav.cases': '案例',
                'nav.about': '关于我们',
                'nav.contact': '联系我们',
                'nav.consult': '立即咨询',
                'hero.title1': '创新科技',
                'hero.title2': '重塑未来',
                'hero.description': '三子宇宙科技专注于软件开发、APP开发与电子产品维护，为企业提供全方位的数字化解决方案，助力企业实现技术创新与业务增长。',
                'hero.cta1': '咨询解决方案',
                'hero.cta2': '了解我们的服务',
                'hero.trust': '受到行业领先企业的信任',
                'hero.completion': '项目完成率',
                'services.title': '我们的核心服务',
                'services.description': '凭借专业的技术团队和丰富的行业经验，我们为客户提供定制化的科技解决方案，满足不同行业的多样化需求。',
                'services.software.title': '软件开发',
                'services.software.description': '提供定制化软件开发服务，包括企业管理系统、客户关系管理系统、电商平台等，满足企业数字化转型需求。',
                'services.software.feature1': '定制化系统开发',
                'services.software.feature2': '企业级应用开发',
                'services.software.feature3': '系统集成与优化',
                'services.app.title': 'APP开发',
                'services.app.description': '专注于iOS和Android平台的移动应用开发，从需求分析到产品上线提供全流程服务，打造用户体验卓越的移动产品。',
                'services.app.feature1': 'iOS应用开发',
                'services.app.feature2': 'Android应用开发',
                'services.app.feature3': '跨平台应用开发',
                'services.maintenance.title': '电子产品维护',
                'services.maintenance.description': '提供专业的电子产品维护与技术支持服务，包括硬件维修、系统升级、故障诊断等，确保设备稳定运行。',
                'services.maintenance.feature1': '硬件维修与维护',
                'services.maintenance.feature2': '系统升级与优化',
                'services.maintenance.feature3': '技术支持与咨询',
                'services.cloud.title': '云计算服务',
                'services.cloud.description': '提供云计算解决方案，包括云服务器部署、云存储服务、云应用开发等，帮助企业降低IT成本，提高运营效率。',
                'services.cloud.feature1': '云服务器部署',
                'services.cloud.feature2': '云存储解决方案',
                'services.cloud.feature3': '云应用开发',
                'services.security.title': '网络安全服务',
                'services.security.description': '提供全面的网络安全解决方案，包括漏洞检测、数据加密、防火墙配置等，保障企业信息安全和业务稳定运行。',
                'services.security.feature1': '漏洞检测与修复',
                'services.security.feature2': '数据加密与保护',
                'services.security.feature3': '安全咨询与培训',
                'services.data.title': '数据分析服务',
                'services.data.description': '提供专业的数据分析与可视化服务，帮助企业挖掘数据价值，做出数据驱动的决策，提升业务竞争力。',
                'services.data.feature1': '数据采集与处理',
                'services.data.feature2': '数据可视化分析',
                'services.data.feature3': '数据挖掘与预测',
                'services.learn_more': '了解更多',
                'stats.projects': '成功项目',
                'stats.clients': '满意客户',
                'stats.engineers': '专业工程师',
                'stats.experience': '行业经验(年)',
                'cases.title': '成功案例展示',
                'cases.description': '我们已为多个行业的客户提供了优质的技术解决方案，帮助客户实现业务增长和数字化转型。',
                'cases.filter.all': '全部案例',
                'cases.filter.software': '软件开发',
                'cases.filter.app': 'APP开发',
                'cases.filter.maintenance': '维护服务',
                'cases.view_details': '查看详情',
                'cases.view_more': '查看更多案例',
                'about.title': '关于三子宇宙科技',
                'about.description1': '深圳市三子宇宙科技有限公司成立于2021年10月，是一家专注于科技创新的企业，致力于为客户提供高质量的软件开发、APP开发和电子产品维护服务。',
                'about.description2': '我们拥有一支经验丰富、技术精湛的专业团队，员工规模虽不到50人，但每位成员都具备扎实的技术功底和丰富的项目经验。我们始终坚持以客户需求为导向，以技术创新为动力，为客户提供定制化的解决方案，帮助客户在数字化时代保持竞争优势。',
                'about.advantage1.title': '专业技术团队',
                'about.advantage1.description': '拥有多年行业经验的技术专家，确保项目质量',
                'about.advantage2.title': '定制化解决方案',
                'about.advantage2.description': '根据客户需求提供个性化的技术解决方案',
                'about.advantage3.title': '优质客户服务',
                'about.advantage3.description': '提供7x24小时技术支持，确保服务质量',
                'about.advantage4.title': '合理的价格体系',
                'about.advantage4.description': '提供高性价比的服务，帮助客户控制成本',
                'about.chart.title': '项目完成情况',
                'about.learn_more': '了解更多关于我们',
                'team.title': '认识我们的团队',
                'team.description': '我们的团队由一群充满激情、技术精湛的专业人士组成，致力于为客户提供最优质的技术解决方案。',
                'testimonials.title': '客户对我们的评价',
                'testimonials.description': '听听我们的客户怎么说，他们的成功是我们最大的动力。',
                'contact.title': '联系我们',
                'contact.description': '无论您有任何疑问或需求，我们的团队都随时准备为您提供帮助和支持。填写下方表单或通过以下方式联系我们，我们将尽快回复您。',
                'contact.address.title': '公司地址',
                'contact.address.content': '深圳市龙华区龙华街道玉翠社区二区16号505',
                'contact.email.title': '电子邮箱',
                'contact.hours.title': '工作时间',
                'contact.hours.content': '周一至周五: 9:00 - 18:00<br>周六至周日: 休息',
                'contact.follow': '关注我们',
                'contact.form.title': '发送咨询',
                'contact.form.name': '姓名',
                'contact.form.name.placeholder': '请输入您的姓名',
                'contact.form.email': '电子邮箱',
                'contact.form.email.placeholder': '请输入您的电子邮箱',
                'contact.form.company': '公司名称',
                'contact.form.company.placeholder': '请输入您的公司名称',
                'contact.form.service': '感兴趣的服务',
                'contact.form.service.placeholder': '请选择服务类型',
                'contact.form.service.software': '软件开发',
                'contact.form.service.app': 'APP开发',
                'contact.form.service.maintenance': '电子产品维护',
                'contact.form.service.cloud': '云计算服务',
                'contact.form.service.security': '网络安全服务',
                'contact.form.service.data': '数据分析服务',
                'contact.form.service.other': '其他服务',
                'contact.form.message': '咨询内容',
                'contact.form.message.placeholder': '请详细描述您的需求或疑问',
                'contact.form.submit': '发送咨询',
                'footer.description': '专注于软件开发、APP开发和电子产品维护，为企业提供全方位的数字化解决方案。',
                'footer.quick_links': '快速链接',
                'footer.services_title': '服务',
                'footer.contact_title': '联系信息',
                'footer.copyright': '© 2024 深圳市三子宇宙科技有限公司 版权所有',
                'footer.icp': 'ICP备案号：粤ICP备2021XXXXXX号'
            }
        };

        // Current language (default: English)
        let currentLang = 'en';

        // Initialize internationalization
        function initI18n() {
            // Check for saved language preference or browser language
            const savedLang = localStorage.getItem('preferred-language');
            const browserLang = navigator.language.startsWith('zh') ? 'zh' : 'en';
            currentLang = savedLang || browserLang;

            // Update HTML lang attribute
            document.getElementById('html-root').setAttribute('lang', currentLang === 'zh' ? 'zh-CN' : 'en');

            // Apply translations
            updateTexts();
        }

        // Update all text content based on current language
        function updateTexts() {
            const elements = document.querySelectorAll('[data-i18n]');
            elements.forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (i18nData[currentLang] && i18nData[currentLang][key]) {
                    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        element.placeholder = i18nData[currentLang][key];
                    } else {
                        element.innerHTML = i18nData[currentLang][key];
                    }
                }
            });
        }

        // Switch language function
        function switchLanguage(lang) {
            if (lang !== currentLang && i18nData[lang]) {
                currentLang = lang;
                localStorage.setItem('preferred-language', lang);
                document.getElementById('html-root').setAttribute('lang', lang === 'zh' ? 'zh-CN' : 'en');
                updateTexts();

                // Update language toggle buttons
                updateLanguageButtons();
            }
        }

        // Update language toggle buttons appearance
        function updateLanguageButtons() {
            const enBtn = document.getElementById('lang-en');
            const zhBtn = document.getElementById('lang-zh');

            if (enBtn && zhBtn) {
                if (currentLang === 'en') {
                    enBtn.classList.add('bg-primary', 'text-white');
                    enBtn.classList.remove('bg-gray-700', 'text-gray-300');
                    zhBtn.classList.add('bg-gray-700', 'text-gray-300');
                    zhBtn.classList.remove('bg-primary', 'text-white');
                } else {
                    zhBtn.classList.add('bg-primary', 'text-white');
                    zhBtn.classList.remove('bg-gray-700', 'text-gray-300');
                    enBtn.classList.add('bg-gray-700', 'text-gray-300');
                    enBtn.classList.remove('bg-primary', 'text-white');
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .text-gradient {
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .bg-glass {
                backdrop-filter: blur(8px);
                -webkit-backdrop-filter: blur(8px);
            }
            .animate-float {
                animation: float 6s ease-in-out infinite;
            }
            .animate-pulse-slow {
                animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="font-inter bg-dark text-light overflow-x-hidden">
<!-- Navigation Bar -->
<header id="navbar" class="fixed w-full top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
            <a href="#" class="flex items-center space-x-2">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center">
                    <span class="text-xl font-bold">3S</span>
                </div>
                <span class="text-xl font-bold" data-i18n="nav.company">Sanzi Universe Technology</span>
            </a>

            <!-- Desktop Navigation -->
            <nav class="hidden lg:flex space-x-8">
                <a href="#home" class="text-light hover:text-primary transition-colors duration-300" data-i18n="nav.home">Home</a>
                <a href="#services" class="text-light hover:text-primary transition-colors duration-300" data-i18n="nav.services">Services</a>
                <a href="#cases" class="text-light hover:text-primary transition-colors duration-300" data-i18n="nav.cases">Cases</a>
                <a href="#about" class="text-light hover:text-primary transition-colors duration-300" data-i18n="nav.about">About Us</a>
                <a href="#contact" class="text-light hover:text-primary transition-colors duration-300" data-i18n="nav.contact">Contact Us</a>
            </nav>

            <!-- Language Toggle & Contact Button -->
            <div class="hidden md:flex items-center space-x-4">
                <!-- Language Toggle -->
                <div class="flex items-center space-x-1 bg-dark/50 rounded-lg p-1">
                    <button id="lang-en" onclick="switchLanguage('en')"
                            class="px-3 py-1 rounded text-sm font-medium transition-all duration-300 bg-primary text-white">
                        EN
                    </button>
                    <button id="lang-zh" onclick="switchLanguage('zh')"
                            class="px-3 py-1 rounded text-sm font-medium transition-all duration-300 bg-gray-700 text-gray-300">
                        中文
                    </button>
                </div>

                <!-- Contact Button -->
                <a href="#contact"
                   class="px-6 py-2 bg-gradient-to-r from-primary to-secondary rounded-lg hover:shadow-lg hover:shadow-primary/20 transition-all duration-300"
                   data-i18n="nav.consult">Get Consultation</a>
            </div>

            <!-- Mobile Menu Button -->
            <button id="menuBtn" class="md:hidden text-2xl">
                <i class="fa fa-bars"></i>
            </button>
        </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div id="mobileMenu" class="md:hidden bg-dark/95 bg-glass hidden">
        <div class="container mx-auto px-4 py-4 flex flex-col space-y-4">
            <!-- Language Toggle for Mobile -->
            <div class="flex items-center justify-center space-x-1 bg-dark/50 rounded-lg p-1 mb-4">
                <button id="mobile-lang-en" onclick="switchLanguage('en')"
                        class="px-3 py-1 rounded text-sm font-medium transition-all duration-300 bg-primary text-white">
                    EN
                </button>
                <button id="mobile-lang-zh" onclick="switchLanguage('zh')"
                        class="px-3 py-1 rounded text-sm font-medium transition-all duration-300 bg-gray-700 text-gray-300">
                    中文
                </button>
            </div>

            <a href="#home" class="py-2 text-light hover:text-primary transition-colors duration-300" data-i18n="nav.home">Home</a>
            <a href="#services" class="py-2 text-light hover:text-primary transition-colors duration-300" data-i18n="nav.services">Services</a>
            <a href="#cases" class="py-2 text-light hover:text-primary transition-colors duration-300" data-i18n="nav.cases">Cases</a>
            <a href="#about" class="py-2 text-light hover:text-primary transition-colors duration-300" data-i18n="nav.about">About Us</a>
            <a href="#contact" class="py-2 text-light hover:text-primary transition-colors duration-300" data-i18n="nav.contact">Contact Us</a>
            <a href="#contact" class="px-6 py-2 bg-gradient-to-r from-primary to-secondary rounded-lg text-center" data-i18n="nav.consult">
                Get Consultation
            </a>
        </div>
    </div>
</header>

<main>
    <!-- Hero Section -->
    <section id="home" class="pt-32 pb-20 md:pt-40 md:pb-32 relative overflow-hidden">
        <!-- Background Decoration -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
            <div class="absolute top-20 right-20 w-96 h-96 bg-primary/10 rounded-full filter blur-3xl"></div>
            <div class="absolute bottom-20 left-20 w-96 h-96 bg-secondary/10 rounded-full filter blur-3xl"></div>
            <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[1000px] h-[1000px] bg-accent/5 rounded-full filter blur-3xl"></div>
        </div>

        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-12 md:mb-0">
                    <h1 class="text-[clamp(2.5rem,5vw,4rem)] font-bold leading-tight mb-6">
                        <span data-i18n="hero.title1">Innovative Technology</span><br>
                        <span class="bg-gradient-to-r from-primary to-secondary text-gradient" data-i18n="hero.title2">Reshaping the Future</span>
                    </h1>
                    <p class="text-lg md:text-xl text-gray-300 mb-8 max-w-lg" data-i18n="hero.description">
                        Sanzi Universe Technology focuses on software development, APP development, and electronic product maintenance, providing comprehensive digital solutions for enterprises to help them achieve technological innovation and business growth.
                    </p>
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <a href="#contact"
                           class="px-8 py-3 bg-gradient-to-r from-primary to-secondary rounded-lg hover:shadow-lg hover:shadow-primary/20 transition-all duration-300 text-center"
                           data-i18n="hero.cta1">
                            Consult Solutions
                        </a>
                        <a href="#services"
                           class="px-8 py-3 border border-primary text-primary hover:bg-primary/10 rounded-lg transition-all duration-300 text-center"
                           data-i18n="hero.cta2">
                            Learn About Our Services
                        </a>
                    </div>

                    <!-- Trust Indicators -->
                    <div class="mt-12">
                        <p class="text-gray-400 text-sm mb-4" data-i18n="hero.trust">Trusted by industry-leading companies</p>
                        <div class="flex flex-wrap items-center gap-8 opacity-70">
                            <img src="https://picsum.photos/id/1/100/40" alt="Partner"
                                 class="h-6 grayscale hover:grayscale-0 transition-all duration-300">
                            <img src="https://picsum.photos/id/2/100/40" alt="Partner"
                                 class="h-6 grayscale hover:grayscale-0 transition-all duration-300">
                            <img src="https://picsum.photos/id/3/100/40" alt="Partner"
                                 class="h-6 grayscale hover:grayscale-0 transition-all duration-300">
                            <img src="https://picsum.photos/id/4/100/40" alt="Partner"
                                 class="h-6 grayscale hover:grayscale-0 transition-all duration-300">
                        </div>
                    </div>
                </div>

                <div class="md:w-1/2 relative">
                    <div class="relative z-10 animate-float">
                        <img src="https://picsum.photos/id/0/600/400" alt="Technology Solutions"
                             class="rounded-xl shadow-2xl shadow-primary/10">
                        <div class="absolute -bottom-6 -left-6 bg-dark p-4 rounded-lg shadow-lg border border-primary/20">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                                    <i class="fa fa-line-chart text-primary text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-400" data-i18n="hero.completion">Project Completion Rate</p>
                                    <p class="text-xl font-bold">98.7%</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full border-2 border-primary/20 rounded-xl -z-10 rotate-3"></div>
                    <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full border-2 border-secondary/20 rounded-xl -z-10 -rotate-3"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-gradient-to-b from-dark to-dark/90">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-4" data-i18n="services.title">Our Core Services</h2>
                <p class="text-lg text-gray-300" data-i18n="services.description">
                    With our professional technical team and rich industry experience, we provide customized technology solutions for clients to meet diverse needs across different industries.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Service Card 1 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-all duration-300">
                        <i class="fa fa-code text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4" data-i18n="services.software.title">Software Development</h3>
                    <p class="text-gray-300 mb-6" data-i18n="services.software.description">
                        Providing customized software development services, including enterprise management systems, customer relationship management systems, e-commerce platforms, etc., to meet enterprise digital transformation needs.
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span data-i18n="services.software.feature1">Customized System Development</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span data-i18n="services.software.feature2">Enterprise Application Development</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span data-i18n="services.software.feature3">System Integration & Optimization</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-primary font-medium hover:text-primary/80 transition-colors duration-300">
                        <span data-i18n="services.learn_more">Learn More</span> <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 服务卡片 2 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-secondary/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-secondary/20 transition-all duration-300">
                        <i class="fa fa-mobile text-secondary text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">APP开发</h3>
                    <p class="text-gray-300 mb-6">
                        专注于iOS和Android平台的移动应用开发，从需求分析到产品上线提供全流程服务，打造用户体验卓越的移动产品。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>iOS应用开发</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>Android应用开发</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>跨平台应用开发</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-secondary font-medium hover:text-secondary/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 服务卡片 3 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-accent/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-accent/20 transition-all duration-300">
                        <i class="fa fa-microchip text-accent text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">电子产品维护</h3>
                    <p class="text-gray-300 mb-6">
                        提供专业的电子产品维护与技术支持服务，包括硬件维修、系统升级、故障诊断等，确保设备稳定运行。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>硬件维修与维护</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>系统升级与优化</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>技术支持与咨询</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-accent font-medium hover:text-accent/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 服务卡片 4 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-all duration-300">
                        <i class="fa fa-cloud text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">云计算服务</h3>
                    <p class="text-gray-300 mb-6">
                        提供云计算解决方案，包括云服务器部署、云存储服务、云应用开发等，帮助企业降低IT成本，提高运营效率。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span>云服务器部署</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span>云存储解决方案</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span>云应用开发</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-primary font-medium hover:text-primary/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 服务卡片 5 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-secondary/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-secondary/20 transition-all duration-300">
                        <i class="fa fa-shield text-secondary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">网络安全服务</h3>
                    <p class="text-gray-300 mb-6">
                        提供全面的网络安全解决方案，包括漏洞检测、数据加密、防火墙配置等，保障企业信息安全和业务稳定运行。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>漏洞检测与修复</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>数据加密与保护</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>安全咨询与培训</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-secondary font-medium hover:text-secondary/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 服务卡片 6 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-accent/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-accent/20 transition-all duration-300">
                        <i class="fa fa-database text-accent text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">数据分析服务</h3>
                    <p class="text-gray-300 mb-6">
                        提供专业的数据分析与可视化服务，帮助企业挖掘数据价值，做出数据驱动的决策，提升业务竞争力。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>数据采集与处理</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>数据可视化分析</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>数据挖掘与预测</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-accent font-medium hover:text-accent/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="py-16 bg-gradient-to-r from-primary/10 to-secondary/10">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div class="p-6">
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-secondary text-gradient mb-2"
                         id="counter1">0
                    </div>
                    <p class="text-gray-300" data-i18n="stats.projects">Successful Projects</p>
                </div>
                <div class="p-6">
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-secondary text-gradient mb-2"
                         id="counter2">0
                    </div>
                    <p class="text-gray-300" data-i18n="stats.clients">Satisfied Clients</p>
                </div>
                <div class="p-6">
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-secondary text-gradient mb-2"
                         id="counter3">0
                    </div>
                    <p class="text-gray-300" data-i18n="stats.engineers">Professional Engineers</p>
                </div>
                <div class="p-6">
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-secondary text-gradient mb-2"
                         id="counter4">0
                    </div>
                    <p class="text-gray-300" data-i18n="stats.experience">Years of Experience</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 案例展示 -->
    <section id="cases" class="py-20 bg-dark">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-4">成功案例展示</h2>
                <p class="text-lg text-gray-300">
                    我们已为多个行业的客户提供了优质的技术解决方案，帮助客户实现业务增长和数字化转型。
                </p>
            </div>

            <!-- 案例筛选 -->
            <div class="flex flex-wrap justify-center gap-4 mb-12">
                <button class="case-filter px-6 py-2 rounded-full bg-primary text-light" data-filter="all">全部案例
                </button>
                <button class="case-filter px-6 py-2 rounded-full bg-dark/50 border border-gray-800 hover:border-primary/50 transition-all duration-300"
                        data-filter="software">软件开发
                </button>
                <button class="case-filter px-6 py-2 rounded-full bg-dark/50 border border-gray-800 hover:border-primary/50 transition-all duration-300"
                        data-filter="app">APP开发
                </button>
                <button class="case-filter px-6 py-2 rounded-full bg-dark/50 border border-gray-800 hover:border-primary/50 transition-all duration-300"
                        data-filter="maintenance">维护服务
                </button>
            </div>

            <!-- 案例网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 案例1 -->
                <div class="case-item group" data-category="software">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/20/600/400" alt="企业管理系统开发"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-primary/80 text-light text-sm rounded-full">软件开发</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">企业管理系统开发</h3>
                    <p class="text-gray-300 mb-4">
                        为制造业客户开发的定制化企业管理系统，整合生产、销售、库存等模块，提升管理效率30%。</p>
                    <a href="#"
                       class="inline-flex items-center text-primary font-medium hover:text-primary/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 案例2 -->
                <div class="case-item group" data-category="app">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/21/600/400" alt="电商APP开发"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-secondary/80 text-light text-sm rounded-full">APP开发</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">电商APP开发</h3>
                    <p class="text-gray-300 mb-4">
                        为零售客户开发的跨平台电商APP，支持多种支付方式和会员体系，上线后月活跃用户达5万+。</p>
                    <a href="#"
                       class="inline-flex items-center text-secondary font-medium hover:text-secondary/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 案例3 -->
                <div class="case-item group" data-category="maintenance">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/22/600/400" alt="医疗设备维护"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-accent/80 text-light text-sm rounded-full">维护服务</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">医疗设备维护</h3>
                    <p class="text-gray-300 mb-4">
                        为医疗机构提供的专业医疗设备维护服务，保障设备正常运行，故障率降低40%。</p>
                    <a href="#"
                       class="inline-flex items-center text-accent font-medium hover:text-accent/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 案例4 -->
                <div class="case-item group" data-category="software">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/23/600/400" alt="客户关系管理系统"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-primary/80 text-light text-sm rounded-full">软件开发</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">客户关系管理系统</h3>
                    <p class="text-gray-300 mb-4">
                        为服务行业客户开发的CRM系统，实现客户信息管理、服务跟踪和数据分析，客户满意度提升25%。</p>
                    <a href="#"
                       class="inline-flex items-center text-primary font-medium hover:text-primary/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 案例5 -->
                <div class="case-item group" data-category="app">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/24/600/400" alt="教育类APP开发"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-secondary/80 text-light text-sm rounded-full">APP开发</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">教育类APP开发</h3>
                    <p class="text-gray-300 mb-4">
                        为教育机构开发的在线学习APP，支持视频课程、在线测试和学习跟踪，注册用户突破10万。</p>
                    <a href="#"
                       class="inline-flex items-center text-secondary font-medium hover:text-secondary/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 案例6 -->
                <div class="case-item group" data-category="maintenance">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/25/600/400" alt="工业设备维护"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-accent/80 text-light text-sm rounded-full">维护服务</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">工业设备维护</h3>
                    <p class="text-gray-300 mb-4">
                        为制造企业提供的工业设备预防性维护服务，延长设备使用寿命，降低维护成本35%。</p>
                    <a href="#"
                       class="inline-flex items-center text-accent font-medium hover:text-accent/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="#contact"
                   class="inline-block px-8 py-3 bg-gradient-to-r from-primary to-secondary rounded-lg hover:shadow-lg hover:shadow-primary/20 transition-all duration-300">
                    查看更多案例
                </a>
            </div>
        </div>
    </section>

    <!-- 关于我们 -->
    <section id="about" class="py-20 bg-gradient-to-b from-dark to-dark/90 relative overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
            <div class="absolute top-40 right-40 w-80 h-80 bg-primary/5 rounded-full filter blur-3xl"></div>
            <div class="absolute bottom-40 left-40 w-80 h-80 bg-secondary/5 rounded-full filter blur-3xl"></div>
        </div>

        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row items-center gap-12">
                <div class="lg:w-1/2">
                    <div class="relative">
                        <img src="https://picsum.photos/id/48/600/600" alt="公司团队"
                             class="rounded-xl shadow-2xl shadow-primary/10">
                        <div class="absolute -bottom-6 -right-6 bg-dark p-6 rounded-lg shadow-lg border border-primary/20 max-w-xs">
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="flex -space-x-2">
                                    <img src="https://picsum.photos/id/64/100/100" alt="团队成员"
                                         class="w-10 h-10 rounded-full border-2 border-dark">
                                    <img src="https://picsum.photos/id/65/100/100" alt="团队成员"
                                         class="w-10 h-10 rounded-full border-2 border-dark">
                                    <img src="https://picsum.photos/id/66/100/100" alt="团队成员"
                                         class="w-10 h-10 rounded-full border-2 border-dark">
                                    <div class="w-10 h-10 rounded-full border-2 border-dark bg-primary/20 flex items-center justify-center text-primary text-xs font-bold">
                                        +20
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-400">专业团队</p>
                                    <p class="font-bold">50+ 技术专家</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 text-yellow-400">
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star-half-o"></i>
                                <span class="ml-2 text-light">4.8/5 客户评分</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="lg:w-1/2">
                    <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-6">关于三子宇宙科技</h2>
                    <p class="text-lg text-gray-300 mb-6">
                        深圳市三子宇宙科技有限公司成立于2021年10月，是一家专注于科技创新的企业，致力于为客户提供高质量的软件开发、APP开发和电子产品维护服务。
                    </p>
                    <p class="text-lg text-gray-300 mb-8">
                        我们拥有一支经验丰富、技术精湛的专业团队，员工规模虽不到50人，但每位成员都具备扎实的技术功底和丰富的项目经验。我们始终坚持以客户需求为导向，以技术创新为动力，为客户提供定制化的解决方案，帮助客户在数字化时代保持竞争优势。
                    </p>

                    <!-- 公司优势 -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-check text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">专业技术团队</h4>
                                <p class="text-gray-300">拥有多年行业经验的技术专家，确保项目质量</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-check text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">定制化解决方案</h4>
                                <p class="text-gray-300">根据客户需求提供个性化的技术解决方案</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-check text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">优质客户服务</h4>
                                <p class="text-gray-300">提供7x24小时技术支持，确保服务质量</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-check text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">合理的价格体系</h4>
                                <p class="text-gray-300">提供高性价比的服务，帮助客户控制成本</p>
                            </div>
                        </div>
                    </div>

                    <!-- 数据图表 -->
                    <div class="bg-dark/50 border border-gray-800 rounded-xl p-6 mb-8">
                        <h4 class="text-xl font-bold mb-4">项目完成情况</h4>
                        <canvas id="projectChart" height="200"></canvas>
                    </div>

                    <a href="#contact"
                       class="inline-block px-8 py-3 bg-gradient-to-r from-primary to-secondary rounded-lg hover:shadow-lg hover:shadow-primary/20 transition-all duration-300">
                        了解更多关于我们
                    </a>
                </div>
            </div>

            <!-- 团队介绍 -->
            <div class="mt-24">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-4">认识我们的团队</h2>
                    <p class="text-lg text-gray-300">
                        我们的团队由一群充满激情、技术精湛的专业人士组成，致力于为客户提供最优质的技术解决方案。
                    </p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- 团队成员1 -->
                    <div class="bg-dark/50 border border-gray-800 rounded-xl overflow-hidden group hover:border-primary/50 transition-all duration-300">
                        <div class="relative overflow-hidden">
                            <img src="https://picsum.photos/id/64/400/400" alt="张明 - 技术总监"
                                 class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                <div class="flex space-x-4">
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-linkedin"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-github"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-1">张明</h3>
                            <p class="text-primary mb-4">技术总监</p>
                            <p class="text-gray-300">
                                拥有10年软件开发经验，曾主导多个大型企业级项目，擅长技术架构设计和团队管理。
                            </p>
                        </div>
                    </div>

                    <!-- 团队成员2 -->
                    <div class="bg-dark/50 border border-gray-800 rounded-xl overflow-hidden group hover:border-primary/50 transition-all duration-300">
                        <div class="relative overflow-hidden">
                            <img src="https://picsum.photos/id/65/400/400" alt="李娜 - 产品经理"
                                 class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                <div class="flex space-x-4">
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-linkedin"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-github"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-1">李娜</h3>
                            <p class="text-secondary mb-4">产品经理</p>
                            <p class="text-gray-300">
                                8年产品设计经验，擅长用户体验设计和产品规划，曾负责多个成功APP产品的设计与上线。
                            </p>
                        </div>
                    </div>

                    <!-- 团队成员3 -->
                    <div class="bg-dark/50 border border-gray-800 rounded-xl overflow-hidden group hover:border-primary/50 transition-all duration-300">
                        <div class="relative overflow-hidden">
                            <img src="https://picsum.photos/id/66/400/400" alt="王强 - 开发工程师"
                                 class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                <div class="flex space-x-4">
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-linkedin"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-github"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-1">王强</h3>
                            <p class="text-accent mb-4">开发工程师</p>
                            <p class="text-gray-300">
                                精通前后端开发技术，5年软件开发经验，擅长解决复杂技术问题，曾参与多个大型项目开发。
                            </p>
                        </div>
                    </div>

                    <!-- 团队成员4 -->
                    <div class="bg-dark/50 border border-gray-800 rounded-xl overflow-hidden group hover:border-primary/50 transition-all duration-300">
                        <div class="relative overflow-hidden">
                            <img src="https://picsum.photos/id/67/400/400" alt="赵静 - 运维工程师"
                                 class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                <div class="flex space-x-4">
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-linkedin"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-github"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-1">赵静</h3>
                            <p class="text-primary mb-4">运维工程师</p>
                            <p class="text-gray-300">
                                6年IT运维经验，精通服务器管理和网络维护，擅长保障系统稳定运行和数据安全。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 客户评价 -->
    <section class="py-20 bg-gradient-to-r from-primary/5 to-secondary/5">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-4">客户对我们的评价</h2>
                <p class="text-lg text-gray-300">
                    听听我们的客户怎么说，他们的成功是我们最大的动力。
                </p>
            </div>

            <div class="testimonial-slider relative">
                <div class="overflow-hidden">
                    <div id="testimonialWrapper" class="flex transition-transform duration-500 ease-in-out">
                        <!-- 评价1 -->
                        <div class="testimonial-item min-w-full md:min-w-[50%] lg:min-w-[33.333%] p-4">
                            <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 h-full">
                                <div class="flex items-center space-x-2 text-yellow-400 mb-6">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                </div>
                                <p class="text-gray-300 mb-8 italic">
                                    "三子宇宙科技为我们开发的企业管理系统极大地提升了公司的运营效率，他们的技术团队专业且负责，能够快速响应我们的需求，提供优质的技术支持。"
                                </p>
                                <div class="flex items-center space-x-4">
                                    <img src="https://picsum.photos/id/91/100/100" alt="陈总 - 某制造企业CEO"
                                         class="w-14 h-14 rounded-full">
                                    <div>
                                        <h4 class="text-xl font-bold">陈总</h4>
                                        <p class="text-gray-400">某制造企业CEO</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 评价2 -->
                        <div class="testimonial-item min-w-full md:min-w-[50%] lg:min-w-[33.333%] p-4">
                            <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 h-full">
                                <div class="flex items-center space-x-2 text-yellow-400 mb-6">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                </div>
                                <p class="text-gray-300 mb-8 italic">
                                    "我们与三子宇宙科技合作开发了一款电商APP，他们的设计和开发能力令人印象深刻，APP上线后用户反馈非常好，帮助我们显著提升了线上销售额。"
                                </p>
                                <div class="flex items-center space-x-4">
                                    <img src="https://picsum.photos/id/92/100/100" alt="林总 - 某零售企业总经理"
                                         class="w-14 h-14 rounded-full">
                                    <div>
                                        <h4 class="text-xl font-bold">林总</h4>
                                        <p class="text-gray-400">某零售企业总经理</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 评价3 -->
                        <div class="testimonial-item min-w-full md:min-w-[50%] lg:min-w-[33.333%] p-4">
                            <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 h-full">
                                <div class="flex items-center space-x-2 text-yellow-400 mb-6">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star-half-o"></i>
                                </div>
                                <p class="text-gray-300 mb-8 italic">
                                    "三子宇宙科技提供的医疗设备维护服务非常专业，他们的工程师技术精湛，响应迅速，帮助我们降低了设备故障率，保障了医院的正常运营。"
                                </p>
                                <div class="flex items-center space-x-4">
                                    <img src="https://picsum.photos/id/93/100/100" alt="张主任 - 某医院设备科主任"
                                         class="w-14 h-14 rounded-full">
                                    <div>
                                        <h4 class="text-xl font-bold">张主任</h4>
                                        <p class="text-gray-400">某医院设备科主任</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 评价4 -->
                        <div class="testimonial-item min-w-full md:min-w-[50%] lg:min-w-[33.333%] p-4">
                            <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 h-full">
                                <div class="flex items-center space-x-2 text-yellow-400 mb-6">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                </div>
                                <p class="text-gray-300 mb-8 italic">
                                    "与三子宇宙科技的合作非常愉快，他们为我们开发的CRM系统帮助我们更好地管理客户关系，提升了客户满意度，他们的服务态度和技术能力都非常出色。"
                                </p>
                                <div class="flex items-center space-x-4">
                                    <img src="https://picsum.photos/id/94/100/100" alt="刘总 - 某服务企业总经理"
                                         class="w-14 h-14 rounded-full">
                                    <div>
                                        <h4 class="text-xl font-bold">刘总</h4>
                                        <p class="text-gray-400">某服务企业总经理</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 控制按钮 -->
                <button id="prevTestimonial"
                        class="absolute top-1/2 left-4 -translate-y-1/2 w-10 h-10 bg-dark/80 rounded-full flex items-center justify-center border border-gray-800 hover:border-primary transition-all duration-300 z-10">
                    <i class="fa fa-chevron-left"></i>
                </button>
                <button id="nextTestimonial"
                        class="absolute top-1/2 right-4 -translate-y-1/2 w-10 h-10 bg-dark/80 rounded-full flex items-center justify-center border border-gray-800 hover:border-primary transition-all duration-300 z-10">
                    <i class="fa fa-chevron-right"></i>
                </button>
            </div>

            <!-- 指示器 -->
            <div class="flex justify-center mt-8 space-x-2">
                <button class="testimonial-dot w-3 h-3 rounded-full bg-primary"></button>
                <button class="testimonial-dot w-3 h-3 rounded-full bg-gray-600"></button>
                <button class="testimonial-dot w-3 h-3 rounded-full bg-gray-600"></button>
                <button class="testimonial-dot w-3 h-3 rounded-full bg-gray-600"></button>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section id="contact" class="py-20 bg-dark relative overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
            <div class="absolute top-20 left-20 w-96 h-96 bg-primary/5 rounded-full filter blur-3xl"></div>
            <div class="absolute bottom-20 right-20 w-96 h-96 bg-secondary/5 rounded-full filter blur-3xl"></div>
        </div>

        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row gap-12">
                <div class="lg:w-1/2">
                    <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-6">联系我们</h2>
                    <p class="text-lg text-gray-300 mb-8">
                        无论您有任何疑问或需求，我们的团队都随时准备为您提供帮助和支持。填写下方表单或通过以下方式联系我们，我们将尽快回复您。
                    </p>

                    <!-- 联系信息 -->
                    <div class="space-y-8 mb-12">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-map-marker text-primary text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">公司地址</h4>
                                <p class="text-gray-300">
                                    深圳市龙华区龙华街道玉翠社区二区16号505
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-envelope text-primary text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">电子邮箱</h4>
                                <p class="text-gray-300 mb-1">
                                    <a href="mailto:<EMAIL>"
                                       class="hover:text-primary transition-colors duration-300"><EMAIL></a>
                                </p>
                                <p class="text-gray-300">
                                    <a href="mailto:<EMAIL>"
                                       class="hover:text-primary transition-colors duration-300"><EMAIL></a>
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-clock-o text-primary text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">工作时间</h4>
                                <p class="text-gray-300">
                                    周一至周五: 9:00 - 18:00<br>
                                    周六至周日: 休息
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- 社交媒体 -->
                    <div>
                        <h4 class="text-xl font-bold mb-4">关注我们</h4>
                        <div class="flex space-x-4">
                            <a href="#"
                               class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center hover:bg-primary/20 transition-all duration-300">
                                <i class="fa fa-weixin text-primary text-xl"></i>
                            </a>
                            <a href="#"
                               class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center hover:bg-primary/20 transition-all duration-300">
                                <i class="fa fa-weibo text-primary text-xl"></i>
                            </a>
                            <a href="#"
                               class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center hover:bg-primary/20 transition-all duration-300">
                                <i class="fa fa-linkedin text-primary text-xl"></i>
                            </a>
                            <a href="#"
                               class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center hover:bg-primary/20 transition-all duration-300">
                                <i class="fa fa-github text-primary text-xl"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="lg:w-1/2">
                    <div class="bg-dark/50 border border-gray-800 rounded-xl p-8">
                        <h3 class="text-2xl font-bold mb-6">发送咨询</h3>
                        <form id="contactForm">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label for="name" class="block text-gray-300 mb-2">姓名</label>
                                    <input type="text" id="name" name="name"
                                           class="w-full bg-dark/80 border border-gray-700 rounded-lg px-4 py-3 text-light focus:border-primary focus:outline-none transition-colors duration-300"
                                           placeholder="请输入您的姓名" required>
                                </div>
                                <div>
                                    <label for="email" class="block text-gray-300 mb-2">电子邮箱</label>
                                    <input type="email" id="email" name="email"
                                           class="w-full bg-dark/80 border border-gray-700 rounded-lg px-4 py-3 text-light focus:border-primary focus:outline-none transition-colors duration-300"
                                           placeholder="请输入您的电子邮箱" required>
                                </div>
                            </div>

                            <div class="mb-6">
                                <label for="company" class="block text-gray-300 mb-2">公司名称</label>
                                <input type="text" id="company" name="company"
                                       class="w-full bg-dark/80 border border-gray-700 rounded-lg px-4 py-3 text-light focus:border-primary focus:outline-none transition-colors duration-300"
                                       placeholder="请输入您的公司名称">
                            </div>

                            <div class="mb-6">
                                <label for="service" class="block text-gray-300 mb-2">感兴趣的服务</label>
                                <select id="service" name="service"
                                        class="w-full bg-dark/80 border border-gray-700 rounded-lg px-4 py-3 text-light focus:border-primary focus:outline-none transition-colors duration-300">
                                    <option value="">请选择服务类型</option>
                                    <option value="software">软件开发</option>
                                    <option value="app">APP开发</option>
                                    <option value="maintenance">电子产品维护</option>
                                    <option value="cloud">云计算服务</option>
                                    <option value="security">网络安全服务</option>
                                    <option value="data">数据分析服务</option>
                                    <option value="other">其他服务</option>
                                </select>
                            </div>

                            <div class="mb-8">
                                <label for="message" class="block text-gray-300 mb-2">咨询内容</label>
                                <textarea id="message" name="message" rows="5"
                                          class="w-full bg-dark/80 border border-gray-700 rounded-lg px-4 py-3 text-light focus:border-primary focus:outline-none transition-colors duration-300"
                                          placeholder="请详细描述您的需求或疑问" required></textarea>
                            </div>

                            <button type="submit"
                                    class="w-full py-3 bg-gradient-to-r from-primary to-secondary rounded-lg hover:shadow-lg hover:shadow-primary/20 transition-all duration-300 text-lg font-medium">
                                发送咨询
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<!-- 页脚 -->
<footer class="bg-dark border-t border-gray-800 py-12">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div>
                <a href="#" class="flex items-center space-x-2 mb-6">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center">
                        <span class="text-xl font-bold">3S</span>
                    </div>
                    <span class="text-xl font-bold">三子宇宙科技</span>
                </a>
                <p class="text-gray-300 mb-6">
                    专注于软件开发、APP开发和电子产品维护，为企业提供全方位的数字化解决方案。
                </p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-300">
                        <i class="fa fa-weixin text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-300">
                        <i class="fa fa-weibo text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-300">
                        <i class="fa fa-linkedin text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-300">
                        <i class="fa fa-github text-xl"></i>
                    </a>
                </div>
            </div>

            <div>
                <h4 class="text-xl font-bold mb-6">快速链接</h4>
                <ul class="space-y-4">
                    <li><a href="#home" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="nav.home">Home</a></li>
                    <li><a href="#services" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="nav.services">Services</a></li>
                    <li><a href="#cases" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="nav.cases">Cases</a></li>
                    <li><a href="#about" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="nav.about">About Us</a></li>
                    <li><a href="#contact" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="nav.contact">Contact Us</a></li>
                </ul>
            </div>

            <div>
                <h4 class="text-xl font-bold mb-6" data-i18n="footer.services_title">Services</h4>
                <ul class="space-y-4">
                    <li><a href="#services" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="services.software.title">Software Development</a></li>
                    <li><a href="#services" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="services.app.title">APP Development</a></li>
                    <li><a href="#services" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="services.maintenance.title">Electronic Product Maintenance</a></li>
                    <li><a href="#services" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="services.cloud.title">Cloud Computing Services</a></li>
                    <li><a href="#services" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="services.security.title">Network Security Services</a></li>
                    <li><a href="#services" class="text-gray-300 hover:text-primary transition-colors duration-300" data-i18n="services.data.title">Data Analysis Services</a></li>
                </ul>
            </div>

            <div>
                <h4 class="text-xl font-bold mb-6" data-i18n="footer.contact_title">Contact Information</h4>
                <ul class="space-y-4">
                    <li class="flex items-start space-x-2">
                        <i class="fa fa-map-marker text-primary mt-1"></i>
                        <span class="text-gray-300" data-i18n="contact.address.content">Room 505, Building 16, District 2, Yucui Community, Longhua Street, Longhua District, Shenzhen</span>
                    </li>
                    <li class="flex items-start space-x-2">
                        <i class="fa fa-envelope text-primary mt-1"></i>
                        <div class="text-gray-300">
                            <a href="mailto:<EMAIL>" class="hover:text-primary transition-colors duration-300"><EMAIL></a><br>
                            <a href="mailto:<EMAIL>" class="hover:text-primary transition-colors duration-300"><EMAIL></a>
                        </div>
                    </li>
                    <li class="flex items-start space-x-2">
                        <i class="fa fa-clock-o text-primary mt-1"></i>
                        <span class="text-gray-300" data-i18n="contact.hours.content">Monday to Friday: 9:00 - 18:00<br>Saturday to Sunday: Closed</span>
                    </li>
                </ul>
            </div>

            <div>
                <h4 class="text-xl font-bold mb-6" data-i18n="contact.follow">Follow Us</h4>
                <div class="flex space-x-4 mb-6">
                    <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary transition-all duration-300">
                        <i class="fa fa-weixin"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary transition-all duration-300">
                        <i class="fa fa-weibo"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary transition-all duration-300">
                        <i class="fa fa-linkedin"></i>
                    </a>
                    <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary transition-all duration-300">
                        <i class="fa fa-github"></i>
                    </a>
                </div>

                <!-- Newsletter Signup -->
                <div>
                    <p class="text-gray-300 mb-4">Subscribe to our newsletter for updates</p>
                    <div class="flex">
                        <input type="email" placeholder="Enter your email"
                               class="flex-1 bg-gray-800 border border-gray-700 rounded-l-lg px-4 py-2 text-light focus:border-primary focus:outline-none">
                        <button class="px-4 py-2 bg-primary rounded-r-lg hover:bg-primary/80 transition-colors duration-300">
                            <i class="fa fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="border-t border-gray-800 pt-8 mt-12">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 mb-4 md:mb-0" data-i18n="footer.copyright">© 2024 Sanzi Universe Technology Co., Ltd. All rights reserved.</p>
                <div class="flex items-center space-x-6">
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-300">Privacy Policy</a>
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-300">Terms of Service</a>
                    <span class="text-gray-400" data-i18n="footer.icp">ICP Registration: 粤ICP备2021XXXXXX号</span>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Back to Top Button -->
<button id="backToTop" class="fixed bottom-8 right-8 w-12 h-12 bg-primary rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 opacity-0 invisible z-50">
    <i class="fa fa-chevron-up text-white"></i>
</button>

<!-- JavaScript -->
<script>
    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initI18n();
        initNavbar();
        initMobileMenu();
        initCounters();
        initCaseFilter();
        initTestimonialSlider();
        initContactForm();
        initBackToTop();
        initProjectChart();
        updateLanguageButtons();
    });

    // Navbar scroll effect
    function initNavbar() {
        const navbar = document.getElementById('navbar');

        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                navbar.classList.add('bg-dark/95', 'bg-glass', 'shadow-lg');
            } else {
                navbar.classList.remove('bg-dark/95', 'bg-glass', 'shadow-lg');
            }
        });
    }

    // Mobile menu toggle
    function initMobileMenu() {
        const menuBtn = document.getElementById('menuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        menuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            const icon = menuBtn.querySelector('i');
            icon.classList.toggle('fa-bars');
            icon.classList.toggle('fa-times');
        });

        // Close mobile menu when clicking on links
        const mobileLinks = mobileMenu.querySelectorAll('a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
                const icon = menuBtn.querySelector('i');
                icon.classList.add('fa-bars');
                icon.classList.remove('fa-times');
            });
        });
    }

    // Update language buttons for mobile
    function updateLanguageButtons() {
        const enBtn = document.getElementById('lang-en');
        const zhBtn = document.getElementById('lang-zh');
        const mobileEnBtn = document.getElementById('mobile-lang-en');
        const mobileZhBtn = document.getElementById('mobile-lang-zh');

        const buttons = [
            { en: enBtn, zh: zhBtn },
            { en: mobileEnBtn, zh: mobileZhBtn }
        ];

        buttons.forEach(buttonSet => {
            if (buttonSet.en && buttonSet.zh) {
                if (currentLang === 'en') {
                    buttonSet.en.classList.add('bg-primary', 'text-white');
                    buttonSet.en.classList.remove('bg-gray-700', 'text-gray-300');
                    buttonSet.zh.classList.add('bg-gray-700', 'text-gray-300');
                    buttonSet.zh.classList.remove('bg-primary', 'text-white');
                } else {
                    buttonSet.zh.classList.add('bg-primary', 'text-white');
                    buttonSet.zh.classList.remove('bg-gray-700', 'text-gray-300');
                    buttonSet.en.classList.add('bg-gray-700', 'text-gray-300');
                    buttonSet.en.classList.remove('bg-primary', 'text-white');
                }
            }
        });
    }

    // Animated counters
    function initCounters() {
        const counters = [
            { id: 'counter1', target: 150 },
            { id: 'counter2', target: 200 },
            { id: 'counter3', target: 50 },
            { id: 'counter4', target: 5 }
        ];

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = counters.find(c => c.id === entry.target.id);
                    if (counter) {
                        animateCounter(entry.target, counter.target);
                        observer.unobserve(entry.target);
                    }
                }
            });
        });

        counters.forEach(counter => {
            const element = document.getElementById(counter.id);
            if (element) observer.observe(element);
        });
    }

    function animateCounter(element, target) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 20);
    }

    // Case filter functionality
    function initCaseFilter() {
        const filterButtons = document.querySelectorAll('.case-filter');
        const caseItems = document.querySelectorAll('.case-item');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');

                // Update active button
                filterButtons.forEach(btn => {
                    btn.classList.remove('bg-primary', 'text-light');
                    btn.classList.add('bg-dark/50', 'border', 'border-gray-800');
                });
                this.classList.add('bg-primary', 'text-light');
                this.classList.remove('bg-dark/50', 'border', 'border-gray-800');

                // Filter cases
                caseItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-category') === filter) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    }

    // Testimonial slider
    function initTestimonialSlider() {
        const wrapper = document.getElementById('testimonialWrapper');
        const prevBtn = document.getElementById('prevTestimonial');
        const nextBtn = document.getElementById('nextTestimonial');
        const dots = document.querySelectorAll('.testimonial-dot');

        if (!wrapper || !prevBtn || !nextBtn) return;

        let currentSlide = 0;
        const totalSlides = 4;

        function updateSlider() {
            const translateX = -currentSlide * (100 / 3); // Show 3 items at once on desktop
            wrapper.style.transform = `translateX(${translateX}%)`;

            // Update dots
            dots.forEach((dot, index) => {
                if (index === currentSlide) {
                    dot.classList.add('bg-primary');
                    dot.classList.remove('bg-gray-600');
                } else {
                    dot.classList.add('bg-gray-600');
                    dot.classList.remove('bg-primary');
                }
            });
        }

        prevBtn.addEventListener('click', function() {
            currentSlide = currentSlide > 0 ? currentSlide - 1 : totalSlides - 1;
            updateSlider();
        });

        nextBtn.addEventListener('click', function() {
            currentSlide = currentSlide < totalSlides - 1 ? currentSlide + 1 : 0;
            updateSlider();
        });

        dots.forEach((dot, index) => {
            dot.addEventListener('click', function() {
                currentSlide = index;
                updateSlider();
            });
        });

        // Auto-play
        setInterval(() => {
            currentSlide = currentSlide < totalSlides - 1 ? currentSlide + 1 : 0;
            updateSlider();
        }, 5000);
    }

    // Contact form handling
    function initContactForm() {
        const form = document.getElementById('contactForm');
        if (!form) return;

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Simple validation
            if (!data.name || !data.email || !data.message) {
                alert(currentLang === 'zh' ? '请填写所有必填字段' : 'Please fill in all required fields');
                return;
            }

            // Simulate form submission
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = currentLang === 'zh' ? '发送中...' : 'Sending...';
            submitBtn.disabled = true;

            setTimeout(() => {
                alert(currentLang === 'zh' ? '感谢您的咨询！我们将尽快回复您。' : 'Thank you for your inquiry! We will get back to you soon.');
                form.reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
    }

    // Back to top button
    function initBackToTop() {
        const backToTopBtn = document.getElementById('backToTop');

        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTopBtn.classList.remove('opacity-0', 'invisible');
                backToTopBtn.classList.add('opacity-100', 'visible');
            } else {
                backToTopBtn.classList.add('opacity-0', 'invisible');
                backToTopBtn.classList.remove('opacity-100', 'visible');
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Project completion chart
    function initProjectChart() {
        const ctx = document.getElementById('projectChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: [
                    currentLang === 'zh' ? '已完成' : 'Completed',
                    currentLang === 'zh' ? '进行中' : 'In Progress',
                    currentLang === 'zh' ? '计划中' : 'Planned'
                ],
                datasets: [{
                    data: [85, 10, 5],
                    backgroundColor: ['#165DFF', '#722ED1', '#06B6D4'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#F8FAFC',
                            padding: 20
                        }
                    }
                }
            }
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
</script>

</body>
</html>