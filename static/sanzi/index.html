<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三子宇宙科技 - 创新科技解决方案提供商</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
    <script>
        // Tailwind 配置
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#722ED1',
                        dark: '#0F172A',
                        light: '#F8FAFC',
                        accent: '#06B6D4'
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .text-gradient {
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .bg-glass {
                backdrop-filter: blur(8px);
                -webkit-backdrop-filter: blur(8px);
            }
            .animate-float {
                animation: float 6s ease-in-out infinite;
            }
            .animate-pulse-slow {
                animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="font-inter bg-dark text-light overflow-x-hidden">
<!-- 导航栏 -->
<header id="navbar" class="fixed w-full top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
            <a href="#" class="flex items-center space-x-2">
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center">
                    <span class="text-xl font-bold">3S</span>
                </div>
                <span class="text-xl font-bold">三子宇宙科技</span>
            </a>

            <!-- 桌面导航 -->
            <nav class="hidden md:flex space-x-8">
                <a href="#home" class="text-light hover:text-primary transition-colors duration-300">首页</a>
                <a href="#services" class="text-light hover:text-primary transition-colors duration-300">服务</a>
                <a href="#cases" class="text-light hover:text-primary transition-colors duration-300">案例</a>
                <a href="#about" class="text-light hover:text-primary transition-colors duration-300">关于我们</a>
                <a href="#contact" class="text-light hover:text-primary transition-colors duration-300">联系我们</a>
            </nav>

            <!-- 联系按钮 -->
            <a href="#contact"
               class="hidden md:block px-6 py-2 bg-gradient-to-r from-primary to-secondary rounded-lg hover:shadow-lg hover:shadow-primary/20 transition-all duration-300">
                立即咨询
            </a>

            <!-- 移动端菜单按钮 -->
            <button id="menuBtn" class="md:hidden text-2xl">
                <i class="fa fa-bars"></i>
            </button>
        </div>
    </div>

    <!-- 移动端导航菜单 -->
    <div id="mobileMenu" class="md:hidden bg-dark/95 bg-glass hidden">
        <div class="container mx-auto px-4 py-4 flex flex-col space-y-4">
            <a href="#home" class="py-2 text-light hover:text-primary transition-colors duration-300">首页</a>
            <a href="#services" class="py-2 text-light hover:text-primary transition-colors duration-300">服务</a>
            <a href="#cases" class="py-2 text-light hover:text-primary transition-colors duration-300">案例</a>
            <a href="#about" class="py-2 text-light hover:text-primary transition-colors duration-300">关于我们</a>
            <a href="#contact" class="py-2 text-light hover:text-primary transition-colors duration-300">联系我们</a>
            <a href="#contact" class="px-6 py-2 bg-gradient-to-r from-primary to-secondary rounded-lg text-center">
                立即咨询
            </a>
        </div>
    </div>
</header>

<main>
    <!-- 英雄区 -->
    <section id="home" class="pt-32 pb-20 md:pt-40 md:pb-32 relative overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
            <div class="absolute top-20 right-20 w-96 h-96 bg-primary/10 rounded-full filter blur-3xl"></div>
            <div class="absolute bottom-20 left-20 w-96 h-96 bg-secondary/10 rounded-full filter blur-3xl"></div>
            <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[1000px] h-[1000px] bg-accent/5 rounded-full filter blur-3xl"></div>
        </div>

        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-12 md:mb-0">
                    <h1 class="text-[clamp(2.5rem,5vw,4rem)] font-bold leading-tight mb-6">
                        创新科技<br>
                        <span class="bg-gradient-to-r from-primary to-secondary text-gradient">重塑未来</span>
                    </h1>
                    <p class="text-lg md:text-xl text-gray-300 mb-8 max-w-lg">
                        三子宇宙科技专注于软件开发、APP开发与电子产品维护，为企业提供全方位的数字化解决方案，助力企业实现技术创新与业务增长。
                    </p>
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <a href="#contact"
                           class="px-8 py-3 bg-gradient-to-r from-primary to-secondary rounded-lg hover:shadow-lg hover:shadow-primary/20 transition-all duration-300 text-center">
                            咨询解决方案
                        </a>
                        <a href="#services"
                           class="px-8 py-3 border border-primary text-primary hover:bg-primary/10 rounded-lg transition-all duration-300 text-center">
                            了解我们的服务
                        </a>
                    </div>

                    <!-- 信任标识 -->
                    <div class="mt-12">
                        <p class="text-gray-400 text-sm mb-4">受到行业领先企业的信任</p>
                        <div class="flex flex-wrap items-center gap-8 opacity-70">
                            <img src="https://picsum.photos/id/1/100/40" alt="合作伙伴"
                                 class="h-6 grayscale hover:grayscale-0 transition-all duration-300">
                            <img src="https://picsum.photos/id/2/100/40" alt="合作伙伴"
                                 class="h-6 grayscale hover:grayscale-0 transition-all duration-300">
                            <img src="https://picsum.photos/id/3/100/40" alt="合作伙伴"
                                 class="h-6 grayscale hover:grayscale-0 transition-all duration-300">
                            <img src="https://picsum.photos/id/4/100/40" alt="合作伙伴"
                                 class="h-6 grayscale hover:grayscale-0 transition-all duration-300">
                        </div>
                    </div>
                </div>

                <div class="md:w-1/2 relative">
                    <div class="relative z-10 animate-float">
                        <img src="https://picsum.photos/id/0/600/400" alt="科技解决方案"
                             class="rounded-xl shadow-2xl shadow-primary/10">
                        <div class="absolute -bottom-6 -left-6 bg-dark p-4 rounded-lg shadow-lg border border-primary/20">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                                    <i class="fa fa-line-chart text-primary text-xl"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-400">项目完成率</p>
                                    <p class="text-xl font-bold">98.7%</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 装饰元素 -->
                    <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full border-2 border-primary/20 rounded-xl -z-10 rotate-3"></div>
                    <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full border-2 border-secondary/20 rounded-xl -z-10 -rotate-3"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- 服务介绍 -->
    <section id="services" class="py-20 bg-gradient-to-b from-dark to-dark/90">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-4">我们的核心服务</h2>
                <p class="text-lg text-gray-300">
                    凭借专业的技术团队和丰富的行业经验，我们为客户提供定制化的科技解决方案，满足不同行业的多样化需求。
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 服务卡片 1 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-all duration-300">
                        <i class="fa fa-code text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">软件开发</h3>
                    <p class="text-gray-300 mb-6">
                        提供定制化软件开发服务，包括企业管理系统、客户关系管理系统、电商平台等，满足企业数字化转型需求。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span>定制化系统开发</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span>企业级应用开发</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span>系统集成与优化</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-primary font-medium hover:text-primary/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 服务卡片 2 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-secondary/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-secondary/20 transition-all duration-300">
                        <i class="fa fa-mobile text-secondary text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">APP开发</h3>
                    <p class="text-gray-300 mb-6">
                        专注于iOS和Android平台的移动应用开发，从需求分析到产品上线提供全流程服务，打造用户体验卓越的移动产品。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>iOS应用开发</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>Android应用开发</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>跨平台应用开发</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-secondary font-medium hover:text-secondary/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 服务卡片 3 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-accent/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-accent/20 transition-all duration-300">
                        <i class="fa fa-microchip text-accent text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">电子产品维护</h3>
                    <p class="text-gray-300 mb-6">
                        提供专业的电子产品维护与技术支持服务，包括硬件维修、系统升级、故障诊断等，确保设备稳定运行。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>硬件维修与维护</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>系统升级与优化</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>技术支持与咨询</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-accent font-medium hover:text-accent/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 服务卡片 4 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-all duration-300">
                        <i class="fa fa-cloud text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">云计算服务</h3>
                    <p class="text-gray-300 mb-6">
                        提供云计算解决方案，包括云服务器部署、云存储服务、云应用开发等，帮助企业降低IT成本，提高运营效率。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span>云服务器部署</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span>云存储解决方案</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-primary"></i>
                            <span>云应用开发</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-primary font-medium hover:text-primary/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 服务卡片 5 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-secondary/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-secondary/20 transition-all duration-300">
                        <i class="fa fa-shield text-secondary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">网络安全服务</h3>
                    <p class="text-gray-300 mb-6">
                        提供全面的网络安全解决方案，包括漏洞检测、数据加密、防火墙配置等，保障企业信息安全和业务稳定运行。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>漏洞检测与修复</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>数据加密与保护</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-secondary"></i>
                            <span>安全咨询与培训</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-secondary font-medium hover:text-secondary/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 服务卡片 6 -->
                <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5 group">
                    <div class="w-14 h-14 bg-accent/10 rounded-lg flex items-center justify-center mb-6 group-hover:bg-accent/20 transition-all duration-300">
                        <i class="fa fa-database text-accent text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">数据分析服务</h3>
                    <p class="text-gray-300 mb-6">
                        提供专业的数据分析与可视化服务，帮助企业挖掘数据价值，做出数据驱动的决策，提升业务竞争力。
                    </p>
                    <ul class="space-y-2 mb-6">
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>数据采集与处理</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>数据可视化分析</span>
                        </li>
                        <li class="flex items-center space-x-2 text-gray-300">
                            <i class="fa fa-check text-accent"></i>
                            <span>数据挖掘与预测</span>
                        </li>
                    </ul>
                    <a href="#contact"
                       class="inline-flex items-center text-accent font-medium hover:text-accent/80 transition-colors duration-300">
                        了解更多 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- 数据统计 -->
    <section class="py-16 bg-gradient-to-r from-primary/10 to-secondary/10">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div class="p-6">
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-secondary text-gradient mb-2"
                         id="counter1">0
                    </div>
                    <p class="text-gray-300">成功项目</p>
                </div>
                <div class="p-6">
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-secondary text-gradient mb-2"
                         id="counter2">0
                    </div>
                    <p class="text-gray-300">满意客户</p>
                </div>
                <div class="p-6">
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-secondary text-gradient mb-2"
                         id="counter3">0
                    </div>
                    <p class="text-gray-300">专业工程师</p>
                </div>
                <div class="p-6">
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-secondary text-gradient mb-2"
                         id="counter4">0
                    </div>
                    <p class="text-gray-300">行业经验(年)</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 案例展示 -->
    <section id="cases" class="py-20 bg-dark">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-4">成功案例展示</h2>
                <p class="text-lg text-gray-300">
                    我们已为多个行业的客户提供了优质的技术解决方案，帮助客户实现业务增长和数字化转型。
                </p>
            </div>

            <!-- 案例筛选 -->
            <div class="flex flex-wrap justify-center gap-4 mb-12">
                <button class="case-filter px-6 py-2 rounded-full bg-primary text-light" data-filter="all">全部案例
                </button>
                <button class="case-filter px-6 py-2 rounded-full bg-dark/50 border border-gray-800 hover:border-primary/50 transition-all duration-300"
                        data-filter="software">软件开发
                </button>
                <button class="case-filter px-6 py-2 rounded-full bg-dark/50 border border-gray-800 hover:border-primary/50 transition-all duration-300"
                        data-filter="app">APP开发
                </button>
                <button class="case-filter px-6 py-2 rounded-full bg-dark/50 border border-gray-800 hover:border-primary/50 transition-all duration-300"
                        data-filter="maintenance">维护服务
                </button>
            </div>

            <!-- 案例网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 案例1 -->
                <div class="case-item group" data-category="software">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/20/600/400" alt="企业管理系统开发"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-primary/80 text-light text-sm rounded-full">软件开发</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">企业管理系统开发</h3>
                    <p class="text-gray-300 mb-4">
                        为制造业客户开发的定制化企业管理系统，整合生产、销售、库存等模块，提升管理效率30%。</p>
                    <a href="#"
                       class="inline-flex items-center text-primary font-medium hover:text-primary/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 案例2 -->
                <div class="case-item group" data-category="app">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/21/600/400" alt="电商APP开发"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-secondary/80 text-light text-sm rounded-full">APP开发</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">电商APP开发</h3>
                    <p class="text-gray-300 mb-4">
                        为零售客户开发的跨平台电商APP，支持多种支付方式和会员体系，上线后月活跃用户达5万+。</p>
                    <a href="#"
                       class="inline-flex items-center text-secondary font-medium hover:text-secondary/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 案例3 -->
                <div class="case-item group" data-category="maintenance">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/22/600/400" alt="医疗设备维护"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-accent/80 text-light text-sm rounded-full">维护服务</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">医疗设备维护</h3>
                    <p class="text-gray-300 mb-4">
                        为医疗机构提供的专业医疗设备维护服务，保障设备正常运行，故障率降低40%。</p>
                    <a href="#"
                       class="inline-flex items-center text-accent font-medium hover:text-accent/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 案例4 -->
                <div class="case-item group" data-category="software">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/23/600/400" alt="客户关系管理系统"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-primary/80 text-light text-sm rounded-full">软件开发</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">客户关系管理系统</h3>
                    <p class="text-gray-300 mb-4">
                        为服务行业客户开发的CRM系统，实现客户信息管理、服务跟踪和数据分析，客户满意度提升25%。</p>
                    <a href="#"
                       class="inline-flex items-center text-primary font-medium hover:text-primary/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 案例5 -->
                <div class="case-item group" data-category="app">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/24/600/400" alt="教育类APP开发"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-secondary/80 text-light text-sm rounded-full">APP开发</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">教育类APP开发</h3>
                    <p class="text-gray-300 mb-4">
                        为教育机构开发的在线学习APP，支持视频课程、在线测试和学习跟踪，注册用户突破10万。</p>
                    <a href="#"
                       class="inline-flex items-center text-secondary font-medium hover:text-secondary/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- 案例6 -->
                <div class="case-item group" data-category="maintenance">
                    <div class="relative overflow-hidden rounded-xl mb-4">
                        <img src="https://picsum.photos/id/25/600/400" alt="工业设备维护"
                             class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-6">
                                <span class="px-3 py-1 bg-accent/80 text-light text-sm rounded-full">维护服务</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold mb-2">工业设备维护</h3>
                    <p class="text-gray-300 mb-4">
                        为制造企业提供的工业设备预防性维护服务，延长设备使用寿命，降低维护成本35%。</p>
                    <a href="#"
                       class="inline-flex items-center text-accent font-medium hover:text-accent/80 transition-colors duration-300">
                        查看详情 <i class="fa fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="#contact"
                   class="inline-block px-8 py-3 bg-gradient-to-r from-primary to-secondary rounded-lg hover:shadow-lg hover:shadow-primary/20 transition-all duration-300">
                    查看更多案例
                </a>
            </div>
        </div>
    </section>

    <!-- 关于我们 -->
    <section id="about" class="py-20 bg-gradient-to-b from-dark to-dark/90 relative overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
            <div class="absolute top-40 right-40 w-80 h-80 bg-primary/5 rounded-full filter blur-3xl"></div>
            <div class="absolute bottom-40 left-40 w-80 h-80 bg-secondary/5 rounded-full filter blur-3xl"></div>
        </div>

        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row items-center gap-12">
                <div class="lg:w-1/2">
                    <div class="relative">
                        <img src="https://picsum.photos/id/48/600/600" alt="公司团队"
                             class="rounded-xl shadow-2xl shadow-primary/10">
                        <div class="absolute -bottom-6 -right-6 bg-dark p-6 rounded-lg shadow-lg border border-primary/20 max-w-xs">
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="flex -space-x-2">
                                    <img src="https://picsum.photos/id/64/100/100" alt="团队成员"
                                         class="w-10 h-10 rounded-full border-2 border-dark">
                                    <img src="https://picsum.photos/id/65/100/100" alt="团队成员"
                                         class="w-10 h-10 rounded-full border-2 border-dark">
                                    <img src="https://picsum.photos/id/66/100/100" alt="团队成员"
                                         class="w-10 h-10 rounded-full border-2 border-dark">
                                    <div class="w-10 h-10 rounded-full border-2 border-dark bg-primary/20 flex items-center justify-center text-primary text-xs font-bold">
                                        +20
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-400">专业团队</p>
                                    <p class="font-bold">50+ 技术专家</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 text-yellow-400">
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star-half-o"></i>
                                <span class="ml-2 text-light">4.8/5 客户评分</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="lg:w-1/2">
                    <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-6">关于三子宇宙科技</h2>
                    <p class="text-lg text-gray-300 mb-6">
                        深圳市三子宇宙科技有限公司成立于2021年10月，是一家专注于科技创新的企业，致力于为客户提供高质量的软件开发、APP开发和电子产品维护服务。
                    </p>
                    <p class="text-lg text-gray-300 mb-8">
                        我们拥有一支经验丰富、技术精湛的专业团队，员工规模虽不到50人，但每位成员都具备扎实的技术功底和丰富的项目经验。我们始终坚持以客户需求为导向，以技术创新为动力，为客户提供定制化的解决方案，帮助客户在数字化时代保持竞争优势。
                    </p>

                    <!-- 公司优势 -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-check text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">专业技术团队</h4>
                                <p class="text-gray-300">拥有多年行业经验的技术专家，确保项目质量</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-check text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">定制化解决方案</h4>
                                <p class="text-gray-300">根据客户需求提供个性化的技术解决方案</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-check text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">优质客户服务</h4>
                                <p class="text-gray-300">提供7x24小时技术支持，确保服务质量</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-check text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">合理的价格体系</h4>
                                <p class="text-gray-300">提供高性价比的服务，帮助客户控制成本</p>
                            </div>
                        </div>
                    </div>

                    <!-- 数据图表 -->
                    <div class="bg-dark/50 border border-gray-800 rounded-xl p-6 mb-8">
                        <h4 class="text-xl font-bold mb-4">项目完成情况</h4>
                        <canvas id="projectChart" height="200"></canvas>
                    </div>

                    <a href="#contact"
                       class="inline-block px-8 py-3 bg-gradient-to-r from-primary to-secondary rounded-lg hover:shadow-lg hover:shadow-primary/20 transition-all duration-300">
                        了解更多关于我们
                    </a>
                </div>
            </div>

            <!-- 团队介绍 -->
            <div class="mt-24">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-4">认识我们的团队</h2>
                    <p class="text-lg text-gray-300">
                        我们的团队由一群充满激情、技术精湛的专业人士组成，致力于为客户提供最优质的技术解决方案。
                    </p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- 团队成员1 -->
                    <div class="bg-dark/50 border border-gray-800 rounded-xl overflow-hidden group hover:border-primary/50 transition-all duration-300">
                        <div class="relative overflow-hidden">
                            <img src="https://picsum.photos/id/64/400/400" alt="张明 - 技术总监"
                                 class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                <div class="flex space-x-4">
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-linkedin"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-github"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-1">张明</h3>
                            <p class="text-primary mb-4">技术总监</p>
                            <p class="text-gray-300">
                                拥有10年软件开发经验，曾主导多个大型企业级项目，擅长技术架构设计和团队管理。
                            </p>
                        </div>
                    </div>

                    <!-- 团队成员2 -->
                    <div class="bg-dark/50 border border-gray-800 rounded-xl overflow-hidden group hover:border-primary/50 transition-all duration-300">
                        <div class="relative overflow-hidden">
                            <img src="https://picsum.photos/id/65/400/400" alt="李娜 - 产品经理"
                                 class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                <div class="flex space-x-4">
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-linkedin"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-github"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-1">李娜</h3>
                            <p class="text-secondary mb-4">产品经理</p>
                            <p class="text-gray-300">
                                8年产品设计经验，擅长用户体验设计和产品规划，曾负责多个成功APP产品的设计与上线。
                            </p>
                        </div>
                    </div>

                    <!-- 团队成员3 -->
                    <div class="bg-dark/50 border border-gray-800 rounded-xl overflow-hidden group hover:border-primary/50 transition-all duration-300">
                        <div class="relative overflow-hidden">
                            <img src="https://picsum.photos/id/66/400/400" alt="王强 - 开发工程师"
                                 class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                <div class="flex space-x-4">
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-linkedin"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-github"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-1">王强</h3>
                            <p class="text-accent mb-4">开发工程师</p>
                            <p class="text-gray-300">
                                精通前后端开发技术，5年软件开发经验，擅长解决复杂技术问题，曾参与多个大型项目开发。
                            </p>
                        </div>
                    </div>

                    <!-- 团队成员4 -->
                    <div class="bg-dark/50 border border-gray-800 rounded-xl overflow-hidden group hover:border-primary/50 transition-all duration-300">
                        <div class="relative overflow-hidden">
                            <img src="https://picsum.photos/id/67/400/400" alt="赵静 - 运维工程师"
                                 class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                <div class="flex space-x-4">
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-linkedin"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-github"></i>
                                    </a>
                                    <a href="#"
                                       class="w-10 h-10 bg-primary/80 rounded-full flex items-center justify-center hover:bg-primary transition-colors duration-300">
                                        <i class="fa fa-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-1">赵静</h3>
                            <p class="text-primary mb-4">运维工程师</p>
                            <p class="text-gray-300">
                                6年IT运维经验，精通服务器管理和网络维护，擅长保障系统稳定运行和数据安全。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 客户评价 -->
    <section class="py-20 bg-gradient-to-r from-primary/5 to-secondary/5">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-4">客户对我们的评价</h2>
                <p class="text-lg text-gray-300">
                    听听我们的客户怎么说，他们的成功是我们最大的动力。
                </p>
            </div>

            <div class="testimonial-slider relative">
                <div class="overflow-hidden">
                    <div id="testimonialWrapper" class="flex transition-transform duration-500 ease-in-out">
                        <!-- 评价1 -->
                        <div class="testimonial-item min-w-full md:min-w-[50%] lg:min-w-[33.333%] p-4">
                            <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 h-full">
                                <div class="flex items-center space-x-2 text-yellow-400 mb-6">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                </div>
                                <p class="text-gray-300 mb-8 italic">
                                    "三子宇宙科技为我们开发的企业管理系统极大地提升了公司的运营效率，他们的技术团队专业且负责，能够快速响应我们的需求，提供优质的技术支持。"
                                </p>
                                <div class="flex items-center space-x-4">
                                    <img src="https://picsum.photos/id/91/100/100" alt="陈总 - 某制造企业CEO"
                                         class="w-14 h-14 rounded-full">
                                    <div>
                                        <h4 class="text-xl font-bold">陈总</h4>
                                        <p class="text-gray-400">某制造企业CEO</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 评价2 -->
                        <div class="testimonial-item min-w-full md:min-w-[50%] lg:min-w-[33.333%] p-4">
                            <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 h-full">
                                <div class="flex items-center space-x-2 text-yellow-400 mb-6">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                </div>
                                <p class="text-gray-300 mb-8 italic">
                                    "我们与三子宇宙科技合作开发了一款电商APP，他们的设计和开发能力令人印象深刻，APP上线后用户反馈非常好，帮助我们显著提升了线上销售额。"
                                </p>
                                <div class="flex items-center space-x-4">
                                    <img src="https://picsum.photos/id/92/100/100" alt="林总 - 某零售企业总经理"
                                         class="w-14 h-14 rounded-full">
                                    <div>
                                        <h4 class="text-xl font-bold">林总</h4>
                                        <p class="text-gray-400">某零售企业总经理</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 评价3 -->
                        <div class="testimonial-item min-w-full md:min-w-[50%] lg:min-w-[33.333%] p-4">
                            <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 h-full">
                                <div class="flex items-center space-x-2 text-yellow-400 mb-6">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star-half-o"></i>
                                </div>
                                <p class="text-gray-300 mb-8 italic">
                                    "三子宇宙科技提供的医疗设备维护服务非常专业，他们的工程师技术精湛，响应迅速，帮助我们降低了设备故障率，保障了医院的正常运营。"
                                </p>
                                <div class="flex items-center space-x-4">
                                    <img src="https://picsum.photos/id/93/100/100" alt="张主任 - 某医院设备科主任"
                                         class="w-14 h-14 rounded-full">
                                    <div>
                                        <h4 class="text-xl font-bold">张主任</h4>
                                        <p class="text-gray-400">某医院设备科主任</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 评价4 -->
                        <div class="testimonial-item min-w-full md:min-w-[50%] lg:min-w-[33.333%] p-4">
                            <div class="bg-dark/50 border border-gray-800 rounded-xl p-8 h-full">
                                <div class="flex items-center space-x-2 text-yellow-400 mb-6">
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                    <i class="fa fa-star"></i>
                                </div>
                                <p class="text-gray-300 mb-8 italic">
                                    "与三子宇宙科技的合作非常愉快，他们为我们开发的CRM系统帮助我们更好地管理客户关系，提升了客户满意度，他们的服务态度和技术能力都非常出色。"
                                </p>
                                <div class="flex items-center space-x-4">
                                    <img src="https://picsum.photos/id/94/100/100" alt="刘总 - 某服务企业总经理"
                                         class="w-14 h-14 rounded-full">
                                    <div>
                                        <h4 class="text-xl font-bold">刘总</h4>
                                        <p class="text-gray-400">某服务企业总经理</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 控制按钮 -->
                <button id="prevTestimonial"
                        class="absolute top-1/2 left-4 -translate-y-1/2 w-10 h-10 bg-dark/80 rounded-full flex items-center justify-center border border-gray-800 hover:border-primary transition-all duration-300 z-10">
                    <i class="fa fa-chevron-left"></i>
                </button>
                <button id="nextTestimonial"
                        class="absolute top-1/2 right-4 -translate-y-1/2 w-10 h-10 bg-dark/80 rounded-full flex items-center justify-center border border-gray-800 hover:border-primary transition-all duration-300 z-10">
                    <i class="fa fa-chevron-right"></i>
                </button>
            </div>

            <!-- 指示器 -->
            <div class="flex justify-center mt-8 space-x-2">
                <button class="testimonial-dot w-3 h-3 rounded-full bg-primary"></button>
                <button class="testimonial-dot w-3 h-3 rounded-full bg-gray-600"></button>
                <button class="testimonial-dot w-3 h-3 rounded-full bg-gray-600"></button>
                <button class="testimonial-dot w-3 h-3 rounded-full bg-gray-600"></button>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section id="contact" class="py-20 bg-dark relative overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
            <div class="absolute top-20 left-20 w-96 h-96 bg-primary/5 rounded-full filter blur-3xl"></div>
            <div class="absolute bottom-20 right-20 w-96 h-96 bg-secondary/5 rounded-full filter blur-3xl"></div>
        </div>

        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col lg:flex-row gap-12">
                <div class="lg:w-1/2">
                    <h2 class="text-[clamp(1.8rem,4vw,3rem)] font-bold mb-6">联系我们</h2>
                    <p class="text-lg text-gray-300 mb-8">
                        无论您有任何疑问或需求，我们的团队都随时准备为您提供帮助和支持。填写下方表单或通过以下方式联系我们，我们将尽快回复您。
                    </p>

                    <!-- 联系信息 -->
                    <div class="space-y-8 mb-12">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-map-marker text-primary text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">公司地址</h4>
                                <p class="text-gray-300">
                                    深圳市龙华区龙华街道玉翠社区二区16号505
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-envelope text-primary text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">电子邮箱</h4>
                                <p class="text-gray-300 mb-1">
                                    <a href="mailto:<EMAIL>"
                                       class="hover:text-primary transition-colors duration-300"><EMAIL></a>
                                </p>
                                <p class="text-gray-300">
                                    <a href="mailto:<EMAIL>"
                                       class="hover:text-primary transition-colors duration-300"><EMAIL></a>
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
                                <i class="fa fa-clock-o text-primary text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">工作时间</h4>
                                <p class="text-gray-300">
                                    周一至周五: 9:00 - 18:00<br>
                                    周六至周日: 休息
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- 社交媒体 -->
                    <div>
                        <h4 class="text-xl font-bold mb-4">关注我们</h4>
                        <div class="flex space-x-4">
                            <a href="#"
                               class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center hover:bg-primary/20 transition-all duration-300">
                                <i class="fa fa-weixin text-primary text-xl"></i>
                            </a>
                            <a href="#"
                               class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center hover:bg-primary/20 transition-all duration-300">
                                <i class="fa fa-weibo text-primary text-xl"></i>
                            </a>
                            <a href="#"
                               class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center hover:bg-primary/20 transition-all duration-300">
                                <i class="fa fa-linkedin text-primary text-xl"></i>
                            </a>
                            <a href="#"
                               class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center hover:bg-primary/20 transition-all duration-300">
                                <i class="fa fa-github text-primary text-xl"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="lg:w-1/2">
                    <div class="bg-dark/50 border border-gray-800 rounded-xl p-8">
                        <h3 class="text-2xl font-bold mb-6">发送咨询</h3>
                        <form id="contactForm">
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label for="name" class="block text-gray-300 mb-2">姓名</label>
                                    <input type="text" id="name" name="name"
                                           class="w-full bg-dark/80 border border-gray-700 rounded-lg px-4 py-3 text-light focus:border-primary focus:outline-none transition-colors duration-300"
                                           placeholder="请输入您的姓名" required>
                                </div>
                                <div>
                                    <label for="email" class="block text-gray-300 mb-2">电子邮箱</label>
                                    <input type="email" id="email" name="email"
                                           class="w-full bg-dark/80 border border-gray-700 rounded-lg px-4 py-3 text-light focus:border-primary focus:outline-none transition-colors duration-300"
                                           placeholder="请输入您的电子邮箱" required>
                                </div>
                            </div>

                            <div class="mb-6">
                                <label for="company" class="block text-gray-300 mb-2">公司名称</label>
                                <input type="text" id="company" name="company"
                                       class="w-full bg-dark/80 border border-gray-700 rounded-lg px-4 py-3 text-light focus:border-primary focus:outline-none transition-colors duration-300"
                                       placeholder="请输入您的公司名称">
                            </div>

                            <div class="mb-6">
                                <label for="service" class="block text-gray-300 mb-2">感兴趣的服务</label>
                                <select id="service" name="service"
                                        class="w-full bg-dark/80 border border-gray-700 rounded-lg px-4 py-3 text-light focus:border-primary focus:outline-none transition-colors duration-300">
                                    <option value="">请选择服务类型</option>
                                    <option value="software">软件开发</option>
                                    <option value="app">APP开发</option>
                                    <option value="maintenance">电子产品维护</option>
                                    <option value="cloud">云计算服务</option>
                                    <option value="security">网络安全服务</option>
                                    <option value="data">数据分析服务</option>
                                    <option value="other">其他服务</option>
                                </select>
                            </div>

                            <div class="mb-8">
                                <label for="message" class="block text-gray-300 mb-2">咨询内容</label>
                                <textarea id="message" name="message" rows="5"
                                          class="w-full bg-dark/80 border border-gray-700 rounded-lg px-4 py-3 text-light focus:border-primary focus:outline-none transition-colors duration-300"
                                          placeholder="请详细描述您的需求或疑问" required></textarea>
                            </div>

                            <button type="submit"
                                    class="w-full py-3 bg-gradient-to-r from-primary to-secondary rounded-lg hover:shadow-lg hover:shadow-primary/20 transition-all duration-300 text-lg font-medium">
                                发送咨询
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<!-- 页脚 -->
<footer class="bg-dark border-t border-gray-800 py-12">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <div>
                <a href="#" class="flex items-center space-x-2 mb-6">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-secondary flex items-center justify-center">
                        <span class="text-xl font-bold">3S</span>
                    </div>
                    <span class="text-xl font-bold">三子宇宙科技</span>
                </a>
                <p class="text-gray-300 mb-6">
                    专注于软件开发、APP开发和电子产品维护，为企业提供全方位的数字化解决方案。
                </p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-300">
                        <i class="fa fa-weixin text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-300">
                        <i class="fa fa-weibo text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-300">
                        <i class="fa fa-linkedin text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-300">
                        <i class="fa fa-github text-xl"></i>
                    </a>
                </div>
            </div>

            <div>
                <h4 class="text-xl font-bold mb-6">快速链接</h4>
                <ul class="space-y-4">
                    <li><a href="#home" class="text-gray-30