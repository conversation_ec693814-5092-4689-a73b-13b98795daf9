function toggleImageSize(image) {
    if (image.style.width === '50px') {
        image.style.width = '600px'; // 放大尺寸
        image.style.height = '600px';
    } else {
        image.style.width = '50px'; // 缩小尺寸
        image.style.height = '50px';
    }
}

 function toggleImageSize(image) {
    var modal = document.getElementById('modal');
    var modalImage = document.getElementById('modal-image');

    // 设置大图
    modalImage.src = image.src;

    // 显示模态框
    modal.style.display = 'flex';

    // 点击模态框外部关闭模态框
    modal.onclick = function() {
        modal.style.display = 'none';
    };
}