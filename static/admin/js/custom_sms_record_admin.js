window.addEventListener("load", function () {
    (function ($) {
        $(document).ready(function () {

            // 图片伪全屏
            const images = document.querySelectorAll('.zoomable');

            images.forEach(img => {
                img.addEventListener('click', function () {
                    if (this.classList.contains('fullscreen')) {
                        // 恢复原始样式
                        this.classList.remove('fullscreen');
                        document.body.style.overflow = ''; // 恢复页面滚动
                    } else {
                        // 设置伪全屏样式
                        this.classList.add('fullscreen');
                        document.body.style.overflow = 'hidden'; // 禁止页面滚动
                    }
                });
            });

            // 添加伪全屏样式
            const style = document.createElement('style');
            style.innerHTML = `
                .fullscreen {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100vw;
                    height: 100vh;
                    object-fit: contain;
                    background: rgba(0, 0, 0, 0.9);
                    z-index: 9999;
                    cursor: pointer;
                }
            `;
            document.head.appendChild(style);

            // 工具函数：回填输入框的值
            function fillInputFromParams($input, paramName) {
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has(paramName)) {
                    $input.val(urlParams.get(paramName));
                }
            }

            // 工具函数：生成输入框
            function createInput(id, placeholder) {
                return $(`<input type="text" id="${id}" placeholder="${placeholder}" style="margin-left: 5px;" />`);
            }

            // 创建选择框
            const $directionFilter = $('<select id="id_direction_filter" style="margin-left: 5px;">')
              .append('<option value="">All</option>')
              .append('<option value="SEND">SEND</option>')
              .append('<option value="RECIEVE">RECIEVE</option>');

            const $statusFilter = $('<select id="id_status_filter" style="margin-left: 5px;">')
              .append('<option value="">All</option>')
              .append('<option value="delivered">delivered</option>')
              .append('<option value="delivery_failed">delivery_failed</option>')
              .append('<option value="delivery_unconfirmed">delivery_unconfirmed</option>')
              .append('<option value="received">received</option>')
              .append('<option value="sending_failed">sending_failed</option>')
              .append('<option value="send_fake">send_fake</option>')
              .append('<option value="sent">sent</option>')
              .append('<option value="sent_failed">sent_failed</option>')
              .append('<option value="webhook_delivered">webhook_delivered</option>');

            // 创建 is_only_image 选择框
            const $isOnlyImageFilter = $('<select id="id_is_only_image_filter" style="margin-left: 5px;">')
              .append('<option value="">All</option>')
              .append('<option value="0">No</option>')
              .append('<option value="1">Yes</option>');

            // 创建字段输入框
            const $fromInput = createInput("id_from_filter", "From Number");
            const $toInput = createInput("id_to_filter", "To Number");
            const $sidInput = createInput("id_sid_filter", "SID");
            const $userIdInput = createInput("id_user_id_filter", "User ID");
            const $wangyiTaskIdInput = createInput("id_wangyi_task_id_filter", "Wangyi Task ID");
            const $contentInput = createInput("id_content_filter", "Content");

            const $startDateInput = createInput("id_start_date_filter", "Start Date");
            const $endDateInput = createInput("id_end_date_filter", "End Date");

            const $searchButton = $('<button type="submit" style="margin-left: 5px;">Search</button>');

            const $clearButton = $('<button type="button" style="margin-left: 5px;">Clear</button>');
            $clearButton.click(function () {
                // 清除所有输入框和选择框
                $directionFilter.val('');
                $statusFilter.val('');
                $isOnlyImageFilter.val('');
                $fromInput.val('');
                $toInput.val('');
                $sidInput.val('');
                $userIdInput.val('');
                $wangyiTaskIdInput.val('');
                $contentInput.val('');
                $startDateInput.val('');
                $endDateInput.val('');
            });


            // 创建并插入表单
            const $filterForm = $('<form id="search_form" method="GET" action="">')
              .append($clearButton)  // 新增的清除按钮
              .append($directionFilter)
              .append($statusFilter)
              .append($isOnlyImageFilter)
              .append($fromInput)
              .append($toInput)
              .append($sidInput)
              .append($userIdInput)
              .append($wangyiTaskIdInput)
              .append($contentInput)
              .append($startDateInput)
              .append($endDateInput)
              .append($searchButton)
              .prependTo('#changelist-form');

            const urlParams = new URLSearchParams(window.location.search);
            fillInputFromParams($directionFilter, 'direction');
            fillInputFromParams($statusFilter, 'status');
            fillInputFromParams($isOnlyImageFilter, 'is_image');
            fillInputFromParams($fromInput, 'from_number');
            fillInputFromParams($toInput, 'to_number');
            fillInputFromParams($sidInput, 'sid');
            fillInputFromParams($userIdInput, 'user_id');
            fillInputFromParams($wangyiTaskIdInput, 'wangyi_task_id');
            fillInputFromParams($contentInput, 'content__icontains');
            fillInputFromParams($startDateInput, 'created_at__gte');
            fillInputFromParams($endDateInput, 'created_at__lte');

            $filterForm.submit(function (e) {
                e.preventDefault();
                const query = [];

                const selectedDirection = $directionFilter.val();
                const selectedStatus = $statusFilter.val();
                const selectedIsOnlyImage = $isOnlyImageFilter.val();
                if (selectedDirection) query.push(`direction=${encodeURIComponent(selectedDirection)}`);
                if (selectedStatus) query.push(`status=${encodeURIComponent(selectedStatus)}`);
                if (selectedIsOnlyImage) query.push(`is_image=${encodeURIComponent(selectedIsOnlyImage)}`);

                const fromNumber = $fromInput.val().trim();
                const toNumber = $toInput.val().trim();
                const sid = $sidInput.val().trim();
                const userId = $userIdInput.val().trim();
                const wangyiTaskId = $wangyiTaskIdInput.val().trim();
                const content = $contentInput.val().trim();
                let startDate = $startDateInput.val().trim();
                let endDate = $endDateInput.val().trim();

                if (fromNumber) query.push(`from_number=${encodeURIComponent(fromNumber)}`);
                if (toNumber) query.push(`to_number=${encodeURIComponent(toNumber)}`);
                if (sid) query.push(`sid=${encodeURIComponent(sid)}`);
                if (userId) query.push(`user_id=${encodeURIComponent(userId)}`);
                if (wangyiTaskId) query.push(`wangyi_task_id=${encodeURIComponent(wangyiTaskId)}`);
                if (content) query.push(`content__icontains=${encodeURIComponent(content)}`);

                // 自动补全日期时间
                const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                if (startDate && dateRegex.test(startDate)) {
                    startDate += " 00:00:00";
                }
                if (endDate && dateRegex.test(endDate)) {
                    endDate += " 23:59:59";
                }

                if (startDate) query.push(`created_at__gte=${encodeURIComponent(startDate)}`);
                if (endDate) query.push(`created_at__lte=${encodeURIComponent(endDate)}`);

                const queryString = query.length > 0 ? `?${query.join('&')}` : '';
                window.location.href = `${window.location.pathname}${queryString}`;
            });
        });
    })(django.jQuery);
});
