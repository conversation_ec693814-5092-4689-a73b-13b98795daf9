window.addEventListener("load", function () {
    (function ($) {
        $(document).ready(function () {

            // 工具函数：回填输入框的值
            function fillInputFromParams($input, paramName) {
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has(paramName)) {
                    $input.val(urlParams.get(paramName));
                }
            }

            // 工具函数：生成输入框
            function createInput(id, placeholder) {
                return $(`<input type="text" id="${id}" placeholder="${placeholder}" style="margin-left: 5px;" />`);
            }

            // 创建字段输入框
            const $orderOriginalTransactionIdInput = createInput("id_order_original_transaction_id_filter", "Order Original Transaction ID");
            const $userIdInput = createInput("id_user_id_filter", "User ID");
            const $certMD5Input = createInput("id_cert_md5_filter", "Cert md5");
            const $startDateInput = createInput("id_start_date_filter", "Start Date");
            const $endDateInput = createInput("id_end_date_filter", "End Date");

            const $searchButton = $('<button type="submit" style="margin-left: 5px;">Search</button>');
            const $clearButton = $('<button type="button" style="margin-left: 5px;">Clear</button>');

            $clearButton.click(function () {
                $orderOriginalTransactionIdInput.val('');
                $userIdInput.val('');
                $certMD5Input.val('');
                $startDateInput.val('');
                $endDateInput.val('');
            });

            // 创建并插入表单
            const $filterForm = $('<form id="search_form" method="GET" action="">')
                .append($clearButton)
                .append($orderOriginalTransactionIdInput)
                .append($userIdInput)
                .append($certMD5Input)
                .append($startDateInput)
                .append($endDateInput)
                .append($searchButton)
                .prependTo('#changelist-form');

            const urlParams = new URLSearchParams(window.location.search);
            fillInputFromParams($orderOriginalTransactionIdInput, 'original_transaction_id');
            fillInputFromParams($userIdInput, 'user_id');
            fillInputFromParams($certMD5Input, 'cert_md5');
            fillInputFromParams($startDateInput, 'created_at__gte');
            fillInputFromParams($endDateInput, 'created_at__lte');

            $filterForm.submit(function (e) {
                e.preventDefault();
                const query = [];

                const orderOriginalTransactionId = $orderOriginalTransactionIdInput.val().trim();
                const userId = $userIdInput.val().trim();
                const certMD5 = $certMD5Input.val();
                let startDate = $startDateInput.val().trim();
                let endDate = $endDateInput.val().trim();

                if (orderOriginalTransactionId) query.push(`original_transaction_id=${encodeURIComponent(orderOriginalTransactionId)}`);
                if (userId) query.push(`user_id=${encodeURIComponent(userId)}`);
                if (certMD5) query.push(`cert_md5=${encodeURIComponent(certMD5)}`);

                const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                if (startDate && dateRegex.test(startDate)) {
                    startDate += " 00:00:00";
                }
                if (endDate && dateRegex.test(endDate)) {
                    endDate += " 23:59:59";
                }

                if (startDate) query.push(`created_at__gte=${encodeURIComponent(startDate)}`);
                if (endDate) query.push(`created_at__lte=${encodeURIComponent(endDate)}`);

                const queryString = query.length > 0 ? `?${query.join('&')}` : '';
                window.location.href = `${window.location.pathname}${queryString}`;
            });
        });
    })(django.jQuery);
});
