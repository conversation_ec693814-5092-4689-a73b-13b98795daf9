from django.db import models


class Tool(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    objects = models.Manager()


class AddVipTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Add User Subscription Days'
        verbose_name_plural = 'Add User Subscription Days'


class AddPointTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Add User Points'
        verbose_name_plural = 'Add User Points'


class RecoverUserVipTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Recover User Vip'
        verbose_name_plural = 'Recover User Vip'


class AllowUserBeingCallByMorePersonTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Call - Allow Someone being called by more person'
        verbose_name_plural = 'Call - Allow Someone being called more person'


class RecoverSendingByDeleteCheckUserRecordsTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Recover Sending & Calling By Delete CheckUserRecords'
        verbose_name_plural = 'Recover Sending & Calling By Delete CheckUserRecords'
