from django.contrib import admin
from django.core.cache import cache
from django.http import JsonResponse
from django.template.response import TemplateResponse
from django.urls import path

from Common.err import ErrInfo
from Common.rediskey import RedisKey
from Number.tools.number_tool import NumberTool
from Order.tools.tool_order import OrderTool
from Order.tools.tool_refresh_order import Refresh<PERSON><PERSON><PERSON>Tool
from Point.pointcommon import <PERSON><PERSON>ommon
from SecPhone import settings
from SecPhone.settings import logger
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from ToolsForBenefit.models import AddVipTool, AddPointTool, AllowUserBeingCallByMorePersonTool, \
    RecoverSendingByDeleteCheckUserRecordsTool, RecoverUserVipTool
from User.tools.user_context_tool import UserContextTool
from User.tools.user_kill_tool import UserKillTool
from User.tools.user_profile_tool import UserProfileTool
from User.tools.user_tool import UserTool


@admin.register(AddVipTool)
class AddVipToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/add_vip_days.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('add_vip_days/', self.admin_site.admin_view(self.add_vip_days), name='add_vip_days'),
        ]
        return custom_urls + urls

    @staticmethod
    def add_vip_days(request):
        if request.method == 'POST':
            user_id = int(request.POST.get('userid').strip())
            add_days = int(request.POST.get('days').strip())
            if -1000 < add_days:
                logger.error(f"[AddVipToolAdmin] user:{user_id}, days:{add_days}")
            else:
                # 因为-1000会触发封号，下面封号已经有通知了，减少重复通知
                logger.warning(f"[AddVipToolAdmin] user:{user_id}, days:{add_days}")

            before_user_profile = UserProfileTool.get_user_profile(user_id, app_version=0)
            before_user_profile = {key: value for key, value in before_user_profile.items() if key == "expire"}

            # 特殊日期用来解除封禁
            if add_days == 3333:
                UserKillTool.un_kill_tmp_user(user_id)
                return JsonResponse({
                    "msg": "解除临时封杀",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3334:
                UserKillTool.un_mute_user_temporarily(user_id)
                return JsonResponse({
                    "msg": "解除临时禁言",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3335:
                cache.set(RedisKey.gen_check_event_allow(user_id), 1, 86400)
                return JsonResponse({
                    "msg": "临时允许打电话（一天有效期）",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3336:
                # 禁言
                UserKillTool.mute_user_temporarily(user_id, 3600)
                # 推送
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.say_you_are_reported_as_harasser("number")
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)
                return JsonResponse({
                    "msg": "临时禁言1小时",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3337:
                # 禁言
                UserKillTool.mute_user_temporarily(user_id, 3600 * 12)
                # 推送
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.say_you_are_reported_as_harasser("number")
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)
                return JsonResponse({
                    "msg": "临时禁言12小时",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3338:
                # 禁言
                UserKillTool.mute_user_temporarily(user_id, 3600 * 24)
                # 推送
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.say_you_are_reported_as_harasser("number")
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)
                return JsonResponse({
                    "msg": "临时禁言24小时",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3339:
                # 禁言
                UserKillTool.mute_user_temporarily(user_id, 3600 * 48)
                # 推送
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.say_you_are_reported_as_harasser("number")
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)
                return JsonResponse({
                    "msg": "临时禁言48小时",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3340:
                # 禁言
                UserKillTool.mute_user_temporarily(user_id, 3600 * 72)
                # 推送
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.say_you_are_reported_as_harasser("number")
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)
                return JsonResponse({
                    "msg": "临时禁言72小时",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })

            if add_days > 2100:
                logger.error(f"[AddVipToolAdmin] add_days to much, error：{add_days}")
                ret = {"error": "add days too much > 2100 invalid"}
                return JsonResponse(ret)

            if not UserTool.get_user_by_id(user_id):
                ret = {"error": "user_id not exists"}
                return JsonResponse(ret)

            # 封号
            if add_days <= -1000:
                UserKillTool.kill_user(user_id, add_days)
            else:
                # 加VIP
                OrderTool.add_user_vip(user_id, add_days)

                # 更新 expired
                RefreshOrderTool.refresh_user_vip(user_id)

            # 推送，如果加很多days可能是后台自己补偿，而不想用户知道
            if 0 < add_days < 200:
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.add_vip(add_days)
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)

            return JsonResponse({
                "user": user_id,
                "before_expired_at": before_user_profile,
                "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
            })

        return TemplateResponse(request, 'admin/add_vip_days.html', {})


@admin.register(AddPointTool)
class AddPointsToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/add_points.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('add_points/', self.admin_site.admin_view(self.add_points), name='add_points'),
        ]
        return custom_urls + urls

    @staticmethod
    def add_points(request):
        if request.method == 'POST':
            user_id = int(request.POST.get('userid').strip())
            points = int(request.POST.get('points').strip())
            logger.error(f"[AddPointsToolAdmin] user:{user_id}, points:{points}")

            if len(request.POST.get('userid')) >= 8:
                logger.error(f"[AddPointsToolAdmin] param failed, error：{request.POST}")
                ret = {"error": "user_id invalid"}
                return JsonResponse(ret)

            if points > 1000:
                logger.error(f"[AddPointsToolAdmin] points to much: {points}")
                ret = {"error": "add days too much > 1000 invalid"}
                return JsonResponse(ret)

            if not UserTool.get_user_by_id(user_id):
                ret = {"error": "user_id not exists"}
                return JsonResponse(ret)

            # 加点数
            before_points = PointCommon.GetPointLeft(user_id)
            PointCommon.Add(user_id, points, 'FATPO-CHARGE', 0)
            current_points = PointCommon.GetPointLeft(user_id)

            # 推送
            if points > 0:
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.add_point(points)
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)

            return JsonResponse({
                "user": user_id,
                "before_points": before_points,
                "current_points": current_points,
            })

        return TemplateResponse(request, 'admin/add_points.html', {})


@admin.register(AllowUserBeingCallByMorePersonTool)
class AllowUserBeingCallByMorePersonToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/allow_user_being_called_by_more_person.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('allow_user_being_called_by_more_person/',
                 self.admin_site.admin_view(self.allow_user_being_called_by_more_person),
                 name='allow_user_being_called_by_more_person'),
        ]
        return custom_urls + urls

    @staticmethod
    def allow_user_being_called_by_more_person(request):
        if request.method == 'POST':
            user_id = int(request.POST.get('userid').strip())
            cache.set(RedisKey.gen_incoming_call_allow(user_id), 1, RedisKey.INCOMING_CALL_ALLOW_SECONDS)

            ret = {"user_id": user_id, "allow_being_call_seconds": RedisKey.INCOMING_CALL_ALLOW_SECONDS}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/allow_user_being_called_by_more_person.html', {})


@admin.register(RecoverSendingByDeleteCheckUserRecordsTool)
class RecoverSendingByDeleteCheckUserRecordsToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/recover_sending_by_del_checkuserrecords.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('recover_sending_by_del_checkuserrecords/',
                 self.admin_site.admin_view(self.recover_sending_by_del_checkuserrecords),
                 name='recover_sending_by_del_checkuserrecords'),
        ]
        return custom_urls + urls

    @staticmethod
    def recover_sending_by_del_checkuserrecords(request):
        if request.method == 'POST':
            user_id = int(request.POST.get('userid').strip())
            logger.warning(f"[RecoverSendingByDeleteCheckUserRecordsToolAdmin] user:{user_id}")

            if not UserTool.get_user_by_id(user_id):
                ret = {"error": "user_id not exists"}
                return JsonResponse(ret)

            records = UserTool.get_check_user_list(user_id)
            res = []
            for r in records:
                res.append({
                    "to_number": r.to_number,
                    "check_event": r.check_event,
                    "created_at": r.created_at,
                })

            UserTool.delete_check_user(user_id)

            logger.warning(
                f"[RecoverSendingByDeleteCheckUserRecordsToolAdmin] user_id:{user_id}, delete size:{len(res)}")
            return JsonResponse({
                "user": user_id,
                "deleted_records": res,
                "deleted_size": len(res),
            })

        return TemplateResponse(request, 'admin/recover_sending_by_del_checkuserrecords.html', {})


@admin.register(RecoverUserVipTool)
class RecoverUserVipToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/recover_user_vip.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('recover_user_vip/', self.admin_site.admin_view(self.recover_user_vip), name='recover_user_vip'),
        ]
        return custom_urls + urls

    @staticmethod
    def recover_user_vip(request):
        if request.method == 'POST':
            user_id = int(request.POST.get('userid').strip())

            if user_id <= 0:
                logger.error(f"[RecoverUserVipToolAdmin] user_id is not int: {user_id}")
                ret = {"error": "user_id is not int"}
                return JsonResponse(ret)

            if OrderTool.is_user_vip_expire(user_id) == ErrInfo.SUCCESS:
                ret = {"error": "user_id is vip"}
                return JsonResponse(ret)

            UserKillTool.un_kill_user(user_id)

            if not UserTool.get_user_by_id(user_id):
                ret = {"error": "user_id not exists"}
                return JsonResponse(ret)

            return JsonResponse({
                "user": user_id,
                "context": UserContextTool.get_user_context_dict(email="", content="", user_id=user_id),
            })

        return TemplateResponse(request, 'admin/recover_user_vip.html', {})
