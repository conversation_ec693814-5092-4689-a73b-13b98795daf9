{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
    <div class="container mt-4">
        <h1>Add User Subscription Days</h1>
        <form id="custom-form" method="post" action="{% url 'admin:add_vip_days' %}">
            {% csrf_token %}
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="userid">User Id:</label>
                    <input type="text" id="userid" name="userid">
                    <button type="button" class="btn btn-danger ml-2" onclick="clearInputs()">Clear</button>
                    <span>fatpo userid: 1214162</span>
                </div>
                <br>
                <div class="form-group col-md-6">
                    <label for="days">Subscription Days:</label>
                    <input type="text" id="days" name="days">
                    <div class="btn-group ml-2" role="group" aria-label="Load Days">
                        <button type="button" class="btn btn-secondary" onclick="setDays(-1990)">色情</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(-1991)">威胁</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(-1992)">骚扰</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(-1995)">诈骗</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(-1999)">种族歧视</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(3333)">解除临时封杀</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(3334)">解除临时禁言</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(3335)">
                            允许打电话额外多3人(1天)
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(3336)">禁言1小时</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(3337)">禁言12小时</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(3338)">禁言24小时</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(3339)">禁言48小时</button>
                        <button type="button" class="btn btn-secondary" onclick="setDays(3340)">禁言72小时</button>
                    </div>
                </div>
                <br>
            </div>
            <button type="submit">Submit</button>
            <!-- Clear button -->
        </form>
        <div class="form-group mt-3">
            <label for="response-textbox">Response</label>
            <textarea id="response-textbox" class="form-control" readonly rows="20" cols="60"></textarea>
        </div>
    </div>

    <script>
        // Function to set the days input value
        function setDays(value) {
            document.getElementById('days').value = value;
        }

        // Function to clear the input fields
        function clearInputs() {
            document.getElementById('userid').value = '';
            document.getElementById('days').value = '';
        }

        // Handle form submission
        document.getElementById('custom-form').onsubmit = function (event) {
            event.preventDefault();
            var formData = new FormData(this);
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                }
            }).then(response => response.json()).then(data => {
                var responseTextbox = document.getElementById('response-textbox');
                responseTextbox.value = JSON.stringify(data, null, 2);
            });
        };
    </script>



<div class="container mt-4">
    <h1>Get User Info By UserId/DeviceId/Number/Email/OriginalTransactionId</h1>
    <form id="custom-form-2" method="post" action="{% url 'admin:get_user_info' %}">
        {% csrf_token %}
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="userid2">User Id:</label>
                <input type="text" id="userid2" name="userid2">
            </div>
            <br>
            <div class="form-group col-md-6">
                <label for="number">Number:</label>
                <input type="text" id="number" name="number">
            </div>
            <br>
            <div class="form-group col-md-6">
                <label for="uuid">DeviceId:</label>
                <input type="text" id="uuid" name="uuid">
            </div>
            <br>
            <div class="form-group col-md-6">
                <label for="original_transaction_id">Original Transaction Id:</label>
                <input type="text" id="original_transaction_id" name="original_transaction_id">
            </div>
            <br>
            <div class="form-group col-md-6">
                <label for="email">Email:</label>
                <input type="text" id="email" name="email">
            </div>
            <br>

        </div>
        <button type="submit">Submit</button>
    </form>
    <div class="form-group mt-3">
        <label for="response-textbox">Response</label>
        <textarea id="response-textbox-2" class="form-control" readonly rows="50" cols="80"></textarea>
    </div>
</div>


<script>
    document.getElementById('custom-form-2').onsubmit = function (event) {
        event.preventDefault();
        var formData = new FormData(this);
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        }).then(response => response.json()).then(data => {
            var responseTextbox = document.getElementById('response-textbox-2');
            responseTextbox.value = JSON.stringify(data, null, 2);
        });
    };
</script>

{% endblock %}
