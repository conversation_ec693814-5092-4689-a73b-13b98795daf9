{# templates/admin/recover_user_vip.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h1>Recover User VIP</h1>
    <form id="custom-form" method="post" action="{% url 'admin:recover_user_vip' %}">
        {% csrf_token %}
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="userid">User Id:</label>
                <input type="text" id="userid" name="userid">
            </div>
            <br>
        </div>
        <button type="submit">Submit</button>
    </form>
    <div class="form-group mt-3">
        <label for="response-textbox">Response</label>
        <textarea id="response-textbox" class="form-control" readonly rows="20" cols="60"></textarea>
    </div>
</div>


<script>
    document.getElementById('custom-form').onsubmit = function (event) {
        event.preventDefault();
        var formData = new FormData(this);
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        }).then(response => response.json()).then(data => {
            var responseTextbox = document.getElementById('response-textbox');
            responseTextbox.value = JSON.stringify(data, null, 2);
        });
    };
</script>
{% endblock %}
