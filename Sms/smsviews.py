import datetime

from django.core.cache import cache

from Call.models import SMSRecord
from Common.err import ErrInfo
from Common.rediskey import RedisKey
from Common.timeutil import TimeUtil
from Common.views import SecPhoneView
from SecPhone import settings
from SecPhone.settings import logger
from Sms.tools.tool_notice import SmsNoticeTool
from Sms.tools.tool_sms import SmsTool
from User.tools.user_tool import UserTool


class GetLatestSms(SecPhoneView):

    @SecPhoneView.VerifySign
    @SecPhoneView.VerifyToken
    def post(self, request):
        post_data, err_code, err_msg = self.ReadPostJson(request, ('latest_ts',))
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        header = self.GetHeaderInRequest(request)
        userid = header['userid']
        latest_ts_ = post_data['latest_ts']
        latest_ts_ = int(latest_ts_) if latest_ts_ else 0  # 格式化int

        # 拿到用户上次的 sms_ts
        user = UserTool.get_user_by_id(user_id=userid)
        before_latest_sms_ts = user.latest_sms_ts if user else 0

        # 取最大的max
        latest_ts_ = max(latest_ts_, before_latest_sms_ts)

        # 拉短信
        records = SmsTool.get_user_latest_sms(userid, latest_ts_)

        # 更新下这个用户的最新 latest_ts
        cache.set(RedisKey.GenSmsLatestTimestamp(userid), latest_ts_, RedisKey.SMS_LATEST_TIMESTAMP_EXPIRE_SECONDS)

        res = []
        for record in records:
            content = record.content
            if content == settings.CUSTOMER_REPLY_FINISH:
                logger.info(f"[GetLatestSms] user:{userid}, skip customer reply finish record")
                continue

            system_notice_code = SmsNoticeTool.get_notice_code(content)
            if system_notice_code:
                content = SmsNoticeTool.change_system_notice_code_to_content(content, system_notice_code)

            if not record.latest_ts or record.latest_ts == 0:
                latest_ts = str(TimeUtil.DateTime2Timestamp(record.created_at))
            else:
                latest_ts = str(record.latest_ts)

            _from = record.from_number
            _to = record.to_number

            if _from == settings.APP_IT_SUPPORT_PHONE:
                _from = settings.APP_IT_SUPPORT_SHOW_PHONE
            if _to == settings.APP_IT_SUPPORT_PHONE:
                _to = settings.APP_IT_SUPPORT_SHOW_PHONE

            status = record.status
            if status == "send_fake":
                status = "delivered"
            if status == "received_fake":
                status = "received"

            res.append({
                "latest_ts": latest_ts,
                "content": content,
                "created_at": record.created_at,
                "from": _from,
                "to": _to,
                "direction": record.direction,
                "status": status,
                "image": record.images,
                "direction_type": 1 if record.direction == settings.SMS_DIRECTION_RECEIVE else 2,  # 1收2发
                "link": record.link,
            })

        # # 系统群发消息
        # self_number = NumberTool.get_number_by_userid(userid)
        # if latest_ts_ < 1731041385833:
        #     logger.info(f"[GetLatestSms] user:{userid}, system notify message delivery failed: 1731041196833")
        #     res.append({
        #         "latest_ts": 1731041385833,
        #         "content": "Dear customers, our sms carrier are currently in the functional maintenance, the sms function may send affected, such as sending failure or can not arrive in time, we sincerely hope your excuse and understanding.",
        #         "created_at": datetime.datetime.now(datetime.timezone.utc),
        #         "from": "+1000009999",
        #         "to": self_number,
        #         "direction": "RECIEVE",
        #         "status": "received",
        #         "image": "",
        #         "direction_type": 1,  # 1收2发
        #         "link": "",
        #     })

        # 更新用户最新的sms ts
        latest_sms_ts = latest_ts_
        if len(res) > 0:
            latest_sms_ts = int(res[-1]["latest_ts"])
            UserTool.update_latest_sms_ts(userid, latest_sms_ts)

        logger.info(f"[GetLatestSms] user:{userid}, before_latest_ts:{latest_ts_}, after_latest_ts:{latest_sms_ts}, "
                    f"size:{len(res)}")
        return self.ReturnSuccess(data={"list": res})


class DeleteOldSystemNoticeSms(SecPhoneView):

    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, ("before_days", "token",))
        if err_code != 0:
            logger.info(f"[DeleteOldSystemNoticeSms] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(err_code, err_msg)

        if data['token'] != 'fatpolalaheihei':
            logger.info(f"[DeleteOldSystemNoticeSms] param error, path: {request.path}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

        before_days = int(data['before_days'])
        if before_days > 100 or before_days <= 0:
            logger.info(f"[DeleteOldSystemNoticeSms] param error, before_days: {before_days}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

        before_date = datetime.datetime.now() - datetime.timedelta(days=before_days)
        res = SMSRecord.objects.filter(
            is_it=1,
            content__startswith='[Notice]',  # 使用 startswith 代替 LIKE '[Notice]%'
            created_at__lt=before_date
        ).delete()

        logger.info(f"[DeleteOldSystemNoticeSms] delete system notice sms res: {res}")
        return self.ReturnSuccess({})
