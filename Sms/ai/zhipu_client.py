# coding=utf-8
import random

from zhipuai import ZhipuAI

from SecPhone.settings import logger

## HH
hh_key = "f67bf0842e8324d0e453e6c2684d3f91.QqUaMrnooQDpC1gN"
client1 = ZhipuAI(api_key=hh_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=2  # 设置最大重试次数
                  )
# zhouzhou
zz_key = "8350b0b9305b4dcf5e6232967b5276a7.nC6UArhOdy9EvTEg"
client2 = ZhipuAI(api_key=zz_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=2  # 设置最大重试次数
                  )

# ran
ran_key = "0b995dfd8ac0cba5ff89a24b126a2314.MtROSnLKozaNrN7T"
client3 = ZhipuAI(api_key=ran_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=2  # 设置最大重试次数
                  )

# fatpo
fatpo_key = "9ccb89c203dc37f34c0b711eff6639fa.oMYKVjZ1AzG5v5Eg"
client4 = ZhipuAI(api_key=fatpo_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=2  # 设置最大重试次数
                  )

# fatpo-us
fatpo_us1_key = "a3fcc3f145d1d7d478ae79a255f0bc30.CI6hI5U3wmFdg6Y6"
client5 = ZhipuAI(api_key=fatpo_us1_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=2  # 设置最大重试次数
                  )

# jiejie1
jiejie1_key = "e47f8df4f8ad1ac30e353d0cccd3dfe8.htKXJkKUWePCikY2"
client6 = ZhipuAI(api_key=jiejie1_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=2  # 设置最大重试次数
                  )

# jiejie-friend
jiejie_friend_key = "132a9f9edb6a39ca8df5ad4df4d9d94b.37rph1AcNdkGNc66"
client7 = ZhipuAI(api_key=jiejie_friend_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=2  # 设置最大重试次数
                  )

# 雄哥
jianxiong_key = "20df9bae8b5a6e61209de7d4a4c3d14e.ma1jmRFLGh3tjrV1"
client8 = ZhipuAI(api_key=jianxiong_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=2  # 设置最大重试次数
                  )

# yikai
yikai_key = "efd90b2735b8055c7349b0cd4d346314.1XPF5Fq3j1UR1IrX"
client9 = ZhipuAI(api_key=yikai_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=2  # 设置最大重试次数
                  )

# zexiong
zexiong_key = "00ff017eba09e3d99e0e6e16499c2913.ghRmOiioqIJHOfAT"
client10 = ZhipuAI(api_key=zexiong_key,
                   timeout=60.0,  # 设置超时时间为60秒
                   max_retries=2  # 设置最大重试次数
                   )

# ilan
ilan_key = "0ca48c7e9414820e9690a065412fc4e5.10pX9URDzeu9e5GT"
client11 = ZhipuAI(api_key=ilan_key,
                   timeout=60.0,  # 设置超时时间为60秒
                   max_retries=2  # 设置最大重试次数
                   )


def get_client_name(client) -> str:
    if client.api_key == hh_key:
        logger.info(f"[SendSms] zhipu-ai, use hh_key")
        return "hh_key"
    elif client.api_key == zz_key:
        logger.info(f"[SendSms] zhipu-ai, use zz_key")
        return "zz_key"
    elif client.api_key == ran_key:
        logger.info(f"[SendSms] zhipu-ai, use ran_key")
        return "ran_key"
    elif client.api_key == fatpo_key:
        logger.info(f"[SendSms] zhipu-ai, use fatpo_key")
        return "fatpo_key"
    elif client.api_key == fatpo_us1_key:
        logger.info(f"[SendSms] zhipu-ai, use fatpo_us1_key")
        return "fatpo_us1_key"
    elif client.api_key == jiejie1_key:
        logger.info(f"[SendSms] zhipu-ai, use jiejie1_key")
        return "jiejie1_key"
    elif client.api_key == jiejie_friend_key:
        logger.info(f"[SendSms] zhipu-ai, use jiejie_friend_key")
        return "jiejie_friend_key"
    elif client.api_key == jianxiong_key:
        logger.info(f"[SendSms] zhipu-ai, use jianxiong_key")
        return "jianxiong_key"
    elif client.api_key == yikai_key:
        logger.info(f"[SendSms] zhipu-ai, use yikai_key")
        return "yikai_key"
    elif client.api_key == zexiong_key:
        logger.info(f"[SendSms] zhipu-ai, use zexiong_key")
        return "zexiong_key"
    elif client.api_key == ilan_key:
        logger.info(f"[SendSms] zhipu-ai, use ilan_key")
        return "ilan_key"


class ZhiPuClient:
    @staticmethod
    def get_zhipu_random_free_client() -> (ZhipuAI, str):
        client = random.choice([client1, client2, client3, client4, client5, client6, client7, client8, client9,
                                client10, client11])

        return client, get_client_name(client)

    @staticmethod
    def get_zhipu_fatpo_client() -> (ZhipuAI, str):
        return client4, get_client_name(client4)
