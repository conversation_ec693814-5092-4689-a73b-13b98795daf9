from django.conf.urls import url

from Sms import Msg, smsviews
from Sms.review import sms_review_tosend_wangyi_view

urlpatterns = [
    # v2 app - telnyx - sms
    url(r'^zhphone/phone/sms/fetchNew/$', smsviews.GetLatestSms.as_view()),
    url(r'^zhphone/phone/sms/deleteOldSystemNoticeSms/$', smsviews.DeleteOldSystemNoticeSms.as_view()),

    url(r'^zhphone/phone/sendsms/$', Msg.SendSMS.as_view()),
    url(r'^zhphone/phone/sendsms/fake/$', Msg.SendSMSFake.as_view()),
    url(r'^zhphone/phone/sendmms/$', Msg.SendMMS.as_view()),
    url(r'^zhphone/phone/incoming/sms/$', Msg.IncomingSMSTelnyx.as_view()),
    url(r'^zhphone/phone/incoming/sms/failed/$', Msg.IncomingSMSTelnyxFailed.as_view()),
    url(r'^zhphone/phone/outbound/sms/webhook/$', Msg.TelnyxOutboundSMSWebhook.as_view()),
    url(r'^zhphone/phone/outbound/sms/failed/$', Msg.TelnyxOutboundSMSFailed.as_view()),

    # backend sms
    url(r'^zhphone/phone/sms/backend/cost/$', Msg.TelnyxBackendCost.as_view()),

    # review sms
    url(r'^zhphone/phone/sms/review/manual/$', sms_review_tosend_wangyi_view.SmsReviewWangyiManual.as_view()),
    url(r'^zhphone/phone/sms/review/manual/callback/$',
        sms_review_tosend_wangyi_view.SmsReviewWangyiManualCallback.as_view()),

    # buka 第三方代发
    url(r'^zhphone/phone/outbound/sms/buka/webhook/$', Msg.BukaOutboundSMSWebhook.as_view()),
]
