import datetime
import json
from threading import Timer

from django.utils.decorators import method_decorator

import SecPhone
import SecPhone.logid_middleware
from Call.models import ForbiddenContact, MaybeBadSms
from Call.utils import CallUtils
from Common.err import ErrInfo
from Common.ratelimit.decorators import ratelimit
from Common.timeutil import TimeUtil
from Common.util import Util
from Common.views import SecPhoneView
from MyTelnyx.my_telnyx import TelnyxUtil
from Number.tools.number_tool import NumberTool
from Number.tools.number_valid_tool import NumberValidTool
from Order.tools.tool_order import OrderTool
from Order.tools.tool_refresh_order import RefreshOrderTool
from Push.push_util import PushUtil
from SecPhone import settings
from SecPhone.settings import logger
from Sms.buka.buka_sms import buka_send_sms
from Sms.filters import filter_constant
from Sms.filters.filter_ban_vip_words import SmsBanVipUtil
from Sms.filters.filter_image import sms_filter_image
from Sms.filters.filter_simple import sms_filter_simple
from Sms.filters.filter_zhipu_proxy_basic_pig import Zhipu<PERSON>ig<PERSON><PERSON>chering<PERSON>til
from Sms.filters.filter_zhipu_proxy_pic import Zhi<PERSON>uPicUtil
from Sms.info import sms_info_bean
from Sms.info.sms_info_bean import SMSInfo
from Sms.msg_pipeline import MsgPipeline
from Sms.tools.tool_forbidden_sms_call import SmsCallForbiddenTool
from Sms.tools.tool_inner_sms import SmsInnerTool
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from Sms.tools.tool_sms import SmsTool
from Sms.tools.tool_sms_add import SmsAddTool
from Sms.tools.tool_sms_cost import SmsCostTool
from Sms.tools.tool_sms_error import SmsErrorTool
from User.tools.user_kill_tool import UserKillTool


class SendSMS(SecPhoneView):

    @SecPhoneView.VerifyToken
    @SecPhoneView.VerifySign
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='150/1h', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='12/60s', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='3/10s', block=True))
    @method_decorator(ratelimit(key='header:x-uuid', rate='150/1h', block=True))
    @method_decorator(ratelimit(key='header:x-uuid', rate='12/60s', block=True))
    @method_decorator(ratelimit(key='header:x-uuid', rate='3/10s', block=True))
    def post(self, request):
        post_data, err_code, err_msg = self.ReadPostJson(request, check_list=['to', 'content'])
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        header = self.GetHeaderInRequest(request)
        ip = request.META.get('HTTP_X_FORWARDED_FOR')
        from_uid = header['userid']
        appid = header['appid']
        uuid = header['uuid']
        from_number = NumberTool.GetNumberByUserId(from_uid)
        to_number = Util.FormatNumberV2(post_data['to'])
        latest_ts = post_data.get('latest_ts', TimeUtil.GetNowTsInInt())
        content = (post_data['content'].replace("\n", " ").replace("\r", " ").replace("\r\n", " ")
                   .replace("\t", " ").replace("\f", " ").replace("\v", " ").strip())

        fake_mode = post_data.get("fake_mode", False)
        logger.info(f"[SendSms] from_user={from_uid}, to_number={to_number}, content=[{content}]")

        # 判断是否是 support 模式
        if SmsItSupportTool.it_support(from_uid, from_number, to_number, content, latest_ts):
            return self.ReturnSuccess()

        # 判断自己账户合法不
        logger.info(f"[SendSms] check is_self_number_ok: {from_uid}")
        err = NumberValidTool.is_self_number_ok(from_uid)
        if err != ErrInfo.SUCCESS:
            logger.warning(f"[SendSms] user: {from_uid} send to: {to_number}, but from: {from_uid} unavailable!")
            return self.ReturnError(err)

        # 判断对方账户合法不
        err = NumberValidTool.is_to_number_ok(to_number)
        if err != ErrInfo.SUCCESS:
            logger.warning(f"[SendSms] user: {from_uid} send to: {to_number}, but to: {to_number} unavailable!")
            return self.ReturnError(err)

        # 不想收到某些骚扰短信
        if ForbiddenContact.objects.filter(number=to_number, direction="OUTGOING").exists():
            logger.error(f"[SendSms] user: {from_uid} send to: {to_number}, but to: {to_number} blocked!")
            return self.ReturnError(ErrInfo.SEND_SMS_CONTACT_BE_BLOCKED)

        # 判断双方是否都是内部用户
        if SmsInnerTool.is_both_inner(from_uid, from_number, to_number, content):
            to_userid = NumberTool.GetUserIdByNumber(to_number)
            SmsInnerTool.add_inner_sms_both(from_uid, to_userid, from_number, to_number, content)
            return self.ReturnSuccess()

        # 判断对方是否拉黑
        if SmsTool.is_reply_stop(from_uid, to_number):
            logger.warning(f"[SendSms] {from_uid}:{from_number}->{to_number}:{content}, not send due to reply stop.")
            push_content = f"message undelivered: {to_number} has blocked you. \ncontent: {content}"
            PushUtil.notify_by_user(from_uid, appid, push_content)
            return self.ReturnError(ErrInfo.SEND_SMS_CONTACT_BE_BLOCKED)

        # 不要太长
        if len(content) == 0:
            logger.warning(f"[SendSms] user {from_uid} 's sms content is empty!")
            return self.ReturnError(ErrInfo.SEND_SMS_CONTENT_EMPTY)
        if len(content) >= settings.VALID_SMS_CONTENT_MAX_LENGTH:
            logger.warning(f"[SendSms] user {from_uid} 's sms content is too long! size:{len(content)}")
            return self.ReturnError(ErrInfo.SEND_SMS_CONTENT_TOO_LONG)

        # 不要携带link,防止很多钓鱼
        origin_content = content
        origin_content = origin_content.replace("’", "'")
        is_ok = sms_filter_simple.check_link(origin_content)
        if not is_ok:
            return self.ReturnError(ErrInfo.SEND_SMS_CONTENT_CONTAINS_LINK)

        # 逆天言论，直接删号
        if SmsBanVipUtil.check_ban_vip_words(origin_content):
            logger.error(f"[SendSms] 直接删号！user {from_uid} 's sms content: {origin_content}")
            OrderTool.add_user_vip(from_uid, -1999)
            vip_ret = RefreshOrderTool.refresh_user_vip(from_uid)
            logger.error(f"[SendSms] 直接删号！user {from_uid} 's vip -1999: {vip_ret}")
            rsp, rsp_msg = NumberTool.unbind_user_phone_number(from_uid)
            if not rsp:
                logger.error(f"[SendSms] unbind_user_phone_number:{from_uid} failed: {rsp_msg}")
            else:
                logger.info(f"[SendSms] unbind_user_phone_number:{from_uid} success")
            return self.ReturnSuccess()

        # 可疑言论，fatpo告警
        is_ban, ban_words = SmsBanVipUtil.check_warning_words(origin_content)
        if is_ban:
            logger.error(f"[SendSms] 可疑诈骗！！user: {from_uid}, ban_words: {ban_words}, sms content: {origin_content}")
            MaybeBadSms(user_id=from_uid, content=origin_content[0:2000]).save()
            ban_limiter = 5
            if from_uid == 1214162:
                ban_limiter = 100
            if MaybeBadSms.objects.filter(user_id=from_uid).count() >= ban_limiter:
                logger.error(f"[SendSms] 可疑诈骗太多了，直接删号！user {from_uid} 's sms content: {origin_content}")
                UserKillTool.kill_user(from_uid, -1998)
                return self.ReturnSuccess()

        sms_info = SMSInfo(user_id=from_uid, from_number=from_number, to_number=to_number,
                           direction=settings.SMS_DIRECTION_SEND,
                           origin_content=origin_content, ip=ip, uuid=uuid, latest_ts=latest_ts,
                           is_fake_mode=fake_mode)
        MsgPipeline.async_send(sms_info)
        return self.ReturnSuccess()


class SendMMS(SecPhoneView):
    @SecPhoneView.VerifyToken
    @SecPhoneView.VerifySign
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/60s', block=True))
    @method_decorator(ratelimit(key='header:x-uuid', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='header:x-uuid', rate='1/60s', block=True))
    def post(self, request):

        post_data, err_code, err_msg = self.ReadPostJson(request, ('to', 'content', 'format'))
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        header = self.GetHeaderInRequest(request)
        request.META.get('HTTP_X_FORWARDED_FOR')
        from_uid = header['userid']
        from_number = NumberTool.GetNumberByUserId(from_uid)
        to_phone = Util.FormatNumberV2(post_data['to'])
        latest_ts = post_data.get('latest_ts', TimeUtil.GetNowTsInStr())
        logger.info(f"[SendSms] {from_uid}:{from_number}->{to_phone}, mms, latest_ts={latest_ts}")

        # 处理图片
        image_urls = []
        image_url, image_md5 = CallUtils.SaveImage(post_data['content'], post_data['format'])
        if image_url is not None:
            image_urls.append(image_url)

        # 如果图片是坏人发送过的，请马上报警
        if SmsTool.is_this_image_bad(image_md5):
            logger.error(
                f"[SendSms] 可疑诈骗！！图片坏人之前发过！user: {from_uid}, image_md5: {image_md5}, image_url: {image_url}")
            MaybeBadSms(user_id=from_uid, content=image_md5 + "\t" + image_url).save()
            ban_limiter = 5
            if from_uid == 1214162:
                ban_limiter = 100
            if MaybeBadSms.objects.filter(user_id=from_uid).count() >= ban_limiter:
                logger.error(f"[SendSms] 可疑诈骗太多了，直接删号！user {from_uid} 's sms image: {image_url}")
                UserKillTool.kill_user(from_uid, -1998)
                return self.ReturnSuccess()

        # 判断是否是 support 模式
        if SmsItSupportTool.mms_it_support(from_uid, from_number, to_phone, image_url, latest_ts):
            return self.ReturnSuccess()

        # 判断自己账户合法不
        logger.info(f"[SendSms] send mms, check is_self_number_ok: {from_uid}, md5:{image_md5}, image:{image_url}")
        err = NumberValidTool.is_self_number_ok(from_uid)
        if err != ErrInfo.SUCCESS:
            logger.warning(f"[SendSms] user: {from_uid} send to: {to_phone}, but from: {from_uid} unavailable!")
            return self.ReturnError(err)

        # 判断对方账户合法不
        err = NumberValidTool.is_to_number_ok(to_phone)
        if err != ErrInfo.SUCCESS:
            logger.warning(f"[SendSms] user: {from_uid} send to: {to_phone}, but to: {to_phone} unavailable!")
            return self.ReturnError(err)

        logger.info(f'[SendSms] image_urls: {image_urls}')
        if len(image_urls) == 0:
            logger.error(f"[SendSms] image_urls is empty, can not send mms!")
            return self.ReturnError(ErrInfo.SEND_MMS_IMAGE_ERROR)

        # 判断对方是否拉黑
        if SmsTool.is_reply_stop(from_uid, to_phone):
            logger.warning(f"[SendSms] {from_uid}:{from_number}->{to_phone}:{image_url}, not send due to reply stop.")
            push_content = f"message undelivered: {to_phone} has blocked you. \nimage_url: {image_url}"
            PushUtil.notify_by_user(from_uid, 1, push_content)
            return self.ReturnError(ErrInfo.SEND_SMS_CONTACT_BE_BLOCKED)

        # 不想收到某些骚扰短信
        if ForbiddenContact.objects.filter(number=to_phone, direction="OUTGOING").exists():
            logger.error(f"[SendSms] user: {from_uid} send to: {to_phone}, but to: {to_phone} blocked!")
            return self.ReturnError(ErrInfo.SEND_SMS_CONTACT_BE_BLOCKED)

        # 判断双方是否都是内部用户
        if SmsInnerTool.is_both_inner(from_uid, from_number, to_phone, image_url):
            to_userid = NumberTool.GetUserIdByNumber(to_phone)
            SmsInnerTool.add_inner_mms_both(from_uid, to_userid, from_number, to_phone, image_url)
            return self.ReturnSuccess()

        # 杀猪盘检测
        conversation_list = SmsTool.get_conversation_sms_top_n_for_pig(from_uid, to_phone, top_n=20)
        latest_sms_list = SmsTool.get_latest_send_sms_top_n(from_uid, top_n=20)
        latest_sms_json_list = [{"content": v.filtered_content if v.filtered_content else v.content,
                                 "to_number": v.to_number} for v in latest_sms_list]
        rsp, reason = ZhipuPigButcheringUtil.is_pic_bad(from_uid, conversation_list, latest_sms_json_list)
        if rsp == filter_constant.AI_RSP_INVALID_CONTENT:
            sms_info = SMSInfo(user_id=from_uid, from_number=from_number, to_number=to_phone,
                               direction=settings.SMS_DIRECTION_SEND,
                               origin_content="", ip="", uuid="", latest_ts=latest_ts, is_fake_mode=False)
            sms_info.image_url = image_url
            sms_info.image_md5 = image_md5
            sms_info.err_rsp_list.append("kill pig:" + reason[0:1900])
            SmsAddTool.add_fake_mms_record(sms_info)
            return self.ReturnSuccess()

        # 判断图片合法不
        logger.info(
            f"[SendSms] mms, user:{from_uid}, from:{from_number}, to:{to_phone}, url:{image_url}, md5:{image_md5},"
            f"start checking...")
        is_image_ok, wangyi_task_id, combine_res = sms_filter_image.check_image_ok(str(from_uid), image_url)
        if not is_image_ok:
            sms_info = SMSInfo(user_id=from_uid, from_number=from_number, to_number=to_phone,
                               direction=settings.SMS_DIRECTION_SEND,
                               origin_content="", ip="", uuid="", latest_ts=latest_ts, is_fake_mode=False)
            sms_info.image_url = image_url
            sms_info.image_md5 = image_md5
            sms_info.wangyi_task_id = wangyi_task_id
            sms_info.err_rsp_list.append(combine_res)
            SmsAddTool.add_fake_mms_record(sms_info)

            # 必须是确定的才入库
            if combine_res.startswith("2_"):
                MaybeBadSms(user_id=from_uid, content=image_md5 + "\t" + image_url).save()
                ban_limiter = 5
                if from_uid == 1214162:
                    ban_limiter = 100
                if MaybeBadSms.objects.filter(user_id=from_uid).count() >= ban_limiter:
                    logger.error(f"[SendSms] 可疑诈骗太多了，直接删号！user {from_uid} 's sms image: {image_url}")
                    UserKillTool.kill_user(from_uid, -1998)
                    return self.ReturnSuccess()

            return self.ReturnSuccess()

        # 用AI判断下图片
        is_ai_valid, ai_reason = ZhiPuPicUtil.check_content_safety(str(from_uid), image_url)
        if not is_ai_valid:
            logger.warning(f"[SendSms] mms, user_id:{from_uid}, from:{from_number}, to:{to_phone}, "
                           f"ai says invalid, url: {image_url}")
            sms_info = SMSInfo(user_id=from_uid, from_number=from_number, to_number=to_phone,
                               direction=settings.SMS_DIRECTION_SEND,
                               origin_content="", ip="", uuid="", latest_ts=latest_ts, is_fake_mode=False)
            sms_info.image_url = image_url
            sms_info.image_md5 = image_md5
            sms_info.wangyi_task_id = wangyi_task_id
            sms_info.err_rsp_list.append(ai_reason)
            SmsAddTool.add_fake_mms_record(sms_info)

            MaybeBadSms(user_id=from_uid, content=image_md5 + "\t" + image_url).save()
            ban_limiter = 5
            if from_uid == 1214162:
                ban_limiter = 100
            if MaybeBadSms.objects.filter(user_id=from_uid).count() >= ban_limiter:
                logger.error(f"[SendSms] 可疑诈骗太多了，直接删号！user {from_uid} 's sms image: {image_url}")
                UserKillTool.kill_user(from_uid, -1998)
                return self.ReturnSuccess()

            return self.ReturnSuccess()

        try:
            logger.info(f"[SendSms] mms, from:{from_number}, to:{to_phone}, url:{image_url}")
            message_sid = TelnyxUtil.send_mms(from_number, to_phone, image_urls[0])

            sms_info = SMSInfo(user_id=from_uid, from_number=from_number, to_number=to_phone,
                               direction=settings.SMS_DIRECTION_SEND,
                               origin_content="", ip="", uuid="", latest_ts=latest_ts, is_fake_mode=False)
            sms_info.sid = message_sid
            sms_info.wangyi_task_id = wangyi_task_id
            sms_info.status = 'sent'
            sms_info.image_url = image_url
            sms_info.image_md5 = image_md5
            sms_info.is_need_calc_cost_later = False  # 因为会有webhook回调来计算
            sms_info.wangyi_task_id = ""
            SmsAddTool.add_mms_record(sms_info)

            return self.ReturnSuccess({"image_urls": image_urls})

        except Exception as e:
            sms_info = SMSInfo(user_id=from_uid, from_number=from_number, to_number=to_phone,
                               direction=settings.SMS_DIRECTION_SEND,
                               origin_content="", ip="", uuid="", latest_ts=latest_ts, is_fake_mode=False)
            sms_info.image_url = image_url
            sms_info.image_md5 = image_md5
            sms_info.wangyi_task_id = ""
            SmsAddTool.add_failed_mms_record(sms_info)

            # 已知的提示友好告警，友好提示
            err, is_unknown = SmsErrorTool.create_sms_error(e, from_number, to_phone)
            if is_unknown:
                logger.error(f"[SendSms] user: {from_uid}, {from_number}->{to_phone}:{image_url} failed", exc_info=True)
            else:
                logger.warning(f"[SendSms] user: {from_uid}, {from_number}->{to_phone}:{image_url} failed",
                               exc_info=True)

            # 加到和 it-support的对话列表
            support_content = SmsNoticeTool.mms_delivery_failed(err.err_msg, image_url)
            SmsItSupportTool.add_support_mms_both_from_it(from_uid, from_number, support_content)

            # 加到未完成短信中
            sms_info = sms_info_bean.create_not_finished_info(user_id=from_uid, from_number=from_number,
                                                              to_number=to_phone,
                                                              direction=settings.SMS_DIRECTION_SEND,
                                                              origin_content='', image_url=image_url)
            sms_info.not_finished_reason = f"[SendMMS] send failed:{str(e)}"
            SmsAddTool.add_not_finished_sms_call_record(sms_info=sms_info,
                                                        event_type=settings.EVENT_TYPE_MMS,
                                                        not_finished_code=settings.NOT_FINISHED_CARRIER_FAILED)

            return self.ReturnError(err.err_code, err.err_msg)


class IncomingSMSTelnyx(SecPhoneView):

    def post(self, request):
        try:
            json_data = json.loads(request.body)
            logger.info(f"[IncomingSMSTelnyx] json: {json_data}")
            self.do_receive_sms(json_data)
            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[IncomingSMSTelnyx] failed, {json.loads(request.body)}", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

    @staticmethod
    def do_receive_sms(json_data: dict):
        logger.info(f"[IncomingSMS] do_receive_sms, json_data: {json_data}")
        event_type = json_data["data"]["event_type"]
        message_id = json_data["data"]["payload"]["id"]
        parts = json_data["data"]["payload"]["parts"]
        occurred_at = json_data["data"]["occurred_at"]
        from_number = json_data["data"]["payload"]["from"]["phone_number"]
        to_number = json_data["data"]["payload"]["to"][0]["phone_number"]
        status = json_data["data"]["payload"]["to"][0]["status"]
        sms_type = json_data["data"]["payload"]["type"]
        media = json_data["data"]["payload"]["media"]
        text = json_data["data"]["payload"]["text"]
        image_url = ""
        if sms_type == "SMS":
            content = text
        elif sms_type == "MMS" and len(media) > 0:
            image_url = media[0]["url"]
            content = ""
        elif sms_type == "MMS" and len(text) > 0:
            image_url = ""
            content = text
        else:
            logger.error(f"[IncomingSMS] failed, error payload: {json_data}")
            sms_info = sms_info_bean.create_not_finished_info(user_id=0, from_number=from_number, to_number=to_number,
                                                              direction=settings.SMS_DIRECTION_RECEIVE,
                                                              origin_content=text, image_url=image_url)
            sms_info.sid = message_id
            sms_info.status = status
            sms_info.not_finished_reason = f"[IncomingSMS] invalid json: {json.dumps(json_data)}"
            (SmsAddTool
             .add_not_finished_sms_call_record(sms_info=sms_info,
                                               event_type=settings.EVENT_TYPE_SMS,
                                               not_finished_code=settings.NOT_FINISHED_INCOMING_REQ_INVALID_JSON
                                               ))
            return

        logger.info(f"[IncomingSMS] event_type:{event_type}, message_id:{message_id}, occurred_at:{occurred_at}, "
                    f"from_number:{from_number}, to_number:{to_number}, sms_type:{sms_type}, content:{content}, "
                    f"image_url:{image_url}, status:{status}")

        # 诈骗检测
        if SmsBanVipUtil.is_incoming_alert(content, from_number):
            user_id = NumberTool.get_user_id_by_number_even_expire(to_number)
            _content = content.replace("\n", " ")
            logger.error(f"对方怀疑我们诈骗!速速查看, user:{user_id}, from:{from_number}, {_content}, {json_data}")

        # 查看用户是否存在
        user_id = NumberTool.get_user_id_by_number_even_expire(to_number)
        if not user_id:
            logger.warning(f"[IncomingSMS] to: {to_number} user not exist, json:{json_data}")
            sms_info = sms_info_bean.create_not_finished_info(user_id=0, from_number=from_number, to_number=to_number,
                                                              direction=settings.SMS_DIRECTION_RECEIVE,
                                                              origin_content=content, image_url=image_url)
            sms_info.sid = message_id
            sms_info.status = status
            sms_info.not_finished_reason = f"[IncomingSMS] can not find user_id by to_number: {to_number}"
            (SmsAddTool
             .add_not_finished_sms_call_record(sms_info=sms_info,
                                               event_type=settings.EVENT_TYPE_SMS,
                                               not_finished_code=settings.NOT_FINISHED_TO_NUMBER_CANNOT_LOCATE_USER
                                               ))
            return

        # 不想收到某些骚扰短信
        if ForbiddenContact.objects.filter(number=from_number, direction="INCOMING").exists():
            logger.warning(f"[IncomingSMS] from_number:{from_number} is forbidden, event_type:{event_type}, "
                           f"message_id:{message_id}, occurred_at:{occurred_at}, "
                           f"from_number:{from_number}, to_number:{to_number}, sms_type:{sms_type}, content:{content}, "
                           f"image_url:{image_url}, status:{status}")
            sms_info = sms_info_bean.create_not_finished_info(user_id=0, from_number=from_number, to_number=to_number,
                                                              direction=settings.SMS_DIRECTION_RECEIVE,
                                                              origin_content=content, image_url="")
            sms_info.sid = message_id
            sms_info.status = status
            sms_info.not_finished_reason = f"[IncomingSMS] forbidden contact of incoming number: {from_number}"
            (SmsAddTool
             .add_not_finished_sms_call_record(sms_info=sms_info,
                                               event_type=settings.EVENT_TYPE_SMS,
                                               not_finished_code=settings.NOT_FINISHED_INCOMING_NUMBER_FORBIDDEN))
            return

        # 如果是之前的用户的对话短信，不要骚扰当前用户
        if SmsCallForbiddenTool.check_if_from_number_seeking_before_user(user_id, from_number, to_number):
            logger.warning(f"[IncomingSMS] from_number is seeking before user, not for user:{user_id}, "
                           f"{from_number}->{to_number}:{content}, give up, json:{json_data}")
            sms_info = sms_info_bean.create_not_finished_info(user_id=user_id, from_number=from_number,
                                                              to_number=to_number,
                                                              direction=settings.SMS_DIRECTION_RECEIVE,
                                                              origin_content=content, image_url=image_url)
            sms_info.sid = message_id
            sms_info.status = status
            sms_info.not_finished_reason = f"[IncomingSMS] from_number:{from_number} is seeking before user_id"
            SmsAddTool.add_not_finished_sms_call_record(sms_info=sms_info,
                                                        event_type=settings.EVENT_TYPE_SMS,
                                                        not_finished_code=settings.NOT_FINISHED_SEEKING_BEFORE_USER)
            return

        # # 如果对方我们闭嘴，我们就暂时禁言
        # if IncomingMsgStopTool.is_stop_message(from_number, content):
        #     logger.warning(f"[IncomingSMS] {user_id}:{from_number}->{to_number}:{content}, is stop, need fatpo check")
        #     UserKillTool.mute_user_temporarily(user_id, 300)
        #     push_content = SmsNoticeTool.say_you_are_reported_as_harasser(from_number)
        #     SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
        #                                      settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)

        # 如果我们给这个号码发送过stop，也不想收到
        if SmsTool.is_stop_someone_incoming(user_id, from_number):
            logger.warning(f"[IncomingSMS] {user_id}:{from_number}->{to_number}:{content}, not send due to reply stop.")
            sms_info = sms_info_bean.create_not_finished_info(user_id=user_id, from_number=from_number,
                                                              to_number=to_number,
                                                              direction=settings.SMS_DIRECTION_RECEIVE,
                                                              origin_content=content, image_url=image_url)
            sms_info.sid = message_id
            sms_info.status = status
            sms_info.not_finished_reason = f"[IncomingSMS] from_number:{from_number} is being stop by user_id before"
            (SmsAddTool
             .add_not_finished_sms_call_record(sms_info=sms_info,
                                               event_type=settings.EVENT_TYPE_SMS,
                                               not_finished_code=settings.NOT_FINISHED_INCOMING_NUMBER_FORBIDDEN))
            return

        latest_ts = TimeUtil.time_str_to_ts(occurred_at)
        PushUtil.notify_by_user(user_id=user_id, appid=1, content=content)

        if image_url:
            sms_info = SMSInfo(user_id=user_id, from_number=from_number, to_number=to_number,
                               direction=settings.SMS_DIRECTION_RECEIVE,
                               origin_content="", ip="", uuid="", latest_ts=latest_ts, is_fake_mode=False)
            sms_info.sid = message_id
            sms_info.status = status
            sms_info.image_url = image_url
            SmsAddTool.add_mms_record(sms_info)
            logger.info(f"[IncomingSMS] {user_id}:{from_number}->{to_number}:{image_url}, push done")
        else:
            sms_info = SMSInfo(user_id=user_id, from_number=from_number, to_number=to_number,
                               direction=settings.SMS_DIRECTION_RECEIVE,
                               origin_content=content[0:1500] + '...' if len(content) > 1500 else content,
                               ip="", uuid="", latest_ts=latest_ts,
                               is_fake_mode=False)

            sms_info.sid = message_id
            sms_info.parts = parts
            sms_info.status = status
            sms_info.filtered_content = ""
            SmsAddTool.add_sms_record(sms_info)
            logger.info(f"[IncomingSMS] {user_id}:{from_number}->{to_number}:{content}, push done")

        return


class IncomingSMSTelnyxFailed(SecPhoneView):

    def post(self, request):
        try:
            json_data = json.loads(request.body)
            logger.warning(f"[IncomingSMSTelnyxFailed] post_data: {json_data}")
            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[IncomingSMSTelnyxFailed] failed, {json.loads(request.body)}", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class TelnyxOutboundSMSWebhook(SecPhoneView):

    def delay_handle(self, thread_logid: str, sid: str, parts: int, cost: float, status: str, from_number: str,
                     content: str, image_url: str, to_carrier: str, err_reason: str):
        try:
            t = Timer(7.0, self.handle, (thread_logid, sid, parts, cost, status, from_number, content,
                                         image_url, to_carrier, err_reason))
            t.start()
        except Exception:
            logger.error(f'[TelnyxOutboundSMSWebhook] sid:{sid} update parts:{parts}, cost:{cost}, status:{status} '
                         f'handle failed', exc_info=True)

    @staticmethod
    def handle(thread_logid: str, sid: str, parts: int, cost: float, status: str, from_number: str, content: str,
               image_url: str, to_carrier: str, err_reason: str):
        try:
            # 设置logid
            SecPhone.logid_middleware.logid_context.set(thread_logid)

            record = SmsTool.get_by_sid(message_sid=sid)
            if not record:
                if len(content) > 0:
                    record = SmsTool.get_failed_sms_record_by_number_content_in_5_minute(from_number, content)
                else:
                    record = SmsTool.get_failed_mms_record_by_number_content_in_5_minute(from_number, image_url)

                if record:
                    logger.error(f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                                 f'image:{image_url}, status:{status}, find failed record success')
                    record.err_code = 0
                    record.save()
                else:
                    logger.warning(f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                                   f'image:{image_url}, status:{status}, find failed record failed again')
                    return

            # 普通的错误原因，简单推送告知
            record.err_reason = err_reason[0:250]
            if "40012" in err_reason or "invalid" in err_reason:
                logger.warning(f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                               f'image:{image_url}, status:{status}, err_reason:{record.err_reason}')
            if "40008" in err_reason:
                push_content = SmsNoticeTool.recipient_message_rejected(record.to_number)
                SmsItSupportTool.add_support_sms(record.user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, record.from_number, push_content)
                logger.warning(f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                               f'image:{image_url}, status:{status}, err_reason:{record.err_reason}, '
                               f'push_content:{push_content}')
            if "40002" in err_reason:
                logger.warning(f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                               f'image:{image_url}, status:{status}, err_reason:{record.err_reason}')
                # spam原因，第一时间先暂时屏蔽了
                UserKillTool.tmp_mute_user(record.user_id, 600)

                # buka代发
                try:
                    if content and len(content) > 0:
                        buka_res, is_ok = buka_send_sms(from_number, record.to_number, content, sid)
                        if is_ok:
                            logger.warning(
                                f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                                f'image:{image_url}, status:{status}, err_reason:{record.err_reason}, '
                                f'buka resend success: {buka_res}')
                            record.is_buka_resent = 1
                            record.save()
                        else:
                            logger.error(
                                f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                                f'image:{image_url}, status:{status}, err_reason:{record.err_reason}, '
                                f'buka resend failed: {buka_res}')
                except Exception:
                    logger.error(f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                                 f'image:{image_url}, status:{status}, err_reason:{record.err_reason}, '
                                 f'buka resend failed', exc_info=True)

            if "40001" in err_reason:
                push_content = SmsNoticeTool.telnyx_say_invalid_number(record.to_number)
                SmsItSupportTool.add_support_sms(record.user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, record.from_number, push_content)
                logger.warning(f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                               f'image:{image_url}, status:{status}, err_reason:{record.err_reason}, '
                               f'push_content:{push_content}')
            if "40010" in err_reason:
                push_content = f"sent failed: {record.content}"
                SmsItSupportTool.add_support_sms(record.user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, record.from_number, push_content)

                push_content = SmsNoticeTool.telnyx_say_not_support_canada_sms(record.to_number)
                SmsItSupportTool.add_support_sms(record.user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, record.from_number, push_content)
                logger.warning(f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                               f'image:{image_url}, status:{status}, err_reason:{record.err_reason}, '
                               f'push_content:{push_content}')

            # todo:查缺补漏
            if (record.err_reason and "40002" not in record.err_reason
                    and "40012" not in record.err_reason
                    and "40008" not in record.err_reason
                    and "40001" not in record.err_reason
                    and "40010" not in record.err_reason):
                logger.error(f'[TelnyxOutboundSMSWebhook] sid:{sid}, from:{from_number}, content:{content}, '
                             f'image:{image_url}, status:{status}, err_reason:{record.err_reason}')

            if parts:
                record.parts = parts
            if cost:
                record.price = cost
            if status and record.status != 'delivered':
                record.status = status
            if to_carrier:
                record.to_carrier = to_carrier

            record.save()

            # 计算点数和cost
            SmsCostTool.update_sms_record_realtime_telnyx(sid)

            logger.info(f'[TelnyxOutboundSMSWebhook] sid:{sid} update parts:{parts}, cost:{cost}, status:{status} '
                        f'update success')
        except Exception:
            logger.error(f'[TelnyxOutboundSMSWebhook] sid:{sid} update parts:{parts}, cost:{cost}, status:{status} '
                         f'handle failed', exc_info=True)

    def post(self, request):

        try:
            json_data = json.loads(request.body)
            logger.info(f'[TelnyxOutboundSMSWebhook] param: {json_data}')

            message_id = json_data["data"]["payload"]["id"]
            parts = json_data["data"]["payload"]["parts"]
            cost = json_data["data"]["payload"]["cost"]["amount"]
            status = json_data["data"]["payload"]["to"][0]["status"]
            from_number = json_data["data"]["payload"]["from"]["phone_number"]
            content = json_data["data"]["payload"]["text"]
            to_carrier = json_data["data"]["payload"]["to"][0]["carrier"]
            image_url = ""
            if len(json_data["data"]["payload"]["media"]) > 0:
                image_url = json_data["data"]["payload"]["media"][0]["url"]

            # 汇总错误信息
            err_reason = ""
            if "errors" in json_data["data"]["payload"] and len(json_data["data"]["payload"]["errors"]) > 0:
                for e in json_data["data"]["payload"]["errors"]:
                    err_reason = err_reason + e["code"] + "  " + e["detail"] + " "

            parts = int(parts) if parts else None
            cost = float(cost) if cost else None

            self.delay_handle(SecPhone.logid_middleware.logid_context.get(),
                              message_id, parts, cost, status, from_number, content, image_url, to_carrier, err_reason)

            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[TelnyxOutboundSMSWebhook] failed, {json.loads(request.body)}", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class TelnyxOutboundSMSFailed(SecPhoneView):

    def post(self, request):
        json_data = json.loads(request.body)
        logger.error(f"[TelnyxOutboundSMSFailed] post_data: {json_data}")
        return self.ReturnSuccess()


class TelnyxBackendCost(SecPhoneView):
    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, ("token", "limit", "days",))
        if err_code != 0:
            logger.error(f"[TelnyxBackendCost] param error：no token")
            return self.ReturnError(ErrInfo.AUTH_PARAMETER_MISS)

        if data['token'] != 'fatpohaha':
            logger.error(f"[TelnyxBackendCost] param error：invalid token")
            return self.ReturnError(ErrInfo.AUTH_PARAMETER_MISS)

        limit = int(data['limit'])
        days = int(data['days'])
        before_date = datetime.datetime.now() - datetime.timedelta(days=days)
        logger.info(f"[TelnyxBackendCost] limit:{limit}, before_date:{before_date}")
        non_price_records = SmsTool.get_n_non_price_sms_before_at(n=limit, before_date=before_date)
        non_points_records = SmsTool.get_n_non_points_sms_before_at(n=limit, before_date=before_date)
        logger.info(f"[TelnyxBackendCost] non_price_records size: {len(non_price_records)}")
        logger.info(f"[TelnyxBackendCost] non_points_records size: {len(non_points_records)}")
        records = list(non_price_records) + list(non_points_records)
        logger.info(f"[TelnyxBackendCost] total size: {len(records)}")
        for r in records:
            try:
                logger.info(f"[TelnyxBackendCost] record:{r.id}, {r.sid}, {r.direction}:{r.from_number}:{r.to_number}, "
                            f"{r.created_at}")
                SmsCostTool.update_sms_record_realtime_telnyx(r.sid)
            except Exception:
                logger.error(f"[TelnyxBackendCost] sid:{r.sid} price failed", exc_info=True)
                continue

        logger.info(f"[TelnyxBackendCost] done size: {len(records)}")
        return self.ReturnSuccess()


class SendSMSFake(SecPhoneView):

    @staticmethod
    def test(content: str, is_use_fatpo_ai: bool) -> dict:
        from_uid = 1214162
        from_number = "+15873320440"
        to_number = "+16062210527"
        latest_ts = TimeUtil.GetNowTsInInt()
        content = (content.replace("\n", " ").replace("\r", " ").replace("\r\n", " ")
                   .replace("\t", " ").replace("\f", " ").replace("\v", " ").strip())

        # 判断是否是 support 模式
        if SmsItSupportTool.it_support(from_uid, from_number, to_number, content, latest_ts):
            return {"rsp": "it_support"}

        # 判断自己账户合法不
        logger.info(f"[SendSms] check is_self_number_ok: {from_uid}")
        err = NumberValidTool.is_self_number_ok(from_uid)
        if err != ErrInfo.SUCCESS:
            logger.warning(f"[SendSms] user: {from_uid} send to: {to_number}, but from: {from_uid} unavailable!")
            return {"rsp": f"is_self_number_ok invalid, from_uid: {from_uid}"}

        # 判断对方账户合法不
        err = NumberValidTool.is_to_number_ok(to_number)
        if err != ErrInfo.SUCCESS:
            logger.warning(f"[SendSms] user: {from_uid} send to: {to_number}, but to: {to_number} unavailable!")
            return {"rsp": f"is_to_number_ok invalid, to_number: {to_number}"}

        # 不想收到某些骚扰短信
        if ForbiddenContact.objects.filter(number=to_number, direction="OUTGOING").exists():
            logger.error(f"[SendSms] user: {from_uid} send to: {to_number}, but to: {to_number} blocked!")
            return {"rsp": f"{to_number} 不想收到某些骚扰短信"}

        # 判断双方是否都是内部用户
        if SmsInnerTool.is_both_inner(from_uid, from_number, to_number, content):
            to_userid = NumberTool.GetUserIdByNumber(to_number)
            SmsInnerTool.add_inner_sms_both(from_uid, to_userid, from_number, to_number, content)
            return {"rsp": "both inner app"}

        # 判断对方是否拉黑
        if SmsTool.is_reply_stop(from_uid, to_number):
            logger.warning(f"[SendSms] {from_uid}:{from_number}->{to_number}:{content}, not send due to reply stop.")
            push_content = f"message undelivered: {to_number} has blocked you. \ncontent: {content}"
            PushUtil.notify_by_user(from_uid, 0, push_content)
            return {"rsp": push_content}

        # 不要太长
        if len(content) == 0:
            logger.warning(f"[SendSms] user {from_uid} 's sms content is empty!")
            return {"rsp": "content is nil"}
        if len(content) >= settings.VALID_SMS_CONTENT_MAX_LENGTH:
            logger.warning(f"[SendSms] user {from_uid} 's sms content is too long! size:{len(content)}")
            return {"rsp": f"content too long: {len(content)}"}

        # 不要携带link,防止很多钓鱼
        origin_content = content
        origin_content = origin_content.replace("’", "'")
        is_ok = sms_filter_simple.check_link(origin_content)
        if not is_ok:
            return {"rsp": f"content contains link: {origin_content}"}

        # 逆天言论，直接删号
        if SmsBanVipUtil.check_ban_vip_words(origin_content):
            logger.error(f"[SendSms] 直接删号！user {from_uid} 's sms content: {origin_content}")
            OrderTool.add_user_vip(from_uid, -1999)
            vip_ret = RefreshOrderTool.refresh_user_vip(from_uid)
            logger.error(f"[SendSms] 直接删号！user {from_uid} 's vip -1999: {vip_ret}")
            rsp, rsp_msg = NumberTool.unbind_user_phone_number(from_uid)
            if not rsp:
                logger.error(f"[SendSms] unbind_user_phone_number:{from_uid} failed: {rsp_msg}")
            else:
                logger.info(f"[SendSms] unbind_user_phone_number:{from_uid} success")
            return {"rsp": f"direct ban vip"}

        # 可疑言论，fatpo告警
        is_ban, ban_words = SmsBanVipUtil.check_warning_words(origin_content)
        if is_ban:
            logger.error(
                f"[SendSms] 可疑诈骗！！user: {from_uid}, ban_words: {ban_words}, sms content: {origin_content}")
            MaybeBadSms(user_id=from_uid, content=origin_content[0:2000]).save()
            ban_limiter = 5
            if from_uid == 1214162:
                ban_limiter = 100
            if MaybeBadSms.objects.filter(user_id=from_uid).count() >= ban_limiter:
                logger.error(f"[SendSms] 可疑诈骗太多了，直接删号！user {from_uid} 's sms content: {origin_content}")
                UserKillTool.kill_user(from_uid, -1998)
                return {"rsp": f"direct ban vip"}

        sms_info = SMSInfo(user_id=from_uid, from_number=from_number, to_number=to_number,
                           direction=settings.SMS_DIRECTION_SEND,
                           origin_content=origin_content, ip="", uuid="", latest_ts=latest_ts,
                           is_fake_mode=True)
        sms_info.is_use_fatpo_ai = is_use_fatpo_ai
        rsp_sms_info = MsgPipeline.send_pipeline("", sms_info)
        rsp_dict = rsp_sms_info.to_dict()
        del rsp_dict["sid"]
        del rsp_dict["ip"]
        del rsp_dict["uuid"]

        return {"content": rsp_dict}


class SendSMSV4(SecPhoneView):
    @SecPhoneView.VerifySignV4
    @method_decorator(ratelimit(key='post:number', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='post:number', rate='1/60s', block=True))
    @method_decorator(ratelimit(key='post:to', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='post:to', rate='1/60s', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/1h', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/60s', block=True))
    def post(self, request):
        try:
            post_data, err_code, err_msg = self.ReadPostJson(request, check_list=['number', 'to', 'content'])
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            from_number = post_data['number']
            to_number = post_data['to']
            content = post_data['content']
            logger.error(f"[SendSMSV4] send v4 sms, from:{from_number}, to:{to_number}, content:{content}")
            TelnyxUtil.send_sms(from_number, to_number, content)
            return self.ReturnSuccess()
        except Exception:
            logger.error("[SendSMSV4] send v4 sms", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class SendMMSV4(SecPhoneView):
    @SecPhoneView.VerifySignV4
    @method_decorator(ratelimit(key='post:number', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='post:number', rate='1/60s', block=True))
    @method_decorator(ratelimit(key='post:to', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='post:to', rate='1/60s', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/1h', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/60s', block=True))
    def post(self, request):
        try:
            post_data, err_code, err_msg = self.ReadPostJson(request, check_list=['number', 'to', 'image_url'])
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            from_number = post_data['number']
            to_number = post_data['to']
            image_url = post_data['image_url']
            logger.error(f"[SendMMSV4] send v4 mms, from:{from_number}, to:{to_number}, image_url:{image_url}")
            TelnyxUtil.send_mms(from_number, to_number, image_url)
            return self.ReturnSuccess()
        except Exception:
            logger.error("[SendMMSV4] send v4 mms", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class BukaOutboundSMSWebhook(SecPhoneView):

    def post(self, request):
        try:
            post_data, err_code, err_msg = self.ReadPostJson(request, check_list=())
            logger.error(f"[BukaOutboundSMSWebhook] buka webhook post_data:{post_data}, POST: {request.POST}")
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            return self.ReturnSuccess()
        except Exception:
            logger.error("[BukaOutboundSMSWebhook] buka webhook post failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

    def get(self, request):
        try:
            get_data, err_code, err_msg = self.GetDataInGet(request, check_list=())
            logger.error(f"[BukaOutboundSMSWebhook] buka webhook get_data:{get_data}, GET: {request.GET}")
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            return self.ReturnSuccess()
        except Exception:
            logger.error("[BukaOutboundSMSWebhook] buka webhook get failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)
