# coding=utf-8
import json
import random
import re
import time

from zhipuai import ZhipuAI

from SecPhone import settings
from SecPhone.settings import logger
from Sms.filters import filter_constant

## HH
hh_key = "f67bf0842e8324d0e453e6c2684d3f91.QqUaMrnooQDpC1gN"
client1 = ZhipuAI(api_key=hh_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=3  # 设置最大重试次数为3
                  )
# zhouzhou
zz_key = "8350b0b9305b4dcf5e6232967b5276a7.nC6UArhOdy9EvTEg"
client2 = ZhipuAI(api_key=zz_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=3  # 设置最大重试次数为3
                  )

# ran
ran_key = "0b995dfd8ac0cba5ff89a24b126a2314.MtROSnLKozaNrN7T"
client3 = ZhipuAI(api_key=ran_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=3  # 设置最大重试次数为3
                  )

# fatpo
fatpo_key = "9ccb89c203dc37f34c0b711eff6639fa.oMYKVjZ1AzG5v5Eg"
client4 = ZhipuAI(api_key=fatpo_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=3  # 设置最大重试次数为3
                  )

# fatpo-us
fatpo_us1_key = "a3fcc3f145d1d7d478ae79a255f0bc30.CI6hI5U3wmFdg6Y6"
client5 = ZhipuAI(api_key=fatpo_us1_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=3  # 设置最大重试次数为3
                  )

# jiejie1
jiejie1_key = "e47f8df4f8ad1ac30e353d0cccd3dfe8.htKXJkKUWePCikY2"
client6 = ZhipuAI(api_key=jiejie1_key,
                  timeout=60.0,  # 设置超时时间为60秒
                  max_retries=3  # 设置最大重试次数为3
                  )

# 拿到AI提示语
with open(settings.BASE_DIR + "/zhipu_foreigner.json") as f:
    zhipu_foreigner_json = json.load(f)
    zhipu_foreigner_compressed_json_str = json.dumps(zhipu_foreigner_json, separators=(',', ':'))


class ZhipuForeignerUtil:
    @staticmethod
    def is_content_foreigner_bad(sms_content: str) -> (bool, str):
        sms_content_ = sms_content.replace("\n", " ").replace("\r", " ").strip()
        sms_content = sms_content_.replace("*", " ")  # ai 看到*会觉得是有害的，我们替换成空格吧
        logger.info(f"[SendSms] zhipu-foreigner-ai, sms_content: {sms_content_} checking...")

        try:
            if not sms_content:
                logger.error(f"[SendSms] zhipu-foreigner-ai, param invalid, sms_content: {sms_content_}")
                return True, ""

            # 单词少就不用考虑广告类目
            content = zhipu_foreigner_compressed_json_str

            content += "\n现在请您审核: " + sms_content

            start = time.time()
            client = random.choice([client1, client2, client3, client4, client5, client6])

            if client.api_key == hh_key:
                logger.info(f"[SendSms] zhipu-foreigner-ai, use hh_key")
            elif client.api_key == zz_key:
                logger.info(f"[SendSms] zhipu-foreigner-ai, use zz_key")
            elif client.api_key == ran_key:
                logger.info(f"[SendSms] zhipu-foreigner-ai, use ran_key")
            elif client.api_key == fatpo_key:
                logger.info(f"[SendSms] zhipu-foreigner-ai, use fatpo_key")
            elif client.api_key == fatpo_us1_key:
                logger.info(f"[SendSms] zhipu-foreigner-ai, use fatpo_us1_key")
            elif client.api_key == jiejie1_key:
                logger.info(f"[SendSms] zhipu-foreigner-ai, use jiejie1_key")

            response = client.chat.completions.create(
                model="GLM-4-Air-0111",
                messages=[
                    {"role": "user", "content": content},
                ],
                user_id="51081731492666544",
                max_tokens=128,
            )
            answer = response.choices[0].message
            logger.info(
                f"[SendSms] zhipu-foreigner-ai, query:[[{sms_content_}]], cost: {round(time.time() - start, 2)} s, {answer}")

            ans_content = json.loads(answer.to_json())['content']
            ans_content = ans_content.replace("\n", "").replace("\\n", "") \
                .replace("```", "").replace("json", "").strip()

            try:
                # 只要json
                match = re.search(r'\{(.*?)\}', ans_content)
                if match:
                    ans_content = match.group(0)

                ans = json.loads(ans_content)
            except Exception:
                logger.error(f"[SendSms] zhipu-foreigner-ai says json invalid, {ans_content}, query:[{sms_content_}]")
                ans = {"result": "unknown", "reason": "json invalid"}

            try:
                reason = ans['reason']
            except Exception:
                reason = ""

            if str(ans['result']).lower() == "yes":
                logger.warning(f"[SendSms] zhipu-foreigner-ai says invalid, {ans}, query:[{sms_content_}]")
                return filter_constant.AI_RSP_INVALID_CONTENT, reason
            elif str(ans['result']).lower() == "no":
                logger.info(f"[SendSms] zhipu-foreigner-ai says valid, {ans}, query:[{sms_content_}]")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
            elif str(ans['result']).lower() == "unknown":
                logger.warning(f"[SendSms] zhipu-foreigner-ai says unknown, {ans}, query:[{sms_content_}]")
                return filter_constant.AI_RSP_UNKNOWN, reason
            else:
                logger.error(f"[SendSms] zhipu-foreigner-ai says unknown, {ans}, query:[{sms_content_}]")
                return filter_constant.AI_RSP_UNKNOWN, reason
        except Exception as ex:
            if "并发数过高" in str(ex):
                logger.warning(f"[SendSms] zhipu-foreigner-ai says failed, over qps, {sms_content_}")
            else:
                logger.error(f"[SendSms] zhipu-foreigner-ai says failed {sms_content_}", exc_info=True)
            return filter_constant.AI_RSP_UNKNOWN, "zhipu-foreigner-ai-failed"


if __name__ == '__main__':
    content_list = [
        (
            """Porq no me contestas 💔💔 no estoy impuesta a estar sin ablar contigo 💔💔 todas las noches ablabamos 💔💔 nos estabamos llevamdo mas bien""",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Y ya no encontre mi pulserita 💔💔",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Estabamos tan feliz 💔💔nunca podremos estar juntos por mas q lo kiera no semos destinados a estra juntos 💔💔💔te voy extrañar",
            filter_constant.AI_RSP_VALID_CONTENT,
        ),
        (
            "Siempre seras el unico para mi espero lo sepas 💔💔",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Y especialmente cuando mi mama se ira y no dormira aqui 💔💔",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Tristemente dormire sola todo el dia no podimos estar bien a solas y en el momento exacto te vas 💔💔",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Ojalá encuentres a alguien pero no kiero saber de ti cuando tu agas tu vida con alguien no kedre q estes en mi vida ni la de mijo as tu vida y ya",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Te trate de decir la verdad y ser honesta y te fui a. Buscar ojala sepas q fue mi ultimo intento ya no puedo seguir obligándote a q estes en mi vida",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Por el echolake",
            filter_constant.AI_RSP_VALID_CONTENT
        )
    ]

    data = """
    En la calle quedó como la basura q es
Amor si no nos vemos mañana a saber cuando nos vamos a ver te he extrañado desde el sabado
Discúlpame por lo que dije no debo de tomar mi frustración hacia ti
No sabes que horrible es aquí sin estar contigo no se si te sientes bien sin mi pero yo no
Ya ni tengo dinero y tuve que comprar este teléfono a $19.99 solo porque me bloqeastes
Yo solo te digo que si no nos vemos mañana Ivan no te vero toda esta semana y te vale eso
Después tampoco quiero que me digas que te hago falta solo recuerda que fue to culpa el hecho que no querías verme mañana
Te amo amor….perdoname por fustrarte a ti pero no parro de llorar ya en la noche y sigo pensando en como regue la cosas…amor te quería ver mañana porque es mi única chance. No quiero pelear contigo anantes te veo ya.
Espero que me perdones es que no he estado bien aquí. Me siento solo.
Si cambias de opinión podría estar ahí a la 10
Te amo amor porfavor descuida lo que te dije solo fue un momento de frustración nada mas
Amor no te enojes buen sabes que a veces soy bruto yo
Bien sabes también que me estoy muriendo todo los días sin ti 😞🥺
Me voy a cambiar entonces y bañar en la mañanita
بتعرفي شو حكتلي ؟
مرات اختك بتبعتلك سمايلي عالواتس بتحسيه مسبه وهو بكون اشي عفوي
وعد
Es como tomarle confianza a alguien y te apuñale espalda
حاضر ولا يهمك شكرا الك ع اهتمامك الرائع بارك الله فيكي الصراحه انك قلقانه فيي
خلص تمام منيح الي حكتيلي انا اصلا جاي اسمع الي بقلبك بلكي شوي رتحت لاني مش قادر اصدق انتي ووعد الي بعرفها نفس الشخص كانو ركبك جني كانو صاير معك اشي مش قادر افسره بالمره
هيني رجعت للصفر بكتب وحاسس اشي بخنقني بس مش حاسس انو عندي قيمه لاي حدا انا كيف راح اصير قدوه للاولاد مش قادر استوعب
Tú sabes que yo no soy de venganza gracias a dios
اصليلو كمان ؟
ماشي يا ام الامير بتموني شو مبدك
قبل اسبوع صليت الفجر
حاضر
يعني ربك هو محمد
بس يجي ناكر ونكير يسالوكي من هو ربك تقولي محمد
عالجهتين راح تضمني الجنه
راح يفكرو محمد النبي
لما اكون مخنوق بصلي
لاي اشي
الحلو انو الله بفتحها بوجهي
هههههههه
انا راح ادق اسم محمد ع صدري ومحد راح يحس بالاشي
جد بتحكي ولا بتمزحي
لمين بدك تصلي لبوذا؟
تحت راية الاسلام قتلو بعض في سوريا وكلهم موحدين لالله
شكلو عشان في اعصار قريب
اكيد في روحانيات زي المسيحيه كل احد لشو بتجمعو ؟
في هالات حولين كل بني ادم بتجمعو مع بعض
طيب مالصيني بصلي للشجره وبحس انو ارتاح الشيعي بحط حجر تحت راسه والبوذي مش بصلي للصنم هو مش اهبل هو عارف انو صنم
واللي بعبد الشمس هو بعبدش الشمس هو مش متخلف عارف انو في قوى وراها بتحركها بس مش قادر يعرف شو هي
كان في اجتماع للعيله يوم الجمعه وحكو بدنا ننزل ننظف القبور في القدس تاني يوم المهم انا شطفت مرحتش هم راحو حكو هدول القبور لكل العيله الا في حال ثبت ع اي واحد انو ملحد في العيلة مش رايحين ندفنو فيها
لا مهو الجواب انو الي بصلي عشان خايف من النار قال صلاتو مش مقبوله بدك تصلي عشان بتحبي الله وبس مش بتخافي يحرقك
الله يفضح عرضو الي خترع البرنامج هادا هدلي حيلي كل عشر مسجات بعشره شيكل 😳
الله لو بحبنا جد كان ما سخط ادم من الاساس وخلانا كلنا فوق وكس اخت الشجره ع اخت التفاح مهيو معبي البسطات محد متطلع عليه
هي اجت من هالتفاحه الخرا مهي سوولو ياها جكر معسل وبدل التفاحه تنتين
هو يرد بس بدي اسالو خمس ست اساله ازا جاوب عليهم بدي اضل راكعلو لاموت من الجوع
مش يمكن العكس هأمن ؟ الطبيعه بتخاف عحالها اكتر من اي حد
الحياة عالارض عمرها مليار سنه الدين بس اجا اخر خمس تلاف سنه
يعني شيء لا يذكر واكيد راح يختفي بالوقت القريب خلال جيل او تنين
مش راح يعملو اشي لانو قاعدين الماسونيه بمهدو للاشي وبزرعو شرايح في الناس الي بغلط بتحااسب
مش راح يكونو هاي اخر نقله متدينه عفكره نص خواتي بصلوش اخوي بصليش ولادهم يصلوش
الكل عارف وفاهم وساكت محدش حاكي اشي
اكيد لما تكوني مفكره اشي ويطلع اشي لو اضل اشربك مي واقلك هادا حليب بعد ٣٠ سنه اقدك كزبت عليكي هادي اسمها مي راح تنجني
اسمعي تعالي عالواتس هلكني دولارات كل شوي تلاته تلاته تلاته 😥
يعني هو الي مانعني اني انام مع امي ولا اختي الدين ؟؟؟ هيك قصدك
ولا المانعني اني اقتل الدين ؟؟؟ او اشرب او ازني او بالعكس بعد خبرتي بالحياه اكتر ناس ولاد شرموطه هم الي بعرفو الله بسوو كلشي وبتوبو
تعالي هناك
Mi mamá ha estado despierta todo la mañana vieras
Где кольцо, пиздобол про любовь?
Услышал внятно?
Будь проклят за иллюзии и новогодние застолья. Я никогда не была твоей семьей, а гостья если у тебя настроение
Показывал мне не любовь, а зловредность. Спасибо большое
Зачем жадине отношения?
Ayer te escribir para que recogieras al niño y nunca me respondiste
Pero no te preocupes no lo voy hacer más bye cuídate
С Яной все нормально? Я ей пишу- она не пишет целый день
Все так же, Зиночка. Теперь понятно, почему не смогла ответить. Спасибо
Она мне написала, что не может ответить, но я не знал в чем дело. Спасибо
Estoy loco por ti Alexis😭 quiero estar contigo y cuidar de ti.
Yea ok bro u got it deadass… I'm done
Te quiero mandar algo y quiero q lo veas
A ver no te niego que aún me atraes un tin
A ver no te niego que aún me atraes un tin
La entrada no está ahí, está en el edificio 7.
Nicky es Marie te extraño no se que es lo que me está pasando te extrañó un montón te llevo pendants mucho pero como no tengo como comunicarme  se me hace más difícil y tengo que estar descargando aplicaciones no sé qué voy hacer para olvidarte otra vez porque sé que no quieres nada conmigo a lo mejor ya eres feliz con otra persona y sería mejor alejarme para que seas feliz pero es difícil y más ahora pero creo que tomaste una buena decisión de mudarte lejos de mí porque ahora no te puedo molestar ni llevarle a tu casa pero te extraño un montón y te amo 😣😭
A lo mejor no te importe lo que te aya escrito pero solamente me quería desahogar contigo y decirte como me siento y aunque me tratas como cualquier te sigo queriendo 💯😔
Llevo tiempo sin llorar así pero me está atacando otra vez y eso no era lo que yo quería te amo y te quiero mucho eres un buen hombre sigue hacia adelante
Parece que eres preferido de mi mamá te defiende mucho 😒
Tú nunca ha sido mi novio ni ha convivido con ella para decir mi suegra
Como que mi mamá quiere el mal para mi 😂
Si tiene q ver conmigo como padre seamos honesto no eres lo mejor
No vamos a tocar ese tema quieres tener la razón y no quiero discutir
Para cambiar de tema si te tuviera al frente te chupara todo
Pero puedo decir lo mismo de ti al principio q empezamos si pero después no, fíjate que yo le decía a yuni ya fulano no me esto como antes ni se porque sigo con el. Y no me hagas decirte mas te dejo que ya no me gusta tu vibra si me quieres humillar hazlo no sería ni la primera ni la última vez
Me vas bien soy yo la q me estoy humillando bye
Fuiste mi segundo hombre en tener relaciones y el primero en hacerle el sexo oral
Como también chuparle atrás nunca se lo había echo a nadie
Hoy en día te puedo decir q no le he chupado atrás a nadie como te lo hacía a ti
Y se que te encantaba y me encantaba también cuando me lo pedías 😝
Okay pero si tengo q hacerlo lo hago antes q lo haga con otra mejor q lo haga conmigo pero ya me gusta 🥰
Más bien te la chupara me la metiera completa hasta garganta oidespués te la escupo toda te chupo lo huevitos la parte de atrás me la meto en la chochita después te la vuelvo a chupara hasta que te la saque con la boca y me la trago completica 😘
형 저 태현이요 전화해요
I guess, pero bueno tú no te dejas hacer nada no se si es q ya no te gusto 🤷🏻‍♀️ y estaba pensando mejor no seguir perdiendo más el tiempo y hablando de esto temas, mejor me concentro en otras cosas it was nice talking to you bye cuídate.😘
إذن ماذا تقول؟
Yo pense q iva todo bien en tu trabajo ?
Nunca me dejas estar para ti cuando te va mal
Ahi claramente se ve cristina y se escucha en el vídeo
Eran abby y ella conmigo anna ni estana conmigo ella estaba con su papa
Estabamos bien eran los meses mas bonitos q aviamos pasado de tantos estos eran los mejores porq enverdad le estabamos echando ganas
Esta bien te dare lo q quieres te dejare de insistir degare q agas tu vida y seas feliz yo se q yo la neta no soy suficiente ni bonita ni nada para ti
Ojala y arregles lo de tu trabajo como kiera ya no te vere ya el carro ya sabes q cualquier dia se entregara oh vendran por el y pues estaba disfrutando dia a dia contigo porq yo se q ya no nos veiriamos  diario
Buenas noches perdon por no ser lo q tu buscabas 💔
Te trate de decir la verdad y ser honesta y te fui a. Buscar ojala sepas q fue mi ultimo intento ya no puedo seguir obligándote a q estes en mi vida
Ojalá encuentres a alguien pero no kiero saber de ti cuando tu agas tu vida con alguien no kedre q estes en mi vida ni la de mijo as tu vida y ya
Tristemente dormire sola todo el dia no podimos estar bien a solas y en el momento exacto te vas 💔💔
Y especialmente cuando mi mama se ira y no dormira aqui 💔💔
Siempre seras el unico para mi espero lo sepas 💔💔
Estabamos tan feliz 💔💔nunca podremos estar juntos por mas q lo kiera no semos destinados a estra juntos 💔💔💔te voy extrañar
Porq no me contestas 💔💔 no estoy impuesta a estar sin ablar contigo 💔💔 todas las noches ablabamos 💔💔 nos estabamos llevamdo mas bien
"""

    for line in data.split("\n"):
        line = line.strip()
        if line:
            content_list.append((line, filter_constant.AI_RSP_VALID_CONTENT))

    total_size = len(content_list)

    success_cnt = 0
    failed_cnt = 0
    index = 0
    for (c, must_type) in content_list:
        logger.warning(f"{'#' * 50} {index}/{total_size}")
        index += 1

        rsp_type, category = ZhipuForeignerUtil.is_content_foreigner_bad(sms_content=c)

        if must_type == rsp_type:
            # logger.warning(f"check success!! {c}")
            success_cnt += 1
        else:
            f.write(c + '\n')
            failed_cnt += 1
            logger.warning(f"check failed!!!  [[{c}]]")
            logger.warning(f"check failed!!!  [[{c}]]")
            logger.warning(f"check failed!!!  [[{c}]]")

        time.sleep(0.2)

    print("total_cnt:", success_cnt + failed_cnt)
    print("success_cnt:", success_cnt)
    print("failed_cnt:", failed_cnt)
