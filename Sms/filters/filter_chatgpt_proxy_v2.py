# encoding=utf-8
import json
import time

import requests.adapters
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter, Retry

from SecPhone.settings import logger
from Sms.filters import filter_constant

# https://aiproxy.io/，<EMAIL>


# 创建一个Retry对象，配置重试策略
retry_strategy = Retry(
    total=3,  # 最大重试次数
    backoff_factor=1,  # 重试间隔时间的回退因子
    status_forcelist=[500, 502, 503, 504],  # 针对这些 HTTP 状态码进行重试
)

# 创建一个Session对象，它会隐式地使用连接池
session = requests.Session()

# 设置重试策略, 设置连接池的大小（例如，最大同时打开的连接数）
adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=10, pool_maxsize=50)
session.mount("http://", adapter)
session.mount("https://", adapter)


class ChatGptProxyUtilV2:
    @staticmethod
    def is_sms_bad(user_id: int, sms_content: str):
        try:
            sms_content = sms_content.replace("\n", " ")

            endpoint = "https://apivip.aiproxy.io/v1/chat/completions"
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer sk-evixMGNXmBw4rxd1eUFOYEXxsX9njJzjTUsSXSRCdKApeLFk"
            }

            if not sms_content:
                logger.error(f"[SendSms] gpt2-ai, param invalid: user_id: {user_id}, sms_content: {sms_content}")
                return filter_constant.AI_RSP_VALID_CONTENT

            content = """{"role":"You are a telecommunications content reviewer, responsible for reviewing the content of English text messages.","task":"Based on the user's input, identify if the text contains harmful, illegal, unethical, or offensive content, and return a JSON result.","harmful_content":{"1":"Drugs, marijuana, illegal substances","2":"Harassment or unsolicited introductions to strangers, e.g., phrases like 'this is', 'from', 'I'm', etc.","3":"Discrimination (racism, sexism, ageism, homophobia, ableism, or any demeaning, insulting, or discriminatory language towards any group)","4":"Violence, threats, intimidation","5":"Scams: lottery, cryptocurrency, investment fraud, romance scams, parcel scams, phishing, etc.","6":"Pornography, subtle pornography, solicitation","7":"Impersonation (fake government agencies, businesses, clubs, foundations)","8":"Fraud (telecom fraud, phishing, identity scams, lottery fraud, threat scams)","9":"Sensitive political content","10":"Harmful or offensive language","11":"Messages containing polite greetings, formal language, or honorifics (e.g., 'Sir', 'Mr.', 'Mrs.'), suggesting an attempt to establish contact with a stranger, which may indicate fraud.","12":"Sender claims to represent an organization, institution, company, or foundation, as all senders are individual persons. This indicates a high likelihood of fraud.","13":"References to packages, tracking numbers, addresses, or delivery requests, as these may suggest courier-related scams.","14":"Urgent or alarming language (e.g., 'urgent', 'important', 'immediate action required'), suggesting potential fraud or manipulation.","15":"References to financial information, money transactions, compensation, or investment opportunities (e.g., Bitcoin, lottery winnings, gift cards, investment opportunities)."},"output_requirements":{"1":"Important: The output must always be in JSON format, e.g., {'result': 'yes', 'reason': 'harassment'}.","2":"The 'result' field should be one of the following: 'yes', 'no', or 'unknown'.","3":"Return only the JSON content. Do not include any additional information."},"rules":{"1":"Identify subtle forms of solicitation and mark them as 'yes'. This includes discussions on price, location, or any element related to solicitation (e.g., phrases like 'new xxx arrived', 'pretty arrived', 'delicious arrived').","2":"Detect subtle scams and mark them as 'yes'. This includes impersonation, time-sensitive deception, situational deception, and financial terms like 'PayPal', 'cash', 'Bitcoin', 'Apple Card', etc.","3":"Detect any mention of institutions, companies, organizations, or official titles as these may indicate fraudulent intent, e.g., 'foundation', 'organization', 'fund', 'Mr.', 'Mrs.', 'Sir'.","4":"Detect any message requesting personal or financial details, urgent language, or references to delivery information as these indicate potential scams (e.g., courier scams or identity fraud).","5":"Use contextual analysis to identify novel or emerging patterns of harmful content and flag them accordingly."},"examples":[{"user_input":"FBI Warning","output":{"result":"yes","reason":"Harassment, fraud"}},{"user_input":"Ana Young are you free? \n\nAbigail Dillen","output":{"result":"yes","reason":"Identity fraud, greeting followed by a name, potentially a phishing scam"}},{"user_input":"This is the ABC Foundation contacting you regarding a donation opportunity.","output":{"result":"yes","reason":"Impersonation, fraud"}},{"user_input":"Package ready for delivery! Please confirm your address here.","output":{"result":"yes","reason":"Courier scam"}},{"user_input":"Urgent: You need to act immediately to secure your account.","output":{"result":"yes","reason":"Urgent language, potential scam"}}],"notes":["If unsure, output 'unknown' and provide a brief explanation.","Stay updated on the latest fraud methods to improve judgment accuracy."]}"""
            content += "请审核:" + sms_content

            payload = {
                "model": "gpt-3.5-turbo",
                "messages": [{"role": "user", "content": content}],
                "user": str(user_id),
                "max_tokens": 64
            }
            start = time.time()
            response = session.post(endpoint, headers=headers, json=payload, timeout=(10, 60))

            rsp_json = response.json()
            if "choices" not in rsp_json or len(rsp_json["choices"]) == 0:
                logger.error(f"[SendSms] gpt2-ai, json unreadable return unknown, user_id: {user_id} "
                             f"chat query:[[{sms_content}]], rsp: {rsp_json}")
                return filter_constant.AI_RSP_UNKNOWN

            answer = rsp_json["choices"][0]["message"]["content"].replace("\n", "").replace("\\n", "") \
                .replace("```", "").replace("json", "").strip()
            logger.info(f"[SendSms] gpt2-ai, user_id: {user_id}, query:[[{sms_content}]], "
                        f"cost: {round(time.time() - start, 2)} s, {answer}")

            bad_rsp_list = ["及时处理", "种族歧视", "暴力威胁"]
            for i in bad_rsp_list:
                if i in answer:
                    return filter_constant.AI_RSP_INVALID_CONTENT

            try:
                answer_json = json.loads(answer)
            except Exception:
                logger.error(f"[SendSms] gpt2-ai says invalid, user_id: {user_id}, query:[[{sms_content}]], {answer}")
                answer_json = {"result": "yes"}

            if answer_json["result"] == "yes":
                logger.warning(f"[SendSms] gpt2-ai says invalid, user_id: {user_id}, query:[[{sms_content}]], {answer}")
                return filter_constant.AI_RSP_INVALID_CONTENT
            elif answer_json["result"] == "no":
                logger.warning(f"[SendSms] gpt2-ai says valid, user_id: {user_id}, query:[[{sms_content}]], {answer}")
                return filter_constant.AI_RSP_VALID_CONTENT
            elif answer_json["result"] == "unknown":
                logger.warning(f"[SendSms] gpt2-ai says unknown, user_id: {user_id}, query:[[{sms_content}]], {answer}")
                return filter_constant.AI_RSP_UNKNOWN

            logger.error(f"[SendSms] gpt2-ai json unreadable return unknown, user_id: {user_id} "
                         f"chat query:[[{sms_content}]], {answer}")
            return filter_constant.AI_RSP_UNKNOWN
        except Exception:
            logger.error(f"[SendSms] gpt2-ai, user_id: {user_id}, query:[[{sms_content}]] failed", exc_info=True)
            return filter_constant.AI_RSP_UNKNOWN


if __name__ == '__main__':
    content_list = [
        "Good morning, Steven. I’ m so soryy, but I’ll be at your place tomorrow. Will call you later:driving now",
        "Mr. Dale. I sent you a message over a week ago about a solution. You didn’t get that?",
        "Not the company is fully back to work and your package is ready for delivery",
        "Your address sir ?	",
        "Text me back date plz I’m start filling it out",
        "Sorry sir for the long delay we’re ready to make it up to you sir",
        "I know you should have got your payment two years ago",
        "You got until 330 to text back",
        "Could u hurry up and send it so I can be sure it’s on there Before I get to the store please",
        "um ima suck yo dick whenever you want me to and ima show my pussy whenever you wanna see it but not rn because my period just came on today",
        "My love are you still up",
        "Greetings from Worldwide Travel Club! This is Matt, Reservations Mgr. We were waiting for you in your scheduled webinar yesterday at 5pm. What happened?",
        "Greetings, You have been selected to pick cotton at the nearest plantation. Be ready at 12AM NOVEMBER 13 SHARP with your belongings. Our Executive Slaves will come get you in a Brown Van, be prepared to be searched down once you've enter the plantation. You are in Plantation Group A.",
        "i ordered a shirt and pants from a store located in the mall named “Sherway Gardens” Can you please pick it for me",
        "700$ overnight? or 200 QV ? call me back if interested",
        "120hhr, 60hr",
        "hhr 200, hr 30",
        "I had a great time having sex with you last night. Do you want to see my big cock?",
        "hello, how are you?",
        "Congratulations, you have received the largest prize in Michigan, $200 million. Please provide your ID information so that I can verify it for you.",
        "FBI warning, you are currently suspected of a criminal offense. Please click on this link immediately, otherwise we will come to arrest you immediately.",
        "I'm in a meeting at the moment. I can't make a call, and i need you to get a task done",
        "Head to the store and keep me updated with the value available for Apple Card",
        "You little dwarf, don't let me see you. If I see you, I'll hit you",
        "Do you still have those drugs from last time?",
        "New tea arrived,come and taste it!",
        "Called Mebo, and I am from Pennsylvania.",
        "Hey it’s McKenna kirras friend your number is on your Facebook so I got it from there. Don’t tell kirra I’m texting you this but let’s plan a surprise trip for kirra and you guys come here. Hunter and I will buy the tickets I just need to know your work shedule since I work too so we can figure something out then I’ll send kirra the tickets to surprise her. Sound like a plan?"
        "Caravelle inn: 1310 N 1st St, San Jose, CA 95112",
        "outcall or incall?",
        "Howard Johnson hotel: 1672 Herndon Rd, Ceres, CA 95307",
        "New tea arrived, come and taste it!",
        "New Pretty flowers arrived",
        "New Pretty ? arrived",
    ]
    for c in content_list:
        logger.warning("#" * 50)
        logger.warning(f"[[{c}]]")
        r = ChatGptProxyUtilV2.is_sms_bad(user_id=0, sms_content=c)
        logger.warning(r)
        time.sleep(0.5)
