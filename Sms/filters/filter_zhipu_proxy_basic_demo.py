# coding=utf-8
import json
import re
import time

from zhipuai import ZhipuAI

from SecPhone import settings
from SecPhone.settings import logger
from Sms.filters import filter_constant


class ZhipuBasicUtilDemo:

    @staticmethod
    def is_content_basic_bad(sms_content: str) -> (int, str):

        # ilan
        ilan_key = "0ca48c7e9414820e9690a065412fc4e5.10pX9URDzeu9e5GT"
        client = ZhipuAI(api_key=ilan_key,
                         timeout=60.0,  # 设置超时时间为60秒
                         max_retries=2  # 设置最大重试次数
                         )

        # 拿到AI提示语
        with open(settings.BASE_DIR + "/zhipu_zh.json", encoding="utf-8") as f:
            zhipu_json = json.load(f)
            zhipu_compressed_json_str = json.dumps(zhipu_json, separators=(',', ':'))

        sms_content_ = sms_content.replace("\n", " ").replace("\r", " ").strip()
        sms_content = sms_content_.replace("*", " ")  # ai 看到*会觉得是有害的，我们替换成空格吧
        logger.info(f"[SendSms] zhipu-ai, sms_content: [{sms_content_}] checking...")

        try:
            if not sms_content:
                logger.error(f"[SendSms] zhipu-ai, param invalid, sms_content: [{sms_content_}]")
                return True, ""

            logger.info(f"[SendSms] zhipu-ai, use normal ai, sms_content: [{sms_content_}]")
            content = zhipu_compressed_json_str
            content += "\n现在请您审核: " + sms_content

            start = time.time()
            response = client.chat.completions.create(
                model="GLM-4-Air-0111",
                messages=[
                    {"role": "user", "content": content},
                ],
                user_id="51081731492666544",
                max_tokens=128,
            )
            answer = response.choices[0].message
            logger.info(
                f"[SendSms] zhipu-ai, query:[[{sms_content_}]], cost: {round(time.time() - start, 2)} s, {answer}")

            ans_content = json.loads(answer.to_json())['content']
            ans_content = ans_content.replace("\n", "").replace("\\n", "") \
                .replace("```", "").replace("json", "").strip()

            try:
                # 只要json
                match = re.search(r'\{(.*?)}', ans_content)
                if match:
                    ans_content = match.group(0)

                ans = json.loads(ans_content)
            except Exception:
                logger.error(f"[SendSms] zhipu-ai says json invalid, {ans_content}, query:[{sms_content_}]")
                ans = {"result": "unknown", "reason": "json invalid"}

            try:
                category = ans['category']
            except Exception:
                category = []

            if str(ans['result']).lower() == "yes":
                logger.warning(f"[SendSms] zhipu-ai says invalid, {ans}, query:[{sms_content_}]")
                return filter_constant.AI_RSP_INVALID_CONTENT, ",".join(category)
            elif str(ans['result']).lower() == "no":
                logger.info(f"[SendSms] zhipu-ai says valid, {ans}, query:[{sms_content_}]")
                return filter_constant.AI_RSP_VALID_CONTENT, ",".join(category)
            elif str(ans['result']).lower() == "unknown":
                logger.warning(f"[SendSms] zhipu-ai says unknown, {ans}, query:[{sms_content_}]")
                return filter_constant.AI_RSP_UNKNOWN, ",".join(category)
            else:
                logger.error(f"[SendSms] zhipu-ai says unknown, {ans}, query:[{sms_content_}]")
                return filter_constant.AI_RSP_UNKNOWN, ",".join(category)
        except Exception as ex:
            if "并发数过高" in str(ex):
                logger.warning(f"[SendSms] zhipu-ai says failed, over qps, {sms_content_}")
            else:
                logger.error(f"[SendSms] zhipu-ai says failed [{sms_content_}]", exc_info=True)
            return filter_constant.AI_RSP_UNKNOWN, "zhipu-failed"


if __name__ == '__main__':
    """
    AI_RSP_INVALID_CONTENT = 0
    AI_RSP_UNKNOWN = 1
    AI_RSP_VALID_CONTENT = 2
    """

    c = ("You Want to have s** with That *****")
    rsp_type, category = ZhipuBasicUtilDemo.is_content_basic_bad(sms_content=c)
    print("rsp_type:", rsp_type)
    print("category:", category)
