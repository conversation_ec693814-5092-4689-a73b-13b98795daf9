import datetime
import hashlib
import json
import random
import time

import pytz
import requests
from gmssl import sm3, func
from requests import adapters

from Common.timeutil import TimeUtil
from SecPhone import settings
from SecPhone.settings import logger
from Sms.info.wangyi_info_bean import WangyiFilterRet


def get_http_session(pool_connections, pool_maxsize, max_retries):
    session = requests.Session()
    # 创建一个适配器，连接池的数量pool_connections, 最大数量pool_maxsize, 失败重试的次数max_retries
    adapter = requests.adapters.HTTPAdapter(pool_connections=pool_connections,
                                            pool_maxsize=pool_maxsize, max_retries=max_retries)
    # 告诉requests，http协议和https协议都使用这个适配器
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session


wy_sessions = get_http_session(5, 20, 3)


class SmsFilterWangyi(object):
    secret_id = "98ace006f700f24d97b39de009cc7e2d"
    secret_key = "e0670e083266908a61c4ab17ff12acd6"
    business_id = "42ebf9a7bd89d725706f4164c8a83b05"
    # self.API_URL = "http://as.dun.163.com/v5/text/check" # 国内
    API_URL = "http://as-text-virginia.dun.163.com/v5/text/check"  # 美东
    VERSION = "v5.2"

    @staticmethod
    def wangyi_gen_signature(params=None):
        buff = ""
        for k in sorted(params.keys()):
            buff += str(k) + str(params[k])
        buff += SmsFilterWangyi.secret_key
        if "signatureMethod" in params.keys() and params["signatureMethod"] == "SM3":
            return sm3.sm3_hash(func.bytes_to_list(bytes(buff, encoding='utf8')))
        else:
            return hashlib.md5(buff.encode("utf8")).hexdigest()

    @staticmethod
    def do_request_wangyi(params: dict) -> dict:
        params["secretId"] = SmsFilterWangyi.secret_id
        params["businessId"] = SmsFilterWangyi.business_id
        params["version"] = SmsFilterWangyi.VERSION
        params["timestamp"] = int(time.time() * 1000)
        params["nonce"] = int(random.random() * 100000000)
        params["signature"] = SmsFilterWangyi.wangyi_gen_signature(params)

        try:
            r = wy_sessions.post(SmsFilterWangyi.API_URL, data=params, timeout=3)
            return r.json()
        except Exception:
            logger.error(f"[SendSms] call wangyi api error, params: {params}", exc_info=True)
            return {}

    @staticmethod
    def gen_filter_by_words(words: list) -> dict:

        word_filter = {}
        for word in words:
            word_filter[word] = '*' * len(word)
        return word_filter

    @staticmethod
    def request_wangyi(user_id: int, content: str, user_created_at: datetime.datetime, ip: str,
                       uuid: str) -> WangyiFilterRet:
        ret = None
        res = WangyiFilterRet()
        res.filter_content = content
        res.is_unknown_content = True

        try:
            # 用户等级，小于 1 个月都是1 级
            current_time = TimeUtil.GetNow()
            time_difference = current_time - user_created_at
            user_level = 1 if time_difference <= datetime.timedelta(days=30) else 3

            ret = SmsFilterWangyi.do_request_wangyi({"dataId": TimeUtil.GetNowTsInStr(),
                                                     "content": content,
                                                     "account": str(user_id),
                                                     "level": user_level,
                                                     "registerTime": TimeUtil.DateTime2Timestamp(user_created_at),
                                                     "ip": ip,
                                                     "deviceId": uuid
                                                     },
                                                    )
            if not ret or "result" not in ret:
                return res

            #  100：色情，200：广告，260：广告法，300：暴恐，400：违禁，500：涉政，600：谩骂，700：灌水，900：其他，1100：涉价值观
            antispam = ret["result"]['antispam']
            task_id = antispam['taskId']
            logger.info(f"[SmsFilterWangyi.get_filtered_content] user_id:{user_id}, task:{task_id}")
            suggestion = antispam.get('suggestion', -1)  # -1是异常值
            label_item = antispam["labels"][0] if len(antispam["labels"]) > 0 else {}
            label = label_item.get("label", 0)
            details = json.dumps(label_item.get('subLabels', []), ensure_ascii=False)

            hits = ret['result']['antispam']['labels']
            first_sub_label = None
            keywords = set()
            for hit in hits:

                if 'subLabels' not in hit:
                    continue

                sub_labels = hit['subLabels']
                for sl in sub_labels:
                    if first_sub_label is None:
                        first_sub_label = int(sl['subLabel'])

                    if 'details' not in sl or not sl['details']:
                        continue

                    kws = sl['details']['hitInfos']
                    for kw in kws:
                        keywords.add(kw['value'])

            filtered_dict = SmsFilterWangyi.gen_filter_by_words(list(keywords))
            keys = filtered_dict.keys()
            filtered_content = content
            if len(keys) > 0:
                # replace phrase first, then the word
                for word in keys:
                    if word.find(' ') != -1:
                        filtered_content = filtered_content.replace(word, filtered_dict[word])

                for word in keys:
                    filtered_content = filtered_content.replace(word, filtered_dict[word])

            wangyi_ret = WangyiFilterRet(task_id=task_id, suggestion=suggestion, label=label, sub_label=first_sub_label,
                                         details=details, filtered_content=filtered_content)
            wangyi_ret.is_unknown_content = False
            logger.info(f"[SendSms] user_id:{user_id}, content:[{content}], task_id:{task_id}, "
                        f"label:{label}, sub_label:{first_sub_label}, filtered_content:[{filtered_content}]")

            # suggestion 0 = 通过， 1  = 怀疑， 2 = 不通过
            if suggestion == settings.SMS_SUGGESTION_OK:
                wangyi_ret.is_ok_content = True

            if label in [600, 700]:
                wangyi_ret.is_ok_content = True

            return wangyi_ret

        except Exception:
            logger.error(f"[SendSms] user_id:{user_id}, wangyi, request wangyi error: ret: {ret}", exc_info=True)
            return res

    @staticmethod
    def is_wangyi_ok(user_id: int, uuid: str, origin_content: str, filtered_content: str,
                     user_created_at: datetime.datetime, ip: str) -> (bool, str, str, dict):
        try:
            wangyi_ret = SmsFilterWangyi.request_wangyi(user_id, filtered_content, user_created_at, ip, uuid)
            filtered_content = wangyi_ret.filter_content
            task_id = wangyi_ret.task_id

            if wangyi_ret.is_ok_content:
                logger.info(f"[SendSms] user_id: {user_id}, wangyi says valid, content:[{origin_content}]")
                return True, filtered_content, task_id, wangyi_ret

            logger.warning(f"[SendSms] user_id: {user_id}, wangyi says invalid, content:[{origin_content}]")

            return False, filtered_content, task_id, wangyi_ret
        except Exception:
            logger.error(f"[SendSms] user_id: {user_id}, wangyi says failed, content:[{origin_content}]", exc_info=True)
            return False, filtered_content, "", {}


if __name__ == '__main__':

    contents = ["Incall or Outcall service?",
                "Bitch you homeless Mf you On Facebook talking about me when you don’t know shit bitch lol I’m glad you ask chel broke ass for answers because that bit don’t know shìt hoe,",
                "hi there, body to body, i would like u to lick me ball?",
                "Okay the cops and I will be there later?",
                "Bully. You're a mother sucking bully. Hope money has your back like you do it's. Lol. You really are so fucking selfish",
                "fuck you",
                "how are you",
                "boomed!!!",
                "This happens to mother****ers who think this is a ****ing game, you decide if you want to end up like them or if you prefer to solve this forever and for good"
                "Got a nice cock and good stamina",
                "I wanna **** your ass and put my tongue inside and start scrolling your cock till you cum in my mouth and I'll allow you put your **** in my mouth and **** all the spam there"
                ]

    for c in contents:
        res = SmsFilterWangyi.is_wangyi_ok(user_id=123, uuid="123-456", origin_content=c, filtered_content=c,
                                           user_created_at=datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=pytz.UTC),
                                           ip="*******")
        print(res)
