# 直接放行的words
# todo: 选出最高频的100个短句
raw_allowed_words = {'Hola', 'do', 'begging', 'Hmmm', 'hello', 'would', 'Wassup', 'baby', 'alarm', 'welcome', 'Maybe',
                     'Huh', 'hi', 'lol', 'Can', 'Babes', 'Eee', 'up', 'i', 'your', 'Im', 'text', 'Mom', 'Wya', '<PERSON>',
                     'That', 'Yess', 'yes', 'Yep', 'there', 'are', 'Really', 'I', 'Get', 'DADDY', 'Right',
                     'okay',
                     'Could', 'How', 'Hiiii', 'Answer', 'to', 'hey', 'Please', 'Hey', 'sorry', 'test', 'Babe',
                     'we', 'h', 'Bet', 'Idc', 'Yeah', 'what', 'Zzzz', 'no', 'love', 'is', 'well', 'you', 'hhh', 'doing',
                     'did', '<PERSON><PERSON><PERSON>', 'Ay', 'Heyyy', 'Your', 'Are', 'talk', 'Baby', 'babe', 'plz', 'aye', 'pls',
                     'Unblock',
                     'babes', 'Why', 'unblock', 'please', 'just', 'hlo', 'It', 'me', 'signal', 'call', 'Yoo', 'good',
                     'Set',
                     'now', 'block', 'Talk', 'Wyd', 'Yes', 'it', 'was', 'Yo', 'wtf', 'u', 'hh', 'sure', 'ok', 'Idk',
                     'y', "still",
                     'thank', 'thanks', 'miss', "morning", "evening", "noon", "afternoon", "night", }

# 将白名单单词统一转换为小写
allowed_words = {word.lower() for word in raw_allowed_words}

# 直接放行的句子
raw_allowed_text = [
    "please unblock me",
    "pls unblock me",
    "unblock me",
    "HELLO MADAM",
    "Hello baby",
    "hey baby",
    "hi baby",
    "baby",
    "please Unblock me",
    "I’m sorry",
    "sorry",
    "hi",
    "hey",
    "hello",
    "2 hours",
    "2 hour",
    "2 min",
    "2 mins",
    "you win",
]


class DirectPassWordsFilterUtil:
    @staticmethod
    def check_direct_pass_words(text: str) -> bool:
        original_text = text
        text = text.lower().replace("\n", " ")
        text = "".join([c for c in text if c.isalpha() or c == ' '])

        words = text.split()
        if len(text) > 100 or len(words) > 10:
            return False

        if all(word in allowed_words for word in words):
            return True

        # 如果是这种"2 mins", "2 hours", "1 hour" 都可以直接过
        if DirectPassWordsFilterUtil.check_if_xx_mins(original_text):
            return True

        return False

    @staticmethod
    def check_if_xx_mins(text: str) -> bool:
        words = text.lower().split()
        if len(words) == 2 and words[0].isdigit() and words[1] in ('min', 'mins', 'hour', 'hours'):
            return True
        return False

    @staticmethod
    def check_if_match_white_list(text: str) -> bool:
        text1 = "".join([c.lower() for c in text if c.isalpha()])
        for text_ in raw_allowed_text:
            text2 = "".join([c.lower() for c in text_ if c.isalpha()])
            if text1 == text2:
                return True
        return False


if __name__ == '__main__':
    content_list = [
        ("Hi", True),
        ("hello?", True),
        ("I’m sorry", True),
        ("how are you ?", True),
        ("please Unblock me", True),
        ("Set YOUR alarm", True),
        ("I'm begging you", True),
        ("I still love you", True),
        ("2 hour", True),
        ("2 hours", True),
        ("2 min", True),
        ("2 mins", True),
        ("2 xx", False),
        ("i miss you", True),
        ("i miss u", True),
        ("Ok have a great day sir", True),
        ("HELLO SIR", True),
        ("Good morning sir how are you doing today?", True),
        ("YESSIR", True),
        ("sir?", True),
        ("sir!!", True),
        ("that's why sir", False),
        ("how time no see sir", False),
        ("hello madam", True),
        ("Hello baby?...", True),
        ("Hello honey?...", True),
        ("honey?...", True),
        ("HOW ARE YOU DOING TODAY?...", True),
    ]

    success_cnt = 0
    failed_cnt = 0
    for (content, should_hit) in content_list:
        print("#" * 50)
        rsp_hit_direct_words = DirectPassWordsFilterUtil.check_direct_pass_words(content)
        if should_hit != rsp_hit_direct_words:
            print(content)
            print(content)
            print(content)
            print(rsp_hit_direct_words)

    print("测试白名单===========")
    print("测试白名单===========")
    print("测试白名单===========")
    success_cnt = 0
    failed_cnt = 0
    for (content, should_hit) in content_list:
        print("#" * 50)
        rsp_hit_direct_words = DirectPassWordsFilterUtil.check_if_match_white_list(content)
        if should_hit != rsp_hit_direct_words:
            print(content)
            print(content)
            print(content)
            print(rsp_hit_direct_words)
