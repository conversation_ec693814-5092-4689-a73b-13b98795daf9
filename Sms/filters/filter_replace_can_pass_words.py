import re


class ReplaceCanPassWordsUtil:
    @staticmethod
    def replace_can_pass_words(text: str) -> str:
        # 使用正则匹配所有邮箱
        # text = Util.mask_emails_correctly(text)

        text = re.sub(r"(?i)Feet job", "f*** job", text)  # 招嫖
        text = re.sub(r"(?i)Cowgirl ride", "c****** ride", text)  # 招嫖
        text = re.sub(r"(?i)Backdoor style", "B******* style", text)  # 招嫖
        text = re.sub(r"(?i)Cream pie", "c**** pie", text)  # 招嫖
        text = re.sub(r"(?i)qk visit", "q* *****", text)  # 快餐
        text = re.sub(r"(?i)chlamydia", "chl******", text)  # 病原体
        text = re.sub(r"(?i)banana", "ba****", text)  # 香蕉
        text = re.sub(r"(?i)insert in", "in****", text)  # 插入
        text = re.sub(r"(?i)holes", "h****", text)  # 洞洞
        text = re.sub(r"(?i)eczema", "ec****", text)  # 湿疹
        text = re.sub(r"(?i)enjoy high", "enjoy h***", text)
        text = re.sub(r"(?i)get high", "get h***", text)
        text = re.sub(r"(?i)Morherfucker", "mf", text)
        text = re.sub(r"(?i)fishy", "f****", text)
        text = re.sub(r"(?i)lay with", "l** with", text)
        text = re.sub(r"(?i)lay up with", "l** up with", text)
        text = re.sub(r"(?i)lay down with", "l** down with", text)
        text = re.sub(r"(?i)laid with", "l*** with", text)
        text = re.sub(r"(?i)laid up with", "l*** up with", text)
        text = re.sub(r"(?i)laid down with", "l*** down with", text)
        text = re.sub(r"(?i)making love", "m***** l***", text)
        text = re.sub(r"(?i)make love", "m*** l***", text)
        text = re.sub(r"(?i)made love", "m*** l***", text)
        text = re.sub(r"(?i)breaking in", "b******* in", text)
        text = re.sub(r"(?i)sex with", "s** with", text)
        text = re.sub(r"(?i)sleep with", "s**** with", text)
        text = re.sub(r"(?i)hookup", "h*** u*", text)
        text = re.sub(r"(?i)hook up", "h*** u*", text)
        text = re.sub(r"(?i)fuck", "f***", text)
        text = re.sub(r"(?i)suck", "s***", text)
        text = re.sub(r"(?i)screw", "s***", text)
        text = re.sub(r"(?i)penetrate", "p******", text)  # 插入，渗透
        text = re.sub(r"(?i)oral sex", "o*** s**", text)
        text = re.sub(r"(?i)intercourse", "i*******", text)
        text = re.sub(r"(?i)masturbate", "m********", text)
        text = re.sub(r"(?i)banging", "b*****", text)
        text = re.sub(r"(?i)porn", "p***", text)
        text = re.sub(r"(?i)erotic", "e****", text)
        text = re.sub(r"(?i)orgasm", "o******", text)
        text = re.sub(r"(?i)pleasure", "p******", text)
        text = re.sub(r"(?i)adult film", "a**** film", text)
        text = re.sub(r"(?i)adult content", "a**** content", text)
        text = re.sub(r"(?i)sexually", "s*******", text)
        text = re.sub(r"(?i)sexual", "s*****", text)
        text = re.sub(r"(?i)piped with", "p**** with", text)
        text = re.sub(r"(?i)hooked up with", "h**** up with", text)
        text = re.sub(r"(?i)get laid", "g** laid", text)
        text = re.sub(r"(?i)have sex", "h** s**", text)
        text = re.sub(r"(?i)intercourse", "inter*****", text)
        text = re.sub(r"(?i)cybersex", "c***** s**", text)
        text = re.sub(r"(?i)dirty talk", "d**** talk", text)
        text = re.sub(r"(?i)phone sex", "p**** s**", text)
        text = re.sub(r"(?i)sex toys", "s** t***", text)
        text = re.sub(r"(?i)ugly ass", "u*** a**", text)
        text = re.sub(r"(?i)ugly", "u***", text)
        text = re.sub(r"(?i)Bald", "b***", text)  # 秃头
        text = re.sub(r"(?i)bitches", "b******", text)
        text = re.sub(r"(?i)bitchs", "b*****", text)
        text = re.sub(r"(?i)Bitch", "b****", text)
        text = re.sub(r"(?i)nutt", "n***", text)  # 性高潮 “nutt” 是俚语，指男性的性高潮或射精。
        text = re.sub(r"(?i)clown", "c****", text)  # 小丑
        text = re.sub(r"(?i)pedophilia", "pe********", text)  # 恋童癖
        text = re.sub(r"(?i)Slut", "s***", text)
        text = re.sub(r"(?i)Asshole", "a******", text)
        text = re.sub(r"(?i)Moron", "m****", text)  # 傻瓜
        text = re.sub(r"(?i)motherfucker", "m***********", text)
        text = re.sub(r"(?i)nugga", "n****", text)
        text = re.sub(r"(?i)niggas", "n*****", text)
        text = re.sub(r"(?i)nigga", "n****", text)
        text = re.sub(r"(?i)niggers", "n******", text)
        text = re.sub(r"(?i)nigger", "n*****", text)
        text = re.sub(r"(?i)nigg", "n***", text)
        text = re.sub(r"(?i)incalls", "i******", text)
        text = re.sub(r"(?i)incall", "i*****", text)
        text = re.sub(r"(?i)outcalls", "o*******", text)
        text = re.sub(r"(?i)outcall", "o******", text)
        text = re.sub(r"(?i)bbbj", "b***", text)
        text = re.sub(r"(?i)bbfs", "b***", text)
        text = re.sub(r"(?i)bbj", "b**", text)
        text = re.sub(r"(?i)bj", "b*", text)
        text = re.sub(r"(?i)hhr", "h**", text)
        text = re.sub(r"(?i)qv", "q*", text)
        text = re.sub(r"(?i)redhead", "r******", text)  # 红发，妓女自我描述
        text = re.sub(r"(?i)red head", "r** ****", text)  # 红发，妓女自我描述
        text = re.sub(r"(?i)titties", "t******", text)
        text = re.sub(r"(?i)titty", "t****", text)
        text = re.sub(r"(?i)gorilla", "g******", text)  # 高大丑陋的
        text = re.sub(r"(?i)hussy", "h****", text)  # 荡妇
        text = re.sub(r"(?i)pusssy", "p*****", text)
        text = re.sub(r"(?i)pussy", "p****", text)
        text = re.sub(r"(?i)pussies", "p******", text)
        text = re.sub(r"(?i)knife", "k****", text)
        text = re.sub(r"(?i)knives", "k*****", text)
        text = re.sub(r"(?i)sexy ", "s***", text)
        text = re.sub(r"(?i)sex", "s**", text)
        text = re.sub(r"(?i)faggot", "f*****", text)
        text = re.sub(r"(?i)dick", "d***", text)
        text = re.sub(r"(?i)jerk off", "j*** ***", text)
        text = re.sub(r"(?i)jack off", "j*** ***", text)
        text = re.sub(r"(?i)jackoff", "j******", text)
        text = re.sub(r"(?i)jerkoff", "j******", text)
        text = re.sub(r"(?i)kill", "k***", text)
        text = re.sub(r"(?i)scumbag", "s******", text)  # 恶棍
        text = re.sub(r"(?i)specials", "s*******", text)  # 特殊服务
        text = re.sub(r"(?i)toys", "t***", text)  # 玩具
        text = re.sub(r"(?i)cards", "ca***", text)
        text = re.sub(r"(?i)card", "ca**", text)
        text = re.sub(r"(?i)videos", "v*****", text)
        text = re.sub(r"(?i)video", "v****", text)
        text = re.sub(r"(?i)tracker", "t******", text)
        text = re.sub(r"(?i)dwarf", "d****", text)  # 侏儒
        text = re.sub(r"(?i)cunt", "c***", text)  # 妓女
        text = re.sub(r"(?i)mariguana", "m******", text)  # 大麻
        text = re.sub(r"(?i)blunt", "b****", text)  # 大麻
        text = re.sub(r"(?i)grasses", "g******", text)  # 大麻
        text = re.sub(r"(?i)grass", "g****", text)  # 大麻
        text = re.sub(r"(?i)weeds", "w****", text)  # 大麻
        text = re.sub(r"(?i)weed", "w***", text)  # 大麻
        text = re.sub(r"(?i)jerk", "j***", text)  # 混蛋
        text = re.sub(r"(?i)whored", "w*****", text)  # 妓女
        text = re.sub(r"(?i)whore", "w****", text)  # 妓女
        text = re.sub(r"(?i)shoot", "s****", text)
        text = re.sub(r"(?i)stink", "s****", text)  # 臭死了
        text = re.sub(r"(?i)toxic", "t****", text)  # 有毒的
        text = re.sub(r"(?i)twerking", "t*******", text)  # 电臀舞
        text = re.sub(r"(?i)Junkie", "j*****", text)  # 瘾君子
        text = re.sub(r"(?i)addict", "add***", text)  # 瘾君子
        text = re.sub(r"(?i)raping", "r*****", text)  # 强奸
        text = re.sub(r"(?i)offender", "o******", text)  # 罪犯
        text = re.sub(r"(?i)death", "d****", text)
        text = re.sub(r"(?i)cummed", "c*****", text)  # 射精
        text = re.sub(r"(?i)cumming", "c******", text)  # 射精
        text = re.sub(r"(?i)ejaculation", "e**********", text)  # 射精
        text = re.sub(r"(?i)ejaculate", "e********", text)  # 射精
        text = re.sub(r"(?i)slide in", "s**** in", text)  # 插入我
        text = re.sub(r"(?i)slide in", "s**** in", text)  # 插入我
        text = re.sub(r"(?i)shady", "s****", text)  # 阴暗的非法的
        text = re.sub(r"(?i)trashy", "t*****", text)  # 低劣的
        text = re.sub(r"(?i)Garbage", "g******", text)  # 垃圾
        text = re.sub(r"(?i)burned", "b*****", text)
        text = re.sub(r"(?i)burn", "b***", text)
        text = re.sub(r"(?i)Mixed", "m****", text)  # 混合的，感觉要么是种族歧视，要么是毒品
        text = re.sub(r"(?i)paying", "p*****", text)
        text = re.sub(r"(?i)payments", "p******", text)
        text = re.sub(r"(?i)payment", "p*****", text)
        text = re.sub(r"(?i)paying", "p*****", text)
        text = re.sub(r"(?i)pay", "p**", text)
        text = re.sub(r"(?i)Black", "b****", text)
        text = re.sub(r"(?i)white", "w****", text)
        text = re.sub(r"(?i)Juicy", "j****", text)  # 多汁的
        text = re.sub(r"(?i)booty", "b****", text)  # 屁股
        text = re.sub(r"(?i)hpv", "h**", text)
        text = re.sub(r"(?i)throat", "th****", text)  # 喉咙
        text = re.sub(r"(?i)charge", "ch****", text)  # 收费
        text = re.sub(r"(?i)naughty", "n******", text)  # 顽皮
        text = re.sub(r"(?i)squirt", "s*****", text)  # 喷射
        text = re.sub(r"(?i)Dildo", "d****", text)  # 一种性玩具
        text = re.sub(r"(?i)thousand", "t*******", text)  # 千
        text = re.sub(r"(?i)licking", "l******", text)  # 舔舔舔
        text = re.sub(r"(?i)lick", "l***", text)  # 舔舔舔
        text = re.sub(r"(?i)taste", "t****", text)  # 味道
        text = re.sub(r"(?i)hrs", "h**", text)  # 时间
        text = re.sub(r"(?i)obese", "o****", text)  # 肥胖的
        text = re.sub(r"(?i)ending my life", "ending my l***", text)  # 自杀
        text = re.sub(r"(?i)ending your life", "ending your l***", text)  # 自杀
        text = re.sub(r"(?i)faggot", "f*****", text)  # 基佬
        text = re.sub(r"(?i)rapist", "r*****", text)  # 强奸
        text = re.sub(r"(?i)to hell", "to h***", text)  # 去死
        text = re.sub(r"(?i)to die", "to d**", text)  # 去死
        text = re.sub(r"(?i)bastards", "ba******", text)  # 一群混蛋
        text = re.sub(r"(?i)bastard", "ba*****", text)  # 混蛋
        text = re.sub(r"(?i)jackass", "j******", text)  # 傻逼
        text = re.sub(r"(?i)pig", "p**", text)  # 傻逼
        text = re.sub(r"(?i)cockhead", "c***h***", text)  # 鸡巴头，傻逼
        text = re.sub(r"(?i)shitbag", "s***b**", text)  # 一堆垃圾
        text = re.sub(r"(?i)asswipe", "as*****", text)  # 蠢货
        text = re.sub(r"(?i)retard", "r*****", text)  # 低能儿
        text = re.sub(r"(?i)ravish", "r*****", text)  # 强奸
        text = re.sub(r"(?i)raped", "r****", text)  # 强奸
        text = re.sub(r"(?i)bimbo", "b****", text)  # 傻女人，花瓶
        text = re.sub(r"(?i)asshat", "a*****", text)  # 蠢货
        text = re.sub(r"(?i)crackhead", "c********", text)  # 吸毒者
        text = re.sub(r"(?i)methhead", "m*******", text)  # 吸毒者
        text = re.sub(r"(?i)abortion", "ab******", text)  # 堕胎
        text = re.sub(r"(?i)blowing", "b******", text)  # 炸毁，blowing you up
        text = re.sub(r"(?i)sissy", "s****", text)  # 娘娘腔
        text = re.sub(r"(?i)maricon", "mar****", text)  # 娘娘腔
        text = re.sub(r"(?i)smelled", "s******", text)  # 闻起来，感觉很冒昧
        text = re.sub(r"(?i)smell", "s****", text)  # 闻起来，感觉很冒昧
        text = re.sub(r"(?i)filth", "f****", text)  # 卑鄙
        text = re.sub(r"(?i)medicine", "med*****", text)  # 药物
        text = re.sub(r"(?i)virgin", "vi****", text)  # 处男处女
        text = re.sub(r"(?i)grave", "gr***", text)  # 坟墓
        text = re.sub(r"(?i)full night", "f*** n****", text)  # 整晚
        text = re.sub(r"(?i)pimpin", "p*****", text)  # 皮条客
        text = re.sub(r"(?i)herpes", "her***", text)  # 疱疹
        text = re.sub(r"(?i)hiv", "h**", text)  # 性传播疾病
        text = re.sub(r"(?i)btc", "b**", text)  # 比特币
        text = re.sub(r"(?i)drugs", "d****", text)  # 吸毒
        text = re.sub(r"(?i)drug", "d***", text)  # 吸毒
        text = re.sub(r"(?i)donation", "don*****", text)  # 多少钱，多少价格，（捐献）啊
        text = re.sub(r"(?i)bullet", "b*****", text)  # 子弹
        text = re.sub(r"(?i)real quick", "r*** q****", text)  # 快餐
        text = re.sub(r"(?i)password", "p***w***", text)  # 密码
        text = re.sub(r"(?i)pistol", "p*****", text)  # 手枪
        text = re.sub(r"(?i)rifle", "r****", text)  # 来复枪
        text = re.sub(r"(?i)revolver", "re******", text)  # 左轮手枪

        return text


if __name__ == '__main__':
    content_list = [
        "Please come lay with me just be with me and love me I'll do anything",
        "i want TO SLEEP WITH YOU",
        "U ugly gorilla titty bitch",
        "BITCH!!Asshole! HOE!",
        "Sending sex pics to you tonight byee",
        "I know u was with a man that's why your dick was so stank u gay. Your a faggot",
        "I'm here to pay all bills u have",
        "continue",
        "phone number +************",
        "can you try to click the bank details pls",
        "another page before that page",
        "<EMAIL>",
        "pls give me 200!!",
        "So why do you block me on WhatsApp??!!"
        "You used to tell Colton I was nuts as. That I was just \"easy pussy\". Fucking disgusting. Do you know how ",
        "stupid you made me look saying all that to him while talking to his sister trying to fuck and hangout with ",
        "her?",
        "This is <NAME_EMAIL>",
        "<EMAIL>",
        "The total is 144$",
        "Send me your zangi number to add you",
        "Why you block me",
        "Better pay that seller bruh or we fucking up your life evetthing family job whatever we'll find it deadass",
        "Sister they said this is on the house arrest so the address cannot be given out",
        "<EMAIL>",
        "My <NAME_EMAIL> and another <NAME_EMAIL>. final <EMAIL>",
        "My mom and sister are family. My father and daughter are here. My son is playing.",
        "My mom and sisters are family. My father and daughters are here. My sons are playing.",
        "mommy, sonning coming in!",
        "Can I eat your ass?",
        "Hello",
        "Im free",
        "I'm free",
        "I am free",
        "You should feel like a LAME!!! anything I ever did to you you forced me to and stop calling people about me cause all they doing is playing dumb and playing in your mf face...",
        "Because you laid up with a 15 year old",
        "Black dudes ain't it back to white",
        "Juicy big booty black girl in a skirt twerking with no panties letting my ass & pussy spread all the way open.",
    ]
    for c in content_list:
        print("###########")
        print(c)
        print(ReplaceCanPassWordsUtil.replace_can_pass_words(c))
