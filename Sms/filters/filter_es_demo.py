from datetime import datetime, timedelta

from django.core.cache import cache
from elasticsearch import Elasticsearch

from SecPhone.settings import logger
from Sms.info.wangyi_info_bean import WangyiFilterRet
from Sms.tools.tool_invalid_sms import SmsInvalidTool

# es 开关
OPEN_ES = True

is_close_es_redis_key = "is_close_es"

# 成人特征-打招呼问地点什么的
ADULT_TRAFFIC_WORDS_FEATURE_1 = ['appointment', 'locate', 'for gas']  # gas类似骗路费
# 成人特征-腻歪
ADULT_TRAFFIC_WORDS_FEATURE_2 = ['sweet', 'sweetie', 'sweetheart', 'baby', 'honey', 'lonely', 'service', 'avail',
                                 'available']
# 成人特征-询价问时间
ADULT_TRAFFIC_WORDS_FEATURE_3 = ['how long', 'hour', 'hrs', 'hhr', 'tonight']
# 成人特征-询价问价格
ADULT_TRAFFIC_WORDS_FEATURE_4 = ['100$', '150$', '200$', '250$', '300$', '350$', '400$', '450$', '500$', '550$', '600$',
                                 '800$', '900$', '1000$', '3000$', '5000$',
                                 '$100', '$150', '$200', '$250', '$300', '$350', '$400', '$450', '$500', '$550', '$600',
                                 '$800', '$900', '$1000', '$3000', '$5000'
                                 ]
# 成人特征-支付
ADULT_TRAFFIC_WORDS_FEATURE_5 = ['apple', 'paypal', 'zelle', 'cash', 'cashapp', 'venmo', 'payment', 'iTunes', 'afford',
                                 'screenshot', 'zip code']

# 成人特征-敏感
ADULT_TRAFFIC_WORDS_FEATURE_6 = ['incall', 'hooker', 'outcall', 'hookup', 'massage', 'nuru', 'horny']

# 自己检查
SELF_ES_CHECK_OK = 0  # 正常
SELF_ES_CHECK_ADULT = -1  # 涉黄
SELF_ES_CHECK_FISH = -2  # 骚扰、钓鱼


class SmsEsFilter:

    def __init__(self):
        self.es_url = ['http://*********:9200']
        self.es = Elasticsearch(self.es_url, request_timeout=0.3)

        # 重定义打分函数
        self.fsq_functions = [
            {"filter": {
                "bool": {
                    "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v in
                               ADULT_TRAFFIC_WORDS_FEATURE_1], "minimum_should_match": 1}},
                "weight": 1},
            {"filter": {
                "bool": {
                    "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v in
                               ADULT_TRAFFIC_WORDS_FEATURE_2], "minimum_should_match": 1}},
                "weight": 2},
            {"filter": {
                "bool": {
                    "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v in
                               ADULT_TRAFFIC_WORDS_FEATURE_3], "minimum_should_match": 1}},
                "weight": 4},
            {"filter": {
                "bool": {
                    "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v in
                               ADULT_TRAFFIC_WORDS_FEATURE_4], "minimum_should_match": 1}},
                "weight": 8},
            {"filter": {
                "bool": {
                    "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v in
                               ADULT_TRAFFIC_WORDS_FEATURE_5], "minimum_should_match": 1}},
                "weight": 16},
            {"filter": {
                "bool": {
                    "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v in
                               ADULT_TRAFFIC_WORDS_FEATURE_6], "minimum_should_match": 1}},
                "weight": 32},
        ]

        # must 语句
        self.must_should_list = [
            {"bool": {"minimum_should_match": 1,
                      "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v
                                 in
                                 ADULT_TRAFFIC_WORDS_FEATURE_1]}},
            {"bool": {"minimum_should_match": 1,
                      "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v
                                 in
                                 ADULT_TRAFFIC_WORDS_FEATURE_2]}},
            {"bool": {"minimum_should_match": 1,
                      "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v
                                 in
                                 ADULT_TRAFFIC_WORDS_FEATURE_3]}},
            {"bool": {"minimum_should_match": 1,
                      "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v
                                 in
                                 ADULT_TRAFFIC_WORDS_FEATURE_4]}},
            {"bool": {"minimum_should_match": 1,
                      "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v
                                 in
                                 ADULT_TRAFFIC_WORDS_FEATURE_5]}},
            {"bool": {"minimum_should_match": 1,
                      "should": [{"match": {"content": v}} if ' ' not in v else {"match_phrase": {"content": v}} for v
                                 in
                                 ADULT_TRAFFIC_WORDS_FEATURE_6]}},
        ]

    def insert(self, index: str, doc: dict):
        try:
            self.es.index(index=index, body=doc, refresh="true")
            logger.info("insert doc to es ok")
        except Exception:
            logger.warning("[SendSms] insert doc to es error:", exc_info=True)

    def violation_traffic_search_for_single_line(self, index: str, userid: int, latest_ts: int) -> int:
        if cache.get(is_close_es_redis_key) is not None:
            logger.info(f"[SendSms] user_id: {userid}, close_es=true")
            return False

        res1 = self._adult_traffic_search_for_single_line(index, userid, latest_ts)
        if not res1:
            return SELF_ES_CHECK_ADULT
        res2 = self._fishing_traffic_search_for_single_line(index, userid, latest_ts)
        if not res2:
            return SELF_ES_CHECK_FISH
        return SELF_ES_CHECK_OK

    def _adult_traffic_search_for_single_line(self, index: str, userid: int, latest_ts: int) -> bool:
        try:
            available_body = self._get_body_by_available()
            incall_outcall_body = self._get_body_by_in_out_call()
            need_service_body = self._get_body_by_need_service()
            price_body = self._get_body_by_price()
            hour_body = self._get_body_by_hours()
            body = {
                "query": {
                    "bool": {
                        "filter": [
                            {
                                "term": {"user_id": userid}
                            },
                            {
                                "range": {"latest_ts": {"gte": latest_ts}}
                            },
                            {
                                "range": {"length": {"lte": 12}}
                            }
                        ],
                        "minimum_should_match": 1,
                        "should": [
                            available_body,
                            incall_outcall_body,
                            need_service_body,
                            price_body,
                            hour_body,
                        ]
                    }
                },

                # 下面是对返回的结果继续排序
                "from": 0,  # 从匹配到的结果中的第几条数据开始返回，值是匹配到的数据的下标，从 0 开始
                "size": 200,  # 返回多少条数据,
                "_source": {
                    "includes": ["content"]
                },
            }
            response = self.es.search(
                index=index,
                body=body,
                # filter_path=["took", "hits.hits._source", "hits.hits._score"]
            )
            if 'hits' not in response:
                return True
            if 'hits' not in response['hits']:
                return True

            if len(response['hits']['hits']) > 0:
                logger.warning(f"[SendSms] {userid} search {index} doc hit adult traffic:"
                               f" {response['hits']['hits']}", exc_info=True)
                return False
            return True
        except Exception as e:
            if "ConnectionTimeout" in str(e):
                logger.warning(f"[SendSms] {userid} search {index} temp close switch")
                cache.set(is_close_es_redis_key, 1, 300)
                return True

            logger.warning(f"[SendSms] {userid} search {index} doc from es error:",
                           exc_info=True)
            return True

    def _fishing_traffic_search_for_single_line(self, index: str, userid: int, latest_ts: int) -> bool:
        try:
            congratulation_body = self._get_fishing_congratulation()
            zelle_payment_body = self._get_fishing_zelle_payment()
            lottery_body = self._get_fishing_lottery()
            nude_pic_body = self._get_fishing_nude_pictures()
            this_is_from_body = self._get_fishig_this_is_from()
            pay_girls_body = self._get_fishig_pay_girls()
            fbi_body = self._get_fishig_fbi()
            package_body = self._get_fishig_package()
            arrest_body = self._get_fishig_arrest()
            delivery_body = self._get_fishig_delivery()
            meeting_call_body = self._get_fishig_meeting_call()
            presentation_body = self._get_fishig_presentation()
            need_cards_body = self._get_fishig_need_cards()

            body = {
                "query": {
                    "bool": {
                        "filter": [
                            {
                                "term": {"user_id": userid}
                            },
                            {
                                "range": {"latest_ts": {"gte": latest_ts}}
                            },
                            {
                                "range": {"length": {"gte": 12}}
                            }
                        ],
                        "minimum_should_match": 1,
                        "should": [
                            congratulation_body,
                            zelle_payment_body,
                            lottery_body,
                            nude_pic_body,
                            this_is_from_body,
                            pay_girls_body,
                            fbi_body,
                            package_body,
                            arrest_body,
                            delivery_body,
                            meeting_call_body,
                            presentation_body,
                            need_cards_body,
                        ]
                    }
                },

                # 下面是对返回的结果继续排序
                "from": 0,  # 从匹配到的结果中的第几条数据开始返回，值是匹配到的数据的下标，从 0 开始
                "size": 200,  # 返回多少条数据,
                "_source": {
                    "includes": ["content"]
                },
            }
            response = self.es.search(
                index=index,
                body=body,
                # filter_path=["took", "hits.hits._source", "hits.hits._score"]
            )
            if 'hits' not in response:
                return True
            if 'hits' not in response['hits']:
                return True

            if len(response['hits']['hits']) > 0:
                logger.warning(f"[SendSms] {userid} search {index} doc hit fishing traffic:"
                               f" {response['hits']['hits']}", exc_info=True)
                return False
            return True
        except Exception as e:
            if "ConnectionTimeout" in str(e):
                logger.warning(f"[SendSms] {userid} search {index} temp close switch")
                cache.set(is_close_es_redis_key, 1, 300)
                return True

            logger.warning(f"[SendSms] {userid} search {index} doc from es error:",
                           exc_info=True)
            return True

    def _get_body_by_available(self):
        body = {
            "bool":
                {
                    "must_not":
                        [
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "operator": "or",
                                                "query": "it’s its it is interview"
                                            }
                                    }
                            }
                        ],
                    "minimum_should_match": 1,
                    "should":
                        [
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "available interested",
                                                "operator": "and"
                                            }
                                    }
                            },
                            {
                                "bool":
                                    {
                                        "must":
                                            [
                                                {
                                                    "bool": {
                                                        "should":
                                                            [
                                                                {
                                                                    "match":
                                                                        {
                                                                            "content":
                                                                                {
                                                                                    "query": "i‘am am iam im I’m I'm you u",
                                                                                    "operator": "or"
                                                                                }
                                                                        }
                                                                },
                                                                {
                                                                    "match_phrase":
                                                                        {
                                                                            "content": "i am"
                                                                        }
                                                                },
                                                                {
                                                                    "match_phrase":
                                                                        {
                                                                            "content": "i m"
                                                                        }
                                                                }
                                                            ]
                                                    }
                                                },
                                                {
                                                    "match":
                                                        {
                                                            "content":
                                                                {
                                                                    "query": "available avail availa",
                                                                    "operator": "or"
                                                                }
                                                        }
                                                }
                                            ]

                                    }
                            }
                        ]
                }

        }

        return body

    def _get_body_by_in_out_call(self):
        body = {
            "bool": {
                "minimum_should_match": 1,
                "should": [
                    {
                        "match": {
                            "content": {
                                "query": "incall outcall",
                                "operator": "and"
                            }
                        }
                    },
                    {
                        "match_phrase": {
                            "content": "in call"
                        }
                    },
                    {
                        "match_phrase": {
                            "content": "out call"
                        }
                    },
                    {
                        "match_phrase": {
                            "content": "in or out"
                        }
                    },
                ]
            }

        }
        return body

    def _get_body_by_need_service(self):
        body = {
            "bool":
                {
                    "minimum_should_match": 1,
                    "should":
                        [
                            {
                                "bool":
                                    {
                                        "must":
                                            [
                                                {
                                                    "match":
                                                        {
                                                            "content":
                                                                {
                                                                    "query": "hour hours",
                                                                    "operator": "or"
                                                                }
                                                        }
                                                },
                                                {
                                                    "match":
                                                        {
                                                            "content":
                                                                {
                                                                    "query": "need service",
                                                                    "operator": "and"
                                                                }
                                                        }
                                                }
                                            ]
                                    }
                            },
                            {
                                "bool":
                                    {
                                        "must":
                                            [
                                                {
                                                    "match":
                                                        {
                                                            "content": "available"
                                                        }
                                                },
                                                {
                                                    "match":
                                                        {
                                                            "content":
                                                                {
                                                                    "query": "need service",
                                                                    "operator": "and"
                                                                }
                                                        }
                                                }
                                            ]
                                    }
                            },
                            {
                                "match_phrase":
                                    {
                                        "content":
                                            {
                                                "query": "need service",
                                                "slop": 5
                                            }
                                    }
                            }
                        ]
                }
        }
        return body

    def _get_body_by_price(self):
        body = {
            "bool":
                {
                    "minimum_should_match": 2,
                    "should":
                        [
                            {
                                "match_phrase":
                                    {
                                        "content": "per hour"
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "$80 $100 $150 $170 $200 $250 $300 $400 $450 $500 $550 $600 $650 $700 $750 $800 $850 $900 $950 $1000 overnight",
                                            "operator": "or"
                                        }
                                    }
                            }
                        ]
                }

        }

        return body

    def _get_body_by_hours(self):
        body = {
            "bool":
                {
                    "must":
                        [
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "hhr",
                                                "operator": "or"
                                            }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "Hr hour hours",
                                                "operator": "or"
                                            }
                                    }
                            }
                        ]
                }
        }

        return body

    def _get_fishing_congratulation(self):
        body = {
            "bool":
                {
                    "must":
                        [
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "win winning winnings winner won zelle payment money qualified",
                                                "operator": "or"
                                            }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "congratulation congratulations",
                                                "operator": "or"
                                            }
                                    }
                            }
                        ]
                }
        }

        return body

    def _get_fishing_zelle_payment(self):
        body = {
            "bool":
                {
                    "must":
                        [
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "Zelle",
                                                "operator": "or"
                                            }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "support business reply support.com",
                                                "operator": "or"
                                            }
                                    }
                            }
                        ]
                }
        }

        return body

    def _get_fishing_lottery(self):
        body = {
            "bool":
                {
                    "must":
                        [
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "lottery"
                                            }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "win winning winnings won winner",
                                                "operator": "or"
                                            }
                                    }
                            }
                        ]
                }
        }

        return body

    def _get_fishing_nude_pictures(self):
        body = {
            "bool":
                {
                    "must":
                        [
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "naked nude",
                                                "operator": "or"
                                            }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "picture pictures pic pics video videos",
                                                "operator": "or"
                                            }
                                    }
                            }
                        ]
                }
        }
        return body

    def _get_fishig_this_is_from(self):
        body = {
            "bool":
                {
                    "must_not": [
                        {
                            "match_phrase":
                                {
                                    "content":
                                        {
                                            "query": "from you"
                                        }
                                }
                        },
                        {
                            "match_phrase":
                                {
                                    "content":
                                        {
                                            "query": "from my"
                                        }
                                }
                        }
                    ],
                    "must":
                        [
                            {
                                "match_phrase":
                                    {
                                        "content":
                                            {
                                                "query": "this is"
                                            }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content":
                                            {
                                                "query": "from"
                                            }
                                    }
                            }
                        ]
                }
        }
        return body

    def _get_fishig_pay_girls(self):
        body = {
            "bool":
                {
                    "filter":
                        [
                            {
                                "range":
                                    {
                                        "length":
                                            {
                                                "gte": 30
                                            }
                                    }
                            }
                        ],
                    "must":
                        [
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "pay"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "girl girls",
                                            "operator": "or"
                                        }
                                    }
                            }
                        ]
                }
        }
        return body

    def _get_fishig_fbi(self):
        body = {
            "bool":
                {
                    "filter":
                        [
                            {
                                "range":
                                    {
                                        "length":
                                            {
                                                "gte": 30
                                            }
                                    }
                            }
                        ],
                    "must":
                        [
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "fbi"
                                        }
                                    }
                            }
                        ]
                }
        }
        return body

    def _get_fishig_package(self):
        body = {
            "bool":
                {
                    "must":
                        [
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "ship package from",
                                            "operator": "and"
                                        }
                                    }
                            }
                        ]
                }
        }
        return body

    def _get_fishig_arrest(self):
        body = {
            "bool":
                {
                    "filter":
                        [
                            {
                                "range":
                                    {
                                        "length":
                                            {
                                                "gte": 30
                                            }
                                    }
                            }
                        ],
                    "minimum_should_match": 2,
                    "should":
                        [
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "legal notice",
                                            "operator": "and"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "arrest against",
                                            "operator": "or"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "tax IRS payment",
                                            "operator": "or"
                                        }
                                    }
                            }
                        ]
                }
        }
        return body

    def _get_fishig_delivery(self):
        body = {
            "bool":
                {
                    "filter":
                        [
                            {
                                "range":
                                    {
                                        "length":
                                            {
                                                "gte": 15
                                            }
                                    }
                            }
                        ],
                    "minimum_should_match": 2,
                    "should":
                        [
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "delivery delivered",
                                            "operator": "or"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "failed fail failure",
                                            "operator": "or"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "package"
                                        }
                                    }
                            }
                        ]
                }
        }
        return body

    def _get_fishig_meeting_call(self):
        body = {
            "bool":
                {
                    "filter":
                        [
                            {
                                "range":
                                    {
                                        "length":
                                            {
                                                "gte": 12
                                            }
                                    }
                            }
                        ],
                    "minimum_should_match": 3,
                    "should":
                        [
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "meeting"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "call"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "task"
                                        }
                                    }
                            }
                        ]
                }
        }
        return body

    def _get_fishig_presentation(self):
        body = {
            "bool":
                {
                    "filter":
                        [
                            {
                                "range":
                                    {
                                        "length":
                                            {
                                                "gte": 12
                                            }
                                    }
                            }
                        ],
                    "minimum_should_match": 3,
                    "should":
                        [
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "presentation"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "card cards Apple certificate",
                                            "operator": "or"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "store stores purchase buy target walmart",
                                            "operator": "or"
                                        }
                                    }
                            }
                        ]
                }
        }
        return body

    def _get_fishig_need_cards(self):
        body = {
            "bool":
                {
                    "filter":
                        [
                            {
                                "range":
                                    {
                                        "length":
                                            {
                                                "gte": 12
                                            }
                                    }
                            }
                        ],
                    "minimum_should_match": 3,
                    "should":
                        [
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "need"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "card cards",
                                            "operator": "or"
                                        }
                                    }
                            },
                            {
                                "match":
                                    {
                                        "content": {
                                            "query": "store stores purchase buy",
                                            "operator": "or"
                                        }
                                    }
                            }
                        ]
                }
        }
        return body

    @staticmethod
    def is_es_ok(user_id: int, uuid: str, content: str, latest_ts: int, from_number: str, to_number: str) -> bool:
        if not OPEN_ES:
            logger.warning(f"[SendSms] user:{user_id}, uuid:{uuid}, content: {content} es is closed")
            return True

        try:
            index_today = datetime.today().strftime('%Y-%m-%d')
            check_res = sms_filter_es.violation_traffic_search_for_single_line(index_today, user_id, latest_ts)
            if check_res != SELF_ES_CHECK_OK:
                label = -1
                if check_res == SELF_ES_CHECK_ADULT:
                    label = 100  # 100 是网易的色情
                elif check_res == SELF_ES_CHECK_FISH:
                    label = 200  # 200 是网易的广告

                # 坏短信存档
                wangyi = WangyiFilterRet(label=label, sub_label=8888)
                SmsInvalidTool.save_invalid_sms(user_id, content, from_number, to_number, wangyi)
                return False
            return True
        except Exception:
            logger.warning(f"[SendSms] search index error, user:{user_id}, content: {content}", exc_info=True)
        return True


def collect_feature_type(num: int) -> list:
    cnt = []
    index = 1
    while num > 0:
        if num % 2 == 1:
            cnt.append(index)
        num = num // 2
        index += 1
    return cnt


sms_filter_es = SmsEsFilter()

if __name__ == '__main__':
    # sms_es_client.insert("test", {"a": 1, "b": 2})
    import sys

    if len(sys.argv) > 1:
        userid = int(sys.argv[1])
    else:
        raise Exception

    if len(sys.argv) > 2:
        index_name = sys.argv[2]
    else:

        index_day2 = (datetime.today() - timedelta(days=2)).strftime('%Y-%m-%d')
        index_day1 = (datetime.today() - timedelta(days=1)).strftime('%Y-%m-%d')
        index_today = datetime.today().strftime('%Y-%m-%d')
        index_name = f"{index_day2},{index_day1},{index_today}"

    # sms_es_client.insert("2022-10-24", {
    #     "user_id": 204563,
    #     "direction": 'SEND',
    #     "from_number": '123',
    #     "to_number": '456',
    #     "content": 'do you still need my service?? sir.',
    #     "created_at": datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
    # })

    print(sms_filter_es.violation_traffic_search_for_single_line(index_name, userid, latest_ts=0))
