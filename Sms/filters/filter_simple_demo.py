# encoding=utf-8
import re

# 小词汇，放到网易会误伤，法律 + 金融 + 政府 + +彩票 + 赌博，这些词不能配置到网易，词汇越短，误伤越广
LITTLE_BAD_WORDS = ["ceo", "fbi", "tort", "jury", "bail", "oath", "lien", "Risk", "qv",
                    "Bond", "Fund", "IOP", "Debt", "Loan", "Owed", "Bond", "69", "recon",
                    "anal", "oral", "anus", "bbj", "ads", "lease", "incall", "outcall"]

# 小词汇，放到网易会误伤，主要是美国机构政府
LITTLE_BAD_UPPER_WORDS = ["FBI", "CIA", "NASA", "DoD", "DOJ", "IRS", "DHS", "FAA", "EPA", "the Fed", "NIH", "SEC",
                          "DOE", "DOE", "HHS", "USPS", "FCC", "NSA", "FTC", "USDA", "DOT", "FEMA", "NPS",
                          "Federal Reserve", "CFPB", "OSHA", "DEA", "ATF", "BOP", "USMS", "ICE", "CBP", "SSA",
                          "NOAA", "FDA", "CDC", "FDIC", "OCC", "ASAC",
                          "FHA", "GAO", "USSS", "NSC", "FCC", "FTC", "FERC", "NLRB", "FHWA", "SBA", "NTSB", "BLM",
                          "SIPC", "NCU",
                          "NARA", "FMC", "OPM", "FAA", "NEA", "NEH", "USGS", "BEA", "NSF", "GPO", "NRO", "NCUA"]

# 不算太坏的单词
NOT_SO_BAD_WORDS = ['naked', 'suck', 'nigga', 'tits', 'available', 'kill', 'bill', 'cops', 'condom', 'cum', 'invoice',
                    'shìt', 'faggot', 'nuru', 'drug', 'anus', 'Bareback', 'hooker', "doggystyle",
                    'Doggy', 'hoe', 'transfer',
                    'whore', 'sex', 'interest', 'ass', 'flabby', 'anal', 'hookup', 'pussy', 'asshole', 'gun', 'bitch',
                    'dick', 'loan', 'nugga', 'account', 'incall', 'fuck', 'fuxk', 'fuk', 'drugs', 'cock', 'ugly',
                    'qv', "piss", 'damage', 'destroy',
                    'Blowjob', 'nigger', 'nude', 'lick', 'slut', 'deepthroat', 'outcall', 'giftcard', 'oral', "swallow",
                    "fullbody", "puss", "penis"]

NOT_SO_BAD_PHRASE = {
    'fuck you': "f**k y*u",
    'gift card': "gift c**d",
    "in call": "in**ll",
    "out call": "out**ll",
    "jerk off": "jer* *ff",
    "my service": "my se***ce",
    "full body": "full b**y",
    "blow job": "bl** job",
    "doggy style": "dog** s**le",
}


class SmsFilter:
    gsm = "@£$¥èéùìòÇ\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞ\x1bÆæßÉ !\"#¤%&'()*+,-./**********:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZ" \
          "ÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà"

    # http 匹配模式
    link_pattern = re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+')

    @staticmethod
    def replace_non_gsm7(text: str) -> str:
        try:
            fixed_text = text.strip().replace('’', '\'').replace(" – ", "-").replace(" ", " ") \
                .replace("©", "@").replace("®️", "@").replace("@️", "@").replace("…", "...").replace("“", "\"") \
                .replace("”", "\"").replace("®", "@").replace("‘", "'").replace("’", "'")
            return fixed_text
        except Exception:
            print(f"[SendSms] replace_non_gsm7 failed:{text}", exc_info=True)
            return text

    @staticmethod
    def _replace_case(old: str, new: str, text: str) -> str:
        index = text.lower().find(old.lower())
        if index == -1:
            return text
        return SmsFilter._replace_case(old, new, text[:index] + new + text[index + len(old):])

    @staticmethod
    def check_link(user_id: int, content: str) -> bool:
        lower_content = content.lower()

        # 判断 t.ly/c12d 这种短链接
        words = content.split()
        for word in words:
            if '.' in word and '/' in word:
                print(f"[SendSms] contains link: {user_id}, {content}")
                return False

        url = re.findall(SmsFilter.link_pattern, lower_content)
        if len(url) > 0:
            print(f"[SendSms] user:{user_id}, {content} with link ### {url}")
            return False

        # 如果单词里面存在 2 个以及以上的.也判定为域名
        s = lower_content.split()
        for i in s:
            # xxx@gmail 邮箱还是要放行的
            if i.count('.') >= 2 and len(i) >= 8:
                t = i.split('.')
                # 放行这种情况："haha..."
                if '' in t:
                    continue
                # 放行邮箱，谷歌，fb，youtube
                if '@' in i or 'youtube' in i or 'google' in i or 'facebook' in i or 'apple' in i:
                    continue

                print(f"[SendSms] user:{user_id} contains link: {content}")
                return False

        return True

    @staticmethod
    def clean_up_text(origin_content: str) -> str:
        try:
            _content = SmsFilter.add_space_after_punctuation(origin_content)
            _content = _content.strip()
            for k, v in NOT_SO_BAD_PHRASE.items():
                _content = re.sub(k, v, _content, flags=re.IGNORECASE)  # 使用正则表达式进行替换，忽略大小写

            # 去掉美元符号，太危险了
            _content = _content.replace("$", "*")

            _splits = _content.replace('\n', ' ').replace('\t', ' ').split()
            new_words = []
            for s in _splits:
                for j in NOT_SO_BAD_WORDS:
                    if j.lower() in s.lower():
                        new_words.append(s.lower().replace(j.lower(), len(j) * '*'))
                        break
                else:
                    new_words.append(s)
            return " ".join(new_words)
        except Exception:
            print(f"[SendSms] clean_up_text failed: {origin_content}", exc_info=True)
            return origin_content

    @staticmethod
    def add_space_after_punctuation(text):
        # 使用正则表达式在标点符号后面添加一个空格，但不会分开多个标点符号
        modified_text = re.sub(r'([.,;!?]+)(?![.,;!?])', r'\1 ', text)
        return modified_text

    @staticmethod
    def check_little_bad_words(user_id: int, origin_content: str, filtered_content: str) -> (bool, str):
        try:
            _content = SmsFilter.add_space_after_punctuation(origin_content)
            _content = _content.strip()

            # 检查一些不好的单词，不方便用第三方处理的单词，误杀太猛了，比如 ceo，很多词组 source of xx = sourceofxx => ceo
            _content = origin_content.replace('\n', ' ').replace('\t', ' ')
            _splits = _content.split()
            _splits = [v.strip() for v in _splits if v]

            # 太短的文本没意义
            if len(_splits) < 8:
                return True, filtered_content

            bad_words_collection = []
            is_ok = True
            for i in _splits:
                # replace and return failed
                for j in LITTLE_BAD_WORDS:
                    if re.sub(r'[^a-zA-Z]', '', i).lower() == j.lower():
                        filtered_content = filtered_content.replace(i, "*" * len(i))
                        is_ok = False
                        bad_words_collection.append(j)

                # replace and return failed
                for j in LITTLE_BAD_UPPER_WORDS:
                    if re.sub(r'[^a-zA-Z]', '', i) == j.upper():
                        filtered_content = filtered_content.replace(i, "*" * len(i))
                        is_ok = False
                        bad_words_collection.append(j)

            if is_ok:
                return True, filtered_content
            else:
                print(
                    f"[SendSms] {user_id}, check bad little words, content:{origin_content}:{bad_words_collection}")
                return False, filtered_content
        except Exception:
            print(f"[SendSms] {user_id}, check bad little words, content:{origin_content}, failed",
                  exc_info=True)
            return True, filtered_content

    @staticmethod
    def contains_non_gsm7(text):
        # https://twiliodeved.github.io/message-segment-calculator/ 检查短信长度
        # GSM-7 编码支持的字符集合
        gsm7_characters = set(
            '@£$¥èéùìòÇØøÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !"#¤%&\'()*+,-./**********:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà\n')

        # 检查文本中是否包含非 GSM-7 字符
        for char in text:
            if char not in gsm7_characters:
                return True

        return False

    @staticmethod
    def handle_sex(origin_content):
        filtered_content = origin_content
        if "hhr" in origin_content or "hr" in origin_content or re.search(r'\d', origin_content):
            print('contains sex')
            filtered_content = filtered_content.replace("hhr", "***")
            filtered_content = filtered_content.replace("hr", "**")
            filtered_content = re.sub(r'\d+', '***', filtered_content)
        print("origin_content:", origin_content)
        print("filtered_content:", filtered_content)


sms_filter_simple = SmsFilter()

if __name__ == '__main__':
    content_list = [
        # "Y se va",
        # "Boy fuck you fr",
        # "FUCK,LOVE,SEX.",
        # "wow, Glad!! Nice to meet you.",
        # "Do you offer full body rubs? Blow job? FULL BODY? FULLBODY?",
        # "need my service? in call or out call? Fuck,Anal,Blowjob,Oral,Bareback Doggystyle",
        # "Hello dear I'm avai***ble do you need my service what city are you in town so i can know the distance between us",
        # "I love s***ing and giving full blow jobs as well as swallowing cum. I really enjoy a*** s**, and love having my tight little a******e filled with a stiff and throbbing cock.",
        #
        # "Very good , I enjoy rimming, a***, orals, squirting, d**** and swallowing cum...",
        # "it's your fault you still on that bullshit an i told you im not with it , not finna keep getting cheated on an you don't gaf about me &. you say i ain't never gaf , rajah on bro yk i always cared so don't lie cause that's gon piss me off",
        # "Go to any store around you and get a apple gift card",
        # "200$ 1 hour fuck you ! make me cum? let's fuck harder? condom? you r a hoe fucking Are u available? still interested?  incall or outcall ? But make sex yeah just run away instead of sex fixing **** she told me she wanted a weekend to herself I clearly have been fighting with everyone to see him so it's not like I'm doing anything wrong I literally can't get out of work today was legit my second day at work you're being so fucking cruel and unfair if you ever actually loved me you would let me see my son",
        # "are you Available?",
        # "Fatpo test:  HELLO THIS IS MICHEAL JAMES FROM AMAZON COMPANY WE ARE TEXTING YOU TO INFORM YIU YOUR AMAZON WILL BE CLOSED SOON.AND IF YOU DONT WANT YOU YO BE CLOSED REPLY NO TO GET MORE ON HOW TO SECURE YOUR IN OTHER TO PREVENT FROM GETTING IT CLOSED",
        # "My name is Dakota Johnson I’m a female hooker Fuck,Anal,Creampie,Blowjob,Oral,Bareback,Doggystyle,Condom or no I charge $150 for 1hr $200 for 2hrs $300 for 4 hrs and $450 for overnight How much would you like to go for ?",
        # "Order id 214368 is still available?",
        # """
        #    Baby please forgive me I NEED YOU…. all you have to do is call them & you’ll see I wasn’t there or didn’t pay for anything. We didn’t fuck bae I don’t know what else to do here I just know we didn’t. If I did I promise I wouldn’t be doing all this I would take whatever you give me but I can’t because I didn’t fuck anyone Sunday man. all you have to do is call them & you’ll see… I asked him “what you usually get” that’s not a question you gotta ask if you’re bout to fuck, I mean come on baby you was just mad & overwhelmed but just call them…. If I got a room they will tell you KAMEISHA WILSON was there, call them. I’m telling you I didn’t have *** with him.
        #    I don’t wanna do this without you ion think I can to be honest….
        #    """,
        # """
        #     ??Located UNION SQUARE / CENTER CITY
        #
        # ???RATES???
        #
        # INCALL ??
        #
        # ?1 Hour��/��600$ GFE
        # ?30 Min /��400$
        #
        # Anal extra $ 100 ??
        #
        # Payment Cash or Zelle ??
        #
        # OUTCALL ?- (Just in hotels) Screening is required
        #
        # ?HOUR / 800 $ + UBER OR TAXI (JUST HOTELS / NOT APARTMENT OR CONDO)
        #
        # ?PAYMENT CASH OR ZELLE?
        #     """,
        # "Make me ride it until my cum is dripping down to ur balls from you making me squirt",
        "Enjoy that FBI sec over there I’m going to the bronx tomorrow after he gets on the bus",
        "Are you still naked in the shower",
        "That's the least of help you can offer atleast we speak face to face to get rid of doubt With my ipad… please help I can come meet you tomorrow then",
        "120 hhr, 160 hr",
        "160",
        "1310 N 1st St, San Jose, CA 95112",
    ]
    for c in content_list:
        print("#" * 50)
        cc = SmsFilter.clean_up_text(c)
        print(c)
        print(cc)

        print(sms_filter_simple.replace_non_gsm7(c))
        r1 = SmsFilter.check_little_bad_words(0, c, c)
        print(r1)

        SmsFilter.handle_sex(c)
