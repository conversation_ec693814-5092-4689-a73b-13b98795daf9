class SmsBanVipUtil:
    @staticmethod
    def check_ban_vip_words(origin_content: str) -> bool:
        lower_origin_content = origin_content.lower()
        if "pick" in lower_origin_content and "cotton" in lower_origin_content:
            return True
        if ("LESBIAN BGN" in lower_origin_content and
                ("chosen" in lower_origin_content or "pick" in lower_origin_content)):
            return True

        return False

    @staticmethod
    def check_warning_words(origin_content: str) -> (bool, list):
        lower_origin_content = origin_content.lower()
        if "winning" in lower_origin_content:
            return True, ["winning"]
        if "congratulations" in lower_origin_content:
            return True, ["congratulations"]
        if "giveaway reward" in lower_origin_content:
            return True, ["giveaway reward"]
        if "receive" in lower_origin_content and "prize" in lower_origin_content:
            return True, ["receive", "prize"]
        if "financial support" in lower_origin_content:
            return True, ["financial support"]
        if "offering" in lower_origin_content and "financial" in lower_origin_content:
            return True, ["offering financial"]
        if "how would you spend" in lower_origin_content and "help" in lower_origin_content:
            return True, ["how would you spend", "help"]
        if "grant funding" in lower_origin_content:
            return True, ["grant funding"]
        if "receive your grant" in lower_origin_content:
            return True, ["receive your grant"]
        if "fight the gorilla" in lower_origin_content or "gorilla affairs" in lower_origin_content:
            return True, ["fight the gorilla", "gorilla affairs"]
        if "fbi" in lower_origin_content or "f.b.i" in lower_origin_content or "Federal Bureau" in lower_origin_content:
            return True, ["fbi", "f.b.i", "Federal Bureau"]
        if "bureau" in lower_origin_content:
            return True, ["bureau"]
        if "hacked" in lower_origin_content:
            return True, ["hacked"]
        if "calling us" in lower_origin_content or "call us" in lower_origin_content:
            return True, ["calling us", "call us"]
        if "officer" in lower_origin_content:  # 我是警官
            return True, ["officer"]
        if "prosecute" in lower_origin_content:  # 诉讼
            return True, ["prosecute"]
        if "warning" in lower_origin_content:  # 警告
            return True, ["warning"]
        if "or we will" in lower_origin_content:  # 警告
            return True, ["or we will"]
        if "legal action" in lower_origin_content:  # 法律行动
            return True, ["legal action"]
        if "criminal" in lower_origin_content or "charges against" in lower_origin_content:  # 刑事
            return True, ["criminal", "charges against"]
        if "might regret" in lower_origin_content:
            return True, ["might regret"]
        if "post your naked" in lower_origin_content:
            return True, ["post your naked"]
        if "elon musk" in lower_origin_content or "facebook ceo" in lower_origin_content or "zuckerberg" in lower_origin_content:
            return True, ["elon musk", "facebook ceo", "zuckerberg"]
        if "will be arrested" in lower_origin_content:
            return True, ["will be arrested"]
        if "darkweb" in lower_origin_content or "dark web" in lower_origin_content:
            return True, ["darkweb", "dark web"]
        if "emergency contact number" in lower_origin_content:
            return True, ["emergency contact number"]
        if "have been selected" in lower_origin_content:
            return True, ["have been selected"]
        if "if you do not respond" in lower_origin_content or "if not respond" in lower_origin_content:
            return True, ["if you do not respond", "if not respond"]
        if "but my pastor" in lower_origin_content or "be glad to help you out" in lower_origin_content or "helping the needy" in lower_origin_content:  # 但是我的牧师让我做好事
            return True, ["but my pastor", "be glad to help you out", "helping the needy"]
        if "wells fargo" in lower_origin_content or "security department" in lower_origin_content:
            return True, ["wells fargo", "security department"]
        if "customer service" in lower_origin_content:
            return True, ["please contact customer service"]
        if "federal government" in lower_origin_content:
            return True, ["federal government"]
        if "inheritance" in lower_origin_content or "require your signature" in lower_origin_content:
            return True, ["inheritance", "require your signature"]
        if "your nudes" in lower_origin_content or "her nudes" in lower_origin_content or "his nudes" in lower_origin_content or "ur nudes" in lower_origin_content:
            return True, ["your nudes"]
        if "nudes" in lower_origin_content:
            return True, ["nudes"]
        if "my superiors" in lower_origin_content:
            return True, ["my superiors"]
        if "dear ms" in lower_origin_content or "hello sir" in lower_origin_content or "hello mr" in lower_origin_content:
            return True, ["dear ms", "hello sir", "hello mr"]
        if "army recruitment" in lower_origin_content:
            return True, ["army recruitment"]
        if "we look forward to" in lower_origin_content:
            return True, ["We look forward to"]
        if "united states army" in lower_origin_content:
            return True, ["United States Army"]
        if "we need u to" in lower_origin_content or "we need you to" in lower_origin_content:
            return True, ["we need u to", "we need you to"]
        if "bank lady" in lower_origin_content:
            return True, ["bank lady"]
        if "introduce myself" in lower_origin_content:
            return True, ["introduce myself"]
        if "agent" in lower_origin_content:
            return True, ["agent"]
        if "agencies" in lower_origin_content:
            return True, ["agencies"]
        if "refused to acknowledge" in lower_origin_content:
            return True, ["refused to acknowledge"]
        if "this is officer" in lower_origin_content:
            return True, ["this is officer"]
        if "fraud alert" in lower_origin_content:
            return True, ["FRAUD ALERT"]
        if "reply yes or no" in lower_origin_content:
            return True, ["Reply Yes or No."]
        if "we are pleased to" in lower_origin_content:
            return True, ["We are pleased to"]
        if "please refer this" in lower_origin_content:
            return True, ["Please refer this"]
        if "trying to reach you" in lower_origin_content:
            return True, ["trying to reach you"]
        if "please call me at" in lower_origin_content:
            return True, ["Please call me at"]
        if "alert from" in lower_origin_content:
            return True, ["Alert from"]
        if "from the" in lower_origin_content and "department" in lower_origin_content:
            return True, [" from the unclaimed department"]
        if "commander" in lower_origin_content:
            return True, ["commander"]
        if "was ready for you" in lower_origin_content:
            return True, ["was ready for you"]
        if "to claim your" in lower_origin_content:
            return True, ["to claim your"]
        if "newspaper" in lower_origin_content:
            return True, ["newspaper"]
        if "you better send" in lower_origin_content:
            return True, ["You better send"]
        if "alert" in lower_origin_content:
            return True, ["ALERT"]
        if "from fedex" in lower_origin_content:
            return True, ["from fedex"]
        if "my fans" in lower_origin_content:
            return True, ["my fans"]
        if "sweepstake" in lower_origin_content:
            return True, ["sweepstake"]
        if "withdrawal" in lower_origin_content:
            return True, ["withdrawal"]
        if "they said we need" in lower_origin_content:
            return True, ["They said we need"]
        if "military" in lower_origin_content:
            return True, ["military"]
        if "scammed money" in lower_origin_content:
            return True, ["scammed money"]
        if "pending transaction" in lower_origin_content:
            return True, ["pending transaction"]
        if "management service" in lower_origin_content:
            return True, ["management service"]
        if "mr" in lower_origin_content and "your wife" in lower_origin_content:
            return True, ["Mr. your wife"]
        if "her bags and stuffs" in lower_origin_content:
            return True, ["her bags and stuffs"]
        if "million dollar" in lower_origin_content:
            return True, ["million dollar"]
        if "your package" in lower_origin_content:
            return True, ["your package"]
        if "the package" in lower_origin_content:
            return True, ["the package"]
        if "unclaimed package" in lower_origin_content:
            return True, ["Unclaimed package"]
        if "federal trade" in lower_origin_content:
            return True, ["Federal Trade Commission"]
        if "something beneficial" in lower_origin_content:
            return True, ["something beneficial for you"]
        if "won" in lower_origin_content and "prize" in lower_origin_content:
            return True, ["Won Prize"]
        if "win" in lower_origin_content and "prize" in lower_origin_content:
            return True, ["win prize"]
        if "publisher clearing house" in lower_origin_content:
            return True, ["Publisher Clearing House"]
        if " pch " in lower_origin_content:
            return True, ["PCH"]
        if "winner list" in lower_origin_content:
            return True, ["winner list"]
        if "tiene 24 horas" in lower_origin_content:  # 你只有24小时
            return True, ["Solo tiene 24 horas"]
        if "más información" in lower_origin_content:  # 更多细节
            return True, ["más información"]
        if "antipedófilo" in lower_origin_content:  # 反恋童癖
            return True, ["Antipedófilo"]
        if "actos sexuales" in lower_origin_content:  # 性行为小组
            return True, ["Actos Sexuales"]
        if "pornográfico" in lower_origin_content:  # P站
            return True, ["pornográfico"]
        if "señor" in lower_origin_content:
            return True, ["Hola señor"]
        if "looks like someone may have" in lower_origin_content:
            return True, ["looks like someone may have"]
        if "team snapchat" in lower_origin_content:
            return True, ["team Snapchat"]
        if "someone has just signed" in lower_origin_content:
            return True, ["Someone has just signed"]
        if "reply help" in lower_origin_content:
            return True, ["Reply HELP"]
        if "reply yes" in lower_origin_content:
            return True, ["Reply YES"]
        if "reply no" in lower_origin_content:
            return True, ["Reply NO"]
        if "stop to cancel" in lower_origin_content:
            return True, ["STOP to cancel"]
        if "reply stop" in lower_origin_content:
            return True, ["Reply STOP"]
        if "your report" in lower_origin_content:
            return True, ["Your report"]
        if "here from" in lower_origin_content:
            return True, ["Here from"]
        if "at&t" in lower_origin_content:
            return True, ["AT&T"]
        if "have been chosen" in lower_origin_content:
            return True, ["have been chosen"]
        if "good morning america" in lower_origin_content:
            return True, ["Good Morning America"]
        if "share your full plans" in lower_origin_content:
            return True, ["share your full plans"]
        if "for your understanding" in lower_origin_content:
            return True, ["for your understanding"]
        if "sincerely" in lower_origin_content and "sir" in lower_origin_content:
            return True, ["Sincerely sir"]
        if "social security" in lower_origin_content:
            return True, ["Social Security"]
        if "good morning mr" in lower_origin_content:
            return True, ["Good morning Mrs"]
        if "important to tell you" in lower_origin_content or "something important" in lower_origin_content:
            return True, ["important to tell you", "something important"]
        if "if you were to receive" in lower_origin_content:
            return True, ["If you were to receive"]
        if "remote desktop" in lower_origin_content:
            return True, ["Remote Desktop Protocol"]
        if "know you very well" in lower_origin_content:
            return True, ["Know you very well"]
        if "wondering why are you" in lower_origin_content:
            return True, ["Wondering why are you"]
        if "sex site" in lower_origin_content:
            return True, ["sex site"]
        if "your complete contact" in lower_origin_content or "your contacts" in lower_origin_content:
            return True, ["your complete contacts"]
        if "sorry for the late response" in lower_origin_content:
            return True, ["sorry for the late response"]
        if "final warning" in lower_origin_content:
            return True, ["FINAL WARNING"]
        if "gracias por" in lower_origin_content:
            return True, ["Gracias por"]
        if "federal" in lower_origin_content:
            return True, ["Federal"]
        if "compensation" in lower_origin_content:
            return True, ["Compensation"]
        if "you will get" in lower_origin_content and "qualify" in lower_origin_content:
            return True, ["You qualify you will get"]
        if "a recruiter at" in lower_origin_content:
            return True, ["a recruiter at"]
        if "earn extra income" in lower_origin_content:
            return True, ["earn extra income"]
        if "applicants must be" in lower_origin_content:
            return True, ["Applicants must be"]
        if "online bank" in lower_origin_content:
            return True, ["Online Banking"]
        if "your agent" in lower_origin_content:
            return True, ["Your agent"]
        if "meeting with" in lower_origin_content:
            return True, ["meeting with"]
        if "superior" in lower_origin_content:
            return True, ["superior"]
        if "tech support" in lower_origin_content:
            return True, ["Tech Support"]
        if "your new address" in lower_origin_content:
            return True, ["Your new address"]
        if "so i can move on" in lower_origin_content:
            return True, ["So I can move on"]
        if "winnings" in lower_origin_content:
            return True, ["Winnings"]
        if "blackmail" in lower_origin_content:
            return True, ["Blackmail"]
        if "single" in lower_origin_content and "years" in lower_origin_content:
            return True, ["Single", "Years"]
        if "no kid" in lower_origin_content and "years" in lower_origin_content:
            return True, ["no kids", "Years"]
        if "no kids" in lower_origin_content:
            return True, ["no kids"]
        if "looking for" in lower_origin_content and "stable" in lower_origin_content:
            return True, ["looking for a stable relationship"]
        if "stable relationship" in lower_origin_content:
            return True, ["stable relationship"]
        if "am an active person" in lower_origin_content:
            return True, ["I am an active person"]
        if "touch to know more" in lower_origin_content:
            return True, ["Touch to know more"]
        if "get to know each other" in lower_origin_content:
            return True, ["Get to know each other"]
        if "court date" in lower_origin_content or "on court" in lower_origin_content or "go to court with" in lower_origin_content:
            return True, ["court date", "on court", "go to court with"]
        if "looking for friendship" in lower_origin_content:
            return True, ["looking for friendship"]
        if "serious man" in lower_origin_content:
            return True, ["serious man"]
        if "living alone" in lower_origin_content:
            return True, ["living alone"]
        if "looking for here" in lower_origin_content:
            return True, ["looking for here"]
        if "living with me" in lower_origin_content:
            return True, ["living with me"]
        if "report" in lower_origin_content and "money" in lower_origin_content:
            return True, ["report that money"]
        if "reportar" in lower_origin_content:  # 法语，报告
            return True, ["reportar"]
        if "following instructions" in lower_origin_content:
            return True, ["following instructions"]
        if "greetings from" in lower_origin_content:
            return True, ["Greetings from"]
        if "they will update" in lower_origin_content:
            return True, ["They will update it"]
        if "look for friends" in lower_origin_content:
            return True, ["Look for friends"]
        if "show your wife" in lower_origin_content:
            return True, ["show your wife"]
        if "chase bank" in lower_origin_content:
            return True, ["Chase Bank"]
        if "serious relationship" in lower_origin_content:
            return True, ["serious relationship"]
        if "looking for a man" in lower_origin_content:
            return True, ["looking for a man"]
        if "getting in a relationship" in lower_origin_content:
            return True, ["getting in a relationship"]
        if "been single" in lower_origin_content:
            return True, ["been single"]
        if "dating group" in lower_origin_content:
            return True, ["dating group"]
        if "share" in lower_origin_content and "about me" in lower_origin_content:
            return True, ["share a little bit about me"]
        if "thank" in lower_origin_content and "for getting back" in lower_origin_content:
            return True, ["Thanks for getting back"]
        if "saw your number" in lower_origin_content:
            return True, ["saw your number"]
        if "employee from" in lower_origin_content:
            return True, ["employee from"]
        if "it seems your" in lower_origin_content:
            return True, ["It seems your account has been"]
        if "your account has" in lower_origin_content:
            return True, ["It seems your account has been"]
        if "account" in lower_origin_content and "accountability" not in lower_origin_content:
            return True, ["account"]
        if "contact soon as possible" in lower_origin_content:
            return True, ["contact soon as possible"]
        if "approved for" in lower_origin_content:
            return True, ["approved for"]
        if "from" in lower_origin_content and "government" in lower_origin_content:
            return True, ["from the government"]
        if "confirm" in lower_origin_content and "information" in lower_origin_content:
            return True, ["Confirm your information"]
        if "funds" in lower_origin_content:
            return True, ["funds"]
        if "your bank" in lower_origin_content:
            return True, ["Your bank"]
        if "driver license" in lower_origin_content or "drives license" in lower_origin_content:
            return True, ["Driver license"]
        if "opportunity for" in lower_origin_content:
            return True, ["opportunity for"]
        if "the agent" in lower_origin_content:
            return True, ["the agent"]
        if "confirm" in lower_origin_content and "code" in lower_origin_content:
            return True, ["Confirm your code"]
        if "right person" in lower_origin_content:
            return True, ["right person"]
        if "qualified" in lower_origin_content:
            return True, ["been qualified"]
        if "cash mail" in lower_origin_content:
            return True, ["cash mailing"]
        if "send your address" in lower_origin_content:
            return True, ["send your address"]
        if "let you regret" in lower_origin_content:
            return True, ["let you regret"]
        if "trust me on" in lower_origin_content:
            return True, ["trust me on"]
        if "had an agreement" in lower_origin_content:
            return True, ["had an agreement"]
        if "won't lie to" in lower_origin_content:
            return True, ["won't lie to"]
        if "neighbor" in lower_origin_content and ("phone" in lower_origin_content or "number" in lower_origin_content):
            return True, ["neighbor phone"]
        if "partner phone" in lower_origin_content:
            return True, ["partner phone"]
        if "phone from" in lower_origin_content:
            return True, ["phone from"]
        if "new phone" in lower_origin_content:
            return True, ["a new phone"]
        if "card ending" in lower_origin_content:
            return True, ["Visa card ending"]
        if "visa card" in lower_origin_content:
            return True, ["Visa card ending"]
        if "once you send" in lower_origin_content:
            return True, ["once you send"]
        if "when i get it paid" in lower_origin_content:
            return True, ["when I get it paid"]
        if "going to lose this job" in lower_origin_content:
            return True, ["going to lose this job"]
        if "i need to borrow" in lower_origin_content:
            return True, ["I need to borrow"]
        if "helping me this time" in lower_origin_content:
            return True, ["helping me this time"]
        if "help me this time" in lower_origin_content:
            return True, ["helping me this time"]
        if "need your help with" in lower_origin_content:
            return True, ["need your help with"]
        if "hope" in lower_origin_content and "bother" in lower_origin_content:
            return True, ["hope bother"]
        if "it's" in lower_origin_content and "let me know" in lower_origin_content:
            return True, ["It's let me know"]
        if "new number" in lower_origin_content or "new phone" in lower_origin_content:
            return True, ["my new number"]
        if "about me" in lower_origin_content and "single" in lower_origin_content:
            return True, ["About me single"]
        if "i'm single" in lower_origin_content or "am single" in lower_origin_content:
            return True, ["i'm single"]
        if "zuckerberg" in lower_origin_content:
            return True, ["Zuckerberg"]
        if "know everything about you" in lower_origin_content:
            return True, ["we know everything about you"]
        if "know each other" in lower_origin_content:
            return True, ["know each other"]
        if "contract" in lower_origin_content:
            return True, ["contract"]
        if "deposited" in lower_origin_content or "deposit" in lower_origin_content:
            return True, ["deposited"]
        if "cibn bank" in lower_origin_content:
            return True, ["CIBN bank"]
        if "texting you because" in lower_origin_content:
            return True, ["texting you because"]
        if "last relationship" in lower_origin_content:
            return True, ["last relationship"]
        if "what qualities" in lower_origin_content:
            return True, ["What qualities"]
        if "seeking" in lower_origin_content and "man" in lower_origin_content:
            return True, ["seeking man"]
        if "right man" in lower_origin_content:
            return True, ["right man"]
        if "can you date if" in lower_origin_content:
            return True, ["Can you date if"]
        if "can I have a picture" in lower_origin_content:
            return True, ["Can I have a picture of"]
        if "man comes along" in lower_origin_content:
            return True, ["right man comes along"]
        if "marital life" in lower_origin_content:
            return True, ["marital life"]
        if "tried to date" in lower_origin_content:
            return True, ["ever tried to date"]
        if "verification code" in lower_origin_content:
            return True, ["verification code"]
        if "fb" in lower_origin_content and "code" in lower_origin_content:
            return True, ["FB code"]
        if "you don't mind" in lower_origin_content:
            return True, ["if you don't mind"]
        if "can i ask you" in lower_origin_content:
            return True, ["Can I ask you"]
        if "what happened to your" in lower_origin_content:
            return True, ["What happened to your"]
        if "do you remember" in lower_origin_content:
            return True, ["Do you remember"]
        if "with my check" in lower_origin_content:
            return True, ["with my check"]
        if "hold my check" in lower_origin_content:
            return True, ["hold my check"]
        if "ups office" in lower_origin_content:
            return True, ["UPS office"]
        if "the ups" in lower_origin_content:
            return True, ["the UPS"]
        if "UPS" in origin_content:
            return True, ["UPS"]
        if "a friend from" in lower_origin_content:
            return True, ["a friend from"]
        if "you will receive" in lower_origin_content:
            return True, ["you will receive"]
        if "number generator" in lower_origin_content:
            return True, ["number generator"]
        if "digit code" in lower_origin_content or "digits code" in lower_origin_content:
            return True, ["digit code"]
        if "we just texted you" in lower_origin_content:
            return True, ["We just texted you"]
        if "thanks for choosing" in lower_origin_content:
            return True, ["Thanks for choosing"]
        if "want to claim" in lower_origin_content:
            return True, ["you want to claim"]
        if "app management" in lower_origin_content:
            return True, ["Cash App management"]
        if "sugar mommy" in lower_origin_content or "sugar daddy" in lower_origin_content:
            return True, ["sugar mommy daddy"]
        if "you received" in lower_origin_content:
            return True, ["You received"]
        if "cash balance" in lower_origin_content:
            return True, ["Cash balance"]
        if "get the code" in lower_origin_content:
            return True, ["get the code"]
        if "your nude" in lower_origin_content:
            return True, ["your nude"]
        if "cooperating or not" in lower_origin_content:
            return True, ["cooperating or not"]
        if "want me to post" in lower_origin_content:
            return True, ["want me to post"]
        if "nude picture" in lower_origin_content:
            return True, ["nude picture"]
        if "cooperate" in lower_origin_content:
            return True, ["cooperate"]
        if "will ruin your life" in lower_origin_content:
            return True, ["Will ruin your life"]
        if "zelle support" in lower_origin_content:
            return True, ["Zelle Support"]
        if "technical issues" in lower_origin_content:
            return True, ["Technical issues"]
        if "next" in lower_origin_content and "hour" in lower_origin_content:
            return True, ["next hour"]
        if "bank account" in lower_origin_content:
            return True, ["bank account"]
        if "naked" in lower_origin_content:
            return True, ["naked picture"]
        if "circulating the internet" in lower_origin_content:
            return True, ["circulating the internet"]
        if "your account" in lower_origin_content:
            return True, ["your account"]
        if "verified to" in lower_origin_content:
            return True, ["Verified to"]
        if "urgent task" in lower_origin_content:
            return True, ["urgent task"]
        if "a real person" in lower_origin_content:
            return True, ["a real person"]
        if "friends phone" in lower_origin_content or "friend's phone" in lower_origin_content:
            return True, ["friend's phone"]
        if "colleague's phone" in lower_origin_content:
            return True, ["colleague's phone"]
        if "return" in lower_origin_content and "phone" in lower_origin_content:
            return True, ["return phone"]
        if "phone card" in lower_origin_content:
            return True, ["phone card"]
        if "file" in lower_origin_content and "processed" in lower_origin_content:
            return True, ["file processed"]
        if "informed you" in lower_origin_content:
            return True, ["informed you"]
        if "the dhhs" in lower_origin_content:
            return True, ["the DHHS"]
        if "documentation" in lower_origin_content:
            return True, ["documentation"]
        if "divorced" in lower_origin_content:
            return True, ["divorced"]
        if "let me know as soon as" in lower_origin_content:
            return True, ["Let me know as soon as"]
        if "get me internet," in lower_origin_content:
            return True, ["Get me internet"]
        if "get me internet," in lower_origin_content:
            return True, ["Get me internet"]
        if "my subscription" in lower_origin_content:
            return True, ["my subscription"]
        if "buy me internet" in lower_origin_content:
            return True, ["buy me internet"]
        if "to inform you" in lower_origin_content:
            return True, ["to inform you"]
        if "package" in lower_origin_content:
            return True, ["package"]
        if "under your name" in lower_origin_content:
            return True, ["under your name"]
        if "been assigned" in lower_origin_content:
            return True, ["been assigned"]
        if "finds you well" in lower_origin_content:
            return True, ["finds you well"]
        if "reaching out to" in lower_origin_content:
            return True, ["reaching out to"]
        if "want this money" in lower_origin_content:
            return True, ["want this money"]
        if "my name is" in lower_origin_content:
            return True, ["my name is"]
        if "informed you" in lower_origin_content:
            return True, ["informed you"]
        if "security alert" in lower_origin_content:
            return True, ["Security Alert"]
        if "you smart" in lower_origin_content:
            return True, ["you smart"]
        if "think" in lower_origin_content and "smart" in lower_origin_content:
            return True, ["think smart"]
        if "was helping you" in lower_origin_content:
            return True, ["was helping you"]
        if "the technician" in lower_origin_content:
            return True, ["the technician"]
        if "apple support" in lower_origin_content:
            return True, ["Apple Support"]
        if "the government" in lower_origin_content:
            return True, ["the government"]
        if "police department" in lower_origin_content:
            return True, ["police department"]
        if "chime tag" in lower_origin_content:
            return True, ["chime tag"]
        if "once you sent" in lower_origin_content:
            return True, ["Once you sent"]
        if "will be available" in lower_origin_content:
            return True, ["will be available"]
        if "waiting to complete" in lower_origin_content:
            return True, ["waiting to complete"]
        if "will be available" in lower_origin_content:
            return True, ["will be available"]
        if "app balance" in lower_origin_content:
            return True, ["cash app balance"]
        if "not a scam" in lower_origin_content:
            return True, ["not a scam"]
        if "check spam" in lower_origin_content:
            return True, ["Check spam"]
        if "department" in lower_origin_content:
            return True, ["department"]
        if "your details" in lower_origin_content:
            return True, ["your details"]
        if "as soon as possible" in lower_origin_content:
            return True, ["as soon as possible"]
        if "please reply to me" in lower_origin_content:
            return True, ["please reply to me"]
        if "more card" in lower_origin_content:
            return True, ["more card"]
        if "giveaway" in lower_origin_content:
            return True, ["giveaway"]
        if "trust bank" in lower_origin_content:
            return True, ["trust bank"]
        if "to confirm" in lower_origin_content:
            return True, ["to confirm"]
        if "your response" in lower_origin_content:
            return True, ["your response"]
        if "shipping" in lower_origin_content:
            return True, ["shipping"]
        if "daughter phone" in lower_origin_content:
            return True, ["daughter phone"]
        if "will claim" in lower_origin_content:
            return True, ["will claim"]
        if "new card" in lower_origin_content:
            return True, ["new card"]
        if "upfront" in lower_origin_content:  # 预付费
            return True, ["upfront"]
        if "if you're interested " in lower_origin_content:
            return True, ["if you're interested"]
        if "i swear" in lower_origin_content and "gonna" in lower_origin_content:
            return True, ["I swear gonna"]
        if "apple card" in lower_origin_content:
            return True, ["Apple Card"]
        if "apple Pay" in lower_origin_content:
            return True, ["Apple Pay"]
        if "accept" in lower_origin_content and "card" in lower_origin_content:
            return True, ["Accept card"]
        if "accept" in lower_origin_content and "cash" in lower_origin_content:
            return True, ["Accept cash"]
        if "get the 15k" in lower_origin_content or "get the 20k" in lower_origin_content or "get the 10k" in lower_origin_content:
            return True, ["get the 15k"]
        if "code" in lower_origin_content:
            return True, ["code"]
        if "debt relief" in lower_origin_content:
            return True, ["debt relief"]
        if "to approve" in lower_origin_content:
            return True, ["to approve"]
        if "trump" in lower_origin_content:
            return True, ["trump"]
        if "payment" in lower_origin_content:
            return True, ["payment"]
        if "fees" in lower_origin_content:
            return True, ["fees"]
        if "sugar plum" in lower_origin_content:
            return True, ["sugar plum"]
        if "money" in lower_origin_content and "confirmed" in lower_origin_content:
            return True, ["money confirmed"]
        if "western union" in lower_origin_content:
            return True, ["western union"]
        if "get the cash" in lower_origin_content:
            return True, ["get the cash"]
        if "delivery guy" in lower_origin_content:
            return True, ["delivery guy"]
        if "delivery" in lower_origin_content and "money" in lower_origin_content:
            return True, ["delivery money"]
        if "delivery" in lower_origin_content and "cash" in lower_origin_content:
            return True, ["delivery cash"]
        if "delivery" in lower_origin_content and "today" in lower_origin_content:
            return True, ["delivery today"]
        if "bitcoin" in lower_origin_content:
            return True, ["bitcoin"]
        if "immediately" in lower_origin_content or "immediate" in lower_origin_content:
            return True, ["immediately"]
        if "emergency" in lower_origin_content:
            return True, ["emergency"]
        if "sergeant" in lower_origin_content:  # 军人
            return True, ["Sergeant"]
        if "book" in lower_origin_content and "hotel" in lower_origin_content:
            return True, ["book hotel"]
        if "looking for someone" in lower_origin_content:
            return True, ["looking for someone"]
        if "looking for" in lower_origin_content:
            return True, ["looking for"]
        if "chime" in lower_origin_content and "screenshot" in lower_origin_content:
            return True, ["chime screenshot"]
        if "chime" in lower_origin_content and "send" in lower_origin_content:
            return True, ["chime send"]
        if "gas" in lower_origin_content and "drive" in lower_origin_content:
            return True, ["gas drive"]
        if "been offered" in lower_origin_content:
            return True, ["been offered"]
        if "will delete" in lower_origin_content:
            return True, ["will delete"]
        if "own" in lower_origin_content and "business" in lower_origin_content:
            return True, ["own business"]
        if "roommate" in lower_origin_content and "phone" in lower_origin_content:
            return True, ["roommate phone"]
        if "i'm live in" in lower_origin_content or "am live in" in lower_origin_content or "im live in" in lower_origin_content:
            return True, ["am live in"]
        if "zangi" in lower_origin_content and "response" in lower_origin_content:
            return True, ["zangi response"]
        if "get the apple" in lower_origin_content:
            return True, ["get the apple"]
        if "available" in lower_origin_content and "honest" in lower_origin_content:
            return True, ["available honest"]
        if "i'm honest" in lower_origin_content:
            return True, ["I'm honest"]
        if "express card" in lower_origin_content:
            return True, ["Express card"]
        if "handsome" in lower_origin_content:
            return True, ["Hello handsome"]
        if "i'm from" in lower_origin_content:
            return True, ["I'm from"]
        if "download signal" in lower_origin_content:
            return True, ["download signal"]
        if "and i'm from" in lower_origin_content:
            return True, ["and i'm from"]
        if "we declined" in lower_origin_content:
            return True, ["We declined"]
        if "your current card" in lower_origin_content:
            return True, ["Your current card"]
        if "fraud" in lower_origin_content:
            return True, ["fraud"]
        if "be banned" in lower_origin_content:
            return True, ["be banned"]
        if "assistant" in lower_origin_content and "number" in lower_origin_content:
            return True, ["assistant number"]
        if "embassy" in lower_origin_content:  # 大使馆
            return True, ["embassy"]
        if "property information" in lower_origin_content:
            return True, ["property information"]
        if "paypal" in lower_origin_content:
            return True, ["paypal"]
        if "prize of" in lower_origin_content:
            return True, ["prize of"]
        if "cash prize" in lower_origin_content:
            return True, ["cash prize"]
        if "delivered to" in lower_origin_content:
            return True, ["delivered to"]
        if "delivery company" in lower_origin_content:
            return True, ["delivery company"]
        if "trust me on" in lower_origin_content:
            return True, ["trust me on"]
        if "money" in lower_origin_content and "believe me" in lower_origin_content:
            return True, ["money believe me"]
        if "money" in lower_origin_content and "trust me" in lower_origin_content:
            return True, ["money trust me"]
        if "surgery" in lower_origin_content:  # 手术
            return True, ["surgery"]
        if "this is agent" in lower_origin_content:
            return True, ["this is agent"]
        if "some questions" in lower_origin_content and "police" in lower_origin_content:
            return True, ["some questions police"]
        if "no married" in lower_origin_content:
            return True, ["no married"]
        if "no kids" in lower_origin_content:
            return True, ["no kids"]
        if "single here" in lower_origin_content:
            return True, ["single here"]
        if "here for fun" in lower_origin_content:
            return True, ["here for fun"]
        if "be transfer to" in lower_origin_content:
            return True, ["be transfer to"]
        if "the transaction" in lower_origin_content:
            return True, ["the transaction"]
        if "i'm live in" in lower_origin_content:
            return True, ["I'm live in"]
        if "i am live" in lower_origin_content:
            return True, ["I'm live in"]
        if "am" in lower_origin_content and "years old" in lower_origin_content:
            return True, ["am years old"]
        if "am" in lower_origin_content and "year old" in lower_origin_content:
            return True, ["am years old"]
        if "reported" in lower_origin_content:
            return True, ["reported"]
        if "reporting" in lower_origin_content:
            return True, ["reporting"]
        if "legal" in lower_origin_content:
            return True, ["legal"]
        if "from vegas" in lower_origin_content:
            return True, ["from Vegas"]
        if "private" in lower_origin_content and "phone" in lower_origin_content:
            return True, ["private phone"]
        if "private" in lower_origin_content and "number" in lower_origin_content:
            return True, ["private number"]
        if "this is dr" in lower_origin_content:
            return True, ["This is Dr"]
        if "refund" in lower_origin_content:
            return True, ["refund"]
        if "total amount" in lower_origin_content:
            return True, ["TOTAL AMOUNT"]
        if "gift card" in lower_origin_content:
            return True, ["gift card"]
        if "receive your money" in lower_origin_content:
            return True, ["receive your money"]
        if "help you with the rest" in lower_origin_content:
            return True, ["help you with the rest"]
        if "can't do video" in lower_origin_content:
            return True, ["can't do video"]
        if "live alone" in lower_origin_content:
            return True, ["live alone"]
        if "hookup" in lower_origin_content:
            return True, ["hookup"]
        if "are you interested" in lower_origin_content:
            return True, ["are you interested"]
        if "the hold up" in lower_origin_content:
            return True, ["the hold up"]
        if "nurse" in lower_origin_content and "assigned" in lower_origin_content:
            return True, ["nurse assigned"]
        if "keep loosing" in lower_origin_content:
            return True, ["keep loosing"]
        if "loosing blood" in lower_origin_content:
            return True, ["loosing blood"]
        if "necessary medical" in lower_origin_content:
            return True, ["necessary medical"]
        if "keep getting worse" in lower_origin_content:
            return True, ["keep getting worse"]
        if "gotten worse" in lower_origin_content:
            return True, ["it's gotten worse"]
        if "medical advice" in lower_origin_content:
            return True, ["medical advice"]
        if "manager call" in lower_origin_content:
            return True, ["manager call"]
        if "did you use" in lower_origin_content:
            return True, ["Did You Use Your"]
        if "horny for your" in lower_origin_content:
            return True, ["horny for your"]
        if "so horny" in lower_origin_content:
            return True, ["so horny"]
        if "top up" in lower_origin_content:  # 充值
            return True, ["top up"]
        if "private project" in lower_origin_content:
            return True, ["private project"]
        if "personal project" in lower_origin_content:
            return True, ["personal project"]
        if "credit card" in lower_origin_content:
            return True, ["credit card"]
        if "card debt" in lower_origin_content:
            return True, ["card debt"]
        if "razor gold" in lower_origin_content:
            return True, ["razor gold"]
        if "details in the mail" in lower_origin_content:
            return True, ["details in the mail"]
        if "credit" in lower_origin_content and "details" in lower_origin_content:
            return True, ["credit details"]
        if "kind of fun" in lower_origin_content:
            return True, ["kind of fun"]
        if "for some fun" in lower_origin_content:
            return True, ["for some fun"]
        if "exchange" in lower_origin_content and "pic" in lower_origin_content:
            return True, ["exchange some pic"]
        if "confidential" in lower_origin_content:  # 保密性
            return True, ["confidential"]
        if "my phone is" in lower_origin_content:
            return True, ["my phone is"]
        if "help me with" in lower_origin_content:
            return True, ["help me with"]
        if "phone" in lower_origin_content and "activated" in lower_origin_content:
            return True, ["phone activated"]
        if "anonymous" in lower_origin_content:
            return True, ["anonymous"]
        if "CEO" in origin_content:
            return True, ["CEO"]
        if "uncleared profit" in lower_origin_content:
            return True, ["uncleared profit"]
        if "cloud" in lower_origin_content and "company" in lower_origin_content:
            return True, ["cloud company"]
        if "crypto" in lower_origin_content and "company" in lower_origin_content:
            return True, ["crypto company"]
        if "wallet" in lower_origin_content:
            return True, ["wallet"]
        if "was robbed" in lower_origin_content:
            return True, ["was robbed"]
        if "car accident" in lower_origin_content:
            return True, ["car accident"]
        if "really hurt and tired" in lower_origin_content:
            return True, ["really hurt and tired"]
        if "my boss" in lower_origin_content:
            return True, ["my boss"]
        if "need the money to" in lower_origin_content:
            return True, ["need the money to"]
        if "get my truck back" in lower_origin_content:
            return True, ["get my truck back"]
        if "rally miss and need" in lower_origin_content:
            return True, ["rally miss and need"]
        if "been out of work" in lower_origin_content:
            return True, ["been out of work"]
        if "can you afford" in lower_origin_content:
            return True, ["can you afford"]
        if "to get gas" in lower_origin_content:
            return True, ["to get gas"]
        if "have a nice fun" in lower_origin_content:
            return True, ["have a nice fun"]
        if "trust me for once" in lower_origin_content:
            return True, ["trust me for once"]
        if "trust me again" in lower_origin_content:
            return True, ["trust me again"]
        if "do this last" in lower_origin_content:
            return True, ["just do this last"]
        if "get things done" in lower_origin_content:
            return True, ["get things done"]
        if "supposed this whole thing" in lower_origin_content:
            return True, ["supposed this whole thing"]
        if "hello" in lower_origin_content and "madam" in lower_origin_content and "mr" in lower_origin_content:
            return True, ["hello madam mr"]
        if "madam" in lower_origin_content:
            return True, ["Madam"]
        if "this is your last step" in lower_origin_content:
            return True, ["this is your last step"]
        if "trust me for once" in lower_origin_content:
            return True, ["trust me for once"]
        if "let's get things done" in lower_origin_content:
            return True, ["let's get things done"]
        if "watch it come to past" in lower_origin_content:
            return True, ["watch it come to past"]
        if "kindly respond back" in lower_origin_content:
            return True, ["Kindly respond back"]
        if "respond back" in lower_origin_content and "on time" in lower_origin_content:
            return True, ["respond back on time"]
        if "deliveries" in lower_origin_content:
            return True, ["deliveries"]
        if "store" in lower_origin_content and "near" in lower_origin_content:
            return True, ["near store"]
        if "store" in lower_origin_content:
            return True, ["store"]
        if "lamborghini" in lower_origin_content:
            return True, ["Lamborghini"]
        if "ferrari" in lower_origin_content:
            return True, ["Ferrari"]
        if "skype" in lower_origin_content:  # 聊天网站
            return True, ["Skype"]
        if "numéro personnel" in lower_origin_content:
            return True, ["numero personnel"]
        if "commandes" in lower_origin_content:
            return True, ["commandes"]
        if "allô" in lower_origin_content:  # 你好
            return True, ["Allô"]
        if "sylvie" in lower_origin_content:
            return True, ["Sylvie"]
        if "beau sourire" in lower_origin_content:  # 美丽的微笑
            return True, ["beau sourire"]
        if "second numéro" in lower_origin_content:
            return True, ["second numero"]
        if "mon amour" in lower_origin_content:
            return True, ["mon amour"]
        if "succession" in lower_origin_content:  # 继承权
            return True, ["succession"]
        if "la petite" in lower_origin_content:  # 小女孩
            return True, ["la petite"]
        if "e-mail" in lower_origin_content:
            return True, ["e-mail"]
        if ".com" in lower_origin_content:
            return True, [".com"]
        if "je t’aime" in lower_origin_content:  # 我爱你
            return True, ["Je t’aime"]
        if "montant" in lower_origin_content:  # 金额
            return True, ["montant"]
        if "document" in lower_origin_content:
            return True, ["documents"]
        if "employé" in lower_origin_content:
            return True, ["employé"]
        if "good day mr" in lower_origin_content:
            return True, ["Good day Mr"]
        if "avoid further" in lower_origin_content:
            return True, ["avoid further"]
        if "law" in lower_origin_content and "enforcement" in lower_origin_content:
            return True, ["law enforcement"]
        if "send" in lower_origin_content and "confirm" in lower_origin_content:
            return True, ["send confirm"]
        if "send" in lower_origin_content and "cancel" in lower_origin_content:
            return True, ["send cancel"]
        if "same here" in lower_origin_content:
            return True, ["same here"]
        if "you located" in lower_origin_content:
            return True, ["you located"]
        if "hope this message" in lower_origin_content:
            return True, ["hope this message"]
        if "investigator" in lower_origin_content:
            return True, ["investigator"]
        if "investigation" in lower_origin_content:
            return True, ["investigation"]
        if "syndicate" in lower_origin_content:
            return True, ["syndicate"]
        if "scam" in lower_origin_content:
            return True, ["scam"]
        if "organized" in lower_origin_content:
            return True, ["organized"]
        if "involving" in lower_origin_content:
            return True, ["involving"]
        if "further details" in lower_origin_content:
            return True, ["further details"]
        if "profession" in lower_origin_content:
            return True, ["profession"]
        if "to do this together" in lower_origin_content:
            return True, ["to do this together"]
        if "naughty" in lower_origin_content:  # 淘气，一般是色情相关
            return True, ["naughty"]
        if "what's your name" in lower_origin_content:
            return True, ["What's your name"]
        if "google chat" in lower_origin_content:
            return True, ["Google Chat"]
        if "whatsapp" in lower_origin_content:
            return True, ["WhatsApp"]
        if "signal" in lower_origin_content:
            return True, ["Signal"]
        if "know each other more" in lower_origin_content:
            return True, ["know each other more"]
        if "afford to" in lower_origin_content:
            return True, ["afford to"]
        if "picture" in lower_origin_content and "card" in lower_origin_content:
            return True, ["picture card"]
        if "delivery" in lower_origin_content:
            return True, ["delivery"]
        if "please call me back" in lower_origin_content:
            return True, ["please call me back"]
        if "customs" in lower_origin_content:  # 海关
            return True, ["customs"]
        if "phone" in lower_origin_content and "messed up" in lower_origin_content:
            return True, ["phone messed up"]
        if "get me a card" in lower_origin_content:
            return True, ["get me a card"]
        if "life with you" in lower_origin_content:  # 共度余生
            return True, ["my life with you"]
        if "the rest of" in lower_origin_content and "life" in lower_origin_content:
            return True, ["the rest of life"]
        if "are you living" in lower_origin_content:
            return True, ["where are you living"]
        if "the card" in lower_origin_content:
            return True, ["the card"]
        if "late fee" in lower_origin_content:  # 滞纳金
            return True, ["Late fee"]
        if "balance" in lower_origin_content:  # 余额
            return True, ["Balance"]
        if "keep me posted" in lower_origin_content:
            return True, ["Keep me posted"]
        if "the pdf" in lower_origin_content:
            return True, ["The pdf"]
        if "pdf" in lower_origin_content and "email" in lower_origin_content:
            return True, ["pdf email"]
        if "the bill" in lower_origin_content:
            return True, ["the bill"]
        if "looking forward to" in lower_origin_content:
            return True, ["looking forward to"]
        if "old fashion looks" in lower_origin_content:
            return True, ["old fashion looks"]
        if "more about myself" in lower_origin_content:
            return True, ["MORE ABOUT MYSELF"]
        if "would be best friend" in lower_origin_content:
            return True, ["would be best friend"]
        if "personalities" in lower_origin_content:
            return True, ["personalities"]
        if "hobbies" in lower_origin_content:
            return True, ["hobbies"]
        if "god fearing" in lower_origin_content:  # 敬畏上帝
            return True
        if "good looking" in lower_origin_content:
            return True, ["good looking"]
        if "kindhearted" in lower_origin_content:
            return True, ["kindhearted"]
        if "i love to" in lower_origin_content:
            return True, ["I love to"]
        if "love animals" in lower_origin_content:
            return True, ["love animals"]
        if "love listening" in lower_origin_content:
            return True, ["love listening"]
        if "love to eat" in lower_origin_content:
            return True, ["love to eat"]
        if "love to go" in lower_origin_content:
            return True, ["love to go"]
        if "love my own" in lower_origin_content:
            return True, ["love my own company"]
        if "my own company" in lower_origin_content:
            return True, ["love my own company"]
        if "have a good sense" in lower_origin_content:
            return True, ["have a good sense"]
        if "humor" in lower_origin_content:
            return True, ["humor"]
        if "understand humans" in lower_origin_content:
            return True, ["understand humans"]
        if "easy to please" in lower_origin_content:
            return True, ["easy to please"]
        if "don't ask for much" in lower_origin_content:
            return True, ["don't ask for much"]
        if "happy about" in lower_origin_content:
            return True, ["happy about"]
        if "so happy that" in lower_origin_content:
            return True, ["so happy that"]
        if "i like my" in lower_origin_content:
            return True, ["I like my"]
        if "i also like" in lower_origin_content:
            return True, ["i also like"]
        if "not a smoker" in lower_origin_content:
            return True, ["am not a smoker"]
        if "a good taste" in lower_origin_content:
            return True, ["has a good taste"]
        if "favorite color" in lower_origin_content:
            return True, ["favorite color"]
        if "very busy" in lower_origin_content and "work" in lower_origin_content:
            return True, ["very busy work"]
        if "very busy" in lower_origin_content and "weekdays" in lower_origin_content:
            return True, ["very busy weekdays"]
        if "new culture" in lower_origin_content:
            return True, ["new culture"]
        if "language" in lower_origin_content:
            return True, ["language"]
        if "am free from" in lower_origin_content:
            return True, ["i am free from"]
        if "simple man" in lower_origin_content:
            return True, ["simple man"]
        if "love life" in lower_origin_content:
            return True, ["love life"]
        if "love nature" in lower_origin_content:
            return True, ["love nature"]
        if "worldly activities" in lower_origin_content:  # 世俗活动
            return True, ["worldly activities"]
        if "calm fun" in lower_origin_content:
            return True, ["calm fun"]
        if "tradition" in lower_origin_content and "culture" in lower_origin_content:
            return True, ["tradition culture"]
        if "christian" in lower_origin_content:
            return True, ["christian"]
        if "christianity" in lower_origin_content:
            return True, ["Christianity"]
        if "rest of my life" in lower_origin_content:
            return True, ["rest of my life"]
        if "i will respect" in lower_origin_content:
            return True, ["i will respect"]
        if "make the future" in lower_origin_content:
            return True, ["make the future"]
        if "reality not dreams" in lower_origin_content:
            return True, ["reality not dreams"]
        if "live happily" in lower_origin_content:
            return True, ["live happily"]
        if "one big family" in lower_origin_content:
            return True, ["one big family"]
        if "your attention" in lower_origin_content:
            return True, ["need your attention"]
        if "need" in lower_origin_content and "attention" in lower_origin_content:
            return True, ["need your attention"]
        if "where are you" in lower_origin_content:
            return True, ["where are you"]
        if "delivered today" in lower_origin_content:
            return True, ["delivered today"]
        if "to complete" in lower_origin_content and "completely" not in lower_origin_content:
            return True, ["to complete"]
        if "complete this" in lower_origin_content:
            return True, ["complete this"]
        if "arrangement" in lower_origin_content:
            return True, ["arrangement"]
        if "take action" in lower_origin_content:
            return True, ["take action"]
        if "completed" in lower_origin_content:
            return True, ["completed"]
        if "permitted" in lower_origin_content:
            return True, ["permitted"]
        if "government" in lower_origin_content:
            return True, ["government"]
        if "before tomorrow" in lower_origin_content:
            return True, ["before tomorrow"]
        if "you will see what" in lower_origin_content:
            return True, ["you will see what"]
        if "that's on you now" in lower_origin_content:  # 取决于你
            return True, ["that's on you now"]
        if "if you don't want to get" in lower_origin_content:
            return True, ["if you don't want to get"]
        if "have" in lower_origin_content and "minutes to" in lower_origin_content:
            return True, ["have minutes to"]
        if "have" in lower_origin_content and "hours to" in lower_origin_content:
            return True, ["have hours to"]
        if "wiping everything" in lower_origin_content:
            return True, ["wiping everything"]
        if "deliver your" in lower_origin_content:
            return True, ["deliver your"]
        if "take" in lower_origin_content and "name off" in lower_origin_content:
            return True, ["taking your name off"]
        if "taking" in lower_origin_content and "name off" in lower_origin_content:
            return True, ["taking your name off"]
        if "getting charge" in lower_origin_content:
            return True, ["getting charge"]
        if "we can get it back" in lower_origin_content:
            return True, ["we can get it back"]
        if "getting everything canceled" in lower_origin_content:
            return True, ["getting everything canceled"]
        if "everything canceled" in lower_origin_content:
            return True, ["getting everything canceled"]
        if "getting" in lower_origin_content and "canceled" in lower_origin_content:
            return True, ["getting everything canceled"]
        if "see what you cause" in lower_origin_content:
            return True, ["see what you cause"]
        if "not my phone" in lower_origin_content:
            return True, ["not my phone"]
        if "doubting me" in lower_origin_content:
            return True, ["doubting me"]
        if "remember me" in lower_origin_content:
            return True, ["remember me"]
        if "forget my name" in lower_origin_content:
            return True, ["forget my name"]
        if "worker phone" in lower_origin_content:
            return True, ["worker phone"]
        if "in case you don't believe" in lower_origin_content:
            return True, ["in case you don't believe"]
        if "want to have fun" in lower_origin_content:
            return True, ["want to have fun"]
        if "how much will you" in lower_origin_content:
            return True, ["How much will you"]
        if "any amount you can" in lower_origin_content:
            return True, ["any amount you can"]
        if "confirmation" in lower_origin_content:
            return True, ["confirmation"]
        if "give us a call" in lower_origin_content:
            return True, ["give us a call"]
        if "we called u" in lower_origin_content:
            return True, ["We called u"]
        if "we called you" in lower_origin_content:
            return True, ["We called you"]
        if ("is this " in lower_origin_content and "who is this" not in lower_origin_content
                and "how is this" not in lower_origin_content):
            return True, ["is this "]
        if "urgent matter" in lower_origin_content:
            return True, ["urgent matter"]
        if "need to" in lower_origin_content and "talk" in lower_origin_content:
            return True, ["need to talk"]
        if "talking to you with" in lower_origin_content:
            return True, ["talking to you with"]
        if "management number" in lower_origin_content:
            return True, ["management number"]
        if "i'm a good" in lower_origin_content:
            return True, ["I'm a good"]
        if "i'm from " in lower_origin_content:
            return True, ["I'm from "]
        if "send me your picture" in lower_origin_content:
            return True, ["Send me your picture"]
        if "something to discuss" in lower_origin_content:
            return True, ["something to discuss"]
        if "stay bless" in lower_origin_content:
            return True, ["Stay bless"]
        if "i'm the one" in lower_origin_content:
            return True, ["I'm the one"]
        if "if you don't pay" in lower_origin_content:
            return True, ["if you don't pay"]
        if "i will post your" in lower_origin_content:
            return True, ["I will post your"]
        if "i have your ssn" in lower_origin_content:
            return True, ["I have your ssn"]
        if "you can be jailed" in lower_origin_content:
            return True, ["you can be jailed"]
        if "put all your picture" in lower_origin_content:
            return True, ["I will put all your picture"]
        if "ruining you" in lower_origin_content:
            return True, ["ruining you"]
        if "or i ruined you" in lower_origin_content:
            return True, ["or I ruined you"]
        if "passengers phone" in lower_origin_content:
            return True, ["passengers phone"]
        if "answer yes or" in lower_origin_content:
            return True, ["answer YES or NO"]
        if "reviewing" in lower_origin_content and "account" in lower_origin_content:
            return True, ["reviewing account"]
        if "need your support" in lower_origin_content:
            return True, ["need your support"]
        if "someone else's phone" in lower_origin_content:
            return True, ["someone else's phone"]
        if "auto shop" in lower_origin_content:  # 汽车修理店
            return True, ["auto shop"]
        if "get my car" in lower_origin_content:
            return True, ["get my car"]
        if "tell me more about" in lower_origin_content:
            return True, ["tell me more about"]
        if "more about you" in lower_origin_content:
            return True, ["more about you"]
        if "know more about" in lower_origin_content:
            return True, ["know more about"]
        if "chat you more" in lower_origin_content:
            return True, ["chat you more"]
        if "an affectionate person" in lower_origin_content:
            return True, ["an affectionate person"]
        if "i like a woman who" in lower_origin_content:
            return True, ["I like a woman who"]
        if "woman who is" in lower_origin_content:
            return True, ["woman who is"]
        if "with no kids" in lower_origin_content:
            return True, ["with no kids"]
        if "relationship with me" in lower_origin_content:
            return True, ["relationship with me"]
        if "make me and you happy" in lower_origin_content:
            return True, ["make me and you happy"]
        if "talk about life and" in lower_origin_content:
            return True, ["talk about life and"]
        if "make you happy with" in lower_origin_content:
            return True, ["make you happy with"]
        if "more pictures of you" in lower_origin_content:
            return True, ["more pictures of you"]
        if "see more people" in lower_origin_content:
            return True, ["see more people"]
        if "doing for a living" in lower_origin_content:  # 你做什么工作？
            return True, ["doing for a living"]
        if "what type of phone" in lower_origin_content:
            return True, ["What type of phone"]
        if "do you see my picture" in lower_origin_content:
            return True, ["Do you see my picture"]
        if "what you look like" in lower_origin_content:
            return True, ["what you look like"]
        if "your name and your picture" in lower_origin_content:
            return True, ["Your name and your picture"]
        if "years and single" in lower_origin_content:
            return True, ["years and single"]
        if "respond my love" in lower_origin_content:
            return True, ["respond my love"]
        if "send me a photo of" in lower_origin_content:
            return True, ["send me a photo of"]
        if "years old single" in lower_origin_content:
            return True, ["years old single"]
        if "been married" in lower_origin_content:
            return True, ["been married"]
        if "i will publish" in lower_origin_content:
            return True, ["I will publish"]
        if "all your relatives" in lower_origin_content:
            return True, ["all your relatives"]
        if "wish to avoid this" in lower_origin_content:
            return True, ["wish to avoid this"]
        if "interpol" in lower_origin_content:  # 国际刑警
            return True, ["Interpol"]
        if "minors harassed" in lower_origin_content:
            return True, ["minors harassed"]
        if "raped online" in lower_origin_content:
            return True, ["raped online"]
        if "pedophilia" in lower_origin_content:  # 恋童癖
            return True, ["pedophilia"]
        if "pedophile" in lower_origin_content:
            return True, ["pedophile"]
        if "transnational crime" in lower_origin_content:
            return True, ["transnational crime"]
        if "database" in lower_origin_content:
            return True, ["database"]
        if "we are listening" in lower_origin_content:
            return True, ["we are listening"]
        if "enough time to waste" in lower_origin_content:
            return True, ["enough time to waste"]
        if "support team" in lower_origin_content:
            return True, ["Support Team"]
        if "recent activity" in lower_origin_content:
            return True, ["recent activity"]
        if "has flagged" in lower_origin_content:
            return True, ["has flagged"]
        if "multiple reports" in lower_origin_content:
            return True, ["multiple reports"]
        if "reports from" in lower_origin_content:
            return True, ["reports from"]
        if "receive credits" in lower_origin_content:
            return True, ["receive credits"]
        if "fraudulent" in lower_origin_content:
            return True, ["fraudulent"]
        if "matchpay" in lower_origin_content:
            return True, ["MatchPay"]
        if "these claims" in lower_origin_content:
            return True, ["These claims"]
        if "are you tired of" in lower_origin_content:
            return True, ["Are you tired of"]
        if "investment" in lower_origin_content:
            return True, ["Investment"]
        if "ll leak ur" in lower_origin_content:
            return True, ["will leak"]
        if "ll leak your" in lower_origin_content:
            return True, ["will leak"]
        if "going to leak" in lower_origin_content:
            return True, ["going to leak"]
        if "imma leak" in lower_origin_content:
            return True, ["imma leak"]
        if "rehabilitation facility" in lower_origin_content:
            return True, ["rehabilitation facility"]
        if "monitor her recovery" in lower_origin_content:
            return True, ["monitor her recovery"]
        if "the offer is" in lower_origin_content:
            return True, ["The offer is"]
        if "current assistant" in lower_origin_content:
            return True, ["current assistant"]
        if "no experience" in lower_origin_content:
            return True, ["No experience"]
        if "specific requirements" in lower_origin_content:
            return True, ["specific requirements"]
        if "flexible hours" in lower_origin_content:
            return True, ["Flexible hours"]
        if "this role is" in lower_origin_content:
            return True, ["This role is"]
        if "imposter" in lower_origin_content:  # 冒名顶替
            return True, ["Imposter"]
        if "in the next hour" in lower_origin_content:
            return True, ["in the next hour"]
        if "t-mobile" in lower_origin_content:
            return True, ["T-Mobile"]
        if "job details" in lower_origin_content:
            return True, ["Job Details"]
        if "accountant" in lower_origin_content:  # 会计
            return True, ["Accountant"]
        if "manager" in lower_origin_content:  # 经理
            return True, ["Manager"]
        if "personal info" in lower_origin_content:
            return True, ["Personal Info"]
        if "and leak" in lower_origin_content:
            return True, ["and leak"]
        if "mirorr pic" in lower_origin_content:
            return True, ["mirorr pic"]
        if "we need to fix this" in lower_origin_content:
            return True, ["we need to fix this"]
        if "verification code" in lower_origin_content:
            return True, ["Verification Code"]
        if "provide us with" in lower_origin_content:
            return True, ["provide us with"]
        if "once you receive it" in lower_origin_content:
            return True, ["once you receive it"]
        if "paperworks" in lower_origin_content:
            return True, ["paperworks"]
        if "name and age" in lower_origin_content:
            return True, ["name and age"]
        if "escort" in lower_origin_content:
            return True, ["escort"]
        if "welcome to" in lower_origin_content:
            return True, ["Welcome To"]
        if "may i help you" in lower_origin_content:
            return True, ["May I Help You"]
        if "can u send me" in lower_origin_content:
            return True, ["Can u send me"]
        if "send me cash app" in lower_origin_content:
            return True, ["Send me cash app"]
        if "send me cashapp" in lower_origin_content:
            return True, ["Send me cashapp"]
        if "i have no money for" in lower_origin_content:
            return True, ["I have no money for"]
        if "send me money to" in lower_origin_content:
            return True, ["send me money to"]
        if "your boss" in lower_origin_content:
            return True, ["Your Boss"]
        if "lend this phone" in lower_origin_content:
            return True, ["lend this phone"]
        if "authority" in lower_origin_content:
            return True, ["Authority"]
        if "give me a last chance" in lower_origin_content:
            return True, ["Give me a last chance"]
        if "we are waiting for you" in lower_origin_content:
            return True, ["We are waiting for you"]
        if "going to post" in lower_origin_content:
            return True, ["going to post"]
        if "social media" in lower_origin_content:
            return True, ["social media"]
        if "your family is going to" in lower_origin_content:
            return True, ["your family is going to"]
        if "see your contact" in lower_origin_content:
            return True, ["see your contact"]
        if "contact on my list" in lower_origin_content:
            return True, ["contact on my list"]
        if "if you didn't comply it" in lower_origin_content:
            return True, ["if you didn't comply it"]
        if "it will be worse" in lower_origin_content:
            return True, ["it will be worse"]
        if "your family are getting" in lower_origin_content:
            return True, ["your family are getting"]
        if "will be the worst part" in lower_origin_content:
            return True, ["will be the worst part"]
        if "sending to all" in lower_origin_content:
            return True, ["sending to all"]
        if "be arrested for" in lower_origin_content:
            return True, ["be arrested for"]
        if "can you send me some" in lower_origin_content:
            return True, ["Can you send me some"]
        if "health and safety" in lower_origin_content:
            return True, ["Health and Safety"]
        if "healthcare professional" in lower_origin_content:
            return True, ["Healthcare Professional"]
        if "local clinic" in lower_origin_content:
            return True, ["Local Clinic"]
        if "guidance and support" in lower_origin_content:
            return True, ["Guidance and Support"]
        if "an accident" in lower_origin_content:
            return True, ["an accident"]
        if "neighbors phone" in lower_origin_content:
            return True, ["neighbors phone"]
        if "if you have any questions" in lower_origin_content:
            return True, ["If you have any questions"]
        if "we have received" in lower_origin_content:
            return True, ["We have received"]
        if "you guys are good for" in lower_origin_content:  # 你们有这个xx优惠
            return True, ["You guys are good for"]
        if "registration for" in lower_origin_content:
            return True, ["Registration for"]
        if "cashapp support" in lower_origin_content:
            return True, ["Cashapp support"]
        if "completed and later failed" in lower_origin_content:
            return True, ["Completed and later failed"]
        if "be refunded" in lower_origin_content:
            return True, ["be refunded"]
        if "from overseas" in lower_origin_content:
            return True, ["from overseas"]
        if "in transit" in lower_origin_content:
            return True, ["in transit"]
        if "this shipment" in lower_origin_content:
            return True, [" this shipment"]
        if "will get posted" in lower_origin_content:
            return True, ["will get posted"]
        if "your attention is" in lower_origin_content:
            return True, ["Your attention is"]
        if "ten thousand" in lower_origin_content:
            return True, ["ten thousand"]
        if "the rest money" in lower_origin_content:
            return True, ["the rest money"]
        if "money is sent out" in lower_origin_content:
            return True, ["money is sent out"]
        if "sending money" in lower_origin_content:
            return True, ["sending money"]
        if "the inspection" in lower_origin_content:
            return True, ["the inspection"]
        if "reply start to" in lower_origin_content:
            return True, ["Reply START to"]
        if "assigned coordinator" in lower_origin_content:
            return True, ["assigned coordinator"]
        if "remaining fund" in lower_origin_content:
            return True, ["remaining fund"]
        if "single or married" in lower_origin_content:
            return True, ["single or married"]
        if "admiral" in lower_origin_content:  # 海军
            return True, ["Admiral"]
        if "be submitted" in lower_origin_content:
            return True, ["be submitted"]
        if "public disturbance" in lower_origin_content:
            return True, ["public disturbance"]
        if "speaking with us" in lower_origin_content:
            return True, ["speaking with us"]
        if "inappropriate conduct" in lower_origin_content:
            return True, ["inappropriate conduct"]
        if "further clarification" in lower_origin_content:
            return True, ["further clarification"]
        if "provide further" in lower_origin_content:
            return True, ["provide further"]
        if "additional concerns" in lower_origin_content:
            return True, ["additional concerns"]
        if "any additional" in lower_origin_content:
            return True, ["any additional"]
        if "if we don't receive" in lower_origin_content:
            return True, ["if we don't receive"]
        if "more serious charges" in lower_origin_content:
            return True, ["more serious charges"]
        if "be issued" in lower_origin_content:
            return True, ["be issued"]
        if "based on your statement" in lower_origin_content:
            return True, ["Based on your statement"]
        if "the incident" in lower_origin_content:
            return True, ["the incident"]
        if "some transactions" in lower_origin_content:
            return True, ["some transactions"]
        if "transactions" in lower_origin_content and "made" in lower_origin_content:
            return True, ["made transactions"]
        if "ugly bitch" in lower_origin_content:
            return True, ["ugly bitch"]
        if "skinny nigger boy" in lower_origin_content:
            return True, ["Skinny nigger boy"]

        return False, []

    @staticmethod
    def is_incoming_alert(text: str, from_number: str) -> bool:
        if not text:
            return False
        if 0 < len(from_number) <= 8:
            return False

        text = text.lower()
        if "stop fucking texting me" == text:
            return True
        if "blocking" == text:
            return True
        if "stop harassing me" == text:
            return True
        if "please stop texting me" == text:
            return True
        if "stop contacting me" == text:
            return True
        if "stop" == text:
            return True
        if "your verification code is" in text:
            return False
        if "This is your authentication code" in text:
            return False
        if "reply stop to" in text:
            return False
        if "fbi" in text:
            return True
        if "reporting" in text:
            return True
        if "reported" in text:
            return True
        if "reporte" in text:  # 西班牙语
            return True
        if "scam" not in text:
            return False

        if "if someone requests this code" in text:
            return False
        if "only in google" in text and "Use code" in text:
            return False
        if "do not share this code" in text:
            return False
        if "you are calling a virtual number" in text:
            return False
        if "never share this code" in text:
            return False
        if "been rip off" in text:
            return False
        return True


if __name__ == '__main__':
    print(SmsBanVipUtil.check_ban_vip_words("you pick cotton"))
    print(SmsBanVipUtil.check_ban_vip_words("""Greetings, You have been selected to pick cotton at the nearest 
    plantation.Be ready at 12AM NOVEMBER 13 SHARP with your belongings. Our Executive Slaves will come get you in a 
    Brown Van, be prepared to be searched down once you've enter the plantation. You are in Plantation Group A.""",
                                            ))
    print(SmsBanVipUtil.check_ban_vip_words(
        "I work with the LESBIAN BGN (be gone now) corporation. I would like to inform you, you HAVE been chosen! You "
        "and 5,265 other lesbians. We've already got some of our workers that work in the branch underneath me ("
        "Messenger) to come and get you! We will definitely update you on info. Make sure you've got your things "
        "packed, we promise to turn you into a brand new person, and by brand new we mean straight. We hope you love "
        "your experience! message sent - 5:44 TUESDAY DECEMBER 17"))
