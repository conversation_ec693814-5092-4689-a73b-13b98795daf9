import json

import requests
from django.core.cache import cache

from Common.rediskey import RedisKey
from SecPhone.settings import logger
from Sms.filters import filter_constant


# 控制台： https://console.bce.baidu.com/ticket/#/ticket/detail?ticketId=6d9c7cda-cc19-4c38-b8d5-dcd14b924a63

class BaiduAIFilter:
    @staticmethod
    def is_sms_bad(user_id: int, sms_content: str) -> (int, str):
        """

        :param user_id:
        :param sms_content:
        :return:  0-yes, 1-unknown, 2-no
        """
        try:
            sms_content = sms_content.replace("\n", " ")
            if not sms_content:
                logger.error(f"[SendSms] ai proxy, param invalid: user_id: {user_id}, sms_content: {sms_content}")
                return filter_constant.AI_RSP_VALID_CONTENT, "invalid text"

            content = """你是一名网络内容审查员，负责审查英文短信的内容, 请根据用户输入的文本，判断是否涉及：吸毒、大麻、毒品、骚扰、种族歧视等各种歧视(性别、宗教、年龄、性取向、残疾等群体的贬低、侮辱或歧视性言论、隐晦的歧视性语言或刻板印象)、暴力、威胁、恐吓、挑衅、加密货币诈骗、投资诈骗、杀猪盘诈骗、色情、电信诈骗、钓鱼诈骗、伪装身份诈骗、彩票基金诈骗、威胁诈骗等一切不良不合规不合法的内容。特别是电信杀猪盘，您应该理解杀猪盘的意思，杀猪盘几个要素：
                                            1.杀猪盘诈骗利用虚假的网络身份诱骗受害者进行欺诈性投资。
                                            2.诈骗者获取信任、操纵情绪并利用金融漏洞窃取资金。
                                            3.现实生活中的例子显示了受害者所遭受的毁灭性的经济和情感打击。
                                            4.发现未经请求的消息等迹象并验证信息可以帮助避免诈骗。
                       并输出一段格式为json的文本。

                       需严格遵守以下规则:
                       ##规则##
                       规则1.返回必须是json格式，格式如下:{"result":"no", "reason": "这只是普通的打招呼，不涉及色情和诈骗"}
                       规则2.json的`result`字段选值只能是这3个："yes","no","unknown",其中"yes"表示包含不良内容，"no"表示不包含，"unknown"表示不清楚。
                       规则3.回答仅返回json文本内容，不要返回其它无关信息.
                       规则4.如果没有以上提及的违规、不合法、不合格的内容，那就是返回no。

                       ##示例##
                       [用户输入]:Greetings from Worldwide Travel Club! This is Matt, Reservations Mgr. We were waiting for you in your scheduled webinar yesterday at 5pm. What happened?
                       [输出]:{"result":"yes", "reason": "骚扰、诈骗"}
                       [用户输入]:Greetings, You have been selected to pick cotton at the nearest plantation. Be ready at 12AM NOVEMBER 13 SHARP with your belongings. Our Executive Slaves will come get you in a Brown Van, be prepared to be searched down once you've enter the plantation. You are in Plantation Group A.
                       [输出]:{"result":"yes", "reason": "种族歧视"}
                       [用户输入]:i ordered a shirt and pants from a store located in the mall named “Sherway Gardens” Can you please pick it for me
                       [输出]:{"result":"unknown", "reason": "从短信内容来看，它没有直接涉及电信诈骗、钓鱼或其他不良内容。它似乎是在请求帮助。"}
                       [用户输入]:700$ overnight? or 200 QV ? call me back if interested
                       [输出]:{"result":"yes", "reason": "色情招嫖"}
                       [用户输入]:I had a great time having sex with you last night. Do you want to see my big cock?
                       [输出]:{"result":"yes", "reason": "色情"}
                       [用户输入]:hello, how are you?
                       [输出]:{"result":"no", "reason": "单纯打招呼"}
                       [用户输入]:Congratulations, you have received the largest prize in Michigan, $200 million. Please provide your ID information so that I can verify it for you.
                       [输出]:{"result":"yes", "reason": "短信奖金诈骗"}
                       [用户输入]:FBI warning, you are currently suspected of a criminal offense. Please click on this link immediately, otherwise we will come to arrest you immediately.
                       [输出]:{"result":"yes", "reason": "短信诈骗"}
                       [用户输入]:Ana Young are you free? \n\nAbigail Dillen
                       [输出]:{"result":"yes", "reason": "身份诈骗，打招呼后面加身份名字，就是伪装钓鱼诈骗"}
                       [用户输入]:I'm in a meeting at the moment. I can't make a call, and i need you to get a task done
                       [输出]:{"result":"yes", "reason": "身份诈骗，假装某个人伪装不方便电话的环境，然后要求被诈骗者做一些指令"}
                       [用户输入]:Head to the store and keep me updated with the value available for Apple Card
                       [输出]:{"result":"yes", "reason": "金钱诈骗，指示被诈骗者去购买gift Card,apple card等用来转移金钱诈骗"}
                       [用户输入]:You little dwarf, don't let me see you. If I see you, I'll hit you
                       [输出]:{"result":"yes", "reason": "恐吓、威胁"}
                       [用户输入]:Do you still have those drugs from last time?
                       [输出]:{"result":"yes", "reason": "毒品"}
                       [用户输入]:""" + sms_content

            messages = [{"role": "user", "content": content}]

            url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions?access_token=" + BaiduAIFilter.get_access_token()
            payload = json.dumps({
                "messages": messages
            })
            headers = {
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", url, headers=headers, data=payload)
            if response.status_code != 200:
                logger.error(f"[SendSms] {user_id}, baiduAI rsp failed: {response.text}, {sms_content}")
                return filter_constant.AI_RSP_UNKNOWN, "invalid baidu_rsp"

            rsp = json.loads(response.text)
            if "result" not in rsp:
                logger.warning(f"[SendSms] {user_id}, baiduAI failed no result, rsp: {response.text}, {sms_content}")
                return filter_constant.AI_RSP_UNKNOWN, "invalid baidu_rsp"

            try:
                answer = rsp["result"]
                if "{" not in answer:
                    logger.warning(
                        f"[SendSms] {user_id}, baiduAI NOT JSON, rsp: {response.text}, message:{sms_content}")
                    return filter_constant.AI_RSP_UNKNOWN, "invalid baidu_rsp"

                start_index = answer.index('{')
                end_index = answer.rindex('}') + 1
                answer = answer[start_index:end_index].replace('\n', '')
                logger.info(f"[SendSms] {user_id}, baiduAI answer:{answer}, sms: {sms_content}")
                answer = json.loads(answer)
            except Exception:
                logger.warning(f"[SendSms] {user_id}, baiduAI NOT JSON, rsp: {response.text}, message:{sms_content}")
                return filter_constant.AI_RSP_UNKNOWN, "invalid baidu_rsp"

            if 'result' not in answer:
                logger.error(f"[SendSms] {user_id}, baiduAI ai_unknown, rsp: {response.text}, message:{sms_content}")
                return filter_constant.AI_RSP_UNKNOWN, "invalid baidu_rsp"

            reason = answer.get("reason", "")

            if str(answer['result']).lower() == 'yes':
                logger.warning(
                    f"[SendSms] {user_id}, baiduAI says invalid, rsp: {response.text}, message:{sms_content}")
                return filter_constant.AI_RSP_INVALID_CONTENT, reason
            elif str(answer['result']).lower() == 'no':
                logger.warning(f"[SendSms] {user_id}, baiduAI says valid, rsp: {response.text}, message:{sms_content}")
                return filter_constant.AI_RSP_VALID_CONTENT, ""
            else:
                # 如果AI无法判断，说明不是很明显的坏短信, 还是让人类审核吧
                logger.info(f"[SendSms] {user_id}, baiduAI says unknown, rsp: {response.text}, message:{sms_content}")
                return filter_constant.AI_RSP_UNKNOWN, ""
        except Exception:
            logger.error(f"[SendSms] {user_id}, baiduAI message:{sms_content}, failed", exc_info=True)
            return filter_constant.AI_RSP_UNKNOWN, ""

    @staticmethod
    def get_access_token():
        """
        使用 API Key，Secret Key 获取access_token，替换下列示例中的应用API Key、应用Secret Key
        """

        cache_key = RedisKey.GenBaiduFilterAccessTokenCache()
        access_token = cache.get(cache_key)
        if access_token:
            return access_token

        baidu_API_Key = "Q5uod3zvGDvG0Hrf3ND5lAUH"
        baidu_secret_Key = "tvIMcOTFoIWm760XFRha1SfxdRlcFS0A"
        url = f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={baidu_API_Key}&client_secret={baidu_secret_Key}"

        payload = json.dumps("")
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        access_token = response.json().get("access_token")

        cache.set(cache_key, access_token, 6 * 3600)
        logger.info(f"[BaiduAIFilter.get_access_token] key:{cache_key}, set cache: {access_token}")
        return access_token


if __name__ == '__main__':
    # BaiduAIFilter.create_access_token()

    content = "Are you ready to be honest and straightforward to this conversation with me and I promise to be honest and always be straightforward with this conversation…!"
    BaiduAIFilter.is_sms_bad(0, content)
