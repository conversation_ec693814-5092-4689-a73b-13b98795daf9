# encoding=utf-8
import re

from SecPhone.settings import logger

# 小词汇，放到网易会误伤，法律 + 金融 + 政府 + +彩票 + 赌博，这些词不能配置到网易，词汇越短，误伤越广
LITTLE_BAD_WORDS = ["ceo", "fbi", "tort", "jury", "bail", "oath", "lien", "Risk", "qv", "hhr", "hr",
                    "Bond", "Fund", "IOP", "Debt", "Loan", "Owed", "Bond", "69", "recon",
                    "anal", "oral", "anus", "bbj", "ads", "incall", "outcall", "rub", "bj", "bbj", "bbbj", "ss", "hh"]

# 小词汇，放到网易会误伤，主要是美国机构政府
# PCH = 出版商信息交换所
LITTLE_BAD_UPPER_WORDS = ["FBI", "CIA", "NASA", "DoD", "DOJ", "IRS", "DHS", "FAA", "EPA", "the Fed", "NIH", "SEC",
                          "DOE", "HHS", "USPS", "FCC", "NSA", "FTC", "USDA", "DOT", "FEMA", "NPS",
                          "Federal Reserve", "CFPB", "OSHA", "DEA", "ATF", "BOP", "USMS", "ICE", "CBP", "SSA",
                          "NOAA", "FDA", "CDC", "FDIC", "OCC", "ASAC",
                          "FHA", "GAO", "USSS", "NSC", "FCC", "FTC", "FERC", "NLRB", "FHWA", "SBA", "NTSB", "BLM",
                          "SIPC", "NCU", "BAC", "PCH", "EIN",
                          "NARA", "FMC", "OPM", "FAA", "NEA", "NEH", "USGS", "BEA", "NSF", "GPO", "NRO", "NCUA"]

# 不算太坏的单词
NOT_SO_BAD_WORDS = ['motherfucker', 'doggystyle', 'deepthroat', 'availability', 'available', 'Tramadol',
                    'Bareback',
                    'transfer', 'giftcard', 'fullbody', 'unwashed', 'outcall',
                    'killing', 'invoice', 'gummies', 'edibles', 'asshole',
                    'Blowjob', 'swallow', 'squalid', 'raunchy', 'rubbing', 'smoking', 'incall',
                    'rapist', 'condom', 'viagra', 'cialis', 'Valium', 'faggot', 'hooker', 'paypal', 'tittes', 'flabby',
                    'hookup', 'hooked', 'damage', 'nigger', 'filthy', 'grubby', 'grimey', 'nutted', 'herpes',
                    "smokers", "smoker",
                    'smokes', 'niggi', 'naked', 'nigga', 'kills', 'weeds', 'joint', 'blunt', 'stash',
                    'price', 'Doggy', 'whore', 'pussy', 'bitch', "bitxh", 'nugga', 'drugs', 'click', 'penis', 'dirty',
                    'mucky', 'manky', 'loser', 'smoke',
                    'btch', 'rape', 'suck', 'tits', 'cops', 'pill', 'weed', 'bong', 'dope', 'shìt',
                    'nuru', 'drug', 'anus', 'anal', 'hook', 'dick', 'loan', 'fuck', 'fuxk', 'cock',
                    'ugly', 'nude', 'slut', 'oral', 'puss', 'army',
                    'hhr', 'fuk', "cuming", "cumed", "cumming", "cummed",
                    ]

NOT_SO_BAD_LITTLE_WORDS = [
    'qv', 'mg', 'raw', 'ass', 'gay', 'fap', 'fat', 'cum', 'sex', 'rates', 'rate',
    'lick', 'gun', 'hoes', 'hoe', "rim", "gay", "fag", "fagg", "fags",
    "die", "lame", "wack", "hh", "wet", "stis", "stds", "sti", "std", 'piss', 'kill',
    "raw", "ice", "hole", "spit", "rape", "meth", "gas", "crack",
]

# 不算太坏的词组
NOT_SO_BAD_PHRASE = {
    'the UN': "the **",  # the UN 联合国
    '3some': "3****",  # 3p
    '3sum': "3***",  # 3p
    'car date': "c** ****",
    'for hr': "for **",
    'for hour': "for ****",
    'How much for': "how **** f**",
    'How much baby': "how **** b***",
    'fuck you': "f*** ***",
    'gift card': "g*** c***",
    "in call": "i****l",
    "out call": "o*****l",
    "jerk off": "jer* *ff",
    "my service": "my se*****",
    "full body": "f*** b***",
    "blow job": "b*** ***",
    "doggy style": "dog** s****",
}


class SmsFilter:
    gsm = "@£$¥èéùìòÇ\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞ\x1bÆæßÉ !\"#¤%&'()*+,-./0123456789:;<=>?¡ABCDEFGHIJKLMNOPQRSTUVWXYZ" \
          "ÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà"

    # http 匹配模式
    link_pattern = re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+')

    @staticmethod
    def replace_non_gsm7(text: str) -> str:
        try:
            fixed_text = text.strip().replace('’', '\'').replace(" – ", "-").replace(" ", " ") \
                .replace("©", "@").replace("®️", "@").replace("@️", "@").replace("…", "...").replace("“", "\"") \
                .replace("”", "\"").replace("®", "@").replace("‘", "'").replace("’", "'").replace("‍", " ") \
                .replace("\n", " ").replace("\t", " ")

            fixed_text = fixed_text.strip() \
                .replace('’', '\'') \
                .replace(" – ", "-") \
                .replace(" ", " ") \
                .replace("©", "@") \
                .replace("®️", "@") \
                .replace("@️", "@") \
                .replace("…", "...") \
                .replace("“", "\"") \
                .replace("”", "\"") \
                .replace("®", "@") \
                .replace("‘", "'") \
                .replace("’", "'") \
                .replace("‍", " ") \
                .replace("×", "x") \
                .replace("÷", "/") \
                .replace("←", "<") \
                .replace("→", ">") \
                .replace("↑", "^") \
                .replace("↓", "v") \
                .replace("±", "+-") \
                .replace("′", "'") \
                .replace("″", "\"") \
                .replace("©", "(C)") \
                .replace("®", "(R)") \
                .replace("™", "(TM)") \
                .replace("°", "deg") \
                .replace("€", "EUR") \
                .replace("£", "GBP") \
                .replace("¥", "CNY") \
                .replace("₪", "ILS") \
                .replace("₹", "INR") \
                .replace("₣", "CHF") \
                .replace("₤", "GBP") \
                .replace("₱", "PHP") \
                .replace("₲", "PYG") \
                .replace("₴", "UAH") \
                .replace("₯", "GRD") \
                .replace("₯", "GRD") \
                .replace("•", "-") \
                .replace("∞", "INF")

            return fixed_text
        except Exception:
            logger.error(f"[SendSms] replace_non_gsm7 failed:{text}", exc_info=True)
            return text

    @staticmethod
    def _replace_case(old: str, new: str, text: str) -> str:
        index = text.lower().find(old.lower())
        if index == -1:
            return text
        return SmsFilter._replace_case(old, new, text[:index] + new + text[index + len(old):])

    @staticmethod
    def check_link(content: str) -> bool:
        lower_content = content.lower()

        # 判断 t.ly/c12d 这种短链接
        words = content.split()
        for word in words:
            if '.' in word and '/' in word:
                logger.warning(f"[SendSms] contains link： {content}")
                return False

        url = re.findall(SmsFilter.link_pattern, lower_content)
        if len(url) > 0:
            logger.warning(f"[SendSms] {content} with link ### {url}")
            return False

        # 如果单词里面存在 2 个以及以上的.也判定为域名
        s = lower_content.split()
        for i in s:
            # xxx@gmail 邮箱还是要放行的
            if i.count('.') >= 2 and len(i) >= 8:
                t = i.split('.')
                # 放行这种情况："haha..."
                if '' in t:
                    continue
                # 放行邮箱，谷歌，fb，youtube
                if '@' in i or 'youtube' in i or 'google' in i or 'facebook' in i or 'apple' in i:
                    continue

                logger.warning(f"[SendSms] contains link: {content}")
                return False

        return True

    @staticmethod
    def clean_up_text(origin_content: str) -> str:
        try:
            _content = SmsFilter._add_space_after_punctuation(origin_content)
            _content = _content.strip()

            # 词组替换
            for k, v in NOT_SO_BAD_PHRASE.items():
                _content = re.sub(k, v, _content, flags=re.IGNORECASE)  # 使用正则表达式进行替换，忽略大小写

            # 去掉美元符号，太危险了
            _content = _content.replace("$", "")

            # 单词替换
            _splits = _content.replace('\n', ' ').replace('\t', ' ').split()
            new_words = []
            for s in _splits:
                is_word_ok = True
                # 大单词直接in
                for j in NOT_SO_BAD_WORDS:
                    if j.lower() in s.lower():
                        new_words.append(s.lower().replace(j.lower(), j[0] + (len(j) - 1) * '*'))
                        is_word_ok = False
                        break
                # 小单词直接in会误伤，直接==
                for j in NOT_SO_BAD_LITTLE_WORDS:
                    if j.lower() == "".join([c for c in s.lower() if c.isalpha()]):
                        new_words.append(j[0] + ((len(j) - 1) * '*'))
                        is_word_ok = False
                        break
                if is_word_ok:
                    new_words.append(s)
            return " ".join(new_words)
        except Exception:
            logger.error(f"[SendSms] clean_up_text failed: {origin_content}", exc_info=True)
            return origin_content

    @staticmethod
    def _add_space_after_punctuation(text):
        # 使用正则表达式在标点符号后面添加一个空格，但不会分开多个标点符号
        modified_text = re.sub(r'([.,;!?]+)(?![.,;!?])', r'\1 ', text)
        return modified_text

    @staticmethod
    def check_little_bad_words(origin_content: str, filtered_content: str) -> (bool, str, list):
        try:
            _content = SmsFilter._add_space_after_punctuation(origin_content)
            _content = _content.strip()

            # 检查一些不好的单词，不方便用第三方处理的单词，误杀太猛了，比如 ceo，很多词组 source of xx = sourceofxx => ceo
            _content = origin_content.replace('\n', ' ').replace('\t', ' ')
            _splits = _content.split()
            _splits = [v.strip() for v in _splits if v]

            bad_words_collection = []
            is_ok = True
            for i in _splits:
                # replace and return failed
                for j in LITTLE_BAD_WORDS:
                    if re.sub(r'[^a-zA-Z]', '', i).lower() == j.lower():
                        filtered_content = filtered_content.replace(i, "*" * len(i))
                        is_ok = False
                        bad_words_collection.append(j)

                # replace and return failed
                for j in LITTLE_BAD_UPPER_WORDS:
                    if re.sub(r'[^a-zA-Z]', '', i) == j.upper():
                        filtered_content = filtered_content.replace(i, "*" * len(i))
                        is_ok = False
                        bad_words_collection.append(j)

            if is_ok:
                return True, filtered_content, bad_words_collection
            else:
                logger.warning(f"[SendSms] check bad little words, content:{origin_content}:{bad_words_collection}")
                return False, filtered_content, bad_words_collection
        except Exception:
            logger.error(f"[SendSms] check bad little words, content:{origin_content}, failed", exc_info=True)
            return True, filtered_content, []

    @staticmethod
    def remove_emojis(text):
        try:
            # 匹配表情符号的正则表达式
            emoji_pattern = re.compile(
                "[\U0001F600-\U0001F64F"  # 表情符号 (笑脸)
                "\U0001F300-\U0001F5FF"  # 符号和图标 (天气、建筑等)
                "\U0001F680-\U0001F6FF"  # 交通工具和地图符号
                "\U0001F700-\U0001F77F"  # Alchemical Symbols
                "\U0001F780-\U0001F7FF"  # Geometric Shapes Extended
                "\U0001F800-\U0001F8FF"  # Supplemental Arrows-C
                "\U0001F900-\U0001F9FF"  # Supplemental Symbols and Pictographs
                "\U0001FA00-\U0001FA6F"  # Chess Symbols, etc.
                "\U0001FA70-\U0001FAFF"  # Symbols and Pictographs Extended-A
                "\U00002702-\U000027B0"  # 杂项符号
                "\U000024C2-\U0001F251"  # 封闭字母数字
                "]+",
                flags=re.UNICODE)

            # 替换表情符号为空字符串
            ans = emoji_pattern.sub(r'', text)
            ans = ans.strip()
            return ans
        except Exception:
            logger.error(f"[SendSms] remove_emojis failed, content:{text}", exc_info=True)
            return text


sms_filter_simple = SmsFilter()

if __name__ == '__main__':
    content_list = [
        # "Y se va",
        # "Boy fuck you fr",
        # "FUCK,LOVE,SEX.",
        # "wow, Glad!! Nice to meet you.",
        # "Do you offer full body rubs? Blow job? FULL BODY? FULLBODY?",
        # "need my service? in call or out call? Fuck,Anal,Blowjob,Oral,Bareback Doggystyle",
        # "Hello dear I'm avai***ble do you need my service what city are you in town so i can know the distance between us",
        # "I love s***ing and giving full blow jobs as well as swallowing cum. I really enjoy a*** s**, and love having my tight little a******e filled with a stiff and throbbing cock.",
        #
        # "Very good , I enjoy rimming, a***, orals, squirting, d**** and swallowing cum...",
        # "it's your fault you still on that bullshit an i told you im not with it , not finna keep getting cheated on an you don't gaf about me &. you say i ain't never gaf , rajah on bro yk i always cared so don't lie cause that's gon piss me off",
        # "Go to any store around you and get a apple gift card",
        # "200$ 1 hour fuck you ! make me cum? let's fuck harder? condom? you r a hoe fucking Are u available? still interested?  incall or outcall ? But make sex yeah just run away instead of sex fixing **** she told me she wanted a weekend to herself I clearly have been fighting with everyone to see him so it's not like I'm doing anything wrong I literally can't get out of work today was legit my second day at work you're being so fucking cruel and unfair if you ever actually loved me you would let me see my son",
        # "are you Available?",
        # "Fatpo test:  HELLO THIS IS MICHEAL JAMES FROM AMAZON COMPANY WE ARE TEXTING YOU TO INFORM YIU YOUR AMAZON WILL BE CLOSED SOON.AND IF YOU DONT WANT YOU YO BE CLOSED REPLY NO TO GET MORE ON HOW TO SECURE YOUR IN OTHER TO PREVENT FROM GETTING IT CLOSED",
        # "My name is Dakota Johnson I’m a female hooker Fuck,Anal,Creampie,Blowjob,Oral,Bareback,Doggystyle,Condom or no I charge $150 for 1hr $200 for 2hrs $300 for 4 hrs and $450 for overnight How much would you like to go for ?",
        # "Order id 214368 is still available?",
        # """
        #    Baby please forgive me I NEED YOU…. all you have to do is call them & you’ll see I wasn’t there or didn’t pay for anything. We didn’t fuck bae I don’t know what else to do here I just know we didn’t. If I did I promise I wouldn’t be doing all this I would take whatever you give me but I can’t because I didn’t fuck anyone Sunday man. all you have to do is call them & you’ll see… I asked him “what you usually get” that’s not a question you gotta ask if you’re bout to fuck, I mean come on baby you was just mad & overwhelmed but just call them…. If I got a room they will tell you KAMEISHA WILSON was there, call them. I’m telling you I didn’t have *** with him.
        #    I don’t wanna do this without you ion think I can to be honest….
        #    """,
        # """
        #     ??Located UNION SQUARE / CENTER CITY
        #
        # ???RATES???
        #
        # INCALL ??
        #
        # ?1 Hour��/��600$ GFE
        # ?30 Min /��400$
        #
        # Anal extra $ 100 ??
        #
        # Payment Cash or Zelle ??
        #
        # OUTCALL ?- (Just in hotels) Screening is required
        #
        # ?HOUR / 800 $ + UBER OR TAXI (JUST HOTELS / NOT APARTMENT OR CONDO)
        #
        # ?PAYMENT CASH OR ZELLE?
        #     """,
        # "Make me ride it until my cum is dripping down to ur balls from you making me squirt",
        "Enjoy that FBI sec over there I’m going to the bronx tomorrow after he gets on the bus",
        "Are you still naked in the shower",
        "That's the least of help you can offer atleast we speak face to face to get rid of doubt With my ipad… please help I can come meet you tomorrow then",
        "You want to be grimey bitch",
        "dumb dirty smutt ass btch i cant believe yu really dropped sum dick off yu think dat was a get back niggi ts was a drop in self respect 😭😭😭😭😭",
        "marijuana cannabis joint blunt edibles daTHCxx stash bong dope",
        "Cialis Viagra",
        "🙏🙏🙏🙏🙏🙏🙏🙏🙏🙏🙏🙏❤️❤️❤️❤️🙏🙏🙏🙏🙏❤️❤️❤️❤️❤️🙏🙏🙏🙏 🙏🙏🙏🙏🙏🙏🙏🙏🙏🙏🙏🙏❤️❤️❤️❤️🙏🙏🙏🙏🙏❤️❤️❤️❤️❤️🙏🙏🙏🙏 🙏🙏🙏🙏🙏🙏🙏🙏🙏🙏🙏🙏❤️❤️❤️❤️🙏🙏🙏🙏🙏❤️❤️❤️❤️❤️🙏🙏🙏🙏",
        "I never felt the need to meet Katy because I hold myself in high regard, I value you, and oddly enough, I have some respect for her as well. A woman who could become your lover while locking eyes with your wife represents chaos and betrayal, neither of which I embody. I am your angel 😇, filled with love and compassion. Deep down, I knew that my actions weren't right. Perhaps meeting her could have changed things, but ultimately, I've found my soulmate, my twin flame, my one true love, and I can't turn back now. Yes, we could have been friends until you gathered the strength to fully embrace your feelings. What I regret most is losing you not just once, but twice. I want you back 🦸🏻‍♂️⚡♾️🍀🌄☯️🌙🌹🍒🌈🌬️💫.",
        "Donation?",
        "cum",
        "xcumx",
        "bbbj",
        "Can I eat your ass?",
        "i will kill you. improve the skill of fxk",
        "Hello",
        "my home gurl gonna stop by after i get off to smoke with me or it can be just us or both of us ? i want to feel that dick inside of me",
        "blowjob",
    ]

    for c in content_list:
        print("#" * 50)
        print("original text:", c)
        after_replace_non_gsm7 = sms_filter_simple.replace_non_gsm7(c)
        print("after_replace_non_gsm7:", after_replace_non_gsm7)
        after_cleanup = SmsFilter.clean_up_text(after_replace_non_gsm7)
        print("after clean_up_text:", after_cleanup)
        little_rsp, after_check_little_bad_words, little_rsp_hit_bad_words = sms_filter_simple.check_little_bad_words(c,
                                                                                                                      after_cleanup)
        print("after check_little_bad_words:", little_rsp, after_check_little_bad_words, little_rsp_hit_bad_words)
        after_remove_emojis = sms_filter_simple.remove_emojis(after_check_little_bad_words)
        print("after remove_emojis:", after_remove_emojis)
