# 直接放弃的words
direct_give_up_words = [
    "Could you do Incall?",
    "Could you do outcall?",
    "car date",
    "car play",
    "trunk date",
    "trunk play",
    "quick visit",
    "qv",
    "a qv",
    "hhr",
    "hr",
    "2 hours",
    "half an hour",
    "For a hoe",
    "suck",
    "fuck",
    "fcked",
    "fucked",
    "Fucking",
    "fuck you",
    "fuck yo",
    "fuck u",
    "fxck u",
    "asshole",
    "bitch",
    "dumbass",
    "The slut",
    "slut",
    "Pussy ass bitch",
    "Pussy",
    "ass",
    "Fucking asshole",
    "u a Fucking asshole",
    "u Fucking asshole",
    "bum",  # 流浪汉
    "Deadbeat",  # 赖账的人
    "nigger",
    "nigga",
    "niggi",
    "nigg",
    "Hey babe rates",
    "Hey rates",
    "babe rates",
    "rate",
    "rates",
    "rates baby",
    "rates babe",
    "Whats your rates",
    "Deadass bitch",
    "You dumb fuck",
    "u dumb fuck",
    "Cunt",  # 女人阴部
    "Fucking dumb cunt",
    "Fuck dumb cunt",
    "what your rate",
    "what your rates",
    "whats your rate",
    "whats your rates",
    "Rates for incall",
    "Rates for outcall",
    "hi available",
    "hello available",
    "Hey available",
    "Hi Are you available?",
    "hey Are you available?",
    "hello Are you available?",
    "Hi Are u available?",
    "hey Are u available?",
    "hello Are u available?",
    "Hi r u available?",
    "hey r u available?",
    "hello r u available?",
    "you available",
    "your available",
    "U available",
    "Available hunny",
    "Available hun",
    "Available babe",
    "Available baby",
    "You available bb",
    "You available babe",
    "You available baby",
    "You available",
    "yes available",
    "now available",
    "are you available",
    "are U available",
    "R U available",
    "I'm available now",
    "am available",
    "am available now",
    "im available",
    "im available now",
    "any outcall?",
    "any incall?",
    "any outcalls?",
    "any incalls?",
    "Hey any incalls?",
    "Hey any incall?",
    "Hey any outcalls?",
    "Hey any outcall?",
    "incall",
    "outcall",
    "in or out",
    "incall or outcall",
    "incall? outcall",
    "faggot",  # 同性恋者（侮辱性）
    "bastard",  # 私生子，混蛋
    "slut",  # 婊子，荡妇
    "retard",  # 傻子，低能儿
    "asswipe",  # 蠢货，傻逼
    "douchebag",  # 混蛋，傻逼
    "fool",  # 傻子，傻逼
    "scumbag",  # 混蛋，垃圾
    "loser",  # 失败者，窝里横
    "idiot",  # 白痴，傻逼
    "dipshit",  # 傻逼
    "shitbag",  # 一堆垃圾
    "dumbass",  # 笨蛋，傻逼
    "cockhead",  # 鸡巴头，傻逼
    "clown",  # 小丑，傻逼
    "gimp",  # 傻逼，废物
    "bimbo",  # 傻女人，花瓶
    "pig",  # 猪（侮辱性）
    "jackass",  # 蠢货，傻逼
    "douche",  # 傻逼，废物
    "screw you",  # 去你妈的，滚蛋
    "fuck off",  # 滚蛋，去你妈的
    "eat shit",  # 吃屎
    "go to hell",  # 去死
    "go to die",  # 去死
    "bastards",  # 一群混蛋
    "ugly",  # 丑陋的
    "ugly ass",  # 丑陋的屁股
    "asshat",  # 蠢货，傻逼
    "whorebag",  # 贱货，妓女
    "freak",  # 怪物，怪人
    "loser",  # 失败者，窝里横
    "shithead",  # 屎头，傻逼
    "crackhead",  # 吸毒者，傻逼
    "methhead",  # 吸毒者，傻逼
    "scumbag",  # 讨厌鬼，混蛋
    "Weird ass bitch",  # 讨厌鬼，混蛋
    "fat",  # 胖子
    "pedophile",  # 恋童癖
    "bald",  # 秃头
]

direct_give_up_single_words = [
    "outcall",
    "outcalls",
    "incall",
    "incalls",
    "qv",
]


class DirectGiveUpWordsFilterUtil:
    @staticmethod
    def check_direct_give_up_words(text: str) -> bool:
        origin_text = text

        text = text.lower().replace("\n", " ")
        text = "".join([c for c in text if c.isalpha()])

        # 把他们都压缩后，一一匹配
        for i in direct_give_up_words:
            match = i.lower().replace(" ", "")
            match = "".join([c for c in match if c.isalpha()])
            if text == match:
                return True

        # 哪怕命中一个，就干掉：qv, incall, outcall
        words = origin_text.split(" ")
        words = [w.lower() for w in words]
        for i in direct_give_up_single_words:
            if i.lower() in words:
                return True

        # 带 fuck you xxx 的都干掉
        if DirectGiveUpWordsFilterUtil.check_direct_give_up_words_v2(origin_text):
            return True

        # 招嫖的直接干掉：带available,rates的短句子都干掉
        if DirectGiveUpWordsFilterUtil.check_direct_give_up_words_v3(origin_text):
            return True

        # 招嫖的直接干掉：i do 800 这种招嫖的
        if DirectGiveUpWordsFilterUtil.check_if_i_do_xxx(origin_text):
            return True

        # 直接报金钱的
        if DirectGiveUpWordsFilterUtil.check_if_money(origin_text):
            return True

        return False

    @staticmethod
    def check_direct_give_up_words_v2(text: str) -> bool:
        text = text.lower().replace("\n", " ")
        text = "".join([c for c in text if c.isalpha() or c == ' '])
        words = text.split()

        # 如果命中 fuck you xxx
        if len(words) == 3 and words[0] == 'fuck' and words[1] == "you":
            return True

        return False

    @staticmethod
    def check_direct_give_up_words_v3(text: str) -> bool:
        text = text.lower().replace("\n", " ")
        text = "".join([c for c in text if c.isalpha() or c == ' '])
        words = text.split()

        # 如果命中 you available?
        if len(words) <= 5:
            if "avail" in words:
                return True
            if "available" in words:
                return True
            if "rates" in words:
                return True

        return False

    @staticmethod
    def check_if_i_do_xxx(text: str) -> bool:
        # 抓到 i do 800 这种招嫖的
        words = text.strip().split()
        if len(words) != 3:
            return False
        if words[0].lower() == "i" and words[1].lower() == 'do' and words[2].isdigit():
            return True
        return False

    @staticmethod
    def check_if_money(text: str) -> bool:
        # 抓到 "$20,000" "$800" "78,932"
        if "$" in text and "," in text:
            text = text.replace("$", "").replace(",", "")
            if text.isdigit():
                return True
        if "$" in text:
            text = text.replace("$", "")
            if text.isdigit():
                return True
        if "," in text:
            text = text.replace(",", "")
            if text.isdigit():
                return True
        return False


if __name__ == '__main__':
    content_list = [
        ("Hi", False),
        ("hello?", False),
        ("I’m sorry", False),
        ("how are you ?", False),
        ("please Unblock me", False),
        ("Set YOUR alarm", False),
        ("fuck you", True),
        ("suck", True),
        ("fuck", True),
        ("Fucking asshole!!", True),
        ("fck.ed", True),
        ("fuck you haha", True),
        ("fuck you haha a", False),
        ("What's your rates", True),
        ("$800", True),
        ("$80,000", True),
        ("80,000", True),
        ("2000", False),
        ("I'm available", True),
        ("You available", True),
        ("incall?", True),
        ("outcall?", True),
        ("in or out?", True),
        ("incall ? outcall?", True),
        ("ugly?", True),
        ("whats your rates?", True),
        ("what your rates?", True),
        ("what your rate?", True),
        ("qv?", True),
        ("quick visit", True),
        ("I'm available now", True),
        ("weird ass bitch ", True),
        ("fat", True),
        ("nigger", True),
        ("bitch", True),
        ("Could you do Incall!!", True),
    ]
    success_cnt = 0
    failed_cnt = 0
    for (content, should_hit) in content_list:
        print("#" * 50)
        rsp_hit_direct_words = DirectGiveUpWordsFilterUtil.check_direct_give_up_words(content)
        if should_hit != rsp_hit_direct_words:
            print(content)
            print(content)
            print(content)
            print(rsp_hit_direct_words)
