import re

import langid
from langdetect import detect

from SecPhone.settings import logger


class SmsEnglishFilter:
    @staticmethod
    def is_chinese(text: str) -> bool:
        try:
            # 一些特征很明显的，相信它的判断
            lang = detect(text)
            print(lang)
            if str(lang).lower() in ["zh-cn", "zh-tw", "zh-hk", "zh-mo", "zh-sg", "zh-my", "zh", "ja", "ko"]:
                return True
            return False
        except Exception as e:
            if "No features in text" in str(e):
                logger.info(f"[SmsEnglishFilter] is_chinese, {text} failed: No features in text")
            else:
                logger.error(f"[SmsEnglishFilter] is_chinese {text} failed", exc_info=True)

            return False

    @staticmethod
    def is_english(text: str) -> bool:
        try:
            """
            “es”：代表西班牙语（Spanish），是西班牙的官方语言，同时也是拉丁美洲众多国家（如墨西哥、阿根廷、哥伦比亚等）广泛使用的语言。
            “fr”：代表法语（French），是法国的官方语言，并且在加拿大的魁北克省等部分地区以及众多非洲国家（像塞内加尔、科特迪瓦等曾是法国殖民地的国家）也被广泛使用。
            “sl”：代表斯洛文尼亚语（Slovenian），是斯洛文尼亚共和国的官方语言。
            “pt”：代表葡萄牙语（Portuguese），葡萄牙的官方语言，也是巴西等多个葡语国家（如安哥拉、莫桑比克等曾为葡萄牙殖民地的国家）通用的语言。
            “nl”：代表荷兰语（Dutch），荷兰的官方语言，在比利时的弗拉芒大区等地也被广泛使用。
            “et”：代表爱沙尼亚语（Estonian），是爱沙尼亚共和国的官方语言。
            """
            # 一些特征很明显的，相信它的判断
            lang = detect(text)
            if str(lang).lower() in ["zh-cn", "zh-tw", "zh-hk", "zh-mo", "zh-sg", "zh-my", "zh", "ja", "ko", "fa", "ar",
                                     "ru"]:
                logger.info(f"[SendSms] checkEng, lang:{lang}, text is non-english:[{text}]")
                return False

            # 太短的单词容易预判
            if len(text.split()) <= 6:
                logger.info(f"[SendSms] checkEng, too short:[{text}], return is eng")
                return True

            # 一些特征很明显的，相信它的判断，并且词组长度达到的
            lang = detect(text)
            if str(lang).lower() in ["vi"]:
                logger.info(f"[SendSms] checkEng, lang:{lang}, text is non-english:[{text}]")
                return False

            if lang in ["es", "fr", "sl", "pt", "nl"]:
                detected_language = langid.classify(text)[0]
                if detected_language == "en":
                    logger.info(f"[SendSms] checkEng, lang:{lang}, "
                                f"but double check is {detected_language}, text is english:[{text}]")
                    return True

                logger.info(f"[SendSms] checkEng, lang:{lang}, "
                            f"double check:{detected_language}, text is non-english:[{text}]")
                return False

            if SmsEnglishFilter.detect_language(text) in ['en', 'unknown']:
                logger.info(f"[SendSms] checkEng, lang:{lang}, text:[{text}] is English")
                return True
            else:
                logger.info(f"[SendSms] checkEng, lang:{lang}, text:[{text}] is non-English")
                return False
        except Exception as e:
            if "No features in text" in str(e):
                logger.info(f"[SendSms] checkEng, [{text}] failed: No features in text")
            else:
                logger.error(f"[SendSms] checkEng, [{text}] failed", exc_info=True)
            return True

    @staticmethod
    def detect_language(text):
        alphabet_sets = {
            'en': set('1234567890abcdefghijklmnopqrstuvwxyz ?!’'),
            # 添加更多语言的字母集合
        }

        # 去除空格和标点符号
        text = re.sub(r'[^\w\s]', '', text).replace(" ", "").replace("\n", "").replace("\t", "")

        text = text.lower()
        char_set = set(text)

        for lang, alphabet in alphabet_sets.items():
            # 计算文本中字符集合和语言字母集合的交集与文本字符集合的比例
            subset_ratio = len(char_set.intersection(alphabet)) / len(char_set)
            if subset_ratio >= 0.8:  # 如果交集比例超过 90%
                return lang

        return 'unknown'  # 如果没有匹配的语言字母集合，返回未知


if __name__ == '__main__':

    content_list = [
        ("Hi bạn, bạn có phải Isabella ko? Mình có zelle cho bạn 1000 tên Trang Nguyen. Bạn đã nhận dc chưa?", False),
        ("Digo yo dammm pero bueno ni modo así eres de terca y boba verdad", False),
        ("Dime voy a tener problemas si te pasa algo Jajaja déjame voy Areir jajajaa", False),
        ("Ve acá no me gusta Tex porfabor", False),
        ("Desbmokeame del número quiero decirte algo y esto es algo tuyo", False),
        ("Why is it 200 hundred dollars now. Say Dollars in English please where in The United States of America",
         True),
        ("just stay far tf away because i’m done fr don’t call because i changed my shìt since you wanted to keep "
         "blocking", True),
        ("hi, how are you?", True),
        ("中国人吗，怎么说啊，兄弟？?", False),
        ("nice u went to see veld lol", True),
        ("You got 10 babe its not okay", True),
        ("Let me take my nap 😴 honey 💋❤️", True),
        ("Fuck you Areesha I hope you die️", True),
        ("안녕하세요, 어떠세요?", False),
        ("こんにちは、元気ですか？", False),
        ("Bonjour, comment ça va ? Cela fait longtemps que je n'ai pas reçu de tes nouvelles.", False),
        ("صندوق فردا ساعت ۱۱ تحویل میدن", False),
        ("Si tú no amas a mi hermana díselo se sincero con ella no juegues con sus sentimientos por favor ella no lo "
         "merece", False),
        ("Hi\nBeen the longest since we spoke. My phone got stolen, I wasn’t around around anyone to help me recovery "
         "my pass data, but I just did and I sent you a WhatsApp message", True),
        ("Can you let me come see you now Jeff", True),
        ("Just like dollar general so u can go video chat", True),
        ("Ya les dijistes que cojes con la luz apagada para que no te vean puerquita ?", False),
        ("En navidad yo estare con el que si me ama y de verdad me quiere se los presentare a mi familia y Ami mama "
         "ese si vale la pena es más el si fue al velorio de mi hermano no lo presente no era el momemto pero en "
         "navidad si lo hare y te juro or dios k PUra Verga te vuelvo a contestar me hablas me ruegas pero mientras "
         "yo estoy con Otro vato y lo sabes te Doy la oportinida", False),
        (
            "it don't matter he gone get hurt regardless", True
        )
    ]
    for c, need_rsp in content_list:
        res = SmsEnglishFilter.is_english(c)
        if need_rsp != res:
            print("#" * 50)
            print(c)
            print(res)
