import hashlib
import json
import random
import time

import requests
import requests.adapters
from gmssl import sm3, func

from SecPhone import settings
from SecPhone.settings import logger


class ImageFilterWangyi:

    def __init__(self):

        self.secret_id = "98ace006f700f24d97b39de009cc7e2d"
        self.secret_key = "e0670e083266908a61c4ab17ff12acd6"
        self.business_id = "66a907100eee075bd3128dbce2e5ad25"
        # self.API_URL = "http://as.dun.163.com/v5/image/check" # 国内
        self.API_URL = "http://as-image-virginia.dun.163.com/v5/image/check"  # 美东
        self.VERSION = "v5.1"
        self.wy_sessions = self.__get_http_session(4, 10, 3)
        self.totally_prohibited_sub_labels = (10001, 10003, 10004, 10006, 10009)

    def __get_http_session(self, pool_connections, pool_maxsize, max_retries):
        session = requests.Session()
        # 创建一个适配器，连接池的数量pool_connections, 最大数量pool_maxsize, 失败重试的次数max_retries
        adapter = requests.adapters.HTTPAdapter(pool_connections=pool_connections,
                                                pool_maxsize=pool_maxsize, max_retries=max_retries)
        # 告诉requests，http协议和https协议都使用这个适配器
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        return session

    def __gen_signature(self, params=None):
        buff = ""
        for k in sorted(params.keys()):
            buff += str(k) + str(params[k])
        buff += self.secret_key
        if "signatureMethod" in params.keys() and params["signatureMethod"] == "SM3":
            return sm3.sm3_hash(func.bytes_to_list(bytes(buff, encoding='utf8')))
        else:
            return hashlib.md5(buff.encode("utf8")).hexdigest()

    def _get_ocr_details(self, ocr_details: dict) -> (str, bool, list, list):
        is_ocr_reject = False
        ocr_contents = []
        reject_labels = []  # 记录命中的关键词
        alert_labels = []  # 记录告警的关键词

        reject_keywords = {
            "$", "money", "nude", "nudes", "news", "fbi", "police", "dollar", "order", "pills", "bank", "card",
            "call us", "publish", "sir", "sincerely", "urgent", "visa",
            "charged", "criminal", "crime", "code", "payment", "this man", "this guy",
            "walmart", "balance", "@", "transaction",
            "robbery", "cashier", "statement", "company", "credit", "credit card",
            "purchase", "player", "game", "champion", "debit", "winner", "winning",
            "greeting", "congratulation", "award", "if you see him", "enjoy it alone",
            "system", "united", "states", "federal", "reserve",
            "transaction", "transactions", "lotery", "lottery", "lotteries", "f.b.i", "agent",
            "label", "mail", "this is", "lives at", "arrested", "family", "yrs old", "yr sold", "years old",
            "year sold", "living alone", "online sex", "spread his", "ashamed",
            "job", "jobs", "interested", "ftc", "office", "federal",
            "legal", "law", "notice", "wells", "fargo", "paypal", "norton", "renew", "information", "invoice",
            "amount", "noticed", "LLC", "processed", "reference", "sale"
        }

        alert_keywords = {"fbi", "ftc", "news", "lotery", "lottery", "lotteries", "arrested", "nude", "lives at",
                          "living alone", "online sex", "spread his", "ashamed", "federal", "agent", "legal", "law",
                          "notice", "deposited",
                          }

        for orc_item in ocr_details.get("details", []):
            ocr_content = orc_item.get('content', "").lower()
            ocr_contents.append(ocr_content)

            # 查找命中的关键词
            matched_keywords = [kw for kw in reject_keywords if kw in ocr_content]
            if matched_keywords:
                is_ocr_reject = True
                reject_labels.extend(matched_keywords)

            # 查找命中的关键词
            matched_keywords_alert = [kw for kw in alert_keywords if kw in ocr_content]
            if matched_keywords_alert:
                is_ocr_reject = True
                alert_labels.extend(matched_keywords_alert)

        return " ".join(ocr_contents), is_ocr_reject, reject_labels, alert_labels

    def __request_wangyi(self, user: str, image_url: str) -> (str, int, int, int, str, str, bool, str, bool):
        # return = task_id, suggestion, label, sub_label, details, orc_contents, is_ocr_reject, ",".join(reject_labels), check_status
        params = {
            "account": settings.EMAIL_PREFIX,
            "images": json.dumps([{"name": image_url, "type": 1, "data": image_url}]),
            "secretId": self.secret_id,
            "businessId": self.business_id,
            "version": self.VERSION,
            "timestamp": int(time.time() * 1000),
            "nonce": int(random.random() * *********),
        }

        params["signature"] = self.__gen_signature(params)

        try:
            r = self.wy_sessions.post(self.API_URL, data=params, timeout=10)
            res = r.json()
            logger.info(f'[SendSms] mms, image_url={image_url}, res={res}')
            antispam = res["result"][0]['antispam']
            suggestion = antispam.get('suggestion', -1)  # -1 是异常值
            task_id = antispam['taskId']
            check_status = antispam.get('status')  # 检测状态：2：检测成功，3：检测失败（ 建议对检测失败状态的数据进行重试，仅针对检测成功状态下的数据结果做业务逻辑处置 ）
            if check_status != 2:
                logger.error(f"[SendSms] mms, user:{user}, image_url={image_url}, check_status={check_status}")
                return task_id, suggestion, -1, -1, "", "", False, "", False

            # 获取ocr
            orc_contents, is_ocr_reject, reject_labels, alert_labels = self._get_ocr_details(
                res["result"][0].get('ocr', {}))
            logger.info(
                f"[SendSms] mms, user:{user},orc_contents:{orc_contents}, is_ocr_reject:{is_ocr_reject}, reject_labels:{reject_labels}, alert_labels:{alert_labels}")

            if alert_labels:
                logger.error(f"[SendSms] user:{user}, 图片告警！可能诈骗短信！alert_labels:{alert_labels}, "
                             f"ocr内容：{orc_contents}, image: {image_url}, reject_labels: {reject_labels}")

            # 解析label
            label_items = antispam["labels"]
            filtered_label_items = []
            for li in label_items:
                # li.label 分类信息，100：色情，110：性感低俗，200：广告，210：二维码，260：广告法，300：暴恐，400：违禁，500：涉政，800：恶心类，900：其他，1100：涉价值观
                # li.level 命中级别，示例值：1：嫌疑，2：不通过，0：正常
                # li.rate 置信度分数，0-1之间取值，1为置信度最高，0为置信度最低。若level为正常，置信度越大，说明正常的可能性越高。若level为嫌疑或不通过，置信度越大，说明垃圾的可能性越高
                # 正常的label放弃
                if li["rate"] > 0.9 and li["level"] == 0:
                    continue
                filtered_label_items.append(li)

            # ocr最高优先级，如果有问题，就直接reject
            if is_ocr_reject:
                return task_id, suggestion, 0, 0, "", orc_contents, True, ",".join(reject_labels), True

            # 说明没有异常的label，是正常图片
            if len(filtered_label_items) == 0:
                return task_id, suggestion, 0, 0, "", "", False, "", True

            # 对 filtered_sub_labels 排序，按 sl.rate 从高到低
            filtered_label_items.sort(key=lambda x: x["rate"], reverse=True)
            label_item = filtered_label_items[0]
            label = label_item["label"]

            sub_label = None
            sub_labels = label_item.get('subLabels', [])

            # 一般情况下拿最高分的
            if len(sub_labels) > 0:
                sub_label_item = sub_labels[0].get('subLabel', None)
                sub_label = int(sub_label_item) if sub_labels is not None else None

            # 但如果有同时命中的，看看是不是有更高优先级的。比如女下体>女臀部
            if len(sub_labels) > 1:
                for i in sub_labels:
                    if i["rate"] > 0.5 and int(i["subLabel"]) in self.totally_prohibited_sub_labels:
                        sub_label = int(i["subLabel"])

            details = json.dumps(label_item.get('subLabels', []), ensure_ascii=False)
            logger.info(f'[SendSms] mms, user:{user}, task_id={task_id}, suggestion={suggestion}, label={label}, '
                        f'sub_label={sub_label}, details={details}')

            return task_id, suggestion, label, sub_label, details, orc_contents, is_ocr_reject, ",".join(
                reject_labels), True

        except Exception as e:
            logger.error(f"[SendSms] mms,user:{user}, call wangyi api error, params: {params}", exc_info=True)
            return None, None, None, None, "", "", False, "", False

    def __check_if_pass(self, suggestion, label, sub_label) -> bool:
        """
        图片subLabel细分类编码对照表
        API: https://support.dun.163.com/documents/588434277524447232?docId=802401923794096128

        wangyi image check lable:
        100：色情，110：性感低俗，200：广告，210：二维码，260：广告法，300：暴恐，400：违禁，500：涉政，800：恶心类，900：其他，1100：涉价值观

        sub_label:
        10000	色情	若命中图片中文字检测策略，可返回文字线索信息
        10001	女下体	-
        10002	女胸	-
        10003	男下体	-
        10004	性行为	-
        10005	臀部	-
        10006	口交	-
        10007	卡通色情	-
        10008	色情人物	人物名
        10009	儿童色情	-
        11000	性感低俗	-
        11001	亲吻	-
        11002	腿部特写	-
        110	性感低俗	11003	图片模型-非漏点赤膊
        110	性感低俗	11004	图片模型-女胸
        110	性感低俗	11005	图片模型-内衣裤
        110	性感低俗	11006	图片模型-女性漏沟
        110	性感低俗	11007	图片模型-男性露点赤膊
        110	性感低俗	11008	图片模型-女性露背
        110	性感低俗	11009	图片模型-男性露背
        110	性感低俗	11010	图片模型-臀部低俗
        110	性感低俗	11011	图片模型-裆部
        110	性感低俗	11012	图片模型-吐舌
        110	性感低俗	11013	图片模型-足部特写
        110	性感低俗	11014	图片模型-高召回露沟
        110	性感低俗	11015	图片模型-床上场景
        110	性感低俗	11016	图片模型-摸胸
        110	性感低俗	11017	图片模型-胸部非裸露特写
        110	性感低俗	11018	图片模型-内衣裤摆拍
        110	性感低俗	11019	图片模型-高召回足部特写
        110	性感低俗	11020	图片模型-亲密CP形象
        110	性感低俗	11021	图片模型-非衣物包裹女性赤膊
        110	性感低俗	11022	图片模型-儿童性感
        110	性感低俗	110316	内衣裤摆拍
        20000	广告	若命中图片中文字检测策略，可返回文字线索信息
        21000	二维码	若命中图片中文字检测策略，可返回文字线索信息
        30000	暴恐	若命中图片中文字检测策略，可返回文字线索信息
        30001	暴恐图集	-
        30002	暴恐旗帜	旗帜名
        30003	暴恐人物	人物名
        30004	暴恐标识	标识名
        30005	暴恐场景	场景名
        40000	违禁	若命中图片中文字检测策略，可返回文字线索信息
        40001	违禁图集	-
        40002	违禁品	违禁品名
        40003	特殊标识	标识名
        40004	血腥模型	-
        40005	公职服饰	服饰名
        40006	不文明	不文明行为
        40007	违禁人物	人物名
        40008	违禁场景	场景名
        40009	火焰	-
        40010	骷髅	-
        40011	货币	-
        40012	毒品	-
        50000	涉政	若命中图片中文字检测策略，可返回文字线索信息
        50001	涉政图集	-
        50002	中国地图	-
        50003	涉政人物	人物名
        50004	涉政旗帜	旗帜名
        50005	涉政标识	标识名
        50006	涉政场景	场景名
        90000	其他	-
        90002	自定义用户名单	名单列表
        90003	自定义IP名单	名单列表


        SMS_SUGGESTION_OK = 0
        SMS_SUGGESTION_SUSPEND = 1
        SMS_SUGGESTION_INVALID = 2

        :param suggestion:
        :param label:
        :return:
        """
        # 无论坏不怀疑，只要涉及就给他 ban 了
        if label in [100, 110, 200, 210, 260, 300, 400, 500, 800, 900]:  # wangyi fail
            return False

        # 不大露骨的女孩子图片，也能接受
        if label == 110 and sub_label in [11000, 11001, 11002, 11003, 11005, 11006, 11007]:
            return True

        # 违禁品，只要涉及就给他 ban 了
        if label in [400, 500]:  # wangyi fail
            return False

        # 一些细分场景也不能要
        if sub_label in [10000, 10001, 10003, 10004, 20000, 21000, 30000, 10009]:
            return False

        # 无论是什么，只要怀疑或者命中，就pass
        if suggestion in [1, 2]:
            return False

        # 因为一些身份证、证件照还是返回500，文字线索，暂时没什么好办法
        if label == 500:
            return False

        return True

    def check_image_ok(self, user: str, image_url: str) -> (bool, str, str):
        try:
            t1 = time.time()
            task_id, suggestion, label, sub_label, details, orc_contents, is_ocr_reject, reject_labels, check_status = self.__request_wangyi(
                user, image_url)
            logger.info(f"[SendSms] mms, user:{user}, task:{task_id}, image_url:{image_url},"
                        f"suggestion:{suggestion}, label:{label}, sub_label:{sub_label}, details:{details}, "
                        f"is_ocr_reject:{is_ocr_reject}, orc_contents:{orc_contents}, check_status:{check_status}")

            if not check_status:
                logger.error(f"[SendSms] mms, user:{user}, wangyi check failed, task:{task_id}, image_url:{image_url}")
                return False, "", ""

            if task_id is None:
                logger.error(f"[SendSms] mms, user:{user}, wangyi says invalid, task:{task_id}, image_url:{image_url}")
                return False, "", ""

            if is_ocr_reject:
                logger.warning(
                    f"[SendSms] mms, user:{user}, wangyi says invalid, task:{task_id}, image_url:{image_url}, "
                    f"orc_contents:{orc_contents}, reject!")
                return False, task_id, f"ocr_reject_{reject_labels}"

            check_info = {
                "task_id": task_id,
                "suggestion": suggestion,
                "label": label,
                "sub_label": sub_label,
                "details": details,
            }

            combine_res = f"{suggestion}_{label}_{sub_label}"

            res = self.__check_if_pass(suggestion, label, sub_label)
            t2 = time.time()
            if res:
                logger.info(
                    f"[SendSms] mms, user:{user}, wangyi says ok, image_url:{image_url}, task:{task_id}, "
                    f"check_info:{check_info}, "
                    f"total cost: {int((t2 - t1) * 1000)} ms")
            else:
                logger.warning(
                    f"[SendSms] mms, user:{user}, wangyi says not ok, image_url:{image_url}, task:{task_id}, "
                    f"check_info:{check_info}, "
                    f"total cost: {int((t2 - t1) * 1000)} ms")

            return res, task_id, combine_res
        except Exception:
            logger.error(f"[SendSms] mms, user:{user}, wangyi says invalid, image_url:{image_url}", exc_info=True)
            return False, "", ""


sms_filter_image = ImageFilterWangyi()

if __name__ == '__main__':
    # print('正常对话',
    #       sms_filter_image.check_image_ok("123",'http://phone2.zehougroup.xyz/uploads/2024-11-28/1732753756_Cy1nxfuW.jpeg'))
    # print('FBI图片',
    #       sms_filter_image.check_image_ok("123",'http://phone2.zehougroup.xyz/uploads/2024-12-12/1733968322_8jcKrWz0.jpeg'))
    # print('药物广告',
    #       sms_filter_image.check_image_ok("123",'http://phone2.zehougroup.xyz/uploads/2024-12-16/1734373548_Ua8crGCF.jpeg'))
    # print('诈骗文字',
    #       sms_filter_image.check_image_ok("123",'http://phone2.zehougroup.xyz/uploads/2024-12-22/1734911045_rcFws083.jpeg'))
    # print('别人的身份证',
    #       sms_filter_image.check_image_ok("123",'http://phone2.zehougroup.xyz/uploads/2024-12-22/1734909633_BGbcQSRC.jpeg'))
    # print('黑人小伙站着',
    #       sms_filter_image.check_image_ok("123",'http://phone2.zehougroup.xyz/uploads/2025-01-26/1737858120_jsVit8SE.jpeg'))
    # print("银行卡", sms_filter_image.check_image_ok("123",
    #     "https://tlnx-mms-media.s3.us-east-1.amazonaws.com/mms/e5e35504ba9410c08413e66bb559f14a/624ec993591d830cc986edbe94d2440597912a9c59dc30f627cdf77f6a03966c.jpeg"))
    # print("银行金额",
    #       sms_filter_image.check_image_ok("123","https://phone2.zehougroup.xyz/uploads/2025-02-01/1738429246_tfyiIN3P.jpeg"))
    # print("法律法规罚款",
    #       sms_filter_image.check_image_ok("123","http://phone2.zehougroup.xyz/uploads/2025-02-02/1738460397_nN8gYKQw.jpeg"))
    # print("发帖网暴",
    #       sms_filter_image.check_image_ok("123","https://api.v3phone.xyz/uploads/2025-02-01/1738377798_wBP4CQNS.jpeg"))
    # print("visa",
    #       sms_filter_image.check_image_ok("123","https://phone2.zehougroup.xyz/uploads/2025-02-02/1738472696_DG1royhj.jpeg"))
    # print("比赛结果",
    #       sms_filter_image.check_image_ok("123","https://api.v3phone.xyz/uploads/2025-01-31/1738357472_0dfgSwBK.jpeg"))
    # print("美元",
    #       sms_filter_image.check_image_ok("123","https://phone2.zehougroup.xyz/uploads/2025-02-07/1738891428_0ihLQ9of.jpeg"))
    # print("敲诈",
    #      sms_filter_image.check_image_ok("123","https://phone2.zehougroup.xyz/uploads/2025-04-19/1745028393_XyelaLpn.jpeg"))
    # print("伪装政府文件",
    #       sms_filter_image.check_image_ok("123","https://phone2.zehougroup.xyz/uploads/2025-04-25/1745623297_RHqOer81.jpeg"))
    # print("FBI证件",
    #       sms_filter_image.check_image_ok("123","http://phone2.zehougroup.xyz/uploads/2025-05-18/1747579648_Lcrz7bSe.jpeg"))
    # print("FBI的人办公（解析失败版本）",
    #      sms_filter_image.check_image_ok("123","http://phone2.zehougroup.xyz/uploads/2025-05-21/1747849350_r5jdvIX0.jpeg"))
    # print("威胁别人（解析失败版本）",
    #       sms_filter_image.check_image_ok("123","http://phone2.zehougroup.xyz/uploads/2025-06-18/1750287007_eYQtchqP.jpeg"))
    # print("白色粉末",
    #       sms_filter_image.check_image_ok("123","http://phone2.zehougroup.xyz/uploads/2025-06-18/1750281722_Eenf6vIh.jpeg"))
    # print("招聘", sms_filter_image.check_image_ok("123","http://phone2.zehougroup.xyz/uploads/2025-06-18/1750278776_bJAFwuR9.jpeg"))jpeg
    # print("FBI,FTC",
    #      sms_filter_image.check_image_ok("123","https://phone2.zehougroup.xyz/uploads/2025-06-18/1750261925_xv5VeIqD.jpeg"))
    # print("法律警告文书", sms_filter_image.check_image_ok("123","https://api.v3phone.xyz/uploads/2025-06-18/1750221527_XFZEVcLO.jpeg"))
    # print("货币",
    #       sms_filter_image.check_image_ok("123","https://api.v3phone.xyz/uploads/2025-06-18/1750232698_w85YIvgU.jpeg"))
    # print("网站上色情",
    #      sms_filter_image.check_image_ok("123",
    #                                      "https://api.v3phone.xyz/uploads/2025-06-19/1750341446_ZkK3CSdI.jpeg"))
    print("网站色情", sms_filter_image.check_image_ok("123",
                                                      "http://phone2.zehougroup.xyz/uploads/2025-07-09/1752103209_XCsFN2MQ.jpeg"))
