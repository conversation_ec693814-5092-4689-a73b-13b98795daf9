# coding=utf-8
import json
import re
import time

from SecPhone.settings import logger
from Sms.ai.zhipu_client import ZhiPuClient
from Sms.filters import filter_constant

origin_prompt = """
你是一个专业的短信内容审查助手，负责帮助中间商判断预处理后的短信内容是否是属于[敲诈勒索]的分类？

### 语言支持:
1. **主要语言**:英语。
2. **次要语言**:西班牙语、法语、韩语、越南语、中文、德语等主流语言。

### 短信内容:
[预处理后的短信内容]

### 输出格式（JSON）:
{
  "result": "blackmail/non-blackmail/unknown",
  "reason": "简要说明原因",
}
"""


class ZhipuBlackMailUtil:
    @staticmethod
    def is_content_blackmail(original_content: str) -> (bool, str):
        original_content = original_content.replace("\n", " ").replace("\r", " ").strip()
        logger.info(f"[SendSms] zhipu-ai-blackmail, original_content: [{original_content}] checking...")

        try:
            if not original_content:
                logger.error(f"[SendSms] zhipu-ai-blackmail, param invalid, sms_content: [{original_content}]")
                return True, ""
            prompt = origin_prompt
            # prompt = prompt.replace("[用户输入的原始短信内容]", original_content)
            prompt = prompt.replace("[预处理后的短信内容]", original_content)

            start = time.time()
            client, client_name = ZhiPuClient.get_zhipu_fatpo_client()
            logger.info(f"[SendSms] zhipu-ai-blackmail, client: {client_name}")

            response = client.chat.completions.create(
                model="GLM-4-Air-0111",  # GLM-4-Air-0111	高性价比	128K	0.0005 元 / 千tokens
                messages=[
                    {"role": "user", "content": prompt},
                ],
                user_id="51081731492666544",
                max_tokens=512,
            )
            answer = response.choices[0].message
            logger.info(
                f"[SendSms] zhipu-ai-blackmail, query:[[{original_content}]], cost: {round(time.time() - start, 2)} s, {answer}")

            ans_content = json.loads(answer.to_json())['content']
            ans_content = ans_content.replace("\n", "").replace("\\n", "") \
                .replace("```", "").replace("json", "").strip()

            try:
                # 只要json
                match = re.search(r'\{(.*?)}', ans_content)
                if match:
                    ans_content = match.group(0)

                ans = json.loads(ans_content)
            except Exception:
                logger.error(
                    f"[SendSms] zhipu-ai-blackmail says json invalid, {ans_content}, query:[{original_content}]")
                ans = {"result": "unknown", "reason": "json invalid"}

            reason = ans['reason']
            if str(ans['result']).lower() == "non-blackmail":
                logger.info(f"[SendSms] zhipu-ai-blackmail says pass, {ans}, query:[{original_content}]")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
            elif str(ans['result']).lower() == "unknown":
                logger.warning(f"[SendSms] zhipu-ai-blackmail says unknown, {ans}, query:[{original_content}]")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
            elif str(ans['result']).lower() == "blackmail":
                logger.warning(f"[SendSms] zhipu-ai-blackmail says review, {ans}, query:[{original_content}]")
                return filter_constant.AI_RSP_INVALID_CONTENT, reason
            else:
                logger.error(f"[SendSms] zhipu-ai-blackmail says unknown, {ans}, query:[{original_content}]")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
        except Exception as ex:
            if "并发数过高" in str(ex) or "qps" in str(ex).lower():
                logger.warning(f"[SendSms] zhipu-ai-blackmail says failed, over qps, {original_content}")
            elif "系统检测到" in str(ex):
                logger.warning(f"[SendSms] zhipu-ai-blackmail says failed, 系统检测到, {original_content}",
                               exc_info=True)
                return filter_constant.AI_RSP_INVALID_CONTENT, "zhipu-invalid-input"
            else:
                logger.error(f"[SendSms] zhipu-ai-blackmail says failed [{original_content}]", exc_info=True)
            return filter_constant.AI_RSP_UNKNOWN, "zhipu-failed"


if __name__ == '__main__':
    content_list = [
        (
            """Cher Monsieur René,

Je me permets de vous contacter en ma qualité de conseiller financier en charge du dossier. Nous avons le plaisir de 
yous informer qu'une de nos clientes a décidé de vous faire don de son capital d'un montant de 10 000 000 $ détenu 
auprès de notre institution.

Ce transfert de fonds est en cours de traitement, et nous vous invitons à prendre contact avec nous afin de finaliser 
les démarches nécessaires pour la réception de ce don.

N'hésitez pas à me contacter pour toute information complémentaire ou pour convenir d'un rendez-vous afin de 
formaliser les étapes suivantes.

Dans l'attente de votre retour.""",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "She trying to hold on to you she know she lost a damn good man n she know ones she let you go it’s over "
            "for it that’s why she trying to keep you",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Go and look for $120 before 12pm so I can be at the court house on or before 2pm which is the stipulated "
            "hearing time",
            filter_constant.AI_RSP_VALID_CONTENT,
        ),
        (
            "Little white trash!",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Call me",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "You can block me but this payment is not in company anymore it on hold right and it won’t complete "
            "without take that step",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "We have tender all her information to FBI and they’ll do a good job job about it this week",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Then she told you that you will receive payment of $32,500 that is not your payment all you have to "
            "receive here is $52,500",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "I will let you know when they’re arrested her and I will send you videos of her",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "I just miss you Sade",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            """Hi Cheryl, I hope you're doing well. I work with Deepak Nathani, a real estate agent at EXP Realty. I 
            noticed your property at 14502 Clover Hill Terrace Bowie, MD 20720 is listed as ""For Sale by Owner."" I 
            believe we can help you sell it faster and at an excellent price. Our fee is only 1% of the sale price, 
            with no additional costs. Would you be open to a quick chat?Feel free to text me, and I’ll connect you 
            with Deepak at ************.Looking forward to hearing from you!""",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Good morning! This is Enterprise Rent A Car. Your insurance company has set up a reservation for you to "
            "pick up a rental vehicle. Please give us a call to schedule a date and time for your arrival. Thank you! ",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Hello , This is James . I found your portfolio interesting on Childrensillustrators. I have a project I "
            "will like you to illustrate . Plz text me back	",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Good morning, Steven. I’ m so soryy, but I’ll be at your place tomorrow. Will call you later:driving now",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Not the company is fully back to work and your package is ready for delivery",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "fuck you asshole",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            """Greetings, You have been selected to pick cotton at the nearest plantation.Be ready at 12AM NOVEMBER 
            13 SHARP with your belongings. Our Executive Slaves will come get you in a Brown Van, be prepared to be 
            searched down once you've enter the plantation. You are in Plantation Group A.""",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "120 hhr, 160 hr",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "um ima suck yo dick whenever you want me to and ima show my pussy whenever you wanna see it but not rn "
            "because my period just came on today",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Greetings from Worldwide Travel Club! This is Matt, Reservations Mgr. We were waiting for you in your "
            "scheduled webinar yesterday at 5pm. What happened?",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "700$ overnight? or 200 QV ? call me back if interested",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "120hhr, 60hr",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "hello, how are you?",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Congratulations, you have received the largest prize in Michigan, $200 million. Please provide your ID "
            "information so that I can verify it for you.",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "FBI warning, you are currently suspected of a criminal offense. Please click on this link immediately, "
            "otherwise we will come to arrest you immediately.",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "You little dwarf, don't let me see you. If I see you, I'll hit you",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Do you still have those drugs from last time?",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Caravelle inn: 1310 N 1st St, San Jose, CA 95112",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "outcall or incall?",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Here is Elon Musk, are you coming to participate in our great initiative?",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "African American",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Will you do a FaceTime show",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Ok. I am trying to have the UN delegate free up my account and let it up for use, since I am off the "
            "camp,.. so I can take care of things down here. It was delayed two and tomorrow because it’s weekend. I "
            "will get updates by Monday.",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Good afternoon, this is Brianna from LA Fitness in Commack, I am one of the fitness specialists here. I "
            "am reaching out to tell you about a member perk we offer, it’s a complementary one hour workout. If this "
            "is something you’d be interested in / would like to know more about please feel free to reach out via "
            "text or call!",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "I will post all your nudes your ssn your address all your info also saying you are into child porn so if "
            "you know what is best for you reply let negotiate",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "I will be ruining you know",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Really you want to make it hard",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Well I already warned you",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "You really want to be jailed right I'm warning you",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Respond now so we negotiate it just 50$",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "What make you think you can be talking to the real trace the real trace doesn't even know who the hell "
            "you are I'm warning you pay it or I ruined you",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "I'm the one the trace adkins you gave me 1193 today now if you don't pay the 50$ I will post your nudes",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Do you want to go do jail I have your ssn everything I will say you send the picture to a 15yeat old kid "
            "so you can be jailed so if you know what is best for you pay the money 50$",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Once I start posting I will put all your picture and say you send it to a 15 year old and put in your "
            "address 812 university dr Louisiana so you can be arrested so if you know what is good for you pay the "
            "50! H",
            filter_constant.AI_RSP_INVALID_CONTENT
        )
    ]

    """
    AI_RSP_INVALID_CONTENT = 0
    AI_RSP_UNKNOWN = 1
    AI_RSP_VALID_CONTENT = 2
    """
    success_cnt = 0
    failed_cnt = 0
    for (c, must_type) in content_list:
        logger.warning("#" * 50)

        rsp_type, category = ZhipuBlackMailUtil.is_content_blackmail(original_content=c)

        if must_type == rsp_type:
            # logger.warning(f"check success!! {c}")
            success_cnt += 1
        else:
            failed_cnt += 1
            logger.warning(f"check failed!!!  [[{c}]]")
            logger.warning(f"check failed!!!  [[{c}]]")
            logger.warning(f"check failed!!!  [[{c}]]")

        time.sleep(0.1)

    print("total_cnt:", success_cnt + failed_cnt)
    print("success_cnt:", success_cnt)
    print("failed_cnt:", failed_cnt)
