from SecPhone.settings import logger


class FilterUnblockUtil:
    @staticmethod
    def is_not_long_and_about_block(text: str) -> bool:
        try:
            # 和拉黑有关
            text = text.lower()
            text = text.strip().strip("\n").replace("'", "").replace("‘", "").replace("’", "")
            words = text.split()
            words = ["".join([c for c in v if c.isalpha()]) for v in words if v]

            if len(words) > 20:
                logger.info(f"[SendSms] is_not_long_and_about_block too long: [{text}]")
                return False

            if "block" not in text and "blocc" not in text and "unlock" not in text:
                logger.info(f"[SendSms] is_not_long_and_about_block without block: [{text}]")
                return False

            # 20个单词，并且带着block的文本，必须包含下面的单词或者词组，才算是一个合理的“解锁诉求”
            combo_words = ["me", "mi", "us", "you", "get", "got", "why", "number", "please", "im", "am",
                           "iam", "her", "she", "been", "u", "yo", "keep", "no more", "it", "I", "again",
                           "PLEASEEEE", "MEE", "MEEE", "MEEEE", "how", "Not", "Still", "Youre", "ur", "our", ]

            flag = False
            for c in combo_words:
                c = c.lower()
                if " " in c:
                    # 词组
                    if c in text:
                        flag = True
                        break
                else:
                    # 单词
                    if c in words:
                        flag = True
                        break
            if flag:
                logger.warning(f"[SendSms] is_not_long_and_about_block: [{text}] ok")
                return True

            if len(words) <= 3:
                logger.warning(f"[SendSms] is_not_long_and_about_block too short: [{text}], is ok")
                return True

            return False
        except Exception as e:
            logger.error(f"[SendSms] is_not_long_and_about_block {text} failed", exc_info=True)
            return False


if __name__ == '__main__':

    data = """
       why block me mfs!
    """
    content_list = [
        (
            "can you unlock my number",
            True
        ),
        (
            """hello how are u""",
            False
        ),
        (
            """why block me mfs!""",
            True
        ),
        (
            """why block me mfs 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20!""",
            False
        ),
        (
            "I'm unblocking you on IG, I want you to see how much we post each other.",
            True
        )
    ]

    total_size = len(content_list)
    success_cnt = 0
    failed_cnt = 0
    index = 0
    for (c, must_type) in content_list:
        logger.warning(f"{'#' * 50} {index}/{total_size} / success rate = {success_cnt / (index + 1)}")
        index += 1

        rsp_type = FilterUnblockUtil.is_not_long_and_about_block(text=c)

        if must_type == rsp_type:
            success_cnt += 1
        else:
            failed_cnt += 1
            if len(c.split()) <= 20:
                logger.warning(f"check failed!!!  [[{c}]], {len(c.split())}")
                logger.warning(f"check failed!!!  [[{c}]], {len(c.split())}")
                logger.warning(f"check failed!!!  [[{c}]], {len(c.split())}")

    print("total_cnt:", success_cnt + failed_cnt)
    print("success_cnt:", success_cnt)
    print("failed_cnt:", failed_cnt)
