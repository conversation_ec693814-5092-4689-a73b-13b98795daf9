import json
import time

import requests.adapters
from requests.adapters import HTT<PERSON><PERSON>pter, Retry

from SecPhone.settings import logger
from Sms.filters import filter_constant

# https://aiproxy.io/，<EMAIL>

# 创建一个Retry对象，配置重试策略
retry_strategy = Retry(
    total=3,  # 最大重试次数
    backoff_factor=1,  # 重试间隔时间的回退因子
    status_forcelist=[500, 502, 503, 504],  # 针对这些 HTTP 状态码进行重试
    method_whitelist=["GET", "POST"]  # 针对 GET 和 POST 请求进行重试
)

# 创建一个Session对象，它会隐式地使用连接池
session = requests.Session()

# 设置重试策略, 设置连接池的大小（例如，最大同时打开的连接数）
adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=10, pool_maxsize=50)
session.mount("http://", adapter)
session.mount("https://", adapter)


class ChatGptProxyUtil:
    @staticmethod
    def is_sms_bad(user_id: int, sms_content: str) -> (int, str):
        try:
            sms_content = sms_content.replace("\n", " ")

            endpoint = "https://apivip.aiproxy.io/v1/chat/completions"
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer sk-evixMGNXmBw4rxd1eUFOYEXxsX9njJzjTUsSXSRCdKApeLFk"
            }

            if not sms_content:
                logger.error(f"[SendSms] gpt1-ai, param invalid: user_id: {user_id}, sms_content: {sms_content}")
                return filter_constant.AI_RSP_VALID_CONTENT

            content = """你是一名网络内容审查员，负责审查英文短信的内容, 请根据用户输入的文本，判断是否涉及：吸毒、大麻、毒品、骚扰、种族歧视等各种歧视(性别、宗教、年龄、性取向、残疾等群体的贬低、侮辱或歧视性言论、隐晦的歧视性语言或刻板印象)、暴力、威胁、恐吓、挑衅、加密货币诈骗、投资诈骗、杀猪盘诈骗、色情、电信诈骗、钓鱼诈骗、伪装身份诈骗、彩票基金诈骗、威胁诈骗等一切不良不合规不合法的内容。特别是电信杀猪盘，您应该理解杀猪盘的意思，杀猪盘几个要素：
                                1.杀猪盘诈骗利用虚假的网络身份诱骗受害者进行欺诈性投资。
                                2.诈骗者获取信任、操纵情绪并利用金融漏洞窃取资金。
                                3.现实生活中的例子显示了受害者所遭受的毁灭性的经济和情感打击。
                                4.发现未经请求的消息等迹象并验证信息可以帮助避免诈骗。
                           并输出一段格式为json的文本。
                           
                           需严格遵守以下规则:
                           ##规则##
                           规则1.返回必须是json格式，格式如下:{"result":"yes", "reason": "这只是普通的打招呼，不涉及色情和诈骗"}
                           规则2.json的`result`字段选值只能是这3个："yes","no","unknown",其中"yes"表示包含不良内容，"no"表示不包含，"unknown"表示不清楚。
                           规则3.回答仅返回json文本内容，不要返回其它无关信息.
                           规则4.如果没有以上提及的违规、不合法、不合格的内容，那就是返回no。

                           ##示例##
                           [用户输入]:Greetings from Worldwide Travel Club! This is Matt, Reservations Mgr. We were waiting for you in your scheduled webinar yesterday at 5pm. What happened?
                           [输出]:{"result":"yes", "reason": "骚扰、诈骗"}
                           [用户输入]:Greetings, You have been selected to pick cotton at the nearest plantation. Be ready at 12AM NOVEMBER 13 SHARP with your belongings. Our Executive Slaves will come get you in a Brown Van, be prepared to be searched down once you've enter the plantation. You are in Plantation Group A.
                           [输出]:{"result":"yes", "reason": "种族歧视"}
                           [用户输入]:i ordered a shirt and pants from a store located in the mall named “Sherway Gardens” Can you please pick it for me
                           [输出]:{"result":"unknown", "reason": "从短信内容来看，它没有直接涉及电信诈骗、钓鱼或其他不良内容。它似乎是在请求帮助。"}
                           [用户输入]:700$ overnight? or 200 QV ? call me back if interested
                           [输出]:{"result":"yes", "reason": "色情招嫖"}
                           [用户输入]:I had a great time having sex with you last night. Do you want to see my big cock?
                           [输出]:{"result":"yes", "reason": "色情"}
                           [用户输入]:hello, how are you?
                           [输出]:{"result":"no", "reason": "单纯打招呼"}
                           [用户输入]:Congratulations, you have received the largest prize in Michigan, $200 million. Please provide your ID information so that I can verify it for you.
                           [输出]:{"result":"yes", "reason": "短信奖金诈骗"}
                           [用户输入]:FBI warning, you are currently suspected of a criminal offense. Please click on this link immediately, otherwise we will come to arrest you immediately.
                           [输出]:{"result":"yes", "reason": "短信诈骗"}
                           [用户输入]:Ana Young are you free? \n\nAbigail Dillen
                           [输出]:{"result":"yes", "reason": "身份诈骗，打招呼后面加身份名字，就是伪装钓鱼诈骗"}
                           [用户输入]:I'm in a meeting at the moment. I can't make a call, and i need you to get a task done
                           [输出]:{"result":"yes", "reason": "身份诈骗，假装某个人伪装不方便电话的环境，然后要求被诈骗者做一些指令"}
                           [用户输入]:Head to the store and keep me updated with the value available for Apple Card
                           [输出]:{"result":"yes", "reason": "金钱诈骗，指示被诈骗者去购买gift Card,apple card等用来转移金钱诈骗"}
                           [用户输入]:You little dwarf, don't let me see you. If I see you, I'll hit you
                           [输出]:{"result":"yes", "reason": "恐吓、威胁"}
                           [用户输入]:Do you still have those drugs from last time?
                           [输出]:{"result":"yes", "reason": "毒品"}
                           [用户输入]:New tea arrived,come and taste it!
                           [输出]:{"result":"yes", "reason": "隐晦的招嫖，色情，暗示会所来了新的女孩子"}
                           [用户输入]:""" + sms_content

            payload = {
                "model": "gpt-3.5-turbo",
                "messages": [{"role": "user", "content": content}],
                "user": str(user_id),
                "max_tokens": 128
            }
            start = time.time()
            response = session.post(endpoint, headers=headers, json=payload, timeout=(10, 60))
            logger.info(response.text)

            rsp_json = response.json()
            if "choices" not in rsp_json or len(rsp_json["choices"]) == 0:
                logger.error(f"[SendSms] gpt1-ai json unreadable return unknown, user_id: {user_id}, "
                             f"query:{sms_content}, rsp: {rsp_json}")
                return filter_constant.AI_RSP_UNKNOWN, ""

            answer = rsp_json["choices"][0]["message"]["content"].replace("\n", "").replace(" ", "").strip()
            logger.info(f"[SendSms] gpt1-ai, user_id: {user_id}, query:[{sms_content}], "
                        f"cost: {round(time.time() - start, 2)} s, {answer}")
            try:
                answer_json = json.loads(answer)
            except json.decoder.JSONDecodeError:
                answer_json = {"result": "unknown"}
                answer = answer.replace(" ", "").strip()
                if answer[-2:] != '\"}':
                    answer = answer + "\"}"
                    answer_json = json.loads(answer)
                if answer[-1] != '}':
                    answer = answer + '}'
                    answer_json = json.loads(answer)

            reason = answer_json.get("reason", "")
            if answer_json["result"] == "yes":
                logger.warning(f"[SendSms] gpt1-ai says invalid, user_id: {user_id}, query:{sms_content}, {answer}")
                return filter_constant.AI_RSP_INVALID_CONTENT, reason
            elif answer_json["result"] == "no":
                logger.warning(f"[SendSms] gpt1-ai says valid, user_id: {user_id}, query:{sms_content}, {answer}")
                return filter_constant.AI_RSP_VALID_CONTENT, ""
            elif answer_json["result"] == "unknown":
                logger.warning(f"[SendSms] gpt1-ai says unknown, user_id: {user_id}, query:{sms_content}, {answer}")
                return filter_constant.AI_RSP_UNKNOWN, ""

            logger.error(f"[SendSms] gpt1-ai return failed, user_id: {user_id}, query:{sms_content}, {answer}")
            return filter_constant.AI_RSP_UNKNOWN, ""
        except Exception:
            logger.error(f"[SendSms] gpt1-ai, user_id: {user_id}, query:{sms_content} failed", exc_info=True)
            return filter_constant.AI_RSP_UNKNOWN, ""


if __name__ == '__main__':
    # content_list = [
    #     "um ima suck yo dick whenever you want me to and ima show my pussy whenever you wanna see it but not rn because my period just came on today",
    #     "My love are you still up",
    # ]
    # for c in content_list:
    #     logger.info("#" * 50)
    #     r = ChatGptProxyUtil.is_sms_bad(user_id=0, sms_content=c)
    #     logger.info(r)

    text = "{\"result\":\"no\", \"reason\": \"这段文本没有涉及色情、营销推销、电信诈骗、钓鱼诈骗、伪装身份诈骗、彩票基金诈"
    try:
        json.loads(text)
    except json.decoder.JSONDecodeError:
        content = text.replace(" ", "").strip()
        if content[-2:] != '\"}':
            content = content + "\"}"
            print(content)
            json.loads(content)
