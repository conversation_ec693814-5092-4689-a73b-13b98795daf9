import datetime
import hashlib
import random
import time

import requests
from gmssl import sm3, func

from Common.timeutil import TimeUtil
from SecPhone import settings
from SecPhone.settings import logger


class SmsFilterWangyiManual(object):

    def __init__(self, business_id: str):

        self.secret_id = "98ace006f700f24d97b39de009cc7e2d"
        self.secret_key = "e0670e083266908a61c4ab17ff12acd6"
        self.business_id = business_id
        # self.API_URL = "http://as.dun.163.com/v5/text/check" # 国内
        self.API_URL = "http://as-text-virginia.dun.163.com/v5/text/check"  # 美东
        self.VERSION = "v5.2"
        self.wy_sessions = self.__get_http_session(5, 20, 3)

    def __get_http_session(self, pool_connections, pool_maxsize, max_retries):
        session = requests.Session()
        # 创建一个适配器，连接池的数量pool_connections, 最大数量pool_maxsize, 失败重试的次数max_retries
        adapter = requests.adapters.HTTPAdapter(pool_connections=pool_connections,
                                                pool_maxsize=pool_maxsize, max_retries=max_retries)
        # 告诉requests，http协议和https协议都使用这个适配器
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        return session

    def __gen_signature(self, params=None):

        buff = ""
        for k in sorted(params.keys()):
            buff += str(k) + str(params[k])
        buff += self.secret_key
        if "signatureMethod" in params.keys() and params["signatureMethod"] == "SM3":
            return sm3.sm3_hash(func.bytes_to_list(bytes(buff, encoding='utf8')))
        else:
            return hashlib.md5(buff.encode("utf8")).hexdigest()

    def __check(self, params) -> dict:

        params["secretId"] = self.secret_id
        params["businessId"] = self.business_id
        params["version"] = self.VERSION
        params["timestamp"] = int(time.time() * 1000)
        params["nonce"] = int(random.random() * 100000000)
        params["signature"] = self.__gen_signature(params)

        try:
            logger.info(f"[SmsFilterWangyiManual] call wangyi api, url:{self.API_URL}, params: {params}")
            r = self.wy_sessions.post(self.API_URL, data=params, timeout=3)
            logger.info(f"[SmsFilterWangyiManual] call wangyi api, rsp: {r.content}")
            return r.json()

        except Exception:
            logger.error(f"[SmsFilterWangyiManual] call wangyi api error, params: {params}", exc_info=True)
            return {}

    def request_wangyi(self, user_id: int, content: str, ip: str, uuid: str, user_level: int,
                       user_created_at: datetime.datetime, user_context: dict):
        ret = None
        try:
            content_str = f"[content]: {content}"
            for k, v in user_context.items():
                content_str += f"[{k}] {v}\n"
            t1 = time.time()
            ret = self.__check({"dataId": TimeUtil.GetNowTsInStr(),
                                "content": content_str,
                                "account": str(user_id),
                                "level": user_level,
                                "ip": ip,
                                "deviceId": uuid,
                                "callbackUrl": settings.SMS_WANGYI_MANUAL_CALLBACK,
                                "registerTime": TimeUtil.DateTime2Timestamp(user_created_at),
                                },
                               )
            t2 = time.time()
            if "result" not in ret or "antispam" not in ret["result"]:
                logger.error(f"[SmsFilterWangyiManual] content:{content} check result is nil: {ret}")
                return None

            antispam = ret["result"]['antispam']
            task_id = antispam['taskId']
            logger.info(f"[SmsFilterWangyiManual] manual judge, task_id:{task_id}, content:{content}. "
                        f"cost:{int((t2 - t1) * 1000)} ms")
            return task_id
        except Exception as e:
            logger.error(f"【SmsFilterWangyiManual】 manual judge failed: ret: {ret}", exc_info=True)
            return None


# 人工审核
sms_filter_wangyi_manual = SmsFilterWangyiManual("caec3a7db294625447e875f8c42e4114")
