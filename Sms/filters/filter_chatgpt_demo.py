import json
import time

import openai

# openai.api_key = "***************************************************"  # <EMAIL>
# fatpo gmail account
# openai.api_key = "***************************************************"
# company account # google 登录
# openai.api_key = "***************************************************"

keys_map = {
    # "<EMAIL>": "***************************************************",
    "fatpo-gmail": "***************************************************",
    "lq-com": "***************************************************",
    # "fatpo-com": "***************************************************",
    "yk-com": "***************************************************",
    # "zx-com": "***************************************************",
    # "jinying": "***************************************************"
}
keys_map_inverse = {
    # "***************************************************": "<EMAIL>",
    "***************************************************": "fatpo-gmail",
    "***************************************************": "lq-com",
    # "***************************************************": "fatpo-com",
    "***************************************************": "yk-com",
    # "***************************************************": "zx-com",
    # "***************************************************": "jinying",
}

keys = list(keys_map.values())

is_close_chatgpt_redis_key = "is_close_chatgpt"


class ChatGptUtilDemo:
    @staticmethod
    def is_sms_phishing(user_id: int, sms_content: str) -> bool:
        try:

            if not sms_content:
                print(f"[ChatGptUtil.is_sms_phishing] param invalid: user_id: {user_id}, sms_content: {sms_content}")
                return True

            openai.api_key = keys_map["fatpo-gmail"]

            content = """你是一名网络内容审查员，负责审查英文短信的内容, 请根据用户输入的文本，判断涉及色情、电信诈骗、钓鱼诈骗、伪装身份诈骗、彩票基金诈骗、威胁诈骗的不良内容，并输出一段格式为json的文本。
                       需严格遵守以下规则:
                       ##规则##
                       规则1.返回必须是json格式，格式如下:{"result":"yes", "reason": "这只是普通的打招呼，不涉及色情和诈骗"}
                       规则2.json的`result`字段选值只能是这3个："yes","no","unknown",其中"yes"表示包含不良内容，"no"表示不包含，"unknown"表示不清楚。
                       规则3.回答仅返回json文本内容，不要返回其它无关信息

                       ##示例##
                       [用户输入]:i ordered a shirt and pants from a store located in the mall named “Sherway Gardens” Can you please pick it for me
                       [输出]:{"result":"unknown", "reason": "从短信内容来看，它没有直接涉及电信诈骗、钓鱼或其他不良内容。它似乎是在请求帮助。"}
                       [用户输入]:700$ overnight? or 200 QV ? call me back if interested
                       [输出]:{"result":"yes", "reason": "色情招嫖"}
                       [用户输入]:I had a great time having sex with you last night. Do you want to see my big cock?
                       [输出]:{"result":"yes", "reason": "色情"}
                       [用户输入]:hello, how are you?
                       [输出]:{"result":"no", "reason": "单纯打招呼"}
                       [用户输入]:Congratulations, you have received the largest prize in Michigan, $200 million. Please provide your ID information so that I can verify it for you.
                       [输出]:{"result":"yes", "reason": "短信奖金诈骗"}
                       [用户输入]:FBI warning, you are currently suspected of a criminal offense. Please click on this link immediately, otherwise we will come to arrest you immediately.
                       [输出]:{"result":"yes", "reason": "短信诈骗"}
                       [用户输入]:""" + sms_content

            start = time.time()
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user",
                           "content": content}],
                user=str(user_id),
                max_tokens=64,
            )
            # print(response)
            answer = response.choices[0].message["content"].replace("\n", "").strip()
            print(f"[ChatGptUtil.is_sms_phishing] user_id: {user_id} chat query:{sms_content}"
                  f"cost: {round(time.time() - start, 2)} s, {response.choices[0].message['content']}")
            print(answer)
            answer_json = json.loads(answer)
            if answer_json["result"] == "yes":
                return True
            return False
        except Exception as e:
            if "Bad gateway" in str(e) or "The server is overloaded or not ready yet" in str(e):
                print(
                    f"[ChatGptUtil.is_sms_phishing] user_id: {user_id}, chat query:{sms_content}, failed, {e}")
                return False

            if 'RateLimitError' in str(e):
                print(
                    f"[ChatGptUtil.is_sms_phishing] user_id: {user_id}, chat query:{sms_content}, failed, {e}")
                return False

            print(f"[ChatGptUtil.is_sms_phishing] user_id: {user_id}, chat query:{sms_content}, failed")
            return False


if __name__ == '__main__':
    content_list = [
        "um ima suck yo dick whenever you want me to and ima show my pussy whenever you wanna see it but not rn because my period just came on today",
        "My love are you still up",
    ]
    for c in content_list:
        print("#" * 50)
        r = ChatGptUtilDemo.is_sms_phishing(user_id=0, sms_content=c)
        print(r)
