# coding=utf-8
import json
import re
import time

from SecPhone.settings import logger
from Sms.ai.zhipu_client import ZhiPuClient
from Sms.filters import filter_constant

origin_prompt = """
你是一个专业的短信内容审查助手，负责帮助中间商判断预处理后的短信内容是否合规。请根据以下规则分析短信内容:

### 绝对禁止的内容:
1. **广告促销短信**:包括但不限于商品促销、打折信息、优惠券、营销活动等，请直接reject。
2. **奖金诈骗**:包括但不限于高额奖金、中奖信息、诱导点击链接领取奖励等。
3. **快递相关内容**:包括但不限于快递、包裹、物流、派送、扣留、缴费等任何与快递相关的内容。无论内容是否看似低风险，只要涉及快递相关内容，直接拦截。
4. **毒品相关**:包括但不限于毒品名称、交易信息、诱导购买毒品等。
5. **虚假逮捕威胁**:包括但不限于“你涉嫌犯罪”“我们正在去逮捕你”“待在原地”等虚假威胁。
6. **恐吓威胁**:包括但不限于“kill you”“burn your house”等暴力恐吓和“send to everybody”这种身败名裂的威胁。"make sure you suffer for it"也是一种威胁。
7. **招嫖相关内容**:包括但不限于性服务、陪聊、约会等涉嫌招嫖的内容
    - 特别注意询价=时间加金钱的组合，如150hhr,200hr等，需要reject。
    - 包括不限于招嫖用语:qv,incall,outcall,facetime show,bj,bbj,gfe等
8. **种族歧视相关内容**:包括但不限于针对种族、肤色、民族的侮辱、歧视性言论。
9. **名人话题相关内容**:包括但不限于美国总统、马斯克、扎克伯格等名人的相关话题，请直接reject。
10. **官方相关内容**:包括但不限于代表联合国、俱乐部Clubs、军方军人、公司、CEO、FBI等官方的自我介绍、立场、观点或活动的内容，请直接reject。
11. **投资收益**:包括公司、项目、账户、支付、收益等和金钱直接相关的，请直接reject。
12. **中介相关**:包括不限于任何中介、中间商、agent内容，请直接reject。
13. **监狱刑期相关**:包括不限于指控、监狱、刑法、法律、判罚等相关内容。
14. **发布帖子相关**:把对方或者对方的东西如照片视频等，发到朋友圈、家人、公共平台如facebook、Twitter等（或者省略了公共平台，直接说我要把你post出去），都属于违规，请直接reject。
15. **个人信息相关**:把对方或者对方家人的地址、工作单位、家庭信息、家庭成员、手机号码等，发出去，都属于违规，请直接reject。
16. **声称让别人知道你**:让你父母知道你，让你朋友知道你，让你工作单位知道你，让你学校知道你，都属于针对个人或个人信息的威胁恐吓，都属于违规，请直接reject。
17. **诱导别人付款**:通过一些话术让别人支付，付款，加油费，路费，等和付款金币有关系的等，都属于违规，请直接reject。
18. **恐吓别人付款**:通过一些话术让别人支付，如果你不付款，你的事情就难以收拾等，都属于违规，请直接reject。
19. **社会性死亡**:让你的领导知道、让你的学校知道、让你老板知道、让你同事知道，“你也不想你的老板看到这个吧？”， “Recognize any of these people?”， “I'll message them one by one“ 之类的，请直接reject。
20. **正式自我介绍句式**:太过正式的自我介绍一般都是诈骗的话术开端，我希望你能够把这些正式的自我介绍给reject，我给你的圈定的需要reject的句式如下:
    - 句式1（需要超过10个单词）:您好/早上好/中午好/晚上好/先生小姐等问候语 + 我是谁/我来自哪里 + 需要你做xx事
    - 句式2（需要超过10个单词）:问候语 + 我是谁/我来自哪里 + 涉及到你的事宜
    - 句式3（需要超过10个单词）:类似“我是一名工程师，同时我也是一名军人等”，“我来这里旅游，可以认识你吗？”此类介绍。
21. **色情内容询问**:“请问您有销售图片或者内容吗”之类的一般都是默认色情图色情内容，请直接reject。
22. **自我标榜真实性**: "Babe if you keep thinking that not a real person or that I'm trying to hurt you or something like that we're not gonna make any headway"，类似这种你以为我是假身份之类的话术，也属于诈骗，请直接reject。
23. **要求验证码的**: "Can I see you a code to confirm this is the right person?"，需要验证码的都是诈骗。
24. **纯粹骂人**: "Spick. A**. Piece. Of. Shit."， "Don't even own a car. Bus riding a** n****", "Broke as bum", "U stupid a** n****", *号表示已经预处理过的脏话，这些都是谩骂、诋毁的短句子，请直接reject。
25. **任何官方机构都拒绝**: "should i call ICE for you" 出现任何官方机构的短句 ICE,FBI等等，请直接reject。
26. **遗产金额诈骗**: "Avec le montant de la succession et tout le reste." 出现任何关于遗产、继承等内容都属于诈骗，请直接reject。
27. **商店诈骗**: 出现任何关于询问商店store距离多远，购买card都属于商店诈骗，请直接reject。
28. **GiftCard诈骗**: 出现任何关于apple card，gift card，stream card等卡片都属于card诈骗，请直接reject。
29. **公司上下文诈骗**: 出现任何关于“我的公司”、“我的办公室”、“我父亲的公司”、“我的员工”，“我的老板”，“我的工作”，“我的工资”等职场相关的上下文内容的，即使是看起来很正常的日常对话，也都属于公司上下文诈骗，请直接reject。
30. **假号码诈骗**: 出现任何"新号码"、"邻居号码"、"朋友号码"、"办公号码"、“我第二个号码”、"我的另外私人号码"、"机场陌生人号码","路边女士号码"等等都属于假号码诈骗，类内容通常是杀猪盘诈骗的铺垫，这是请直接reject。
31. **虚拟币诈骗**: 出现任何和虚拟币相关的如bitcoin都属于虚拟币诈骗，请直接reject。
32. **职场虚拟人设诈骗**: 出现任何职场身份描述，提及职场相关场景细节，提及职场相关元素（构建虚假职场人设）的内容，“我是公司老板”“在办公室上班”“管理员工”“负责招聘”“今天有工作会议”，“办公室忙碌”“处理员工问题”“工作加班”“公司事务”，此类内容通常是杀猪盘诈骗的铺垫，请直接reject。
33. **会议诈骗**: 出现任何会议相关的话题，“我今天有一个重要的会议”，“我正在会议中”等，此类内容通常是杀猪盘诈骗的铺垫，请直接reject。
34. **聊天软件转移诈骗**: 出现任何其他聊天app的内容比如:Facebook、iMessage、messenger、google chat、whatsapp、telegram、signal、line、skype、terms、site、website 等聊天软件，此类内容通常是杀猪盘诈骗的铺垫，请直接reject。
35. **金融资产诈骗**: 出现资产、余额、银行、债务、股票等金融相关内容，此类内容通常是杀猪盘诈骗的铺垫，请直接reject。
36. **损失诈骗**: 你造成了xx后果，你即将损失xx等内容，带着威胁意味，比如"Just know there it's no way there can get it done anymore can you see what you cause", 此类内容通常是杀猪盘诈骗的铺垫，请直接reject。
37. **未成年话题**: 所有涉及未成年的敏感话题，请直接reject。
38. **假装熟悉诈骗**: 涉及“你还记得我吗”，“你不会这么快把我忘记了吧？”之类的话题，此类内容通常是杀猪盘诈骗的铺垫，请直接reject。
39. **网络乞丐**: 涉及“你可以给我发几块钱买食物吗”，“Can you send me 6 dollars for food”, "can u help with 10(没带单位一般是$)"，此类内容通常是小额诈骗，请直接reject。
40. **机场诈骗**: 出现机场、航班、机票等和飞机有关的内容，都属于人设铺垫，此类内容通常是杀猪盘诈骗的铺垫，请直接reject。
41. **出差诈骗**: 出现出差，“要去出差”，“出差刚回来很忙”等出差有关的内容，都属于人设铺垫，此类内容通常是杀猪盘诈骗的铺垫，请直接reject。
42. **代金券诈骗**: 出现任何有关代金券等内容，都属于代金券诈骗，此类内容通常是杀猪盘诈骗的铺垫，请直接reject。
43. **描述介绍家人诈骗**: 为了人设，杀猪盘通常会介绍自己、家庭、家人的情况，“和自己的爸爸沟通了”，都属于人设铺垫，此类内容通常是杀猪盘诈骗的铺垫，请直接reject。
44. **转去邮件场景诈骗**: 出现任何需要去邮件查看的内容，"check your email", "the details should be in e-mail", "file in mail"， "go to your mailbox", 都属于杀猪盘的铺垫，因为我们这个平台不允许发送违规的东西，所以犯罪分子就会拿到受害者的邮箱，然后让受害者转去邮件查看，请直接reject。
45. **官职诈骗**: 出现任何官职，比如海军上将、上士、部门主管, 都属于杀猪盘的铺垫，请直接reject。


### 其他禁止内容:
1. **违法内容**:包括但不限于诈骗、赌博、色情、暴力、恐怖主义等违法信息。
2. **高风险内容**:包括但不限于诱导点击链接、虚假信息、恶意营销、侵犯隐私等。

### 豁免内容:
1. **内容中包括但不全是脏话和负面情绪表达**:如“fuck”“hate”,"fucking","bitch","ass","nigga","shit"等，仅作为情绪表达，不视为违规。
2. **内容中包括但不全是强烈情感表达**:如“I fucking hate you”“I'm so angry”等，仅作为情绪宣泄，不视为违规。

### 语言支持:
1. **主要语言**:英语。
2. **次要语言**:西班牙语、法语、韩语、越南语、中文、德语等主流语言。

### 审查要求:
1. 请完整review完[绝对禁止的内容]的全部[45]条规则，其中任何一条规则涉及，都应该直接标记为“违规”。
2. 请注意，部分[短信内容]可能存在拼写错误的情况，需要你自己纠正。
3. 首先检查短信内容是否涉及[绝对禁止的内容]。如果涉及，不需要继续审查，直接标记为“违规”，并说明具体原因。
4. 如果短信内容不涉及[绝对禁止的内容]，继续检查是否涉及“其他禁止内容”。如果涉及，标记为“违规”，并说明具体原因。
5. 如果短信内容不涉及[绝对禁止的内容]，短信内容仅为脏话和负面情绪表达，标记为“通过”，并说明原因。
6. 如果短信内容不涉及[绝对禁止的内容]，短信内容为强烈情感表达，标记为“通过”，并说明原因。
7. 如果短信内容不涉及[绝对禁止的内容]，短信内容模糊或存在潜在风险，但未涉及绝对禁止的内容，标记为“通过”，并说明原因。
8. 如果短信内容不涉及[绝对禁止的内容]和[其他禁止内容]，请直接标记为“通过”，无需进一步分析。

### 短信内容:
[预处理后的短信内容]

### 输出格式（JSON）:
{
  "result": "pass/reject/review",
  "reason": "简要说明原因",
  "risk_level": "high/medium/low",
  "action": "allow/block/manual_review"
}
"""


class ZhipuBasicUtil:
    @staticmethod
    def is_content_basic_bad(original_content: str, filtered_content: str) -> (bool, str):
        original_content = original_content.replace("\n", " ").replace("\r", " ").strip()
        filtered_content = filtered_content.replace("\n", " ").replace("\r", " ").strip()
        logger.info(f"[SendSms] zhipu-ai, original_content: [{original_content}],"
                    f" filtered_content:[{filtered_content}] checking...")

        try:
            if not original_content:
                logger.error(f"[SendSms] zhipu-ai, param invalid, sms_content: [{original_content}]")
                return True, ""
            prompt = origin_prompt
            # prompt = prompt.replace("[用户输入的原始短信内容]", original_content)
            prompt = prompt.replace("[预处理后的短信内容]", filtered_content)

            start = time.time()
            client, client_name = ZhiPuClient.get_zhipu_fatpo_client()
            logger.info(f"[SendSms] zhipu-ai, client: {client_name}")

            response = client.chat.completions.create(
                model="GLM-4-Air-0111",  # GLM-4-Air-0111	高性价比	128K	0.0005 元 / 千tokens
                # model="GLM-4-FlashX",  # GLM-4-FlashX	高速低价	128K	0.0001 元 / 千tokens
                # model="GLM-4-Plus",  # GLM-4-Plus	高智能旗舰	128K	0.05 元 / 千tokens
                # model="glm-4-air",  # GLM-4-Air 预计2025年12月下线	128K	0.0005 元 / 千tokens
                # model="glm-4-flash",  # GLM-4-Flash	语言模型	免费
                messages=[
                    {"role": "user", "content": prompt},
                ],
                user_id="51081731492666544",
                max_tokens=512,
            )
            answer = response.choices[0].message
            logger.info(
                f"[SendSms] zhipu-ai, query:[[{original_content}]], cost: {round(time.time() - start, 2)} s, {answer}")

            ans_content = json.loads(answer.to_json())['content']
            ans_content = ans_content.replace("\n", "").replace("\\n", "") \
                .replace("```", "").replace("json", "").strip()

            try:
                # 只要json
                match = re.search(r'\{(.*?)}', ans_content)
                if match:
                    ans_content = match.group(0)

                ans = json.loads(ans_content)
            except Exception:
                logger.error(f"[SendSms] zhipu-ai says json invalid, {ans_content}, query:[{original_content}]")
                ans = {"result": "unknown", "reason": "json invalid"}

            reason = ans['reason']
            if (str(ans['result']).lower() != "pass"
                    and ("奖金" in reason or "中奖" in reason or "winning" in reason)
                    and len(original_content.split()) > 20):
                logger.error(f"[SendSms] zhipu-ai says 诈骗, {ans}, query:[{original_content}]")

            if str(ans['result']).lower() == "pass":
                logger.info(f"[SendSms] zhipu-ai says pass, {ans}, query:[{original_content}]")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
            elif str(ans['result']).lower() == "reject":
                logger.warning(f"[SendSms] zhipu-ai says reject, {ans}, query:[{original_content}]")
                return filter_constant.AI_RSP_INVALID_CONTENT, reason
            elif str(ans['result']).lower() == "review":
                logger.warning(f"[SendSms] zhipu-ai says review, {ans}, query:[{original_content}]")
                return filter_constant.AI_RSP_INVALID_CONTENT, reason
            else:
                logger.error(f"[SendSms] zhipu-ai says unknown, {ans}, query:[{original_content}]")
                return filter_constant.AI_RSP_UNKNOWN, reason
        except Exception as ex:
            if "并发数过高" in str(ex) or "qps" in str(ex).lower():
                logger.warning(f"[SendSms] zhipu-ai says failed, over qps, {original_content}")
            elif "系统检测到" in str(ex):
                logger.warning(f"[SendSms] zhipu-ai says failed, 系统检测到, {original_content}", exc_info=True)
                return filter_constant.AI_RSP_INVALID_CONTENT, "zhipu-invalid-input"
            else:
                logger.error(f"[SendSms] zhipu-ai says failed [{original_content}]", exc_info=True)
            return filter_constant.AI_RSP_UNKNOWN, "zhipu-failed"


if __name__ == '__main__':
    content_list = [
        (
            """Cher Monsieur René,

Je me permets de vous contacter en ma qualité de conseiller financier en charge du dossier. Nous avons le plaisir de 
yous informer qu'une de nos clientes a décidé de vous faire don de son capital d'un montant de 10 000 000 $ détenu 
auprès de notre institution.

Ce transfert de fonds est en cours de traitement, et nous vous invitons à prendre contact avec nous afin de finaliser 
les démarches nécessaires pour la réception de ce don.

N'hésitez pas à me contacter pour toute information complémentaire ou pour convenir d'un rendez-vous afin de 
formaliser les étapes suivantes.

Dans l'attente de votre retour.""",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "She trying to hold on to you she know she lost a damn good man n she know ones she let you go it’s over "
            "for it that’s why she trying to keep you",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Go and look for $120 before 12pm so I can be at the court house on or before 2pm which is the stipulated "
            "hearing time",
            filter_constant.AI_RSP_INVALID_CONTENT,
        ),
        (
            "Little white trash!",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Call me",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "You can block me but this payment is not in company anymore it on hold right and it won’t complete "
            "without take that step",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "We have tender all her information to FBI and they’ll do a good job job about it this week",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Then she told you that you will receive payment of $32,500 that is not your payment all you have to "
            "receive here is $52,500",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "I will let you know when they’re arrested her and I will send you videos of her",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "I just miss you Sade",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            """Hi Cheryl, I hope you're doing well. I work with Deepak Nathani, a real estate agent at EXP Realty. I 
            noticed your property at 14502 Clover Hill Terrace Bowie, MD 20720 is listed as ""For Sale by Owner."" I 
            believe we can help you sell it faster and at an excellent price. Our fee is only 1% of the sale price, 
            with no additional costs. Would you be open to a quick chat?Feel free to text me, and I’ll connect you 
            with Deepak at ************.Looking forward to hearing from you!""",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Good morning! This is Enterprise Rent A Car. Your insurance company has set up a reservation for you to "
            "pick up a rental vehicle. Please give us a call to schedule a date and time for your arrival. Thank you! ",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Hello , This is James . I found your portfolio interesting on Childrensillustrators. I have a project I "
            "will like you to illustrate . Plz text me back	",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Good morning, Steven. I’ m so soryy, but I’ll be at your place tomorrow. Will call you later:driving now",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Not the company is fully back to work and your package is ready for delivery",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "fuck you asshole",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            """Greetings, You have been selected to pick cotton at the nearest plantation.Be ready at 12AM NOVEMBER 
            13 SHARP with your belongings. Our Executive Slaves will come get you in a Brown Van, be prepared to be 
            searched down once you've enter the plantation. You are in Plantation Group A.""",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "120 hhr, 160 hr",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "um ima suck yo dick whenever you want me to and ima show my pussy whenever you wanna see it but not rn "
            "because my period just came on today",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Greetings from Worldwide Travel Club! This is Matt, Reservations Mgr. We were waiting for you in your "
            "scheduled webinar yesterday at 5pm. What happened?",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "700$ overnight? or 200 QV ? call me back if interested",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "120hhr, 60hr",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "hello, how are you?",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "Congratulations, you have received the largest prize in Michigan, $200 million. Please provide your ID "
            "information so that I can verify it for you.",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "FBI warning, you are currently suspected of a criminal offense. Please click on this link immediately, "
            "otherwise we will come to arrest you immediately.",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "You little dwarf, don't let me see you. If I see you, I'll hit you",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Do you still have those drugs from last time?",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Caravelle inn: 1310 N 1st St, San Jose, CA 95112",
            filter_constant.AI_RSP_VALID_CONTENT
        ),
        (
            "outcall or incall?",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Here is Elon Musk, are you coming to participate in our great initiative?",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "African American",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Will you do a FaceTime show",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Ok. I am trying to have the UN delegate free up my account and let it up for use, since I am off the "
            "camp,.. so I can take care of things down here. It was delayed two and tomorrow because it’s weekend. I "
            "will get updates by Monday.",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
        (
            "Good afternoon, this is Brianna from LA Fitness in Commack, I am one of the fitness specialists here. I "
            "am reaching out to tell you about a member perk we offer, it’s a complementary one hour workout. If this "
            "is something you’d be interested in / would like to know more about please feel free to reach out via "
            "text or call!",
            filter_constant.AI_RSP_INVALID_CONTENT
        ),
    ]

    """
    AI_RSP_INVALID_CONTENT = 0
    AI_RSP_UNKNOWN = 1
    AI_RSP_VALID_CONTENT = 2
    """
    success_cnt = 0
    failed_cnt = 0
    for (c, must_type) in content_list:
        logger.warning("#" * 50)

        rsp_type, category = ZhipuBasicUtil.is_content_basic_bad(original_content=c, filtered_content=c)

        if must_type == rsp_type:
            # logger.warning(f"check success!! {c}")
            success_cnt += 1
        else:
            failed_cnt += 1
            logger.warning(f"check failed!!!  [[{c}]]")
            logger.warning(f"check failed!!!  [[{c}]]")
            logger.warning(f"check failed!!!  [[{c}]]")

        time.sleep(0.1)

    print("total_cnt:", success_cnt + failed_cnt)
    print("success_cnt:", success_cnt)
    print("failed_cnt:", failed_cnt)
