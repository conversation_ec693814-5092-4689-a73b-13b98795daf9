import random
import time

import openai
from django.core.cache import cache

from SecPhone.settings import logger
from Sms.info.wangyi_info_bean import WangyiFilterRet
from Sms.tools.tool_invalid_sms import SmsInvalidTool
from Sms.tools.tool_sms import SmsTool

# jinying  # ***************************************************
# openai.api_key = "***************************************************"  # <EMAIL>
# fatpo gmail account
# openai.api_key = "***************************************************"
# company account # google 登录
# openai.api_key = "***************************************************"

keys_map = {
    # "<EMAIL>": "***************************************************",
    "fatpo-gmail": "***************************************************",
    "lq-com": "***************************************************",
    # "fatpo-com": "***************************************************",
    "yk-com": "***************************************************",
    # "zx-com": "***************************************************",
    # "jinying": "***************************************************"
}
keys_map_inverse = {
    # "***************************************************": "<EMAIL>",
    "***************************************************": "fatpo-gmail",
    "***************************************************": "lq-com",
    # "***************************************************": "fatpo-com",
    "***************************************************": "yk-com",
    # "***************************************************": "zx-com",
    # "***************************************************": "jinying",
}

keys = list(keys_map.values())

is_close_chatgpt_redis_key = "is_close_chatgpt"


class ChatGptUtil:
    @staticmethod
    def is_sms_phishing(user_id: int, prompt: str, from_number: str, to_number: str) -> bool:
        # 轮流来着
        key = random.choice(keys)
        key_name = keys_map_inverse.get(key, "unknown")

        try:
            if cache.get(is_close_chatgpt_redis_key) is not None:
                logger.info(f"[ChatGptUtil.is_sms_phishing] user_id: {user_id}, prompt: {prompt}, close_chatgpt=true")
                return False

            if user_id == 0 or not prompt:
                logger.error(f"[ChatGptUtil.is_sms_phishing] param invalid: user_id: {user_id}, prompt: {prompt}")
                return True

            openai.api_key = key
            logger.info(f"[ChatGptUtil.is_sms_phishing] user_id: {user_id}, chatgpt:{keys_map_inverse[key]}")

            records_cnt = SmsTool.get_conversation_cnt(user_id, to_number)
            if records_cnt == 0:
                prefix = "假设一个陌生人发一条短信给你，你觉得它是不是在钓鱼或者诈骗，再次明确，你不用附带任何理由，仅仅返回true或者false就可以了，如果不是英语直接返回false，短信内容:"
            else:
                prefix = f"假设有人之前给你发过{records_cnt}条短信，现在对方再发了一条，你看看这条新发送的短信是不是在钓鱼或者诈骗，再次明确，你不用附带任何理由，仅仅返回true或者false就可以了，如果不是英语直接返回false，短信内容:"

            start = time.time()
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user",
                           "content": prefix + prompt}],
                user=str(user_id),
                max_tokens=64,
            )
            answer = response.choices[0].message["content"].replace("\n", "").strip()
            log_message = f"[ChatGptUtil.is_sms_phishing] user_id: {user_id} chat query:{prefix + prompt}, " \
                          f"answer:{answer}, " \
                          f"cost: {round(time.time() - start, 2)} s, {response.choices[0].message['content']}"
            logger.info(log_message)
            if 'true' in answer.lower():
                logger.warning(log_message)
                # 记录坏短信
                SmsInvalidTool.save_invalid_sms(user_id, prompt, from_number, to_number, WangyiFilterRet(label=9999))
                return True
            return False
        except Exception as e:
            if "Bad gateway" in str(e) or "The server is overloaded or not ready yet" in str(e):
                logger.warning(
                    f"[ChatGptUtil.is_sms_phishing] user_id: {user_id}, key:{key_name}, chat query:{prompt}, failed, {e}")
                return False

            if 'RateLimitError' in str(e):
                logger.warning(
                    f"[ChatGptUtil.is_sms_phishing] user_id: {user_id}, key:{key_name}, chat query:{prompt}, failed, {e}")
                cache.set(is_close_chatgpt_redis_key, 1, 300)
                return False

            logger.warning(
                f"[ChatGptUtil.is_sms_phishing] user_id: {user_id}, key:{key_name}, chat query:{prompt}, failed",
                exc_info=True)
            return False


sms_filter_chatgpt = ChatGptUtil()

if __name__ == '__main__':
    r = ChatGptUtil.is_sms_phishing(user_id=0, prompt="My love are you still up", from_number="", to_number="")
    print(r)
