# coding=utf-8
import json
import re
import time

from SecPhone.settings import logger
from Sms.ai.zhipu_client import ZhiPuClient
from Sms.filters import filter_constant

origin_prompt = """你是一名专业的短信内容审查助手，专注于识别“杀猪”诈骗信息。请根据以下信息分析当前短信内容是否属于杀猪，以及[被审查用户]是否表现出潜在杀猪行为：  

### [被审查用户]发送的短信内容：  
{current_content}  

### 审查语言
英语、法语、德语、西班牙语、日韩、越南语等世界主流语言

### 历史对话记录（如有，最近20条）：  
{conversation_history}  

### 用户消息模式（如有，最近20条）：  
{user_pattern}  

### 输出格式（JSON）：  
{
  "result": "high-risk/normal",
  "reason": "Briefly explain the reason",
  "risk_level": "high/medium/low"
}

### 输出要求：  
1. 请注意json格式，单引号和双引号必须严格正确。
"""

origin_pic_prompt = """你是一名专业的短信内容审查助手，专注于识别“杀猪盘”诈骗信息。请根据以下信息分析[被审查用户]是否表现出潜在杀猪盘行为：  

### 历史对话记录（如有，最近20条）：  
{conversation_history}  

### 用户消息模式（如有，最近20条）：  
{user_pattern}  

### 输出格式（JSON）：  
{
  "result": "high-risk/normal",
  "reason": "Briefly explain the reason",
  "risk_level": "high/medium/low"
}

### 输出要求：  
1. 请注意json格式，单引号和双引号必须严格正确。
"""


class ZhipuPigButcheringUtil:
    @staticmethod
    def is_content_basic_bad(user: str, current_content: str, conversation_context_20_content_list: list,
                             latest_send_20_messages: list) -> (bool, str):
        """
        检测短信内容是否为杀猪盘诈骗

        Args:
            user: user
            current_content: 当前需要审核的短信内容
            conversation_context_20_content_list: 与当前接收者的最近20条对话记录
            latest_send_20_messages: 用户最近发送的20条短信(可能发给不同接收者)

        Returns:
            (是否违规, 违规原因)
        """
        current_content = current_content.replace("\n", " ").replace("\r", " ").strip()

        # 构建对话历史字符串
        conversation_history = ""
        if conversation_context_20_content_list and len(conversation_context_20_content_list) > 0:
            conversation_history = "\n".join(
                [f"Message{i + 1}: {msg}" for i, msg in enumerate(conversation_context_20_content_list[-20:])])

        # 构建用户发送模式字符串
        user_pattern = ""
        if latest_send_20_messages and len(latest_send_20_messages) > 0:
            user_pattern = "\n".join([f"[User under review] Send record:{i + 1}: {msg}"
                                      for i, msg in enumerate(latest_send_20_messages[:20])])

        logger.info(f"[SendSms] user:{user}, zhipu-pig-ai, current_content: [{current_content}] checking...")

        try:
            if not current_content:
                logger.error(f"[SendSms] zhipu-pig-ai, param invalid, current_content empty")
                return True, "empty content"

            prompt = origin_prompt
            prompt = prompt.replace("{current_content}", current_content)
            prompt = prompt.replace("{conversation_history}", conversation_history)
            prompt = prompt.replace("{user_pattern}", user_pattern)

            start = time.time()
            client, client_name = ZhiPuClient.get_zhipu_fatpo_client()
            logger.info(f"[SendSms] user:{user}, zhipu-pig-ai, prompt: {prompt}")

            response = client.chat.completions.create(
                model="glm-4-air-250414",
                messages=[
                    {"role": "user", "content": prompt},
                ],
                user_id="51081731492666544",
                max_tokens=512,
            )
            answer = response.choices[0].message
            logger.info(f"[SendSms] user:{user}, zhipu-pig-ai, query:[[{current_content}]], "
                        f"cost: {round(time.time() - start, 2)} s, {answer}")

            ans_content = json.loads(answer.to_json())['content']
            ans_content = ans_content.replace("\n", "").replace("\\n", "") \
                .replace("```", "").replace("json", "").strip()

            try:
                # 提取JSON
                match = re.search(r'\{(.*?)}', ans_content)
                if match:
                    ans_content = match.group(0)

                ans = json.loads(ans_content)
            except Exception:
                if "fraud" in ans_content or "high-risk" in ans_content:
                    logger.error(f"[SendSms] user:{user}, zhipu-pig-ai json invalid but fraud: {ans_content}, "
                                 f"query:[{current_content}]")
                    return filter_constant.AI_RSP_INVALID_CONTENT, ans_content
                if "result\": \"normal" in ans_content:
                    logger.warning(f"[SendSms] user:{user}, zhipu-pig-ai says normal, {ans_content}, "
                                   f"query:[{current_content}]")
                    return filter_constant.AI_RSP_VALID_CONTENT, ans_content

                logger.error(
                    f"[SendSms] user:{user}, zhipu-pig-ai says json invalid, {ans_content}, query:[{current_content}]")
                ans = {"result": "unknown", "reason": "json invalid"}

            reason = ans.get('reason', '')

            if str(ans.get('result', '')).lower() == "normal":
                logger.info(f"[SendSms] user:{user}, zhipu-pig-ai says normal, {ans}, query:[{current_content}]")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
            elif str(ans.get('result', '')).lower() == "fraud" or str(ans.get('result', '')).lower() == "high-risk":
                logger.warning(f"[SendSms] user:{user}, 杀猪盘警告, {ans}, query:[{current_content}], prompt:{prompt}")
                return filter_constant.AI_RSP_INVALID_CONTENT, reason
            elif str(ans.get('result', '')).lower() == "medium-risk":
                logger.warning(f"[SendSms] user:{user}, zhipu-pig-ai says medium-risk,{ans}, query:[{current_content}]")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
            elif str(ans.get('result', '')).lower() == "low-risk":
                logger.warning(f"[SendSms] user:{user}, zhipu-pig-ai says low-risk, {ans}, query:[{current_content}]")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
            else:
                logger.error(f"[SendSms] user:{user}, zhipu-pig-ai says unknown, {ans}, query:[{current_content}]")
                return filter_constant.AI_RSP_UNKNOWN, reason
        except Exception as ex:
            if "并发数过高" in str(ex) or "qps" in str(ex).lower():
                logger.warning(f"[SendSms] user:{user},  zhipu-pig-ai says failed, over qps, {current_content}")
            elif "系统检测到" in str(ex):
                logger.error(f"[SendSms] user:{user}, zhipu-pig-ai says failed, 系统检测到, {current_content}",
                             exc_info=True)
                return filter_constant.AI_RSP_INVALID_CONTENT, "zhipu-invalid-input系统检测到"
            else:
                logger.error(f"[SendSms] user:{user},  zhipu-pig-ai says failed [{current_content}]", exc_info=True)
            return filter_constant.AI_RSP_UNKNOWN, "zhipu-pig-failed"

    @staticmethod
    def is_pic_bad(user: str, conversation_context_20_content_list: list, latest_send_20_messages: list) -> (bool, str):
        """
        检测短信内容是否为杀猪盘诈骗

        Args:
            user: user
            conversation_context_20_content_list: 与当前接收者的最近20条对话记录
            latest_send_20_messages: 用户最近发送的20条短信(可能发给不同接收者)

        Returns:
            (是否违规, 违规原因)
        """
        # 没历史数据，就不审查了
        if not conversation_context_20_content_list and not latest_send_20_messages:
            return filter_constant.AI_RSP_VALID_CONTENT, "no history data"

        # 构建对话历史字符串
        conversation_history = ""
        if conversation_context_20_content_list and len(conversation_context_20_content_list) > 0:
            conversation_history = "\n".join(
                [f"Message{i + 1}: {msg}" for i, msg in enumerate(conversation_context_20_content_list[-20:])])

        # 构建用户发送模式字符串
        user_pattern = ""
        if latest_send_20_messages and len(latest_send_20_messages) > 0:
            user_pattern = "\n".join([f"[User Under Review] Send record{i + 1}: {msg}"
                                      for i, msg in enumerate(latest_send_20_messages[:20])])

        try:
            prompt = origin_pic_prompt
            prompt = prompt.replace("{conversation_history}", conversation_history)
            prompt = prompt.replace("{user_pattern}", user_pattern)

            start = time.time()
            client, client_name = ZhiPuClient.get_zhipu_fatpo_client()
            logger.info(f"[SendSms] user:{user}, zhipu-pig-ai-pic, prompt: {prompt}")

            response = client.chat.completions.create(
                model="glm-4-air-250414",
                messages=[
                    {"role": "user", "content": prompt},
                ],
                user_id="51081731492666544",
                max_tokens=512,
            )
            answer = response.choices[0].message
            logger.info(f"[SendSms] user:{user}, zhipu-pig-ai-pic, cost: {round(time.time() - start, 2)} s, {answer}")

            ans_content = json.loads(answer.to_json())['content']
            ans_content = ans_content.replace("\n", "").replace("\\n", "") \
                .replace("```", "").replace("json", "").strip()

            try:
                # 提取JSON
                match = re.search(r'\{(.*?)}', ans_content)
                if match:
                    ans_content = match.group(0)

                ans = json.loads(ans_content)
            except Exception:
                if "fraud" in ans_content or "high-risk" in ans_content:
                    logger.error(f"[SendSms] user:{user}, zhipu-pig-ai-pic json invalid but fraud: {ans_content}")
                    return filter_constant.AI_RSP_INVALID_CONTENT, ans_content
                if "result\": \"normal" in ans_content:
                    logger.warning(f"[SendSms] user:{user}, zhipu-pig-ai says normal, {ans_content}")
                    return filter_constant.AI_RSP_VALID_CONTENT, ans_content

                logger.error(
                    f"[SendSms] user:{user}, zhipu-pig-ai-pic says json invalid, {ans_content}")
                ans = {"result": "unknown", "reason": "json invalid"}

            reason = ans.get('reason', '')

            if str(ans.get('result', '')).lower() == "normal":
                logger.info(f"[SendSms] user:{user}, zhipu-pig-ai-pic says normal, {ans}")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
            elif str(ans.get('result', '')).lower() == "fraud" or str(ans.get('result', '')).lower() == "high-risk":
                logger.warning(f"[SendSms] user:{user}, 杀猪盘警告, {ans}, prompt:{prompt}")
                return filter_constant.AI_RSP_INVALID_CONTENT, reason
            elif str(ans.get('result', '')).lower() == "medium-risk":
                logger.warning(f"[SendSms] user:{user}, zhipu-pig-ai says medium-risk,{ans}")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
            elif str(ans.get('result', '')).lower() == "low-risk":
                logger.warning(f"[SendSms] user:{user}, zhipu-pig-ai says low-risk, {ans}")
                return filter_constant.AI_RSP_VALID_CONTENT, reason
            else:
                logger.error(f"[SendSms] user:{user}, zhipu-pig-ai-pic says unknown, {ans}")
                return filter_constant.AI_RSP_UNKNOWN, reason
        except Exception as ex:
            if "并发数过高" in str(ex) or "qps" in str(ex).lower():
                logger.warning(f"[SendSms] user:{user},  zhipu-pig-ai-pic says failed, over qps")
            elif "系统检测到" in str(ex):
                logger.warning(f"[SendSms] user:{user}, zhipu-pig-ai-pic says failed, 系统检测到", exc_info=True)
                return filter_constant.AI_RSP_INVALID_CONTENT, "zhipu-invalid-input系统检测到"
            else:
                logger.error(f"[SendSms] user:{user},  zhipu-pig-ai-pic says failed", exc_info=True)
            return filter_constant.AI_RSP_UNKNOWN, "zhipu-pig-pic-failed"
