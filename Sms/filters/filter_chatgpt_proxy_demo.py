import json
import time

import requests


class ChatGptUtilDemo:
    @staticmethod
    def is_content_bad(user_id: int, sms_content: str):
        endpoint = "https://api.aiproxy.io/v1/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer sk-evixMGNXmBw4rxd1eUFOYEXxsX9njJzjTUsSXSRCdKApeLFk"
        }

        if not sms_content:
            print(f"[ChatGptUtil.is_sms_phishing] param invalid: user_id: {user_id}, sms_content: {sms_content}")
            return True

        content = """你是一名网络内容审查员，负责审查英文短信的内容, 请根据用户输入的文本，判断是否涉及：吸毒、大麻、毒品、骚扰、种族歧视等各种歧视(性别、宗教、年龄、性取向、残疾等群体的贬低、侮辱或歧视性言论、隐晦的歧视性语言或刻板印象)、暴力、威胁、恐吓、挑衅、加密货币诈骗、投资诈骗、杀猪盘诈骗、色情、隐晦的招嫖邀请、电信诈骗、钓鱼诈骗、伪装身份诈骗、彩票基金诈骗、威胁诈骗等一切不良不合规不合法的内容。特别是电信杀猪盘，您应该理解杀猪盘的意思，杀猪盘几个要素：
                    1.杀猪盘诈骗利用虚假的网络身份诱骗受害者进行欺诈性投资。
                    2.诈骗者获取信任、操纵情绪并利用金融漏洞窃取资金。
                    3.现实生活中的例子显示了受害者所遭受的毁灭性的经济和情感打击。
                    4.发现未经请求的消息等迹象并验证信息可以帮助避免诈骗。
                   并输出一段格式为json的文本。
                   
                   需严格遵守以下规则:
                   ##规则##
                   规则1.返回必须是json格式，格式如下:{"result":"yes", "reason": "这只是普通的打招呼，不涉及色情和诈骗"}
                   规则2.json的`result`字段选值只能是这3个："yes","no","unknown",其中"yes"表示包含不良内容，"no"表示不包含，"unknown"表示不清楚。
                   规则3.回答仅返回json文本内容，不要返回其它无关信息.
                   规则4.如果没有以上提及的违规、不合法、不合格的内容，那就是返回no。

                   ##示例##
                   [用户输入]:Greetings from Worldwide Travel Club! This is Matt, Reservations Mgr. We were waiting for you in your scheduled webinar yesterday at 5pm. What happened?
                   [输出]:{"result":"yes", "reason": "骚扰、诈骗"}
                   [用户输入]:Greetings, You have been selected to pick cotton at the nearest plantation. Be ready at 12AM NOVEMBER 13 SHARP with your belongings. Our Executive Slaves will come get you in a Brown Van, be prepared to be searched down once you've enter the plantation. You are in Plantation Group A.
                   [输出]:{"result":"yes", "reason": "种族歧视"}
                   [用户输入]:i ordered a shirt and pants from a store located in the mall named “Sherway Gardens” Can you please pick it for me
                   [输出]:{"result":"unknown", "reason": "从短信内容来看，它没有直接涉及电信诈骗、钓鱼或其他不良内容。它似乎是在请求帮助。"}
                   [用户输入]:700$ overnight? or 200 QV ? call me back if interested
                   [输出]:{"result":"yes", "reason": "色情招嫖"}
                   [用户输入]:I had a great time having sex with you last night. Do you want to see my big cock?
                   [输出]:{"result":"yes", "reason": "色情"}
                   [用户输入]:hello, how are you?
                   [输出]:{"result":"no", "reason": "单纯打招呼"}
                   [用户输入]:Congratulations, you have received the largest prize in Michigan, $200 million. Please provide your ID information so that I can verify it for you.
                   [输出]:{"result":"yes", "reason": "短信奖金诈骗"}
                   [用户输入]:FBI warning, you are currently suspected of a criminal offense. Please click on this link immediately, otherwise we will come to arrest you immediately.
                   [输出]:{"result":"yes", "reason": "短信诈骗"}
                   [用户输入]:Ana Young are you free? \n\nAbigail Dillen
                   [输出]:{"result":"yes", "reason": "身份诈骗，打招呼后面加身份名字，就是伪装钓鱼诈骗"}
                   [用户输入]:I'm in a meeting at the moment. I can't make a call, and i need you to get a task done
                   [输出]:{"result":"yes", "reason": "身份诈骗，假装某个人伪装不方便电话的环境，然后要求被诈骗者做一些指令"}
                   [用户输入]:Head to the store and keep me updated with the value available for Apple Card
                   [输出]:{"result":"yes", "reason": "金钱诈骗，指示被诈骗者去购买gift Card,apple card等用来转移金钱诈骗"}
                   [用户输入]:You little dwarf, don't let me see you. If I see you, I'll hit you
                   [输出]:{"result":"yes", "reason": "恐吓、威胁"}
                   [用户输入]:Do you still have those drugs from last time?
                   [输出]:{"result":"yes", "reason": "毒品"}
                   [用户输入]:New tea arrived,come and taste it!
                   [输出]:{"result":"yes", "reason": "new tea会被暗示成新的女孩子, 隐晦的招嫖，色情，暗示会所来了新的女孩子"}
                   [用户输入]:Called Mebo, and I am from Pennsylvania.
                   [输出]:{"result":"yes", "reason": "伪造身份造假"}
                   [用户输入]:Hey it’s McKenna kirras friend your number is on your Facebook so I got it from there. Don’t tell kirra I’m texting you this but let’s plan a surprise trip for kirra and you guys come here. Hunter and I will buy the tickets I just need to know your work shedule since I work too so we can figure something out then I’ll send kirra the tickets to surprise her. Sound like a plan?
                   [输出]:{"result":"yes", "reason": "听起来像是蛊惑，诈骗，伪造身份的诈骗"}
                   
                   [用户输入]:""" + sms_content

        payload = {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": content}],
            "user": str(user_id),
            "max_tokens": 64
        }
        start = time.time()
        response = requests.post(endpoint, headers=headers, json=payload)
        print(response.text)

        answer = response.json()["choices"][0]["message"]["content"].replace("\n", "").strip()
        print(f"[ChatGptUtil.is_sms_phishing] user_id: {user_id} chat query:[[{sms_content}]]"
              f"cost: {round(time.time() - start, 2)} s, {answer}")
        print(answer)
        try:
            answer_json = json.loads(answer)
        except json.decoder.JSONDecodeError:
            answer_json = {"result": "unknown"}
            answer = answer.replace(" ", "").strip()
            if answer[-2:] != '\"}':
                answer = answer + "\"}"
                answer_json = json.loads(answer)
            if answer[-1] != '}':
                answer = answer + '}'
                answer_json = json.loads(answer)
        if answer_json["result"] == "yes":
            return True
        return False


if __name__ == '__main__':
    content_list = [
       # """Greetings, You have been selected to pick cotton at the nearest plantation.Be ready at 12AM NOVEMBER 13 SHARP with your belongings. Our Executive Slaves will come get you in a Brown Van, be prepared to be searched down once you've enter the plantation. You are in Plantation Group A.""",
        "New tea is here, come and taste it!",
        "120 hhr, 160 hr",
    ]
    for c in content_list:
        print("#" * 50)
        r = ChatGptUtilDemo.is_content_bad(user_id=0, sms_content=c)
        print(r)
