import json
import re
import time
from typing import Tuple

from SecPhone.settings import logger
from Sms.ai.zhipu_client import ZhiPuClient

client, client_name = ZhiPuClient.get_zhipu_fatpo_client()
model_name = "glm-4v-plus-0111"
security_prompt = """你是一名专业的图片审查专家，专注于识别图片是否违禁、涉黄、名人、黄金、钞票、香车等。  
### 检测要求：
需要对一张图片进行安全审核，它可能涉黄，违禁。 如果你认为这张图片不应该过审，请返回 invalid，并给一个小于100个单词的reason。 否则返回 normal。

### 违规规则
1. 违禁图片：暴力、血腥、恐怖、毒品、枪支、刀具、军火、爆炸物等。  
2. 涉黄图片：色情、性行为、性暗示、过度裸露、低俗擦边内容。  
3. 名人: 涉及明星、政治人物、公众人物的照片。 
4. 金钱、黄金、钞票、香车等直接吸引人的诈骗图片
5. 证件隐私：身份证、护照、驾驶证、学生证、工牌等。  
6. 官方文件：获奖证书、发票、合同、银行通知函、政府/公司通知。
7. 获奖图片
8. 任何形式的通知
9. paypal、cashapp等和资金有关的app等
10. 联系方式：手机号、微信号、QQ、邮箱、二维码等联系方式。  

### 输出格式（JSON）：  
{
  "result": "normal/invalid",
  "reason": "Briefly explain the reason"
}

### 输出要求：  
1. 请注意json格式，单引号和双引号必须严格正确。
"""


class ZhiPuPicUtil:
    """内容审核工具类"""

    @staticmethod
    def check_content_safety(user: str, image_url: str) -> Tuple[bool, str]:
        try:
            start = time.time()
            response = ZhiPuPicUtil._call_zhipu_api(image_url)

            if not response or not response.choices:
                return False, "Empty response from API"

            answer = response.choices[0].message
            logger.info(f"[SendSms] user:{user}, zhipu-pic-ai, query:[[{image_url}]], "
                        f"cost: {round(time.time() - start, 2)} s, {answer}")

            ans_content = json.loads(answer.to_json())['content']
            ans_content = ans_content.replace("\n", "").replace("\\n", "") \
                .replace("```", "").replace("json", "").strip()

            try:
                # 提取JSON
                match = re.search(r'\{(.*?)}', ans_content)
                if match:
                    ans_content = match.group(0)

                ans = json.loads(ans_content)
            except Exception:
                if "result\": \"normal" in ans_content:
                    logger.error(f"[SendSms] user:{user}, zhipu-pig-ai json invalid but true: {ans_content}, "
                                 f"image_url: {image_url}")
                    return True, ans_content
                if "result\": \"invalid" in ans_content:
                    logger.error(f"[SendSms] user:{user}, zhipu-pig-ai json invalid but false: {ans_content}, "
                                 f"image_url: {image_url}")
                    return False, ans_content

                logger.error(f"[SendSms] user:{user}, zhipu-pig-ai says json invalid, {ans_content}, image:{image_url}")
                ans = {"result": "unknown", "reason": "json invalid"}

            reason = ans.get('reason', '')

            if str(ans.get('result', '')).lower() == "normal":
                logger.info(f"[SendSms] user:{user}, zhipu-pig-ai says normal, {ans}, image:[{image_url}]")
                return True, reason
            if str(ans.get('result', '')).lower() == "invalid":
                logger.info(f"[SendSms] user:{user}, zhipu-pig-ai says normal, {ans}, image:[{image_url}]")
                return False, reason
            return False, "unknown:" + str(ans_content)
        except Exception as e:
            return False, f"Error checking content safety: {str(e)}"

    @staticmethod
    def _call_zhipu_api(image_url: str):
        """
        调用智谱AI API

        Args:
            image_url: 图片URL

        Returns:
            Any: API原始响应
        """
        try:
            response = client.chat.completions.create(
                model=model_name,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": security_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_url
                                }
                            }
                        ]
                    }
                ]
            )
            return response
        except Exception as e:
            raise Exception(f"API call error: {str(e)}")


if __name__ == '__main__':
    is_valid, _reason = ZhiPuPicUtil.check_content_safety(
        "123",
        "https://api.v3phone.xyz/uploads/2025-06-19/1750341446_ZkK3CSdI.jpeg"  # 涉黄
        # "https://api.v3phone.xyz/uploads/2025-06-13/1749852439_N7O1dmvh.jpeg"  # 正常网页
        # "http://phone2.zehougroup.xyz/uploads/2025-06-18/1750287007_eYQtchqP.jpeg"  # 威胁
        # "http://phone2.zehougroup.xyz/uploads/2025-09-07/1757216681_G3tnJgfW.jpeg"  # 扎克伯格
        # "https://p29.zdusercontent.com/attachment/2374520/ni3pmwGDzgW3mi2dVbhat0ThL?token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2In0..w34_L0DYDZmWS-3ohRZ5xA.OvkfHtGGrHc7FKIVS9Rcm69YoaQtfQjhSn92hEpvrrXwodkn3eUcnjBAd0rH2TYlmRt0NPORDkGv2TUAcU8lOGnF1rleQTumhc_ZdqtAunLqbtOv82blDKUSd7e4XMCGt_W2sAsdVhoG2Rw8vp4acza_TmBRJX5xP2FfkYQy-EO_GsktfSkXmwGlKeMk4zSKBQfe2kL5-6BS0xT6jjeG5HwZtCPyS8wRwcAgAhHi1nZWq018yr4Sk_j07Zf2Dgaiu4wvE42xmCas4wSkLOIsavKVf1GopTXmSjvCO_nooWk.CoLtQebTyjbHeGCHdHgW8Q"  # 银行通知单
        # "https://p29.zdusercontent.com/attachment/2374520/wKoWHkVESnxC24VCfAWMJAYSC?token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2In0..qJ-Ujz1XEtbS9DH4DKGoug.nZoVozmzS8jdImRTgE1HgEJ36ySrcfFww_9yyoRNFxMAyXf8niLP17M-RYkHaCoRLLseOZTGkLlbzTWvqRDOqAei4U1uYCczxTJhG935i-Xoy1Mr2RBp47MLqFLaRKPPbswPAupm39VznGAxlq1VaILxFp2LpBJVuZDMyMGo_3eksDYTVrKav2b4bH0W4zNvukazbm-rtoVUvfor0PiTiT3LgTgKqoOErPF7iz45kIj_xO6SOeTZVovFSGKMKGrD2Pth6-YoaRocneGHGovxZpEiA5U__929JaUwR4Jv7ak.kxTiRAK4Tut6lXp1q57hXA"  # paypal
    )
    print(f"is_valid: {is_valid}, reason: {_reason}")
