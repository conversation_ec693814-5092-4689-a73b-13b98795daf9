import json

from googleapiclient import discovery

API_KEY = 'AIzaSyDHpDskbGpy2CjbvpSvlgg3TLAvnL7GVLM'

context = '''
hi sweetie are you available tonight?
Hey 
Are you available?
Hi
Hello 
Nice how long do yoy wanna
Hey princess love to do a massage with you nuru but I only do cash if that’s good let me know we can do this today I’m horny?
I'm available
I'd like an appointment for later tonight 
Hello 👋
Can i have more info?
Hi, I just landed on your pictures and loved them.
Hi. 
What's your name and where are you from dear fan
Oh nice talking to you
Hello 👋 
Cota the 💈 barber 
Are you still interested
Are you there
Are you there
'''

if __name__ == '__main__':
    client = discovery.build(
        "commentanalyzer",
        "v1alpha1",
        developerKey=API_KEY,
        discoveryServiceUrl="https://commentanalyzer.googleapis.com/$discovery/rest?version=v1alpha1",
        static_discovery=False,
    )

    analyze_request = {
        'comment': {'text': context},
        'requestedAttributes': {'TOXICITY': {}},
        'spanAnnotations': True

    }

    response = client.comments().analyze(body=analyze_request).execute()
    print(json.dumps(response, indent=2))
