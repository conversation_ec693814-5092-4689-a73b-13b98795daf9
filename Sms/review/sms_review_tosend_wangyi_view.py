import datetime
import json

from django.core.cache import cache

from Common.err import ErrInfo
from Common.timeutil import TimeUtil
from Common.views import SecPhoneView
from Order.tools.tool_order import OrderTool
from SecPhone import settings
from SecPhone.settings import logger
from Sms.filters.filter_wangyi_manual import sms_filter_wangyi_manual
from Sms.info.sms_info_bean import SMSInfo
from Sms.msg_pipeline import MsgPipeline
from Sms.tools.tool_sms_add import SmsAddTool
from Sms.tools.tool_sms_review import SmsReviewTool
from User.tools.user_tool import UserTool


class SmsReviewWangyiManual(SecPhoneView):
    @staticmethod
    def get_user_level(user_created_at: datetime.datetime) -> int:
        # 用户等级，小于 1 个月都是1 级
        current_time = TimeUtil.GetNow()
        time_difference = current_time - user_created_at
        user_level = 1 if time_difference <= datetime.timedelta(days=30) else 3
        return user_level

    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, ("fatpotoken",))
        if err_code != 0:
            logger.info(f"[SmsReviewWangyiManual] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

        if data["fatpotoken"] != "jiejie":
            logger.info(f"[SmsReviewWangyiManual] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(err_code, err_msg)

        records = SmsReviewTool.get_all_need_to_review_sms()
        res = []
        for index, r in enumerate(records):
            if r.user_id in settings.WHITE_USER_LIST:
                logger.info(f"[SmsReviewWangyiManual] user: {r.user_id} is white user, not send")
                r.review_result = "user is while user, no more judge"
                r.deleted = 1
                r.save()
                continue

            # 防止重复送审
            key = "send_to_review_sms" + str(r.id)
            is_send = cache.get(key)
            logger.info(f"[SmsReviewWangyiManual] wait for review key：{key}, is_send:{is_send}")
            if is_send:
                continue

            user = UserTool.get_user_by_id(r.user_id)
            if not user:
                r.review_result = "user is deleted, no more judge"
                r.deleted = 1
                r.save()
                continue

            SmsReviewTool.update_review_record(r, user)
            user_context = {
                "MessageId": r.id,
                "UserId": r.user_id,
                "Number": r.from_number + "->" + r.to_number,
                "ContentZh": r.content_zh,
                "ReviewReason": r.not_send_reason,
                "Created": TimeUtil.GetBeijingTimeStr(r.created_at),
                "VipExpired": OrderTool.get_user_expire(r.user_id),
                "RegisterDays": r.user_register_days,
                "ContactCount": r.contact_count,
                "SmsCount": r.sms_count,
                "Last10Sms": r.last_n_sms_zh,
            }
            user_level = self.get_user_level(user.created_at)
            wangyi_task_id = sms_filter_wangyi_manual.request_wangyi(r.user_id, r.content, r.ip, r.uuid, user_level,
                                                                     user.created_at, user_context)
            r.wangyi_task_id = wangyi_task_id
            r.save()

            cache.set(key, "1", 30 * 24 * 3600)

        logger.info(f"[SmsReviewWangyiManual] wait for review size：{len(res)}")
        return self.ReturnSuccess(data=res)


class SmsReviewWangyiManualCallback(SecPhoneView):
    def post(self, request):
        logger.info(f"[SmsReviewWangyiManualCallback] {request.body} {request.POST}")
        callback_data = request.POST.get("callbackData", [])
        if len(callback_data) == 0:
            logger.error(f"[SmsReviewWangyiManualCallback] callback size is 0")
            return self.ReturnSuccessToWangyi()

        callback_data = json.loads(callback_data)
        logger.info(f"[SmsReviewWangyiManualCallback] callback_data: {callback_data}")
        antispam = callback_data['antispam']
        task_id = antispam['taskId']
        result_type = antispam['resultType']

        if result_type != 2:
            logger.error(f"[SmsReviewWangyiManualCallback] record not manual judge: {task_id}")
            return self.ReturnErrorToWangyi(ErrInfo.UNKNOWN_SERVER_ERROR)

        record = SmsReviewTool.get_by_wangyi_taskid(task_id)
        if not record:
            return self.ReturnSuccessToWangyi()

        suggestion = antispam['suggestion']
        if suggestion == 0:
            logger.info(f"[SmsReviewWangyiManualCallback] approve, {suggestion}-{task_id},"
                        f"filtered_content: {record.filtered_content}, origin_content={record.content}")

            # 正常发送
            sms_info = SMSInfo(user_id=record.user_id, from_number=record.from_number, to_number=record.to_number,
                               direction=settings.SMS_DIRECTION_SEND,
                               origin_content=record.content, ip='', uuid='', latest_ts=record.latest_ts,
                               is_fake_mode=False)
            sms_info.is_first_check_bad = True
            sms_info.ai_rsp = SMSInfo.AI_UNKNOWN
            MsgPipeline.do_send(sms_info)

            record.review_result = "approve"
            record.deleted = 1
            record.judge_type = 1
            record.save()
            return self.ReturnSuccessToWangyi()
        elif suggestion == 2:
            logger.warning(f"[SmsReviewWangyiManualCallback] reject, {suggestion}-{task_id},"
                           f"filtered_content: {record.filtered_content}, origin_content={record.content}")
            record.review_result = "reject"
            record.deleted = 1
            record.judge_type = 1
            record.save()

            # fake发送
            sms_info = SMSInfo(user_id=record.user_id, from_number=record.from_number, to_number=record.to_number,
                               direction=settings.SMS_DIRECTION_SEND,
                               origin_content=record.content, ip='', uuid='', latest_ts=record.latest_ts,
                               is_fake_mode=False)
            sms_info.is_first_check_bad = True
            sms_info.ai_rsp = SMSInfo.AI_UNKNOWN
            sms_info.is_fake_send = True
            sms_info.err_rsp_list = ["wangyi-reject"]
            SmsAddTool.add_fake_sms_record(sms_info)

            return self.ReturnSuccessToWangyi()
        else:
            logger.error(
                f"[SmsReviewWangyiManualCallback] suggestion:{suggestion}, manual judge invalid action: {task_id}, "
                f"filtered_content: {record.filtered_content}")
            return self.ReturnErrorToWangyi(ErrInfo.UNKNOWN_SERVER_ERROR)
