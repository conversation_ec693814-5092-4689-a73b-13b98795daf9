class WangyiFilterRet(object):

    def __init__(self, task_id='', suggestion=0, label=0, sub_label=0, details='', filtered_content=''):
        self.is_unknown_content = True
        self.is_ok_content = False
        self.task_id = task_id
        self.suggestion = suggestion
        self.label = label  # 自定义 999 is chatgpt   7777-link,8888-es,9999-ai
        self.sub_label = sub_label  # 自定义 8888 is es
        self.details = details
        self.filter_content = filtered_content

    def __str__(self):
        return f"is_unknown_content={self.is_unknown_content},is_ok_content={self.is_ok_content},task_id={self.task_id}," \
               f"suggestion={self.suggestion},label={self.label},sub_label={self.sub_label},details={self.details}," \
               f"filter_content={self.filter_content}"
