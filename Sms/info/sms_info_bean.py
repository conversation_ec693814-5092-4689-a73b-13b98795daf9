import json


class SMSInfo:
    AI_VALID = 1
    AI_INVALID = 2
    AI_UNKNOWN = 3

    def __init__(self, user_id: int, from_number: str, to_number: str, origin_content: str, latest_ts: int, ip: str,
                 uuid: str, is_fake_mode: bool, direction: str):
        self.user_id = user_id
        self.from_number = from_number
        self.to_number = to_number
        self.origin_content = origin_content
        self.filtered_content = origin_content
        self.is_first_check_bad = False
        self.is_bot_bad = False
        self.ai_rsp = 0
        self.is_fake_send = False
        self.is_non_english = False
        self.is_send_failed = False
        self.ip = ip
        self.uuid = uuid
        self.latest_ts = int(latest_ts)
        self.is_fake_mode = is_fake_mode
        self.is_use_fatpo_ai = True
        self.err_rsp_list = []
        self.direction = direction
        self.sid = ""
        self.status = "sent"
        self.parts = 0
        self.image_url = ""
        self.is_need_calc_cost_later = True  # 需要等会儿，自己计算cost
        self.not_finished_reason = ''  # 未完成短信的原因
        self.is_reviewed = 0  # 审核
        self.wangyi_task_id = ""  # 网易 id
        self.image_md5 = ""  # 图片md5

    def __str__(self):
        return f"SMSInfo(user_id={self.user_id}, from_number={self.from_number}, to_number={self.to_number}, " \
               f"origin_content={self.origin_content}, latest_ts={self.latest_ts}, ip={self.ip}, " \
               f"uuid={self.uuid}, is_fake_mode={self.is_fake_mode}, direction={self.direction})," \
               f"is_reviewed:{self.is_reviewed},wangyi_task_id:{self.wangyi_task_id}"

    def to_json(self):
        return json.dumps(self.__dict__)

    def to_dict(self):
        return self.__dict__


def create_not_finished_info(user_id: int, direction: str, from_number: str, to_number: str,
                             origin_content: str, image_url: str):
    sms_info = SMSInfo(user_id=user_id, direction=direction, from_number=from_number, to_number=to_number,
                       origin_content=origin_content, ip="", uuid="", latest_ts=0, is_fake_mode=False)
    sms_info.image_url = image_url
    return sms_info
