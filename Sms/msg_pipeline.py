import copy
import math
import threading

import SecPhone.logid_middleware
from Common import util
from Common.util import Util
from MyTelnyx.my_telnyx import TelnyxUtil
from SecPhone import settings
from SecPhone.settings import logger
from Sms.filters import filter_constant
from Sms.filters.filter_baidu import Bai<PERSON><PERSON><PERSON>ilter
from Sms.filters.filter_combo_words import Combo<PERSON>ordsFilterUtil
from Sms.filters.filter_direct_giveup_words import DirectGiveUpWordsFilterUtil
from Sms.filters.filter_direct_pass_words import DirectPassWordsFilterUtil
from Sms.filters.filter_english import SmsEnglishFilter
from Sms.filters.filter_replace_can_pass_words import ReplaceCanPassWordsUtil
from Sms.filters.filter_simple import sms_filter_simple
from Sms.filters.filter_zhipu_proxy_basic import ZhipuBasicUtil
from Sms.filters.filter_zhipu_proxy_basic_pig import ZhipuPigButcheringUtil
from Sms.filters.filter_zhipu_proxy_blackmail import ZhipuBlackMailUtil
from Sms.info import sms_info_bean
from Sms.info.sms_info_bean import SMSInfo
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from Sms.tools.tool_sms import SmsTool
from Sms.tools.tool_sms_add import SmsAddTool
from Sms.tools.tool_sms_error import SmsErrorTool
from Sms.tools.tool_sms_review import SmsReviewTool
from User.tools.user_kill_tool import UserKillTool


class MsgPipeline(object):
    @staticmethod
    def async_send(sms_info: SMSInfo):
        # 子线程任务函数，使用上下文运行
        thread = threading.Thread(target=MsgPipeline.send_pipeline,
                                  args=(SecPhone.logid_middleware.logid_context.get(), sms_info,))
        thread.start()

    @staticmethod
    def send_pipeline(thread_logid: str, sms_info: SMSInfo) -> SMSInfo:
        print_content = sms_info.origin_content.replace("\n", " ").replace("\t", "").strip()
        try:
            # 设置logid
            SecPhone.logid_middleware.logid_context.set(thread_logid)

            # 检查合规
            sms_info = MsgPipeline.check_traffic_violations(sms_info)
            user_id = sms_info.user_id

            # 如果是暂时屏蔽
            if UserKillTool.is_tmp_mute(user_id):
                logger.warning(
                    f"[SendSms] user_id:{user_id}, query:[{print_content}] to:{sms_info.to_number}, tmp mute")
                sms_info.err_rsp_list.append("tmp mute")
                sms_info.is_reviewed = 1
                SmsAddTool.add_fake_sms_record(sms_info)
                return sms_info

            # 如果短期内给某个人有连续发送，就不发送了
            history_sms_5_minutes_ago = SmsTool.get_by_to_number_and_content(user_id, sms_info.to_number,
                                                                             sms_info.origin_content)
            if history_sms_5_minutes_ago:
                logger.warning(
                    f"[SendSms] user_id:{user_id}, query:[{print_content}] to:{sms_info.to_number}, send in 5 min")
                sms_info.err_rsp_list.append("send in 5min")
                sms_info.is_reviewed = 1  # 不用人工二审
                SmsAddTool.add_fake_sms_record(sms_info)
                return sms_info

            # 如果命中一些黑名单的就不要检测了
            if DirectGiveUpWordsFilterUtil.check_direct_give_up_words(sms_info.origin_content):
                logger.info(f"[SendSms] user_id:{user_id}, query:[{print_content}] hit black, direct giveup")
                sms_info.err_rsp_list.append("骂人_有害")
                sms_info.is_reviewed = 1  # 不用人工二审
                SmsAddTool.add_fake_sms_record(sms_info)
                return sms_info

            # 如果一些严格的白名单，可以发送
            if DirectPassWordsFilterUtil.check_if_match_white_list(sms_info.origin_content):
                logger.info(f"[SendSms] user_id:{user_id}, query:[{print_content}] hit white, direct pass")
                sms_info.ai_rsp = SMSInfo.AI_VALID
                MsgPipeline.do_send(sms_info)
                return sms_info

            # 拿到用户的对话次数，这个特征非常关键，熟人与否，很重要！！！
            delivered_cnt, received_cnt = SmsTool.get_number_delivered_and_received(user_id, sms_info.to_number)
            logger.info(f"[SendSms] user:{user_id}, delivered_cnt: {delivered_cnt}, received_cnt:{received_cnt}")

            # 杀猪盘检测
            conversation_list = SmsTool.get_conversation_sms_top_n_for_pig(user_id, sms_info.to_number, top_n=20)
            latest_sms_list = SmsTool.get_latest_send_sms_top_n(user_id, top_n=20)
            latest_sms_json_list = [{"content": v.filtered_content if v.filtered_content else v.content,
                                     "to_number": v.to_number} for v in latest_sms_list]
            rsp, reason = ZhipuPigButcheringUtil.is_content_basic_bad(str(user_id), sms_info.filtered_content,
                                                                      conversation_list, latest_sms_json_list)
            if rsp == filter_constant.AI_RSP_INVALID_CONTENT:
                sms_info.err_rsp_list.append("kill pig:" + reason[0:1900])
                SmsAddTool.add_fake_sms_record(sms_info)
                return sms_info

            # 自家fatpoAI审核
            if user_id == 1214162 and sms_info.is_fake_mode and sms_info.is_use_fatpo_ai == False:
                # 测试模式下，并且主动关闭fatpo ai，才不走fatpo ai
                logger.warning(
                    f"[SendSms] user:{user_id}, is_use_fatpo_ai:{sms_info.is_use_fatpo_ai}, skip fatpo ai...")
            else:
                bad_words, replace_text = ComboWordsFilterUtil.check_combo_words(sms_info.origin_content,
                                                                                 sms_info.filtered_content)
                if len(bad_words) > 0:
                    if delivered_cnt >= 30 and received_cnt >= 15:
                        # 有问题的话，熟人就替换****
                        sms_info.filtered_content = replace_text
                        logger.warning(f"[SendSms] user:{user_id}, fatpo-ai says bad but valid, "
                                       f"delivered_cnt:{delivered_cnt}, received_cnt:{received_cnt}, "
                                       f"sms: [{print_content}], after filter:[{sms_info.filtered_content}]")
                        sms_info.err_rsp_list.append(",".join(bad_words))
                        sms_info.err_rsp_list.append("fatpo_replace_auto")
                    else:
                        # 有问题的话，生人就直接屏蔽fake 了
                        sms_info.filtered_content = replace_text
                        logger.warning(f"[SendSms] user:{user_id}, fatpo-ai says bad sms: [{bad_words}] "
                                       f"in [{print_content}], direct give up...")
                        sms_info.err_rsp_list.append(",".join(bad_words))
                        SmsAddTool.add_fake_sms_record(sms_info)
                        return sms_info
                else:
                    logger.info(f"[SendSms] user:{user_id}, fatpo-ai says valid sms: [{print_content}]")

            # 如果命中一些白名单的就不要检测了
            if DirectPassWordsFilterUtil.check_direct_pass_words(sms_info.origin_content):
                logger.info(f"[SendSms] user_id:{user_id}, query:[{print_content}] hit white, direct pass")
                sms_info.ai_rsp = SMSInfo.AI_VALID
                MsgPipeline.do_send(sms_info)
                return sms_info

            # 字母太少就不要检测了吧 -1
            if 0 < len(sms_info.origin_content) <= 5:
                logger.info(f"[SendSms] user_id:{user_id}, query:[{print_content}], direct pass...")
                sms_info.ai_rsp = SMSInfo.AI_VALID
                sms_info.err_rsp_list.append("direct_pass_little_words1")
                MsgPipeline.do_send(sms_info)
                return sms_info

            # 大多数*就放弃吧
            if Util.is_most_asterisks_count(sms_info.filtered_content):
                logger.warning(f"[SendSms] content most star, {user_id}:{sms_info.filtered_content}")
                sms_info.err_rsp_list.append("too_many_star")
                SmsAddTool.add_fake_sms_record(sms_info)
                return sms_info

            # 字母太少就不要检测了吧 -2
            if len(sms_info.origin_content) < 15 and Util.calc_letter_cnt(sms_info.origin_content) <= 4:
                logger.info(f"[SendSms] user_id:{user_id}, query:[{print_content}] easy, direct pass...")
                sms_info.ai_rsp = SMSInfo.AI_VALID
                sms_info.err_rsp_list.append("direct_pass_little_words2")
                MsgPipeline.do_send(sms_info)
                return sms_info

            # 额外加智谱 AI的审核，通审
            ai_zhipu_ans, zhipu_category = ZhipuBasicUtil.is_content_basic_bad(sms_info.origin_content,
                                                                               sms_info.filtered_content)
            if ai_zhipu_ans == filter_constant.AI_RSP_INVALID_CONTENT:
                logger.info(f"[SendSms] user:{user_id}, zhipu-ai says bad sms, direct give up...")
                sms_info.ai_rsp = SMSInfo.AI_INVALID
                sms_info.err_rsp_list.append(f"zhipu-ai says invalid:{zhipu_category[0:128]}")
                SmsAddTool.add_fake_sms_record(sms_info)

                # 如果是敲诈勒索，直接删号
                if ZhipuBlackMailUtil.is_content_blackmail(
                        sms_info.origin_content) == filter_constant.AI_RSP_INVALID_CONTENT:
                    logger.error(
                        f"[SendSms] 敲诈勒索，直接删号！user_id: {user_id} 's sms content: {sms_info.origin_content}")
                    UserKillTool.kill_user(user_id, -1997)
                    return sms_info

                return sms_info

            # 如果智普说行
            if ai_zhipu_ans == filter_constant.AI_RSP_VALID_CONTENT:
                logger.info(f"[SendSms] user:{user_id} no need check by other ai, direct pass...")
                MsgPipeline.do_send(sms_info)
                return sms_info

            # 开始AI二审
            ai_flag = "baidu"
            ai_ans, ai_reason = BaiduAIFilter.is_sms_bad(user_id, sms_info.origin_content)

            # 开始根据AI结果分流
            if ai_ans == filter_constant.AI_RSP_INVALID_CONTENT:
                # AI明确不行的，直接不走人类审核，一棍子打死！
                logger.info(f"[SendSms] user:{user_id}, ai says bad sms, direct give up: [{print_content}]")
                sms_info.ai_rsp = SMSInfo.AI_INVALID
                sms_info.err_rsp_list.append(f"{ai_flag}-ai says invalid:" + ai_reason[0:40])
                SmsAddTool.add_fake_sms_record(sms_info)
                return sms_info

            elif ai_ans == filter_constant.AI_RSP_UNKNOWN:
                # AI拿不准的，走人工审核，看怎么抢救
                logger.warning(f"[SendSms] user:{user_id}, ai says unknown sms, send to human check: [{print_content}]")
                sms_info.err_rsp_list.append("ai says unknown")
                sms_info.ai_rsp = SMSInfo.AI_UNKNOWN
                SmsReviewTool.save_review_sms_v2(sms_info)
                return sms_info

            elif ai_ans == filter_constant.AI_RSP_VALID_CONTENT:
                # AI 明确不是坏短信的，直接放行
                logger.info(f"[SendSms] user:{user_id}, ai says ok sms, direct pass...")
                sms_info.ai_rsp = SMSInfo.AI_VALID
                MsgPipeline.do_send(sms_info)
                return sms_info

            else:
                # 编码错误：返回值异常的，让人类来
                logger.error(f"[SendSms] user:{user_id} need check by ai, ai says unknown sms, send to human check..."
                             f"[{print_content}]")
                sms_info.err_rsp_list.append("ai says unknown")
                sms_info.ai_rsp = SMSInfo.AI_UNKNOWN
                SmsReviewTool.save_review_sms_v2(sms_info)
                return sms_info

        except Exception as e:
            user_id = sms_info.user_id
            from_number = sms_info.from_number
            to_number = sms_info.to_number
            origin_content = sms_info.origin_content
            logger.error(f"[SendSms] {user_id}:{from_number}->{to_number}:[{print_content}], failed:", exc_info=True)

            # 加到未完成短信中
            sms_info = sms_info_bean.create_not_finished_info(user_id=user_id, from_number=from_number,
                                                              to_number=to_number,
                                                              direction=settings.SMS_DIRECTION_SEND,
                                                              origin_content=origin_content, image_url='')
            sms_info.not_finished_reason = f"[SendSMS] send failed:{str(e)}"
            SmsAddTool.add_not_finished_sms_call_record(sms_info=sms_info,
                                                        event_type=settings.EVENT_TYPE_SMS,
                                                        not_finished_code=settings.NOT_FINISHED_CARRIER_FAILED)
            return sms_info

    @staticmethod
    def check_traffic_violations(sms_info: SMSInfo) -> SMSInfo:
        user_id = sms_info.user_id
        to_number = sms_info.to_number

        # 替换一下奇怪的字符 先预处理：替换非GSM7，比如标点符号词汇
        sms_info.origin_content = sms_filter_simple.replace_non_gsm7(sms_info.origin_content)
        sms_info.filtered_content = sms_info.origin_content

        # 替换不好的词汇 1: sex words
        sms_info.filtered_content = ReplaceCanPassWordsUtil.replace_can_pass_words(sms_info.filtered_content)

        # 替换不好的词汇 2: 无伤大雅单词如 fuck, sex, nigga
        sms_info.filtered_content = sms_filter_simple.clean_up_text(sms_info.filtered_content)

        # 替换不好的词汇 3: 容易误伤小单词
        is_ok, _filtered_content, bad_words_collection = sms_filter_simple.check_little_bad_words(
            sms_info.origin_content,
            sms_info.filtered_content)
        sms_info.filtered_content = _filtered_content
        if not is_ok:
            logger.warning(
                f"[SendSms] user: {user_id} send to: {to_number}, little words:[{sms_info.filtered_content}]")
            sms_info.err_rsp_list.append(f'contains ban word_{"#".join(bad_words_collection)}')
            sms_info.is_first_check_bad = True

        # 去掉表情，但是如果只有一个表情就算了
        _filtered_content = sms_filter_simple.remove_emojis(sms_info.filtered_content)
        if len(_filtered_content) > 0:
            sms_info.filtered_content = _filtered_content

        # 替换带符号的字母比如 corazón perdó
        sms_info.filtered_content = util.Util.remove_accents(sms_info.filtered_content)

        # 非英语的就不操心了
        is_english = SmsEnglishFilter.is_english(sms_info.origin_content)
        if not is_english:
            logger.info(f"[SendSms] non english pass, user:{user_id}, content: {sms_info.origin_content}")
            sms_info.is_non_english = True

        return sms_info

    @staticmethod
    def do_send(sms_info: SMSInfo):
        if sms_info.is_fake_mode:
            logger.warning(f"[SendSms] fake sms return: {sms_info.origin_content}")
            return

        user_id = sms_info.user_id
        from_number = sms_info.from_number
        to_number = sms_info.to_number
        origin_content = sms_info.origin_content

        try:
            filtered_content = sms_info.filtered_content.strip()
            if len(filtered_content) == 0:
                logger.error(f"[SendSms] content is nil, {user_id}:{from_number}->{to_number}")
                return

            # 初次发送带Reply STOP
            if SmsTool.if_first_conversation(user_id, to_number) and user_id not in settings.WHITE_USER_LIST:
                filtered_content = filtered_content + " [Reply STOP to end receipt]"
                logger.info(f"[SendSms] first_conversation, {user_id}:{from_number}->{to_number}:{filtered_content}")

            logger.info(f"[SendSms] do send, {user_id}:{from_number}->{to_number}:{filtered_content}")
            sms_info.is_need_calc_cost_later = False  # 因为会有webhook回调来计算
            batch_size = 600
            content_size = len(filtered_content)
            if content_size <= batch_size:
                sms_info.sid = TelnyxUtil.send_sms(from_number, to_number, filtered_content)
                SmsAddTool.add_sms_record(sms_info)
                logger.info(f"[SendSms] message sid: {sms_info.sid}, {user_id} -> {to_number} success")
            else:
                batch_cnt = math.ceil(content_size / batch_size)
                for i in range(batch_cnt):
                    _content = filtered_content[i * batch_size:(i + 1) * batch_size]
                    sms_info_copy = copy.deepcopy(sms_info)
                    sms_info_copy.sid = TelnyxUtil.send_sms(from_number, to_number, _content)
                    sms_info_copy.filtered_content = _content
                    sms_info_copy.latest_ts = sms_info_copy.latest_ts + 1 + i
                    SmsAddTool.add_sms_record(sms_info=sms_info_copy)
                    logger.info(f"[SendSms] batch:{i}, length: {len(_content)}, sid: {sms_info_copy.sid}, "
                                f"{user_id} -> {to_number} success")

        except Exception as e:
            sms_info.is_send_failed = True
            SmsAddTool.add_failed_sms_record(sms_info)

            # 已知的提示友好告警，友好提示
            err, is_unknown = SmsErrorTool.create_sms_error(e, from_number, to_number)
            if is_unknown:
                logger.error(f"[SendSms] {user_id}:{from_number}->{to_number}:{origin_content} failed", exc_info=True)
            else:
                logger.warning(f"[SendSms] {user_id}:{from_number}->{to_number}:{origin_content} failed", exc_info=True)

            # 加到和 it-support的对话列表
            support_content = SmsNoticeTool.sms_delivery_failed(err.err_msg, origin_content)
            SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                             settings.APP_IT_SUPPORT_SHOW_PHONE, from_number, support_content)
