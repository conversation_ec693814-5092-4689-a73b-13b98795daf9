# https://www.onbuka.com/zh-cn/sms-api3/
# hahailan


import hashlib
import json
import time

import requests

base_url = "https://api.onbuka.com/v3"
api_key = "VeRLsEP0"
api_pwd = "b8Zdrcp6"
appid = "lht7BOEP"


def create_headers():
    timestamp = int(time.time())
    s = "%s%s%s" % (api_key, api_pwd, str(timestamp))
    sign = hashlib.md5(s.encode(encoding='UTF-8')).hexdigest()

    headers = {
        'Content-Type': 'application/json;charset=utf-8',
        'Sign': sign,
        'Timestamp': str(timestamp),
        'Api-Key': api_key
    }

    return headers


headers = create_headers()


def buka_send_sms(from_number: str, to_number: str, content: str, sid: str) -> (dict, bool):
    url = "%s/sendSms" % base_url
    to_number = to_number.replace("+", "")
    number_suffix = from_number[-4:]
    body = {"appId": appid, "numbers": to_number,
            "content": f"[From +1******{number_suffix}][not reply this number] {content}",
            "senderId": "", "orderId": sid}
    rsp = requests.post(url, json=body, headers=headers)
    if rsp.status_code == 200:
        res = json.loads(rsp.text)
        return res, True
    else:
        return rsp.content, False


if __name__ == '__main__':
    res, is_ok = buka_send_sms("+13151234567", "+16166996028", "wow! unblock me!!!", "1")
    print(res, is_ok)
