import math

from Call.models import SMSRecord, SMSCallNotFinishedRecord
from Common.util import Util
from SecPhone import settings
from SecPhone.settings import logger
from Sms.info.sms_info_bean import SMSInfo
from Sms.tools.tool_sms_cost import SmsCostTool


class SmsAddTool:

    @staticmethod
    def add_sms_record(sms_info: SMSInfo):
        sid = sms_info.sid
        direction = sms_info.direction
        user_id = sms_info.user_id
        from_number = sms_info.from_number
        to_number = sms_info.to_number
        content = sms_info.origin_content
        filtered_content = sms_info.filtered_content
        latest_ts = sms_info.latest_ts
        status = sms_info.status
        is_non_english = sms_info.is_non_english
        is_first_check_bad = sms_info.is_first_check_bad
        is_bot_bad = sms_info.is_bot_bad
        ai_rsp = sms_info.ai_rsp
        parts = sms_info.parts
        first_check_reason = ",".join(sms_info.err_rsp_list)
        wangyi_task_id = sms_info.wangyi_task_id

        if filtered_content and not parts:
            parts = math.ceil(len(filtered_content) / 140)

        if content and not parts:
            parts = math.ceil(len(content) / 140)

        if not status:
            status = "sent"

        if not sid or not user_id or (not content and not filtered_content) or not from_number or not to_number \
                or not latest_ts or not parts or not status:
            logger.error(f'[SendSms] add_sms_record invalid, sid:{sid}, direction:{direction}, user:{user_id}, '
                         f'from:{from_number}, to:{to_number}, content:[{content}], latest_ts:{latest_ts}, '
                         f'status:{status}, parts:{parts},status:{status},wangyi_task_id:{wangyi_task_id}')

        logger.info(f'[SendSms] add_sms_record sid:{sid}, direction:{direction}, user:{user_id}, from:{from_number},'
                    f'to:{to_number}, content:[{content}], latest_ts:{latest_ts}, status:{status}, parts:{parts},'
                    f'first_check_reason:{first_check_reason},status:{status},wangyi_task_id:{wangyi_task_id}')

        record = SMSRecord(
            sid=sid, user_id=user_id, direction=direction.upper(), from_number=from_number, to_number=to_number,
            content=content, filtered_content=filtered_content, latest_ts=int(latest_ts),
            status=status, err_code=0, is_non_english=is_non_english, is_first_check_bad=is_first_check_bad,
            is_bot_bad=is_bot_bad, ai_rsp=ai_rsp, parts=parts, first_check_reason=first_check_reason,
            wangyi_task_id=wangyi_task_id,
        )
        record.save()

        # 延迟触发cost
        if sms_info.is_need_calc_cost_later:
            logger.info(f'[SendSms] add_sms_record sid:{sid} need to calc cost later')
            SmsCostTool.update_sms_record_telnyx_delay(sid)
        else:
            logger.info(f'[SendSms] add_sms_record sid:{sid} no need to calc cost later')

    @staticmethod
    def add_failed_sms_record(sms_info: SMSInfo):
        sid = Util.GenFakeSId()
        user_id = sms_info.user_id
        from_number = sms_info.from_number
        to_number = sms_info.to_number
        direction = sms_info.direction
        content = sms_info.origin_content
        filtered_content = sms_info.filtered_content
        latest_ts = sms_info.latest_ts
        is_first_check_bad = sms_info.is_first_check_bad
        is_bot_bad = sms_info.is_bot_bad
        ai_rsp = sms_info.ai_rsp
        first_check_reason = ",".join(sms_info.err_rsp_list)
        wangyi_task_id = sms_info.wangyi_task_id

        logger.info(f'[SendSms] add_failed_sms_record direction:{direction}, user:{user_id}, from:{from_number}, '
                    f'to:{to_number}, content:[{content}], latest_ts:{latest_ts}, '
                    f'is_first_check_bad:{is_first_check_bad}, is_bot_bad:{is_bot_bad}, ai_rsp:{ai_rsp},'
                    f'first_check_reason:{first_check_reason},wangyi_task_id:{wangyi_task_id}')
        record = SMSRecord(
            sid=sid, user_id=user_id, direction=direction.upper(), from_number=from_number, to_number=to_number,
            content=content, filtered_content=filtered_content, latest_ts=int(latest_ts), status="sent_failed",
            err_code=1, is_first_check_bad=is_first_check_bad, is_bot_bad=is_bot_bad, ai_rsp=ai_rsp, price=0, point=0,
            first_check_reason=first_check_reason, is_non_english=sms_info.is_non_english,
            wangyi_task_id=wangyi_task_id,
        )
        record.save()

    @staticmethod
    def add_mms_record(sms_info: SMSInfo):
        sid = sms_info.sid
        direction = sms_info.direction
        user_id = sms_info.user_id
        from_number = sms_info.from_number
        to_number = sms_info.to_number
        latest_ts = sms_info.latest_ts
        status = sms_info.status
        image_url = sms_info.image_url
        first_check_reason = ",".join(sms_info.err_rsp_list)
        wangyi_task_id = sms_info.wangyi_task_id

        if not sid or not user_id or not image_url or not from_number or not to_number or not latest_ts:
            logger.error(f'[SendSms] add_mms_record invalid, sid:{sid}, direction:{direction}, user:{user_id}, '
                         f'from:{from_number}, to:{to_number}, image_url={image_url}, latest_ts={latest_ts}, '
                         f'status={status},wangyi_task_id:{wangyi_task_id}')

        logger.info(f'[SendSms] add_mms_record sid:{sid}, direction:{direction}, user:{user_id}, from:{from_number}, '
                    f'to:{to_number}, image_url={image_url}, latest_ts={latest_ts},'
                    f'first_check_reason:{first_check_reason},wangyi_task_id:{wangyi_task_id}')

        record = SMSRecord(
            sid=sid, user_id=user_id, direction=direction.upper(), from_number=from_number, to_number=to_number,
            images=image_url, latest_ts=int(latest_ts), status=status, err_code=0, parts=0,
            first_check_reason=first_check_reason, wangyi_task_id=wangyi_task_id, is_image=1,
            image_md5=sms_info.image_md5,
        )
        record.save()

        # 延迟触发cost
        if sms_info.is_need_calc_cost_later:
            logger.info(f'[SendSms] add_mms_record sid:{sid} need to calc cost later')
            SmsCostTool.update_sms_record_telnyx_delay(sid)
        else:
            logger.info(f'[SendSms] add_mms_record sid:{sid} no need to calc cost later')

    @staticmethod
    def add_failed_mms_record(sms_info: SMSInfo):
        sid = Util.GenFakeSId()
        user_id = sms_info.user_id
        from_number = sms_info.from_number
        to_number = sms_info.to_number
        direction = sms_info.direction
        latest_ts = sms_info.latest_ts
        image_url = sms_info.image_url
        first_check_reason = ",".join(sms_info.err_rsp_list)
        wangyi_task_id = sms_info.wangyi_task_id

        logger.info(f'[SendSms] add_failed_sms_record direction:{direction}, user:{user_id}, from:{from_number}, '
                    f'to:{to_number}, image_url:{image_url}, latest_ts:{latest_ts},'
                    f'first_check_reason:{first_check_reason},wangyi_task_id:{wangyi_task_id}')
        record = SMSRecord(
            sid=sid, user_id=user_id, direction=direction.upper(), from_number=from_number, to_number=to_number,
            images=image_url, latest_ts=int(latest_ts), status="sent_failed", err_code=1,
            first_check_reason=first_check_reason, wangyi_task_id=wangyi_task_id, is_image=1,
        )
        record.save()

    @staticmethod
    def add_fake_sms_record(sms_info: SMSInfo):
        if sms_info.is_fake_mode:
            sms_info.is_reviewed = 1
            sms_info.status = "tested"

        sid = Util.GenFakeSId()
        user_id = sms_info.user_id
        from_number = sms_info.from_number
        to_number = sms_info.to_number
        content = sms_info.origin_content
        filtered_content = sms_info.filtered_content
        latest_ts = sms_info.latest_ts
        reason = sms_info.err_rsp_list
        is_first_check_bad = sms_info.is_first_check_bad
        is_bot_bad = sms_info.is_bot_bad
        ai_rsp = sms_info.ai_rsp
        first_check_reason = ",".join(sms_info.err_rsp_list)
        parts = math.ceil(len(sms_info.origin_content) / 140)
        wangyi_task_id = sms_info.wangyi_task_id

        logger.warning(f'[SendSms] add_fake_sms_record user:{user_id}, reason: {reason}, '
                       f'from:{from_number}, to:{to_number}, content=[{content}], latest_ts={latest_ts},'
                       f'is_first_check_bad:{is_first_check_bad}, is_bot_bad:{is_bot_bad}, ai_rsp:{ai_rsp},'
                       f'first_check_reason:{first_check_reason},parts:{parts},filtered_content:{filtered_content}',
                       f'is_reviewed:{sms_info.is_reviewed},wangyi_task_id:{wangyi_task_id}')
        record = SMSRecord(
            sid=sid, user_id=user_id, direction=settings.SMS_DIRECTION_SEND,
            from_number=from_number,
            to_number=to_number,
            content=content,
            filtered_content=filtered_content,
            latest_ts=int(latest_ts),
            status="send_fake",
            is_non_english=sms_info.is_non_english,
            is_fake_send=1,
            is_first_check_bad=is_first_check_bad,
            is_bot_bad=is_bot_bad,
            ai_rsp=ai_rsp,
            price=0,
            first_check_reason=first_check_reason,
            parts=parts,
            is_reviewed=sms_info.is_reviewed,
            wangyi_task_id=wangyi_task_id,
        )
        record.save()

        # 延迟触发cost
        SmsCostTool.update_sms_record_realtime_telnyx(sid)

    @staticmethod
    def add_not_finished_sms_call_record(sms_info: SMSInfo, event_type: str, not_finished_code: int):
        try:
            user_id = sms_info.user_id
            from_number = sms_info.from_number
            to_number = sms_info.to_number
            not_finished_reason = sms_info.not_finished_reason
            content = sms_info.origin_content
            image_url = sms_info.image_url
            logger.warning(f'[SmsAddTool] add_not_finished_sms_call_record user:{user_id}, '
                           f'not_finished_reason: {not_finished_reason},'
                           f'from:{from_number}, to:{to_number}, content=[{content}],image_url:{image_url}')

            # 短信彩信内部微调
            if event_type == settings.EVENT_TYPE_SMS:
                if content:
                    event_type = settings.EVENT_TYPE_SMS
                if image_url:
                    event_type = settings.EVENT_TYPE_MMS

            record = SMSCallNotFinishedRecord(
                user_id=user_id,
                direction=settings.SMS_DIRECTION_SEND,
                from_number=from_number,
                to_number=to_number,
                content=content,
                image_url=image_url,
                event_type=event_type,
                not_finished_reason=not_finished_reason,
                not_finished_code=not_finished_code,
            )

            if sms_info.sid:
                record.message_sid = sms_info.sid
            if sms_info.status:
                record.message_status = sms_info.status

            record.save()
        except Exception:
            logger.error(f'[SmsAddTool] add_not_finished_sms_call_record user:{sms_info} failed', exc_info=True)

    @staticmethod
    def add_fake_mms_record(sms_info: SMSInfo):
        sid = Util.GenFakeSId()
        user_id = sms_info.user_id
        from_number = sms_info.from_number
        to_number = sms_info.to_number
        image_url = sms_info.image_url
        latest_ts = sms_info.latest_ts
        reason = sms_info.err_rsp_list
        is_first_check_bad = sms_info.is_first_check_bad
        is_bot_bad = sms_info.is_bot_bad
        ai_rsp = sms_info.ai_rsp
        first_check_reason = ",".join(sms_info.err_rsp_list)
        wangyi_task_id = sms_info.wangyi_task_id
        logger.warning(f'[SendSms] add_fake_mms_record user:{user_id}, reason: {reason}, '
                       f'from:{from_number}, to:{to_number}, image_url={image_url}, latest_ts={latest_ts},'
                       f'is_first_check_bad:{is_first_check_bad}, is_bot_bad:{is_bot_bad}, ai_rsp:{ai_rsp},'
                       f'first_check_reason:{first_check_reason}, is_reviewed:{sms_info.is_reviewed},'
                       f'wangyi_task_id:{wangyi_task_id}')
        record = SMSRecord(
            sid=sid, user_id=user_id, direction=settings.SMS_DIRECTION_SEND,
            from_number=from_number,
            to_number=to_number,
            content='',
            filtered_content='',
            images=image_url,
            latest_ts=int(latest_ts),
            status="send_fake",
            is_fake_send=1,
            is_first_check_bad=is_first_check_bad,
            is_bot_bad=is_bot_bad,
            ai_rsp=ai_rsp,
            price=0,
            first_check_reason=first_check_reason,
            parts=0,
            is_reviewed=sms_info.is_reviewed,  # 默认不需要 review
            wangyi_task_id=wangyi_task_id,
            is_image=1,
            image_md5=sms_info.image_md5,
        )
        record.save()

        # 延迟触发cost
        SmsCostTool.update_sms_record_realtime_telnyx(sid)
