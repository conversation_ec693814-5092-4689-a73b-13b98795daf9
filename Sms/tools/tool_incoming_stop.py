import json
import re
import time

import zhipuai

from SecPhone.settings import logger
from Sms.ai.zhipu_client import ZhiPuClient

prompt = """你是一名英语短信审核员，负责判断接收到的短信内容是否包含“停止”（stop）的意图。

待审核短信内容：
<content>{content}</content>

判断规则如下：
1. 若短信包含礼貌表达“pls stop”、“please stop”或“just stop”，判定为有停止意图。
2. 若短信表述为“如果你再打电话或发短信给我，我就XXX”，如 “If u keep texting me I will report this to the authorities”，判定为有停止意图。
3. 若短信提到“我会举报你（的号码）”，如 “I will report this number” ，判定为有停止意图。
4. 若短信提到“不要再打电话给我”，“不要再发短信给我”如 “DONT text ME”， “DONT CALL ME”， “DONT CALLING ME” ，判定为有停止意图。  
   **（注意：即使语法错误，如 "DONT CALLING ME"，也应判定为有停止意图）**
5. 若短信有不礼貌表达，如 “stop asshole”、“fuck u stop”、“fucking stop” 或 “please don't text me again”，判定为有停止意图。
6. 短信中可能有拼写错误（如 “dnot” 识别为 “don’t”）或语法错误（如 “calling” 识别为 “call”），但仍能表达“停止”意图时，判定为有停止意图。  
   **（例如，“DONT CALLING ME” 应该理解为 “DONT CALL ME”）**
7. 若短信包含“text stop to opt - out”或类似退订指令，这是系统选项，不判定为有停止意图。
8. 支持模糊匹配，允许一定拼写错误，如 “dnot” 可识别为 “don’t” 。结合上下文分析，即便有拼写或语法错误，只要结合句子结构能推断出“停止”意图，也判定为有。

<judgment>[请在此处填写"Yes"或"No"]</judgment>
"""


class IncomingMsgStopTool:
    @staticmethod
    def is_stop_message(from_number: str, content: str) -> bool:
        try:
            # 确保是人类的号码
            if len(from_number) <= 10:
                return False

            # 可能是图片
            if not content:
                return False

            content = content.replace("\n", "").replace("\t", "").strip().lower()
            if content == "stop":
                logger.warning(f"[IncomingMsg][is_stop_message] from_number:{from_number}, query:[[{content}]] stop")
                return True

            # 一般都很短
            if len(content.split(" ")) >= 20:
                logger.info(f"[IncomingMsg][is_stop_message] from_number:{from_number}, query:[[{content}]] "
                            f"too long, give up")
                return False

            # 太短的不用检查
            if len(content) <= 3:
                logger.info(f"[IncomingMsg][is_stop_message] from_number:{from_number}, query:[[{content}]] "
                            f"too short, give up")
                return False

            client, client_name = ZhiPuClient.get_zhipu_fatpo_client()

            start = time.time()
            response = client.chat.completions.create(
                model="GLM-4-Air-0111",
                messages=[
                    {"role": "user", "content": prompt.replace("{content}", content)},
                ],
                user_id="51081731492666544",
                max_tokens=64,
            )
            answer = response.choices[0].message
            logger.info(f"[IncomingMsg][is_stop_message] zhipu-ai, from_number:{from_number}, query:[[{content}]], "
                        f"cost: {round(time.time() - start, 2)} s, {answer}")

            ans_content = json.loads(answer.to_json())['content']
            ans_content = (ans_content.replace("\n", "").replace("\\n", "").replace("```", "")
                           .replace("json", "").strip()).lower()

            if "请把需要审核的短信" in ans_content or "我会根据你的规则" in ans_content:
                logger.warning(f"[IncomingMsg][is_stop_message] zhipu-ai, answer invalid, from_number:{from_number}, "
                               f"query:[[{content}]], answer:{ans_content}")
                return False

            ans_content = IncomingMsgStopTool.extract_judgment(ans_content)
            ans_content = (ans_content.replace("</judgment>", "")
                           .replace("<judgment>", "").strip().lower())

            logger.info(f"[IncomingMsg][is_stop_message] zhipu-ai, from_number:{from_number},  query:[[{content}]], "
                        f"final answer: {ans_content}")

            if ans_content == "yes":
                logger.warning(f"[IncomingMsg][is_stop_message] zhipu-ai, answer yes, from_number:{from_number}, "
                               f"query:[[{content}]]")
                return True
            if ans_content == "no":
                logger.warning(f"[IncomingMsg][is_stop_message] zhipu-ai, answer no, from_number:{from_number}, "
                               f"query:[[{content}]]")
                return False
            if ans_content == "":
                logger.warning(f"[IncomingMsg][is_stop_message] zhipu-ai, answer invalid, from_number:{from_number}, "
                               f"query:[[{content}]], {answer}")
                return False
            logger.error(f"[IncomingMsg][is_stop_message] zhipu-ai, answer invalid, from_number:{from_number}, "
                         f"query:[[{content}]], {answer}")
            return False
        except zhipuai.APIRequestFailedError as e:
            if "不安全或敏感内容" in str(e):
                logger.warning(f"[IncomingMsg][is_stop_message] zhipu-ai failed, from_number:{from_number}, "
                               f"query:[[{content}]]: {e}")
            else:
                logger.error(f"[IncomingMsg][is_stop_message] zhipu-ai failed, from_number:{from_number}, "
                             f"query:[[{content}]]", exc_info=True)
        except Exception as e:
            logger.error(f"[IncomingMsg][is_stop_message] zhipu-ai failed, from_number:{from_number}, "
                         f"query:[[{content}]]: {e}", exc_info=True)
            return False

    @staticmethod
    def clean_judgment_tag(content):
        # 修正 <judgment"Yes"</judgment> 或 <judgment "Yes"</judgment> → <judgment>Yes</judgment>
        content = re.sub(r'<judgment["\s]*([^">]*)["\s]*>', r'<judgment>\1>', content, flags=re.IGNORECASE)

        # 修正 < judgment >Yes</ judgment > → <judgment>Yes</judgment>
        content = re.sub(r'<\s*judgment\s*>', '<judgment>', content, flags=re.IGNORECASE)
        content = re.sub(r'<\s*/\s*judgment\s*>', '</judgment>', content, flags=re.IGNORECASE)

        # 修正 <judgment>Yes< /judgment> → <judgment>Yes</judgment>
        content = re.sub(r'<judgment>(.*?)<\s*/\s*judgment\s*>', r'<judgment>\1</judgment>', content,
                         flags=re.IGNORECASE)

        # 只保留第一个完整的 <judgment> 标签内容，删除其他
        matches = re.findall(r'<judgment>(.*?)</judgment>', content, flags=re.IGNORECASE)
        if matches:
            content = f"<judgment>{matches[0]}</judgment>"
        else:
            content = ""

        return content

    @staticmethod
    def extract_judgment(text):
        if "judgment" not in text:
            return ""

        text = text.replace("\n", "")

        if "\"Yes\"<".lower() in text.lower():
            return "yes"

        if "\"No\"<".lower() in text.lower():
            return "no"

        if ">Yes<".lower() in text.lower():
            return "yes"

        if ">No<".lower() in text.lower():
            return "no"

        if "No".lower() in text.lower():
            return "no"

        if "Yes".lower() in text.lower():
            return "yes"

        text = IncomingMsgStopTool.clean_judgment_tag(text)
        match = re.search(r'<judgment>(.*?)</judgment>', text, re.IGNORECASE)
        if match:
            return match.group(1).strip().lower()
        else:
            logger.error(f"[IncomingMsg][is_stop_message] zhipu-ai, judgment tag not found in response: {text}")
            return ""


if __name__ == '__main__':
    content_list = [
        ("If u keep texting me I will report this to the authorities", True),
        ("DONT CALLING ME!!!", True),
        ("how are u", False),
        ("stop", True),
        ("pls stop", True),
        ("please stop", True),
        ("DONT text ME idgaf!!!", True),
        ("stop please", True),
        ("stop pls", True),
        ("dnot texting again", True),
        ("dude you're pissing me off", False),
        ("u will not be coming here that's for s fact", False),
        ("westkiss hair:welcome, beauty!glad to have you here!be our new member, enjoy $180 off "
         "coupon!!!>https://westkiss.catse.co/25mf5lkr0u text stop to opt-out", False),
    ]
    for (c, expected_rsp) in content_list:
        print("#######################")
        print("content:", c)
        print("expected_rsp:", expected_rsp)
        ai_rsp = IncomingMsgStopTool.is_stop_message("+1234567890", c)
        if expected_rsp != ai_rsp:
            print("expected_rsp:", expected_rsp, "!=", "ai_rsp:", ai_rsp, "content:", c)
            print("expected_rsp:", expected_rsp, "!=", "ai_rsp:", ai_rsp, "content:", c)
            print("expected_rsp:", expected_rsp, "!=", "ai_rsp:", ai_rsp, "content:", c)
