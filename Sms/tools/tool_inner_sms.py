from Call.models import SMSRecord
from Common.timeutil import TimeUtil
from Common.util import Util
from Number.tools.number_tool import NumberTool
from Push.push_util import PushUtil
from SecPhone import settings
from SecPhone.settings import logger
from Sms.tools.tool_sms_cost import SmsCostTool


class SmsInnerTool:
    @staticmethod
    def is_both_inner(user_id: int, from_number: str, to_number: str, content: str) -> bool:
        if NumberTool.is_number_in_inventory(from_number) and NumberTool.is_number_in_inventory(to_number):
            logger.info(f"[SmsInnerTool] is_both_inner user:{user_id}, {from_number} -> {to_number}: {content}")
            return True
        return False

    @staticmethod
    def add_inner_sms_both(from_user_id: int, to_user_id: int, from_number: str, to_number: str, content: str):
        try:
            logger.warning(f"[SmsInnerTool] add_inner_sms_both, {from_user_id}->{to_user_id}, "
                           f"{from_number}->{to_number}, content:{content}")
            outgoing_sid = SmsInnerTool.add_inner_sms(from_user_id, settings.SMS_DIRECTION_SEND,
                                                      from_number, to_number, content)
            incoming_sid = SmsInnerTool.add_inner_sms(to_user_id, settings.SMS_DIRECTION_RECEIVE,
                                                      from_number, to_number, content)
            logger.info(f"[SmsInnerTool] add_inner_sms_both, {from_user_id}->{to_user_id}, "
                        f"{from_number}->{to_number}, outgoing_sid:{outgoing_sid}, incoming_sid:{incoming_sid}")

            SmsCostTool.update_sms_record_realtime_telnyx(outgoing_sid)
            SmsCostTool.update_sms_record_realtime_telnyx(incoming_sid)

        except Exception:
            logger.error(f"[SmsInnerTool] add_inner_sms_both, {from_user_id}->{to_user_id}, "
                         f"{from_number}->{to_number}, content:{content}", exc_info=True)

    @staticmethod
    def add_inner_mms_both(from_user_id: int, to_user_id: int, from_number: str, to_number: str, image_url: str):
        try:
            logger.info(f"[SmsInnerTool] add_inner_sms_both, {from_user_id}->{to_user_id}, "
                        f"{from_number}->{to_number}, image_url:{image_url}")
            outgoing_sid = SmsInnerTool.add_inner_sms(from_user_id, settings.SMS_DIRECTION_SEND,
                                                      from_number, to_number, "", image_url)
            incoming_sid = SmsInnerTool.add_inner_sms(to_user_id, settings.SMS_DIRECTION_RECEIVE,
                                                      from_number, to_number, "", image_url)
            logger.info(f"[SmsInnerTool] add_inner_sms_both, {from_user_id}->{to_user_id}, "
                        f"{from_number}->{to_number}, outgoing_sid:{outgoing_sid}, incoming_sid:{incoming_sid}")

            SmsCostTool.update_sms_record_realtime_telnyx(outgoing_sid)
            SmsCostTool.update_sms_record_realtime_telnyx(incoming_sid)

        except Exception:
            logger.error(f"[SmsInnerTool] add_inner_mms_both, {from_user_id}->{to_user_id}, "
                         f"{from_number}->{to_number}, image_url:{image_url}", exc_info=True)

    @staticmethod
    def add_inner_sms(user_id: int, direction: str, from_number: str, to_number: str, content: str, images_urls=''):
        fake_message_sid = Util.GenFakeSId()
        if direction == settings.SMS_DIRECTION_SEND:
            status = "delivered"
        else:
            status = "received"

        logger.info(f'[add_inner_sms] sid:{fake_message_sid}, direction:{direction}, user:{user_id}, '
                    f'{from_number}->{to_number}, content={content}, images_urls={images_urls}, status:{status}')
        try:
            latest_ts = TimeUtil.GetNowTsInInt()
            record = SMSRecord(
                sid=fake_message_sid, user_id=user_id, direction=direction.upper(), from_number=from_number,
                to_number=to_number,
                content=content, filtered_content='', images=images_urls, latest_ts=latest_ts,
                status=status, price=0, point=1, is_inner=1, parts=1,
            )
            record.save()

            if images_urls:
                record.parts = 0
                record.save()

            # 因为是假短信，只能自己实现推送
            if direction == settings.SMS_DIRECTION_RECEIVE:
                # 不要给自己推送了
                if user_id == settings.APP_IT_SUPPORT_USERID:
                    pass
                else:
                    PushUtil.notify_by_user(user_id, 1, content)


        except Exception:
            logger.error(f"[SmsInnerTool.add_inner_sms] user:{user_id}, from:{from_number}, to:{to_number}, "
                         f"sid:{fake_message_sid}, content:{content}, save error: ", exc_info=True)

        return fake_message_sid
