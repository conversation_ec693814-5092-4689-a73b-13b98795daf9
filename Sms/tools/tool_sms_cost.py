from threading import Timer

from django.conf import settings

from MyTelnyx.my_telnyx import TelnyxUtil
from Point.models import PointRecord
from Point.pointcommon import PointCommon
from SecPhone.settings import logger
from Sms.tools.tool_sms import SmsTool


class SmsCostTool:

    @staticmethod
    def update_sms_record_telnyx_delay(sid):
        t = Timer(15.0, SmsCostTool.update_sms_record_realtime_telnyx, (sid,))
        t.start()

    @staticmethod
    def update_sms_record_realtime_telnyx(sid: str, sms_record=None):
        if sms_record:
            logger.info(f"[update_sms_record_realtime_telnyx] sid:{sid} without search db")
            record = sms_record
        else:
            record = SmsTool.get_by_sid(sid)
            if not record:
                logger.error(f"[update_sms_record_realtime_telnyx] give up update cost, sid not in db: {sid}")
                return

        # 判断是否彩信
        is_mms = True if record.images else False

        try:
            # price
            if record.price is None or record.price == -1:
                cost = TelnyxUtil.get_sms_cost_by_id(sid)
                if cost:
                    record.price = cost

            # points
            if is_mms is False:
                record.point = settings.POINT_PER_UNIT['SMS'] * max(record.parts, 1)
            else:
                record.point = settings.POINT_PER_UNIT['MMS']

            if record.point < 1:
                logger.error(f"[update_sms_record_realtime_telnyx] sid: {sid}, points invalid: {record.point}")
                record.point = 1

            record.save()

            # 如果没有处理过点数，顺便处理
            if PointRecord.objects.filter(user_id=record.user_id, record_id=record.id,
                                          event__in=['SMS', 'MMS']).count() == 0:
                msg_type = 'SMS' if is_mms is False else 'MMS'
                PointCommon.Add(record.user_id, 0 - record.point, msg_type, record.id)
                logger.info(f"[update_sms_record_realtime_telnyx] user_id:{record.user_id}, sid:{sid},"
                            f" add point record: {record.point}")
            else:
                logger.info(f"[update_sms_record_realtime_telnyx] user_id:{record.user_id}, sid:{sid} "
                            f"already handle point, give up handle point.")
        except Exception:
            logger.error(f"[update_sms_record_realtime_telnyx] user_id:{record.user_id}, record_id: {record.id}, "
                         f"sid: {sid} error", exc_info=True)
