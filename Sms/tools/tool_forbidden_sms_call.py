from Call.tool_call import CallTool
from Number.tools.number_tool import <PERSON>Tool
from SecPhone.settings import logger
from Sms.tools.tool_sms import SmsTool


class SmsCallForbiddenTool:
    @staticmethod
    def check_if_from_number_seeking_before_user(current_user_id: int, from_number: str, to_number: str):
        # 普通的公司号码如谷歌，直接返回False
        if len(from_number) < 10:
            logger.info(
                f"[SmsCallForbiddenTool] check_if_from_number_seeking_before_user, user_id:{current_user_id}, "
                f"from_number:{from_number} too short, give up checking")
            return False

        # 找到这个号码的之前的使用者的user_id_list
        before_user_ids = NumberTool.get_before_user_id_list_by_number(to_number)

        # 遍历这些前任们，看看这个from_number是不是曾经有联系过，是的话，就说明这个from_number是来找前任的
        for before_user_id in before_user_ids:
            before_sms_cnt = SmsTool.get_conversation_cnt_both_direction(before_user_id, from_number)
            before_call_cnt = CallTool.get_conversation_cnt_both_direction(before_user_id, from_number)

            # 还得是来回对话 1 轮，才算是熟人前任
            if before_sms_cnt + before_call_cnt > 0:
                logger.warning(
                    f"[SmsCallForbiddenTool] check_if_from_number_seeking_before_user, user_id:{current_user_id}, "
                    f"incoming from_number:{from_number}, in before_user_id:{before_user_id}'s conversation, "
                    f"before_sms_cnt: {before_sms_cnt}, before_call_cnt: {before_call_cnt}")
                return True
        return False
