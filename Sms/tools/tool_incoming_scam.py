import json
import logging
import re
import time

from SecPhone.settings import logger
from Sms.ai.zhipu_client import ZhiPuClient

prompt = """你将作为一名英语短信审核员。你的任务是判断接收到的短信内容是否包含“停止”（stop）的意图。
以下是需要审核的短信内容：
<content>{content}</content>

请参考以下场景：
1.如果短信中包含礼貌表达，如“pls stop”、“please stop”或“just stop”，则明确表达了停止的意图。
2.如果短信中包含不礼貌的表达，如“stop asshole”、“fuck u stop”、“fucking stop”或“please don't text me again”，也同样表达了停止的意图。
3.仔细检查短信中的用词和短语。如果发现上述表达或其他能传达“停止交谈”，“停止会话”，“停止回复”，“停止发短信”等“停止”意图的短语，则判定该短信包含“stop”意图。否则，判定不包含。
4.如果短信中包含“text stop to opt-out”或类似退订指令，这是系统提供的标准选项，而非用户主动表达的停止意图，因此不应判断为“stop”。
5.模糊匹配：允许一定程度的拼写错误，例如“dnot”可以识别为“don’t”, 上下文分析：即使有拼写错误，但结合整体句子结构（如“texting again”），仍能推断出“停止”意图。

<judgment>[Provide "Yes" or "No" here]</judgment>

"""

prompt = """你将作为一名短信内容审核员，负责判断接收到的短信内容是否包含对发送者存在骚扰行为、诈骗行为的怀疑或明确指控。请根据以下短信内容作出判断，并以 JSON 格式返回结果：  

### 短信内容：
<content>{content}</content>  

### 判断标准：
1. **怀疑或指控**：短信中是否包含怀疑或指控的语言，例如：  
   - 怀疑语言：如“this seems like a scam”，“is this real or a scam?”，“is this legitimate?”等。  
   - 指控语言：如“you are a scammer”，“this is fraudulent”，“stop scamming people”等。  
2. **威胁举报**：短信中是否提及报警或举报行为，例如：“I'm reporting this to the authorities”，“I'm filing a complaint about this scam”等。  
3. **请求验证**：短信中是否提出要求验证信息真实性的问题，如“can I trust this offer?”，“is this real?”等。  
4. **排除标准**：
   - 提供具体的链接或信息（如结算、补偿等），但未包含怀疑或指控语言的，不应误判为诈骗。  
   - 单纯的通知信息，如“请尽快填写申请表”，且没有怀疑诈骗的内容，不应视为怀疑或指控诈骗。  
   - 包含合法且无恶意意图的链接（如订单确认、结算等），应当排除在外，尤其当短信中并未直接表达怀疑或指控。  
5. **补充注意**：
   - 如果短信仅包含链接或指令内容（如“请填写此表格以完成结算”），且没有直接表示怀疑、举报或指控的语言，则不应被判断为诈骗。  
   - 拼写错误、模糊表达等可能暗示怀疑，但必须结合整体语境判断，避免误判。  

### 输出格式：
请以 JSON 格式返回结果，包含以下字段：
- `judgment`：值为 `"Yes"`（如果短信包含怀疑、指控、举报、请求验证等内容），否则值为 `"No"`。
- `reason`：简要说明判断理由，例如 `"包含指控诈骗的词汇"`，或 `"仅为普通通知，无怀疑或指控"`。

### 示例：
1. 短信："I will report this number"
返回：{ "judgment": "Yes", "reason": "包含威胁举报的内容" }

2. 短信："If u keep texting me I will report this to the authorities"
返回：{ "judgment": "Yes", "reason": "包含威胁举报的内容" }

3. 短信："stop harassment"
返回：{ "judgment": "Yes", "reason": "包含骚扰相关的指控" }

4. 短信："stop"
返回：{ "judgment": "Yes", "reason": "包含骚扰相关的指控" }

5. 短信："Your order has been shipped"
返回：{ "judgment": "No", "reason": "普通通知信息，无怀疑或指控" }

6. 短信: "dnot texting again"
返回：{ "judgment": "Yes", "reason": "让对方不要再发送短信过来，已经是造成骚扰了" }
"""


class IncomingMsgScamTool:
    @staticmethod
    def is_scam_message(from_number: str, content: str) -> bool:
        try:
            # 确保是人类的号码
            if len(from_number) <= 10:
                return False

            # 可能是图片
            if not content:
                return False

            content = content.strip().lower()
            if content == "scam":
                logger.warning(f"[IncomingMsg][is_scam_message] from_number:{from_number}, query:[[{content}]] scam")
                return True

            client, client_name = ZhiPuClient.get_zhipu_fatpo_client()
            start = time.time()

            response = client.chat.completions.create(
                model="GLM-4-Air-0111",
                messages=[{"role": "user", "content": prompt.replace("{content}", content)}],
                user_id="51081731492666544",
                max_tokens=64,
            )

            answer = response.choices[0].message
            logger.info(f"[IncomingMsg][is_scam_message] zhipu-ai, from_number:{from_number}, query:[[{content}]], "
                        f"cost: {round(time.time() - start, 2)}s, response: {answer}")

            # **优化解析：直接解析 JSON，避免手动字符串处理**
            judgment, reason = IncomingMsgScamTool.extract_judgment(answer)

            if judgment == "yes":
                logger.error(
                    f"[IncomingMsg][is_scam_message] Scam detected, from_number:{from_number}, reason: {reason}, "
                    f"content:{content}")
                return True
            elif judgment == "no":
                logger.warning(
                    f"[IncomingMsg][is_scam_message] No scam detected, from_number:{from_number}, reason: {reason}, "
                    f"content:{content}")
                return False
            else:
                logger.error(
                    f"[IncomingMsg][is_scam_message] Invalid response, from_number:{from_number}, raw: {answer}, "
                    f"content:{content}")
                return False
        except Exception as e:
            logger.error(f"[IncomingMsg][is_scam_message] Processing failed, from_number:{from_number},"
                         f" content:{content}error: {e}", exc_info=True)
            return False

    @staticmethod
    def clean_json_string(raw_text):
        """去除 ```json 代码块及多余换行符"""
        return re.sub(r"^```json\s*|\s*```$", "", raw_text, flags=re.MULTILINE).strip()

    @staticmethod
    def clean_and_fix_json(raw_text):
        """去除 ```json 代码块，并尝试补全不完整的 JSON"""
        # 去除 Markdown 代码块
        cleaned_text = re.sub(r"^```json\s*|\s*```$", "", raw_text, flags=re.MULTILINE).strip()

        try:
            # 尝试解析 JSON
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            # 如果解析失败，尝试补全缺失的大括号
            fixed_text = cleaned_text.rstrip() + "}"  # 补上 }
            try:
                return json.loads(fixed_text)
            except json.JSONDecodeError:
                return {"error": "无法修复 JSON", "original_text": raw_text}

    @staticmethod
    def extract_judgment(answer):
        """解析 JSON 并去掉 Markdown 代码块"""
        try:
            parsed_data = IncomingMsgScamTool.clean_and_fix_json(answer.content)

            judgment = parsed_data.get("judgment", "").strip().lower()
            reason = parsed_data.get("reason", "No reason provided").strip()

            return judgment, reason
        except (json.JSONDecodeError, KeyError, ValueError, TypeError) as e:
            logger.error(f"[IncomingMsg][extract_judgment] Failed to parse response JSON: {answer}, error: {e}")
            return "", ""


if __name__ == '__main__':
    content_list = [
        ("i will report this number", True),
        ("stop scamming", True),
        ("yo fucking scammer", True),
        ("how are u", False),
        ("stop", True),
        ("pls stop", True),
        ("please stop", True),
        ("stop please", True),
        ("stop pls", True),
        ("dnot texting again", True),
        ("dude you're pissing me off", False),
        ("westkiss hair:welcome, beauty!glad to have you here!be our new member, enjoy $180 off "
         "coupon!!!>https://westkiss.catse.co/25mf5lkr0u text stop to opt-out", False),
        ("https://vlc.calgaslitigation.com/here’s some information regarding a gas rebate that will expire today. "
         "please fill out the application.              fill this out to get the $750 settlementpls complete this "
         "today. to receive $750 settlement for overpriced gas in 2015 - https://vlc.calgaslitigation.com/deadline is "
         "today https://vlc.calgaslitigation.com/",
         False)
    ]
    for (c, rsp) in content_list:
        logging.info("#######################")
        logging.info(f"[{c}] should be = {rsp}")
        true_rsp = IncomingMsgScamTool.is_scam_message("+1234567890", c)
        if true_rsp != rsp:
            logging.warning(f"true_rsp:{true_rsp} != rsp:{rsp}, content:{c}")
            logging.warning(f"true_rsp:{true_rsp} != rsp:{rsp}, content:{c}")
            logging.warning(f"true_rsp:{true_rsp} != rsp:{rsp}, content:{c}")
