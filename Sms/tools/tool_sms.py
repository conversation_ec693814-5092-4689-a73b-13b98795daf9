import datetime
from typing import Union, List

from django.db.models import Q

from Call.models import SMSRecord, BadImage, MaybeBadSms
from SecPhone import settings
from SecPhone.settings import logger


class SmsTool:
    @staticmethod
    def get_contact_cnt(user_id: int) -> int:
        user_send_numbers_cnt = SMSRecord.objects.filter(user_id=user_id,
                                                         direction=settings.SMS_DIRECTION_SEND) \
            .values("to_number").distinct().count()
        return user_send_numbers_cnt

    @staticmethod
    def get_conversation_sms(user_id: int, number: str) -> list:
        from_records = SMSRecord.objects.filter(user_id=user_id, from_number=number).all()
        to_records = SMSRecord.objects.filter(user_id=user_id, to_number=number).all()

        # 合并两个查询集并按 id 排序
        combined_records = from_records | to_records  # union() 会合并两个查询集
        combined_records = combined_records.order_by('id')  # 按照 id 排序

        return combined_records

    @staticmethod
    def get_latest_send_sms_top_n(user_id: int, top_n: int) -> list:
        recent_sms_list = SMSRecord.objects.filter(user_id=user_id,
                                                   is_image=0,
                                                   status__in=["delivered", "sent"],
                                                   direction=settings.SMS_DIRECTION_SEND).order_by('-id')[:top_n]
        return recent_sms_list

    @staticmethod
    def get_conversation_sms_top_n_for_pig(user_id: int, to_number: str, top_n: int) -> list:
        # 获取记录并转换为列表
        incoming_records = list(
            SMSRecord.objects.filter(user_id=user_id, is_image=0,
                                     from_number=to_number).order_by('-id')[:top_n])
        outgoing_records = list(
            SMSRecord.objects.filter(user_id=user_id, is_image=0,
                                     to_number=to_number,
                                     status__in=["delivered", "sent"]).order_by('-id')[:top_n])

        conversation_json_list = []

        # 处理接收的消息
        for c in incoming_records:
            content = c.filtered_content if c.filtered_content else c.content
            conversation_json_list.append({
                "from": "[Recipient]",
                "to": "[User Under Review]",
                "content": content,
                "timestamp": c.latest_ts if c.latest_ts else 0,
            })

        # 处理发送的消息
        for c in outgoing_records:
            content = c.filtered_content if c.filtered_content else c.content
            conversation_json_list.append({
                "from": "[User Under Review]",
                "to": "[Recipient]",
                "content": content,
                "timestamp": c.latest_ts if c.latest_ts else 0,
            })

        # 按timestamp降序排序
        conversation_json_list = sorted(conversation_json_list, key=lambda x: x['timestamp'], reverse=True)

        return conversation_json_list

    @staticmethod
    def get_contact_cnt_no_it_real_delivery(user_id: int) -> int:
        user_send_numbers_cnt = (SMSRecord.objects.filter(user_id=user_id,
                                                          status="delivered",
                                                          is_it=0,
                                                          direction=settings.SMS_DIRECTION_SEND)
                                 .values("to_number").distinct().count())
        return user_send_numbers_cnt

    @staticmethod
    def get_contact_cnt_before_ts(user_id: int, before_ts: int) -> int:
        target_numbers_cnt = SMSRecord.objects.filter(user_id=user_id,
                                                      direction=settings.SMS_DIRECTION_SEND,
                                                      latest_ts__gt=before_ts
                                                      ).values("to_number").distinct().count()
        return target_numbers_cnt

    @staticmethod
    def get_conversation_cnt(user_id: int, to_number: str) -> int:
        records_cnt = SMSRecord.objects.filter(user_id=user_id, to_number=to_number).count()
        return records_cnt

    @staticmethod
    def get_conversation_cnt_with_delivered(user_id: int, to_number: str) -> int:
        records_cnt = SMSRecord.objects.filter(user_id=user_id, to_number=to_number, status='delivered').count()
        return records_cnt

    @staticmethod
    def get_number_delivered_and_received(user_id: int, to_number: str) -> (int, int):
        delivered_cnt = SMSRecord.objects.filter(user_id=user_id, to_number=to_number, status='delivered').count()
        received_cnt = SMSRecord.objects.filter(user_id=user_id, from_number=to_number,
                                                status='webhook_delivered').count()
        return delivered_cnt, received_cnt

    @staticmethod
    def get_number_sent_and_get_cnt(user_id: int, to_number: str) -> (int, int):
        delivered_cnt = SMSRecord.objects.filter(user_id=user_id, to_number=to_number).count()
        received_cnt = SMSRecord.objects.filter(user_id=user_id, from_number=to_number).count()
        return delivered_cnt, received_cnt

    @staticmethod
    def get_number_sent_and_get_cnt_gt_id(user_id: int, to_number: str, record_id: int) -> (int, int):
        delivered_cnt = SMSRecord.objects.filter(user_id=user_id, to_number=to_number, id__gt=record_id).count()
        received_cnt = SMSRecord.objects.filter(user_id=user_id, from_number=to_number, id__gt=record_id).count()
        return delivered_cnt, received_cnt

    @staticmethod
    def get_conversation_cnt_both_direction(user_id: int, number: str) -> int:
        from_cnt = SMSRecord.objects.filter(user_id=user_id, from_number=number).count()
        to_cnt = SMSRecord.objects.filter(user_id=user_id, to_number=number).count()
        return from_cnt + to_cnt

    @staticmethod
    def get_no_read_cnt(user_id: int, latest_ts: int) -> int:
        no_read_sms_cnt = SMSRecord.objects.filter(user_id=user_id, latest_ts__gt=latest_ts,
                                                   direction=settings.SMS_DIRECTION_RECEIVE,
                                                   deleted=0).count()
        return no_read_sms_cnt

    @staticmethod
    def get_total_receive_cnt(user_id: int) -> int:
        receive_sms_count = (SMSRecord.objects.filter(user_id=user_id,
                                                      direction=settings.SMS_DIRECTION_RECEIVE,
                                                      is_it=0, err_code=0).count())
        return receive_sms_count

    @staticmethod
    def get_total_send_cnt(user_id: int) -> int:
        send_sms_count = (SMSRecord.objects.filter(user_id=user_id,
                                                   direction=settings.SMS_DIRECTION_SEND,
                                                   is_it=0, err_code=0).count())
        return send_sms_count

    @staticmethod
    def get_total_send_cnt_by_to_number(user_id: int, to_number: str) -> int:
        send_sms_count = (SMSRecord.objects.filter(user_id=user_id,
                                                   direction=settings.SMS_DIRECTION_SEND,
                                                   to_number=to_number,
                                                   status="delivered",
                                                   is_it=0, err_code=0).count())
        return send_sms_count

    @staticmethod
    def get_total_send_cnt_no_it_real_delivery(user_id: int) -> int:
        send_sms_count = (SMSRecord.objects.filter(user_id=user_id,
                                                   direction=settings.SMS_DIRECTION_SEND,
                                                   status="delivered",
                                                   is_it=0, err_code=0).count())
        return send_sms_count

    @staticmethod
    def get_total_failed_cnt(user_id: int) -> int:
        sms_send_failed_count = SMSRecord.objects.filter(user_id=user_id, err_code__gt=0).exclude(
            err_code=100).count()
        return sms_send_failed_count

    @staticmethod
    def get_from_numbers_when_incoming(user_id: int, before_date: datetime.datetime) -> list:
        records = SMSRecord.objects.filter(user_id=user_id,
                                           direction=settings.SMS_DIRECTION_RECEIVE,
                                           created_at__gte=before_date).values('from_number').distinct().all()
        return records

    @staticmethod
    def get_all_from_numbers_cnt_when_incoming(user_id: int) -> int:
        cnt = SMSRecord.objects.filter(user_id=user_id, direction=settings.SMS_DIRECTION_RECEIVE
                                       ).values('from_number').distinct().count()
        return cnt

    @staticmethod
    def get_all_to_numbers_cnt_when_outgoing(user_id: int) -> int:
        cnt = SMSRecord.objects.filter(user_id=user_id, direction=settings.SMS_DIRECTION_SEND
                                       ).values('to_number').distinct().count()
        return cnt

    @staticmethod
    def get_records_by_customer_service(user_id: int, before: datetime.datetime) -> list:
        records = SMSRecord.objects.filter(user_id=user_id, created_at__gte=before).order_by("id").all()
        return records

    @staticmethod
    def get_newest_record_by_customer_service(user_id: int, direction: str, before: datetime.datetime) \
            -> Union[SMSRecord, None]:
        record = SMSRecord.objects.filter(user_id=user_id, created_at__gte=before,
                                          direction=direction).order_by("-id").first()
        return record

    @staticmethod
    def get_total_sms(user_id: int) -> list:
        records = SMSRecord.objects.filter(user_id=user_id, latest_ts__gt=0, deleted=0).all()
        return records

    @staticmethod
    def get_total_sms_after_ts(user_id: int, latest_ts: int) -> list:
        records = SMSRecord.objects.filter(user_id=user_id, latest_ts__gt=latest_ts, deleted=0).all()
        return records

    @staticmethod
    def get_total_receive_sms_after_ts(user_id: int, latest_ts: int) -> list:
        records = SMSRecord.objects.filter(user_id=user_id,
                                           direction=settings.SMS_DIRECTION_RECEIVE,
                                           latest_ts__gt=latest_ts, deleted=0).all()
        return records

    @staticmethod
    def get_latest_10_send_sms(user_id: int) -> list:
        recent_sms_list = SMSRecord.objects.filter(user_id=user_id,
                                                   direction=settings.SMS_DIRECTION_SEND).order_by('-id')[:10]
        return recent_sms_list

    @staticmethod
    def get_n_non_price_sms_before_at(n: int, before_date: datetime.datetime) -> list:
        non_price_sms_list = SMSRecord.objects.filter(price__isnull=True,
                                                      err_code=0,
                                                      created_at__gte=before_date)[:n]
        return non_price_sms_list

    @staticmethod
    def get_n_non_points_sms_before_at(n: int, before_date: datetime.datetime) -> list:
        non_points_sms_list = SMSRecord.objects.filter(price__isnull=True,
                                                       err_code=0,
                                                       created_at__gte=before_date)[:n]
        return non_points_sms_list

    @staticmethod
    def get_latest_top_n_sms(user_id: int, n: int) -> list:
        records = SMSRecord.objects.filter(user_id=user_id).order_by('-id')[:n]
        return records

    @staticmethod
    def get_latest_top_n_it_sms(user_id: int, n: int) -> list:
        records = SMSRecord.objects.filter(user_id=user_id, is_it=1).order_by('-id')[:n]
        return records

    @staticmethod
    def get_latest_top_n_non_it_sms(user_id: int, n: int) -> list:
        records = SMSRecord.objects.filter(user_id=user_id, is_it=0).order_by('-id')[:n]
        return records

    @staticmethod
    def get_by_sid(message_sid: str) -> Union[SMSRecord, None]:
        record = SMSRecord.objects.filter(sid=message_sid).first()
        return record

    @staticmethod
    def get_by_id(_id: int) -> Union[SMSRecord, None]:
        record = SMSRecord.objects.filter(id=_id).first()
        return record

    @staticmethod
    def get_user_all_images(user_id: int) -> List[SMSRecord]:
        records = SMSRecord.objects.filter(user_id=user_id, is_image=1, direction=settings.SMS_DIRECTION_SEND).all()
        return records

    @staticmethod
    def remove_user_all_bad_images(user_id: int):
        BadImage.objects.filter(user_id=user_id).delete()

    @staticmethod
    def remove_user_all_maybe_bad_images(user_id: int):
        MaybeBadSms.objects.filter(user_id=user_id).delete()

    @staticmethod
    def is_this_image_bad(image_md5: str) -> bool:
        return BadImage.objects.filter(image_md5=image_md5).exists()

    @staticmethod
    def add_bad_image_to_call_bad_image(user_id: int, image_md5: str, image_url: str):
        if BadImage.objects.filter(user_id=user_id, image_md5=image_md5).exists():
            return
        BadImage(user_id=user_id, image_md5=image_md5, image_url=image_url).save()

    @staticmethod
    def get_failed_sms_record_by_number_content_in_5_minute(from_number: str, content: str) -> Union[SMSRecord, None]:
        now = datetime.datetime.now(datetime.timezone.utc)
        five_minutes_ago = now - datetime.timedelta(minutes=10)
        prefix_content = content[0:30]
        record = SMSRecord.objects.filter(from_number=from_number,
                                          status="sent_failed",
                                          created_at__gte=five_minutes_ago,
                                          filtered_content__startswith=prefix_content,
                                          ).first()
        return record

    @staticmethod
    def get_failed_mms_record_by_number_content_in_5_minute(from_number: str, image_url: str) -> Union[SMSRecord, None]:
        now = datetime.datetime.now(datetime.timezone.utc)
        five_minutes_ago = now - datetime.timedelta(minutes=10)
        record = SMSRecord.objects.filter(from_number=from_number,
                                          status="sent_failed",
                                          created_at__gte=five_minutes_ago,
                                          images=image_url,
                                          ).first()
        return record

    @staticmethod
    def get_it_by_both_number(user_id: int, number: str, before_date: datetime.datetime) -> list:
        query = Q(user_id=user_id, created_at__gte=before_date, is_it=1)
        query = query & (Q(from_number=number) | Q(to_number=number))

        records = SMSRecord.objects.filter(query).order_by("id").all()

        return records

    @staticmethod
    def is_reply_stop(user_id: int, to_phone: str) -> bool:
        sms_list = SMSRecord.objects.filter(user_id=user_id, from_number=to_phone,
                                            direction=settings.SMS_DIRECTION_RECEIVE).order_by('-id')[:10]
        start_flag = False
        stop_flag = False
        start_ts = -1
        stop_ts = -1
        for i in sms_list:
            if "stop" == str(i.content).lower().strip():
                stop_flag = True
                if i.latest_ts > stop_ts:
                    stop_ts = i.latest_ts
            if "start" == str(i.content).lower().strip():
                start_flag = True
                if i.latest_ts > start_ts:
                    start_ts = i.latest_ts

        # 只有 stop，那就是禁止了
        if stop_flag and not start_flag:
            return True

        # 没有 stop 也没有 start，放行
        if not stop_flag and not start_flag:
            return False

        # 有 stop 也有 start，看最晚的那条优先级最高
        if stop_flag and start_flag:
            if stop_ts > start_ts:
                logger.warning(f"[SmsTool.is_reply_stop] {user_id}, stop_ts:{stop_ts}, start_ts:{start_ts}, still stop")
                return True
            else:
                logger.warning(f"[SmsTool.is_reply_stop] {user_id}, stop_ts:{stop_ts}, start_ts:{start_ts}, not stop")
                return False
        return False

    @staticmethod
    def is_stop_someone_incoming(user_id: int, to_phone: str) -> bool:
        sms_list = SMSRecord.objects.filter(user_id=user_id, to_number=to_phone,
                                            direction=settings.SMS_DIRECTION_SEND).order_by('-id')[:10]
        start_flag = False
        stop_flag = False
        start_ts = -1
        stop_ts = -1
        for i in sms_list:
            if "stop" == str(i.content).lower().strip():
                stop_flag = True
                if i.latest_ts > stop_ts:
                    stop_ts = i.latest_ts
            if "start" == str(i.content).lower().strip():
                start_flag = True
                if i.latest_ts > start_ts:
                    start_ts = i.latest_ts

        # 只有 stop，那就是禁止了
        if stop_flag and not start_flag:
            logger.warning(f"[SmsTool.is_stop_someone_incoming] {user_id}, from number:{to_phone} stop")
            return True

        # 没有 stop 也没有 start，放行
        if not stop_flag and not start_flag:
            return False

        # 有 stop 也有 start，看最晚的那条优先级最高
        if stop_flag and start_flag:
            if stop_ts > start_ts:
                logger.warning(f"[SmsTool.is_stop_someone_incoming] {user_id}, from number:{to_phone} "
                               f"stop_ts:{stop_ts}, start_ts:{start_ts}, still stop")
                return True
            else:
                logger.warning(f"[SmsTool.is_stop_someone_incoming] {user_id}, from number:{to_phone} "
                               f"stop_ts:{stop_ts}, start_ts:{start_ts}, not stop")
                return False
        return False

    @staticmethod
    def if_first_conversation(user_id: int, to_phone: str) -> bool:
        sms_count = SMSRecord.objects.filter(user_id=user_id, to_number=to_phone,
                                             direction=settings.SMS_DIRECTION_SEND).count()
        if sms_count == 0:
            return True
        return False

    @staticmethod
    def get_user_latest_sms(user_id: int, latest_ts: int) -> list:
        logger.info(f"[GetLatestSms] user_id:{user_id}, latest_ts:{latest_ts}")

        # <= before_ts 的短信都视为已删除
        # latest_ts为0时，拉用户的全部收和发的短信，前提是[已删除]的不再拉取
        if latest_ts == 0:
            records = SmsTool.get_total_sms_after_ts(user_id, latest_ts)
        else:
            # 否则只拉用户的比latest_ts大的收短信
            records = SmsTool.get_total_receive_sms_after_ts(user_id, latest_ts)
        return records

    @staticmethod
    def is_user_first_feedback(feedback_user_id: int, feedback_from_number: str) -> bool:
        if not SMSRecord.objects.filter(user_id=feedback_user_id,
                                        from_number=feedback_from_number,
                                        to_number=settings.APP_IT_SUPPORT_SHOW_PHONE).exists():
            return True
        return False

    @staticmethod
    def get_by_to_number_and_content(user_id: int, to_number: str, content: str) -> list:
        five_minutes_ago = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(minutes=5)
        records = SMSRecord.objects.filter(user_id=user_id,
                                           to_number=to_number,
                                           content=content,
                                           direction=settings.SMS_DIRECTION_SEND,
                                           status='delivered',
                                           created_at__gte=five_minutes_ago).first()
        return records
