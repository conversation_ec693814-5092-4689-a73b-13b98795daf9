from typing import Union

from Call.models import SMSRecordWaitForReview
from Common.timeutil import TimeUtil
from SecPhone import settings
from SecPhone.settings import logger
from Sms.info.sms_info_bean import SMSInfo
from Sms.tools.tool_sms import SmsTool
from User.models import User
from User.tools.user_tool import UserTool


class SmsReviewTool:
    @staticmethod
    def update_review_record(r: SMSRecordWaitForReview, user: User):
        if not r:
            logger.warning(f"[update_review_record] review record is None")
            return None
        if not User:
            logger.warning(f"[update_review_record] review record is None, user is not exists, record_id: {r.id}, "
                           f"user:{r.user_id}")
            return None

        if not r.user_register_days:
            r.user_register_days = UserTool.get_user_register_days(user.id)
        if not r.user_vip_expired_at:
            r.user_vip_expired_at = user.expired_at
        if not r.contact_count:
            # 用户联系人数量
            r.contact_count = SmsTool.get_contact_cnt(r.user_id)
        if not r.sms_count:
            # 发送短信数量
            r.sms_count = SmsTool.get_total_send_cnt(r.user_id)
        if not r.last_n_sms:
            # 用户最近10条短信
            recent_sms_list = SmsTool.get_latest_10_send_sms(r.user_id)
            last_n_sms_list = [f"{[v.to_number]} {v.content}" for v in recent_sms_list]
            r.last_n_sms = "\n".join([f"[{index}]: {v}" for index, v in enumerate(last_n_sms_list)])
        r.save()

    @staticmethod
    def get_user_all_need_to_review_sms(user_id: int) -> list:
        records = SMSRecordWaitForReview.objects.filter(user_id=user_id, deleted=0).all()
        return records

    @staticmethod
    def get_all_need_to_review_sms() -> list:
        records = SMSRecordWaitForReview.objects.filter(deleted=0).all()
        return records

    @staticmethod
    def get_by_wangyi_taskid(task_id: str) -> Union[SMSRecordWaitForReview, None]:
        record = SMSRecordWaitForReview.objects.filter(wangyi_task_id=task_id, deleted=0).first()
        return record

    @staticmethod
    def get_by_id(message_id: int) -> Union[SMSRecordWaitForReview, None]:
        record = SMSRecordWaitForReview.objects.filter(id=message_id, deleted=0).first()
        return record

    @staticmethod
    def save_review_sms_v2(sms_info: SMSInfo):
        user_id = sms_info.user_id
        origin_content = sms_info.origin_content
        filtered_content = sms_info.filtered_content
        from_number = sms_info.from_number
        to_number = sms_info.to_number
        not_send_reason = ",".join(sms_info.err_rsp_list)

        user = UserTool.get_user_by_id(user_id)
        if not user:
            logger.warning(f"[SmsReviewTool.save_review_sms] user is deleted, give up")
            return

        latest_ts = TimeUtil.GetNowTsInInt()
        record = SMSRecordWaitForReview(
            not_send_reason=not_send_reason,
            user_id=user_id,
            direction=settings.SMS_DIRECTION_SEND,
            from_number=from_number,
            to_number=to_number,
            content=origin_content,
            content_zh="",
            filtered_content=filtered_content,
            latest_ts=latest_ts,
            ip=user.ip,
            uuid=user.uuid,
        )
        record.save()
