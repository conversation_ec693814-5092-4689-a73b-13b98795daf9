# -*- coding: utf-8 -*-

# This code shows an example of text translation from English to Simplified-Chinese.
# This code runs on Python 2.7.x and Python 3.x.
# You may install `requests` to run this code: pip install requests
# Please refer to `https://api.fanyi.baidu.com/doc/21` for complete api document


# 百度翻译云平台： https://fanyi-api.baidu.com/api/trans/product/desktop?req=detail

import random
from hashlib import md5

import requests
from django.core.cache import cache

from SecPhone.settings import logger

default_key = "zehou"
key_map = {
    "zehou": {  # 88107
        "appid": '20230330001620603',
        "appkey": 'mylvxuPuX0ThXe4Nl0Mg'
    },
    "fatpo": {  # 39212
        "appid": '20230330001620599',
        "appkey": 'rhBS5WYxZ8Lq2HPWpF8n'
    },
    "kay": {  # 09743
        "appid": '20230812001778401',
        "appkey": 'v8sZuPTDJ0qMwX0gLrVk'
    },
    "fatpo2": {  # 68129212
        "appid": '20230812001778411',
        "appkey": 'pbbBoxNHOPrDzc2h7RDO'
    },
    "jj": {
        "appid": '20230812001778534',
        "appkey": 'LYxOdKLALpM2_Q_zx4Ic'
    },
    "ran": {
        "appid": '20230813001778840',
        "appkey": 'md10Q9BdmFjGSDRpVecF'
    }
}
key_client_list = list(key_map.keys())

# cache client key
change_client_key = "baidufanyi:change_client"


def get_next_client(selected_value: str):
    # 使用循环遍历列表，找到选定值的下一个值
    for i in range(len(key_client_list)):
        if key_client_list[i] == selected_value:
            # 判断是否已到列表末尾
            if i + 1 < len(key_client_list):
                return key_client_list[i + 1]
            else:
                return key_client_list[0]
    return None


# For list of language codes, please refer to `https://api.fanyi.baidu.com/doc/21`
from_lang = 'en'
to_lang = 'zh'

endpoint = 'http://api.fanyi.baidu.com'
path = '/api/trans/vip/translate'
url = endpoint + path


# Generate salt and sign
def make_md5(s, encoding='utf-8'):
    return md5(s.encode(encoding)).hexdigest()


def tran(query: str):
    """
    返回值：
    {
        "from": "zh",
        "to": "en",
        "trans_result": [
            {
                "src": "刘德华",
                "dst": "Lau Andy"
            }
        ]
    }
    """
    try:
        if not query:
            logger.error(f"[baidu_tran_util] query is invalid")
            return query

        query = query.replace("\n", " ").replace("\r", " ")

        client_value = cache.get(change_client_key)
        if client_value is None:
            client_value = default_key
        logger.info(f"[baidu_tran_util] query:{query}, client:{client_value}")

        appid = key_map[client_value]["appid"]
        appkey = key_map[client_value]["appkey"]

        salt = random.randint(32768, 65536)
        sign = make_md5(appid + query + str(salt) + appkey)

        # Build request
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        payload = {'appid': appid, 'q': query, 'from': from_lang, 'to': to_lang, 'salt': salt, 'sign': sign}

        # Send request
        r = requests.post(url, params=payload, headers=headers, timeout=3)
        result = r.json()

        # 如果有错误，则放弃翻译
        if 'error_code' in result:
            next_client = get_next_client(client_value)
            logger.warning(f"[baidu_tran_util] error:{result}, query:{query}, current client:{client_value}, "
                           f"change to another client: {next_client}")
            cache.set(change_client_key, next_client, 3600 * 12)
            return query

        if 'trans_result' in result and len(result['trans_result']) > 0:
            return result['trans_result'][0]['dst']
    except Exception:
        logger.warning(f"[baidu_tran_util] failed, query:{query}", exc_info=True)
        return query


if __name__ == '__main__':
    query = '刘德华'
    print(tran(query))
