from SecPhone import settings
from SecPhone.settings import logger


class SmsNoticeTool:
    @staticmethod
    def get_notice_code(content: str):
        if len(content) >= 13 and content[0:2] == "{{" and content[11:13] == "}}":
            system_notice_code = content[0:13]  # 格式标准：{{CODE_0001}}
            return system_notice_code
        return ""

    @staticmethod
    def change_system_notice_code_to_content(content: str, code: str) -> str:
        if code == settings.SYSTEM_NOTICE_LOCK_NUMBER_SUCCESS:
            system_content, _, _ = SmsNoticeTool.lock_number_success('')
            content = content.replace(settings.SYSTEM_NOTICE_LOCK_NUMBER_SUCCESS, system_content)
            logger.info(f"[GetLatestSms] change_system_notice_code_to_content, lock number success, "
                        f"change content: {content}")
            return content

        if code == settings.SYSTEM_NOTICE_LOCK_NUMBER_HALF_SUCCESS:
            system_content, _, _ = SmsNoticeTool.lock_number_half_success('')
            content = content.replace(settings.SYSTEM_NOTICE_LOCK_NUMBER_HALF_SUCCESS, system_content)
            logger.info(f"[GetLatestSms] change_system_notice_code_to_content, lock number half success, "
                        f"change content: {content}")
            return content

        if code == settings.SYSTEM_NOTICE_MISS_INCOMING_CALL:
            system_content, _, _ = SmsNoticeTool.miss_incoming_call('')
            content = content.replace(settings.SYSTEM_NOTICE_MISS_INCOMING_CALL, system_content)
            logger.info(f"[GetLatestSms] change_system_notice_code_to_content, missing call, "
                        f"change content: {content}")
            return content

        if code == settings.SYSTEM_NOTICE_GO_AND_PICK_NUMBER:
            system_content, _, _ = SmsNoticeTool.go_and_pick_number()
            content = content.replace(settings.SYSTEM_NOTICE_GO_AND_PICK_NUMBER, system_content)
            logger.info(f"[GetLatestSms] change_system_notice_code_to_content, go and pick number, "
                        f"change content: {content}")
            return content

        logger.error(f"[GetLatestSms] change_system_notice_code_to_content, no need to change, content: {content}")
        return content

    @staticmethod
    def lock_number_success(number: str) -> (str, str, str):
        system_content = f"[Notice] Great job! You've successfully completed the initialization of " \
                         f"your virtual number. Now, you're ready to send text messages, make calls, " \
                         f"and even receive calls. " \
                         f"If you encounter any issues or need assistance, " \
                         f"feel free to reach out to our customer support."

        db_content = f"{settings.SYSTEM_NOTICE_LOCK_NUMBER_SUCCESS} Number: {number}"
        push_content = system_content + f" Number: {number}"
        logger.info(f"[SmsNoticeTool] lock_number_success: {push_content}")
        return system_content, db_content, push_content

    @staticmethod
    def lock_number_half_success(number: str) -> (str, str, str):
        system_content = f'[Notice] Well done! You have successfully locked the number, ' \
                         f'but it is not fully yours yet. Once you have completed your subscription, ' \
                         f'this number will be at your service anytime. ' \
                         f'Do not hesitate, go ahead and complete the subscription now! \n\n' \
                         f'How to complete a subscription? \nGo to "Me" view and click "Expire date".'
        db_content = f"{settings.SYSTEM_NOTICE_LOCK_NUMBER_HALF_SUCCESS} Number: {number}"
        push_content = system_content + f" Number: {number}"
        logger.info(f"[SmsNoticeTool] lock_number_half_success: {push_content}")
        return system_content, db_content, push_content

    @staticmethod
    def miss_incoming_call(from_number: str) -> (str, str, str):
        system_content = f'[Notice] You have a missed call, please call back in time!'
        db_content = f"{settings.SYSTEM_NOTICE_MISS_INCOMING_CALL}. Incoming number: {from_number}"
        push_content = system_content + f" Incoming number: {from_number}"
        logger.info(f"[SmsNoticeTool] miss_incoming_call: {push_content}")
        return system_content, db_content, push_content

    @staticmethod
    def go_and_pick_number() -> (str, str, str):
        system_content = f'[Notice] You can now go and pick out a nice number. ' \
                         f'Please note that the cost of replacing the number is very high for us, so please pick carefully!'
        db_content = f"{settings.SYSTEM_NOTICE_GO_AND_PICK_NUMBER}"
        push_content = system_content
        logger.info(f"[SmsNoticeTool] go_and_pick_number: {system_content}")
        return system_content, db_content, push_content

    @staticmethod
    def sms_delivery_failed(err_msg: str, origin_content: str) -> str:
        content = "[Notice] message undelivered: " + err_msg
        content = content + "\n\ncontent: " + origin_content
        logger.info(f"[SmsNoticeTool] sms_delivery_failed: {content}")
        return content

    @staticmethod
    def mms_delivery_failed(err_msg: str, image_url: str) -> str:
        content = "[Notice] message undelivered: " + err_msg
        content = content + "\n\nimage: " + image_url
        logger.info(f"[SmsNoticeTool] mms_delivery_failed: {content}")
        return content

    @staticmethod
    def follow_correct_number_format(to_phone: str) -> str:
        content = f"[Notice] Hello, we noticed that the format of your recipient's phone number ({to_phone}) is incorrect. " \
                  "Currently, we only support phone numbers from the United States and Canada " \
                  "in the following format: +18881234567.\n\n" \
                  "Please feel free to reach out to our customer service team for any questions " \
                  "or concerns you may have.\n\nWe are here to assist you.!"
        logger.info(f"[SmsNoticeTool] follow_correct_number_format: {content}")
        return content

    @staticmethod
    def number_dont_want_been_contacted(to_phone: str) -> str:
        content = f"[Notice] This number ({to_phone}) has indicated that it doesn't like to be disturbed or contacted."
        return content

    @staticmethod
    def request_too_fast() -> str:
        content = "[Notice] We sincerely apologize for the inconvenience caused by your frequent requests. " \
                  "Due to this high frequency, the telecommunications service provider may consider it " \
                  "as abnormal traffic. " \
                  "Please try sending your request again later, or alternatively, " \
                  "you can contact customer service for further assistance."
        logger.info(f"[SmsNoticeTool] request_too_fast: {content}")
        return content

    @staticmethod
    def feedback_auto_reply(feedback_content: str) -> str:
        content = f"[Notice] We received your feedback. If you have any further questions, " \
                  f"you can also contact me here, and I will get back to you promptly. " \
                  f"Your feedback: [{feedback_content}]"
        logger.info(f"[SmsNoticeTool] feedback_auto_reply: {content}")
        return content

    @staticmethod
    def add_point(points: int) -> str:
        content = f"[Notice] Greetings! You have been given points: {points}, please check your account."
        logger.info(f"[SmsNoticeTool] add_point: {content}")
        return content

    @staticmethod
    def add_vip(add_days: int) -> str:
        content = f"[Notice] Greetings! You have been given free subscriptions: {add_days} days, " \
                  f"please check your account."
        logger.info(f"[SmsNoticeTool] add_vip: {content}")
        return content

    @staticmethod
    def get_back_number(number: str) -> str:
        content = f"[Notice] Congratulations! Your number: {number} is back!"
        logger.info(f"[SmsNoticeTool] get_back_number: {content}")
        return content

    @staticmethod
    def get_back_account():
        content = f"[Notice] Congratulations, your account has returned to normal. " \
                  f"Please do not delete the account casually as data may not be recoverable."
        logger.info(f"[SmsNoticeTool] get_back_account: {content}")
        return content

    @staticmethod
    def recover_subscription():
        content = f"[Notice] Congratulations, your account has returned to normal. " \
                  f"You can try reopening the app to see if there are any issues. " \
                  f"Please feel free to contact us at any time."
        logger.info(f"[SmsNoticeTool] get_back_account: {content}")
        return content

    @staticmethod
    def get_bind_subscription_failed():
        content = (f"[Notice] Hello, we have noticed that the subscription you attempted is already bound to another "
                   f"device. Please log in using your previous device. Or contact the customer service center to "
                   f"handle your subscription issue.")
        logger.info(f"[SmsNoticeTool] get_back_account: {content}")
        return content

    @staticmethod
    def temporary_ban_notification() -> str:
        content = ("[Notice] Hello. Your account has been temporarily suspended due to violations or abnormal "
                   "activities. Considering security risks, we have temporarily banned your account. If you have "
                   "further questions, please contact our customer service center.")
        logger.info(f"[SmsNoticeTool] temporary_ban_notification: {content}")
        return content

    @staticmethod
    def account_recovery_notification() -> str:
        content = ("[Notice] Hello. Your account has been successfully recovered. The previous temporary suspension "
                   "was caused by an accidental operation of the AI system, which was not due to any violations or "
                   "abnormal activities on your part. Your account can now be used normally. If you encounter any "
                   "issues during use, please contact our customer service center for assistance.")
        logger.info(f"[SmsNoticeTool] account_recovery_notification: {content}")
        return content

    @staticmethod
    def temporary_ban_notification_pornographic() -> str:
        content = """Dear User,

We have detected that your account has been involved in activities promoting obscene content or solicitation, which violates our app's community guidelines and terms of use.

As such, your account has been suspended in accordance with our user policy. This action is taken to maintain a safe and respectful environment for all users.

If you believe this suspension was made in error or if you have any questions, please contact our customer support team for assistance.

Thank you for your cooperation."""
        logger.info(f"[SmsNoticeTool] temporary_ban_notification_pornographic: {content}")
        return content

    @staticmethod
    def temporary_ban_notification_fraud() -> str:
        content = """Dear User,

We regret to inform you that your number has been reported by the telecom operator for alleged involvement in fraudulent or scam activities. As a result, the number has been reclaimed.

We sincerely apologize for any inconvenience caused. Should there be any updates, we will notify you promptly.

If you have any questions or require further assistance, please feel free to contact our customer support team.

Thank you for your understanding and cooperation."""
        logger.info(f"[SmsNoticeTool] temporary_ban_notification_fraud: {content}")
        return content

    @staticmethod
    def temporary_ban_notification_threaten() -> str:
        content = """Dear User,

    We have received a report from a telecommunications provider alleging that your account has been involved in threatening behavior. Such actions are a violation of our app's community guidelines and terms of use.

    As a result, we have taken action to suspend your account in accordance with our user policy.

    If you have any questions or believe this action was taken in error, please feel free to contact our customer support team for further assistance.

    Thank you for your understanding."""
        logger.info(f"[SmsNoticeTool] temporary_ban_notification_threaten: {content}")
        return content

    @staticmethod
    def temporary_ban_notification_racial_discrimination() -> str:
        content = """Dear User,  

We have determined that your account has been involved in discriminatory activities, which are strictly prohibited by our app's community guidelines and terms of use.  

To ensure a safe and inclusive environment for all users, your account has been suspended in accordance with our user policy.  

If you believe this action was taken in error or have any questions, please contact our customer support team for further assistance.  

Thank you for your cooperation."""

        logger.info(f"[SmsNoticeTool] temporary_ban_notification_racial_discrimination: {content}")
        return content

    @staticmethod
    def temporary_ban_notification_harassment():
        content = """Dear User,

We regret to inform you that your account has been reported by the telecom operator for alleged acts of harassment or offensive behavior. According to our App’s User Policy, such actions are strictly prohibited. As a result, we have suspended your account.

If you have any questions or concerns, please feel free to contact our Customer Support Center. We are here to assist you.

Thank you for your understanding and cooperation."""
        logger.info(f"[SmsNoticeTool] temporary_ban_notification_harassment: {content}")
        return content

    @staticmethod
    def invalid_messaging_destination_number(number: str):
        content = f"sorry, the recipient {number} is invalid messaging destination number! Please check again."
        logger.info(f"[SmsNoticeTool] Invalid_messaging_destination_number: {content}")
        return content

    @staticmethod
    def recipient_message_rejected(number: str) -> str:
        content = f"Sorry, the carrier for {number} rejected the message."
        logger.info(f"[SmsNoticeTool] recipient_message_rejected: {content}")
        return content

    @staticmethod
    def telnyx_say_you_spam(number: str):
        content = (f"Sorry, the recipient {number} has reported you for spam activity. "
                   f"Please refrain from messaging this number again and ensure compliance with platform rules. "
                   f"Thank you for your understanding.")
        logger.info(f"[SmsNoticeTool] telnyx_say_you_spam: {content}")
        return content

    @staticmethod
    def telnyx_say_invalid_number(number: str):
        content = (f"Sorry, the recipient {number} is either a landline or a non-routable wireless number.")
        logger.info(f"[SmsNoticeTool] telnyx_say_invalid_number: {content}")
        return content

    @staticmethod
    def say_you_are_reported_as_harasser(number: str):
        content = f"Hello, it seems that the recipient {number} has indicated they don't wish to receive your messages. Please refrain from sending messages for the time being. Thank you for your cooperation."
        logger.info(f"[SmsNoticeTool] say_you_are_reported_as_harasser: {content}")
        return content

    @staticmethod
    def image_failed():
        content = (f"Our app's image sending feature is temporarily unavailable. We are working to fix the issue as "
                   f"soon as possible. Thank you for your patience!")
        logger.info(f"[SmsNoticeTool] image_failed: {content}")
        return content

    @staticmethod
    def telnyx_say_not_support_canada_sms(number: str):
        content = ("Hello, due to the latest SMS regulations, telecom operators are no longer supporting outgoing SMS "
                   "for Canadian numbers. However, you can still use this number normally for outgoing calls, "
                   "incoming calls, and receiving SMS. Would you like to switch to a better US number? If so, "
                   "please contact our customer service to submit a ticket, and we will replace your number "
                   f"immediately. If not, you can continue using this number {number} for calls and SMS reception.")
        logger.info(f"[SmsNoticeTool] telnyx_say_you_spam: {content}")
        return content
