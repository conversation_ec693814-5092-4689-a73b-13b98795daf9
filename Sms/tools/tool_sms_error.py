from Common.err import ErrInfo


class SmsErrorTool:
    @staticmethod
    def create_sms_error(e, from_number: str, to_number: str):
        extra_tips_msg = "Please contact customer service for feedback within the app or " \
                         "<NAME_EMAIL>. Let them handle your issue."
        err_str = str(e)
        is_unknown = True
        if 'Blocked due to STOP message' in err_str:
            err = ErrInfo(ErrInfo.SEND_SMS_ERROR, f"The number:{to_number} has blocked you")
            is_unknown = False
        elif '502 Bad Gateway' in err_str:
            err = ErrInfo(ErrInfo.SEND_SMS_ERROR, f"SMS server is temporarily unavailable. Please try again later.")
            is_unknown = False
        elif 'HTTP response code was 503' in err_str:
            err = ErrInfo(ErrInfo.SEND_SMS_ERROR, f"SMS server is temporarily unavailable. Please try again later.")
            is_unknown = False
        elif "Invalid 'from' addressFull details" in err_str:
            err = ErrInfo(ErrInfo.SEND_SMS_ERROR, f"Your number：{from_number} is not configured correctly on the "
                                                  f"platform. {extra_tips_msg}")
            is_unknown = False
        elif ("Invalid destination number" in err_str or "Invalid phone number" in err_str
              or "Invalid 'to' address" in err_str):
            err = ErrInfo(ErrInfo.SEND_SMS_ERROR, f"Invalid destination number: {to_number}. Please check again.")
            is_unknown = False
        elif "Invalid source number" in err_str:
            err = ErrInfo(ErrInfo.SEND_SMS_ERROR, f"Invalid source number: {from_number}. Please check again. "
                                                  f"{extra_tips_msg}")
            is_unknown = False
        elif "Message too large" in err_str:
            err = ErrInfo(ErrInfo.SEND_SMS_ERROR, f"Message too large. Please shorten the text message")
            is_unknown = True
        elif "Temporarily unusable" in err_str:
            err = ErrInfo(ErrInfo.SEND_SMS_ERROR, f"The number you purchased is still initializing. "
                                                  f"Please wait a few minutes before sending the SMS.")
            is_unknown = False
        else:
            err = ErrInfo(ErrInfo.SEND_SMS_ERROR, f"Unable to send sms, {extra_tips_msg}")
        return err, is_unknown
