from typing import Union

from Call.models import SmsCheck
from SecPhone import settings
from Sms.info.wangyi_info_bean import WangyiFilterRet
from User.tools.user_tool import UserTool


class SmsInvalidTool:
    @staticmethod
    def get_by_message_id(message_id: int) -> Union[SmsCheck, None]:
        record = SmsCheck.objects.filter(id=message_id, deleted=0, reviewed=0).first()
        return record

    @staticmethod
    def get_user_invalid_sms_cnt(user_id: int) -> int:
        invalid_sms_count = SmsCheck.objects.filter(user_id=user_id,
                                                    direction=settings.SMS_DIRECTION_SEND,
                                                    deleted=0).count()
        return invalid_sms_count

    @staticmethod
    def get_user_invalid_sms_in_bad_labels_cnt(user_id: int) -> int:
        illegal_sms_cnt = SmsCheck.objects.filter(user_id=user_id,
                                                  direction=settings.SMS_DIRECTION_SEND,
                                                  label__in=settings.SMS_NOT_ALLOW_LABELS_WHEN_FILTER,
                                                  deleted=0).count()
        return illegal_sms_cnt

    @staticmethod
    def get_user_all_invalid_records(user_id: int) -> list:
        records = SmsCheck.objects.filter(user_id=user_id, direction=settings.SMS_DIRECTION_SEND, deleted=0).all()
        return records

    @staticmethod
    def get_user_need_to_review_records_cnt(user_id: int) -> int:
        invalid_sms_count = SmsCheck.objects.filter(user_id=user_id, deleted=0, reviewed=0).count()
        return invalid_sms_count

    @staticmethod
    def get_user_need_to_review_records(user_id: int) -> list:
        records = SmsCheck.objects.filter(user_id=user_id,
                                          label__in=settings.SMS_NOT_ALLOW_LABELS_WHEN_FILTER,
                                          deleted=0, reviewed=0).all()
        return records

    @staticmethod
    def get_latest_n_need_to_review_records(n: int) -> (list, int):
        records = SmsCheck.objects.filter(deleted=0, reviewed=0,
                                          direction=settings.SMS_DIRECTION_SEND,
                                          label__in=settings.SMS_NOT_ALLOW_LABELS_WHEN_FILTER) \
                      .order_by('-id')[:n]
        total_size = SmsCheck.objects.filter(deleted=0, reviewed=0,
                                             direction=settings.SMS_DIRECTION_SEND,
                                             label__in=settings.SMS_NOT_ALLOW_LABELS_WHEN_FILTER).count()
        return records, total_size

    @staticmethod
    def save_invalid_sms(user_id: int, origin_content: str, from_number: str, to_number: str,
                         wangyi: Union[WangyiFilterRet, None]):
        user = UserTool.get_user_by_id(user_id)
        if not user:
            return

        if wangyi:
            invalid_sms = SmsCheck(user_id=user_id, uuid=user.uuid, appid=user.appid,
                                   direction=settings.SMS_DIRECTION_SEND,
                                   content=origin_content, filtered_content=wangyi.filter_content,
                                   task_id=wangyi.task_id, suggestion=wangyi.suggestion, label=wangyi.label,
                                   sub_label=wangyi.sub_label,
                                   details=wangyi.details,
                                   from_number=from_number, to_number=to_number)
            invalid_sms.save()
        else:
            invalid_sms = SmsCheck(user_id=user_id, uuid=user.uuid, appid=user.appid,
                                   direction=settings.SMS_DIRECTION_SEND,
                                   content=origin_content, filtered_content=None,
                                   from_number=from_number, to_number=to_number)
            invalid_sms.save()

    @staticmethod
    def clear_user_all_records(user_id: int) -> int:
        rows_affected = SmsCheck.objects.filter(user_id=user_id, deleted=0).update(deleted=1)
        return rows_affected

    @staticmethod
    def clear_user_some_records(user_id: int, record_ids: list) -> int:
        rows_affected = SmsCheck.objects.filter(user_id=user_id, deleted=0, id__in=record_ids).update(deleted=1)
        return rows_affected
