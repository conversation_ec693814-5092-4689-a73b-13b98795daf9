import re
import threading

import SecPhone.logid_middleware
from AICustomerService.ai_customer_service import AiCustomerServiceUtil
from Call.models import SMSRecord
from Common.timeutil import TimeUtil
from Common.util import Util
from Number.tools.number_tool import NumberTool
from Order.tools.tool_refresh_order import Refresh<PERSON><PERSON><PERSON>Tool
from Push.push_util import PushUtil
from SecPhone import settings
from SecPhone.settings import logger
from Sms.tools.tool_sms import SmsTool
from User.tools.user_context_tool import UserContextTool
from User.tools.user_tool import UserTool


class SmsItSupportTool:

    @staticmethod
    def _is_it_support_phone(number: str) -> bool:
        return number == settings.APP_IT_SUPPORT_PHONE or number == settings.APP_IT_SUPPORT_SHOW_PHONE

    @staticmethod
    def _get_to_userid(to_number: str) -> int:
        if not to_number:
            logger.error(f"[SmsItSupportTool] _get_to_userid to_number is invalid:{to_number}")
            return 0
        if to_number[0:4] == settings.APP_IT_SUPPORT_NO_NUMBER_PREFIX:
            logger.info(f"[SmsItSupportTool] _get_to_userid:{to_number} is user_id prefix")
            return UserTool.get_userid_from_mock_number(to_number)
        elif NumberTool.is_number_in_inventory(to_number):
            logger.info(f"[SmsItSupportTool] _get_to_userid:{to_number} is in db")
            return NumberTool.get_user_id_by_number_even_expire(to_number)
        return 0

    @staticmethod
    def complete_ticket_url(content):
        try:
            # 定义正则表达式模式，捕获所有包含 TICKET_URL 的情况
            pattern = re.compile(r'[\{\[\(\<]*TICKET_URL[\}\]\)\>\{]*')

            def replacer(match):
                return '{{TICKET_URL}}'

            # 使用正则表达式的 sub 方法进行替换
            corrected_content = pattern.sub(replacer, content)

            return corrected_content
        except Exception:
            content_ = content.replace("\n", " ")
            logger.error(f"[SmsItSupportTool] complete_ticket_url failed: {content_}", exc_info=True)
            return content

    @staticmethod
    def set_ai_answer(thread_logid: str, user_id: int, query_number: str, query: str):
        try:
            # 设置logid
            SecPhone.logid_middleware.logid_context.set(thread_logid)

            if not query_number:
                query_number = UserTool.create_mock_number(user_id)

            logger.info(f"[SmsItSupportTool] set_ai_answer, user_id:{user_id}, query_number:{query_number}, "
                        f"query:{query}")

            if "http://" in query or "https://" in query:
                answer = "Hello! You have sent a link or image. To better assist you and ensure your materials are promptly forwarded to the technical department, we have more suitable channels for uploading these files. Please click here to submit a ticket, and we will respond to you within 24 to 48 hours. Thank you! {{TICKET_URL}}"
            else:
                answer = AiCustomerServiceUtil.get_ai_answer(query)

            if not answer:
                answer = "Hello! All our agents are currently busy now. Could you please try again in 2 minutes or submit a ticket here: {{TICKET_URL}} Our tech team will review your request and process it within 24-48 hours. Please note that changing numbers is not done at will due to the associated costs, and each request will be considered on a case-by-case basis. If you have any specific concerns or reasons for the change, you can include them in your ticket for the tech team's consideration."
                logger.warning(f"[SmsItSupportTool] default ai answer: {answer}")
            else:
                if "{{TICKET_URL}}" not in answer:
                    answer = answer + "\n\n\nIf you have any further questions, you can contact our engineering team through a ticket: {{TICKET_URL}} Our tech team will review your request and process it within 24-48 hours."

            user = UserTool.get_user_by_id(user_id)

            link = ""
            if "{{TICKET_URL}}" in answer:
                link = f"{settings.STATIC_URL_BASE}/submit_ticket/index.html?wxid={user.uuid}"
                answer = answer.replace("{{TICKET_URL}}", link)
            if "[TICKET_URL]" in answer:
                link = f"{settings.STATIC_URL_BASE}/submit_ticket/index.html?wxid={user.uuid}"
                answer = answer.replace("[TICKET_URL]", link)

            if "TICKET_URL" in answer:
                _answer = answer
                # 处理下 bad case
                answer = SmsItSupportTool.complete_ticket_url(answer)
                link = f"{settings.STATIC_URL_BASE}/submit_ticket/index.html?wxid={user.uuid}"
                answer = answer.replace("{{TICKET_URL}}", link)
                logger.error(f"[SmsItSupportTool] ai bad case: user_id:{user_id}, before:{_answer}, after:{answer}")

            if link:
                # 旧版本需要2条客服
                if user.app_version < 300600:
                    logger.info(f"[SmsItSupportTool] set_ai_answer, user_id:{user_id}, app_version:{user.app_version}, "
                                f"query_number:{query_number}, query:{query}, answer:{answer}, link: {link}")
                    answer += "\n\n[Please copy the link below and open it in your browser]"
                    SmsItSupportTool.add_support_sms_both_from_it_with_link(to_user_id=user_id, to_number=query_number,
                                                                            content=answer, link=link, is_ai_answer=1)
                    SmsItSupportTool.add_support_sms_both_from_it_with_link(to_user_id=user_id, to_number=query_number,
                                                                            content=link, link=link, is_ai_answer=1)
                else:
                    logger.info(f"[SmsItSupportTool] set_ai_answer, user_id:{user_id}, app_version:{user.app_version}, "
                                f"query_number:{query_number}, query:{query}, answer:{answer}")
                    SmsItSupportTool.add_support_sms_both_from_it_with_link(to_user_id=user_id, to_number=query_number,
                                                                            content=answer, link=link, is_ai_answer=1)
            else:
                logger.info(f"[SmsItSupportTool] set_ai_answer, user_id:{user_id}, query_number:{query_number}, "
                            f"query:{query}, answer:{answer}")
                SmsItSupportTool.add_support_sms_both_from_it_with_link(to_user_id=user_id, to_number=query_number,
                                                                        content=answer, link="", is_ai_answer=1)
        except Exception:
            query_ = query.replace("\n", " ")
            logger.error(f"[SmsItSupportTool] set_ai_answer, user_id:{user_id}, query_number:{query_number}, "
                         f"query:{query_}", exc_info=True)

    @staticmethod
    def it_support(user_id: int, from_number: str, to_number: str, content: str, latest_ts: int):
        try:
            if SmsItSupportTool._is_it_support_phone(to_number):
                SmsItSupportTool.add_support_sms_both_from_feedback(user_id, from_number, content)

                # AI 异步发送
                t = threading.Thread(target=SmsItSupportTool.set_ai_answer,
                                     args=(SecPhone.logid_middleware.logid_context.get(),
                                           user_id, from_number, content,))
                t.start()

                return True

            if SmsItSupportTool._is_it_support_phone(from_number):
                to_userid = SmsItSupportTool._get_to_userid(to_number)
                if to_userid:
                    SmsItSupportTool.add_support_sms_both_from_it(to_userid, to_number, content)
                    return True

            return False
        except Exception:
            logger.error(f"[SmsItSupportTool] it support:{user_id}, {from_number}->{to_number}, "
                         f"content:{content}, latest_ts:{latest_ts}, failed", exc_info=True)
            return False

    @staticmethod
    def mms_it_support(user_id: int, from_number: str, to_number: str, image_url: str, latest_ts: int):
        try:
            if SmsItSupportTool._is_it_support_phone(to_number):
                SmsItSupportTool.add_support_mms_both_from_feedback(user_id, from_number, image_url)

                # AI 异步发送
                t = threading.Thread(target=SmsItSupportTool.set_ai_answer,
                                     args=(SecPhone.logid_middleware.logid_context.get(),
                                           user_id, from_number, image_url,))
                t.start()

                return True

            if SmsItSupportTool._is_it_support_phone(from_number):
                to_userid = SmsItSupportTool._get_to_userid(to_number)
                if to_userid:
                    SmsItSupportTool.add_support_mms_both_from_it(to_userid, to_number, image_url)
                    return True

            return False
        except Exception:
            logger.error(f"[SmsItSupportTool] it support:{user_id}, {from_number}->{to_number}, "
                         f"image_url:{image_url},latest_ts:{latest_ts}, failed", exc_info=True)
            return False

    @staticmethod
    def add_support_sms_both_from_feedback(feedback_user_id: int, feedback_from_number: str, content: str):
        try:
            if not feedback_from_number:
                feedback_from_number = UserTool.create_mock_number(feedback_user_id)

            it_support_user_id = settings.APP_IT_SUPPORT_USERID

            latest_ts = TimeUtil.GetNowTsInInt()
            if SmsTool.is_user_first_feedback(feedback_user_id, feedback_from_number):
                # 有一些用户首次投诉过来，可能是因为订阅问题，此时更新下订阅
                ret = RefreshOrderTool.refresh_user_vip(feedback_user_id)
                logger.info(f"[SmsItSupportTool] {feedback_user_id}:{feedback_from_number}:{content}, order:{ret}")

                user_context_str = UserContextTool.get_user_context_str(email="", content="[首次会话携带用户信息]",
                                                                        user_id=feedback_user_id)

                SmsItSupportTool.add_support_sms(it_support_user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 feedback_from_number, settings.APP_IT_SUPPORT_SHOW_PHONE,
                                                 user_context_str, latest_ts=latest_ts + 0)

            SmsItSupportTool.add_support_sms(feedback_user_id, settings.SMS_DIRECTION_SEND,
                                             feedback_from_number, settings.APP_IT_SUPPORT_SHOW_PHONE, content)
            SmsItSupportTool.add_support_sms(it_support_user_id, settings.SMS_DIRECTION_RECEIVE,
                                             feedback_from_number, settings.APP_IT_SUPPORT_SHOW_PHONE, content,
                                             latest_ts=latest_ts + 1)
            logger.info(f"[SmsItSupportTool] {feedback_user_id}:{feedback_from_number}:{content}")
        except Exception:
            logger.error(f"[SmsItSupportTool] {feedback_user_id}:{feedback_from_number}:{content} failed",
                         exc_info=True)

    @staticmethod
    def add_support_mms_both_from_feedback(feedback_user_id: int, feedback_from_number: str, image_url: str):
        try:
            if not feedback_from_number:
                feedback_from_number = UserTool.create_mock_number(feedback_user_id)

            it_support_user_id = settings.APP_IT_SUPPORT_USERID
            SmsItSupportTool.add_support_sms(feedback_user_id, settings.SMS_DIRECTION_SEND,
                                             feedback_from_number, settings.APP_IT_SUPPORT_SHOW_PHONE, '', image_url)
            SmsItSupportTool.add_support_sms(it_support_user_id, settings.SMS_DIRECTION_RECEIVE,
                                             feedback_from_number, settings.APP_IT_SUPPORT_SHOW_PHONE, '', image_url)
            logger.info(f"[SmsItSupportTool] {feedback_user_id}:{feedback_from_number}:{image_url}")
        except Exception:
            logger.error(f"[SmsItSupportTool] {feedback_user_id}:{feedback_from_number}:{image_url} failed",
                         exc_info=True)

    @staticmethod
    def add_support_sms_both_from_it(to_user_id: int, to_number: str, content: str):
        try:
            from_user_id = settings.APP_IT_SUPPORT_USERID
            SmsItSupportTool.add_support_sms(from_user_id, settings.SMS_DIRECTION_SEND,
                                             settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, content)
            SmsItSupportTool.add_support_sms(to_user_id, settings.SMS_DIRECTION_RECEIVE,
                                             settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, content)
            logger.info(f"[SmsItSupportTool] sms it reply user:{to_user_id}, to:{to_number}, content:{content}")
        except Exception:
            logger.error(f"[SmsItSupportTool] sms it reply user:{to_user_id}:{to_number}, content:{content} failed",
                         exc_info=True)

    @staticmethod
    def add_support_sms_both_from_it_with_link(to_user_id: int, to_number: str, content: str, link: str,
                                               is_ai_answer: int):
        try:
            if not UserTool.get_user_by_id(to_user_id):
                logger.error(f"[SmsItSupportTool] user not exists: {to_user_id}")
                return

            from_user_id = settings.APP_IT_SUPPORT_USERID
            SmsItSupportTool.add_support_sms_with_link(from_user_id, settings.SMS_DIRECTION_SEND,
                                                       settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, content, link,
                                                       is_ai_answer)
            SmsItSupportTool.add_support_sms_with_link(to_user_id, settings.SMS_DIRECTION_RECEIVE,
                                                       settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, content, link,
                                                       is_ai_answer)
            logger.info(f"[SmsItSupportTool] sms it reply user:{to_user_id}, to:{to_number}, content:{content}")
        except Exception:
            logger.error(f"[SmsItSupportTool] sms it reply user:{to_user_id}:{to_number}, content:{content} failed",
                         exc_info=True)

    @staticmethod
    def add_support_sms_both_from_it_v2(to_user_id: int, to_number: str, db_content: str, push_content: str):
        try:
            from_user_id = settings.APP_IT_SUPPORT_USERID
            SmsItSupportTool.add_support_sms_v2(from_user_id, settings.SMS_DIRECTION_SEND,
                                                settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, db_content,
                                                push_content)
            SmsItSupportTool.add_support_sms_v2(to_user_id, settings.SMS_DIRECTION_RECEIVE,
                                                settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, db_content,
                                                push_content)
            logger.info(f"[SmsItSupportTool] sms it reply user:{to_user_id}, to:{to_number}, "
                        f"db_content:{db_content}, push_content:{push_content}")
        except Exception:
            logger.error(f"[SmsItSupportTool] sms it reply user:{to_user_id}:{to_number}, db_content:{db_content}, "
                         f"push_content:{push_content}, failed", exc_info=True)

    @staticmethod
    def add_support_mms_both_from_it(to_user_id: int, to_number: str, image_url: str):
        try:
            from_user_id = settings.APP_IT_SUPPORT_USERID
            SmsItSupportTool.add_support_sms(from_user_id, settings.SMS_DIRECTION_SEND,
                                             settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, '', image_url)
            SmsItSupportTool.add_support_sms(to_user_id, settings.SMS_DIRECTION_RECEIVE,
                                             settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, '', image_url)

            logger.info(f"[SmsItSupportTool] mms it reply user:{to_user_id}:{to_number}, image_url:{image_url}")
        except Exception:
            logger.error(f"[SmsItSupportTool] mms it reply user:{to_user_id}:{to_number}, image_url:{image_url} failed",
                         exc_info=True)

    @staticmethod
    def add_support_sms(user_id, direction, from_number, to_number, content, images_urls=None, latest_ts=None):
        fake_message_sid = Util.GenFakeSId()
        if direction == settings.SMS_DIRECTION_SEND:
            status = "delivered"
        else:
            status = "received"

        logger.info(f'[add_support_sms] sid:{fake_message_sid}, direction:{direction}, user:{user_id}, '
                    f'{from_number}->{to_number}, content={content}, images_urls={images_urls}, status:{status}')
        try:
            if not latest_ts:
                latest_ts = TimeUtil.GetNowTsInInt()
            record = SMSRecord(
                sid=fake_message_sid, user_id=user_id, direction=direction.upper(), from_number=from_number,
                to_number=to_number,
                content=content, filtered_content='', images=images_urls, latest_ts=latest_ts,
                status=status, price=0, point=0, err_code=0, is_it=1
            )
            record.save()

            # 因为是假短信，只能自己实现推送
            if direction == settings.SMS_DIRECTION_RECEIVE:
                # 系统通知就不要推送了
                if content == settings.CUSTOMER_REPLY_FINISH:
                    return
                # 不要给自己推送了
                if user_id == settings.APP_IT_SUPPORT_USERID:
                    return

                PushUtil.notify_by_user(user_id, 1, content)

        except Exception:
            logger.error(f"[SmsItSupportTool.add_support_sms] user:{user_id}, from:{from_number}, to:{to_number}, "
                         f"sid:{fake_message_sid}, content:{content}, save error: ", exc_info=True)

    @staticmethod
    def add_support_sms_with_link(user_id: int, direction: str, from_number: str, to_number: str, content: str,
                                  link: str, is_ai_answer: int, images_urls=None, latest_ts=None):
        fake_message_sid = Util.GenFakeSId()
        if direction == settings.SMS_DIRECTION_SEND:
            status = "delivered"
        else:
            status = "received"

        logger.info(f'[add_support_sms] sid:{fake_message_sid}, direction:{direction}, user:{user_id}, '
                    f'{from_number}->{to_number}, content={content}, images_urls={images_urls}, status:{status}')
        try:
            if not latest_ts:
                latest_ts = TimeUtil.GetNowTsInInt()
            record = SMSRecord(
                sid=fake_message_sid, user_id=user_id, direction=direction.upper(), from_number=from_number,
                to_number=to_number,
                content=content, filtered_content='', images=images_urls, latest_ts=latest_ts,
                status=status, price=0, point=0, err_code=0, is_it=1, link=link, is_ai_answer=is_ai_answer,
            )
            record.save()

            # 因为是假短信，只能自己实现推送
            if direction == settings.SMS_DIRECTION_RECEIVE:
                # 系统通知就不要推送了
                if content == settings.CUSTOMER_REPLY_FINISH:
                    return
                # 不要给自己推送了
                if user_id == settings.APP_IT_SUPPORT_USERID:
                    return

                PushUtil.notify_by_user(user_id, 1, content)

        except Exception:
            logger.error(f"[SmsItSupportTool.add_support_sms] user:{user_id}, from:{from_number}, to:{to_number}, "
                         f"sid:{fake_message_sid}, content:{content}, save error: ", exc_info=True)

    @staticmethod
    def add_support_sms_v2(user_id, direction, from_number, to_number, db_content, push_content, images_urls=None,
                           latest_ts=None):
        fake_message_sid = Util.GenFakeSId()
        if direction == settings.SMS_DIRECTION_SEND:
            status = "delivered"
        else:
            status = "received"

        logger.info(f'[add_support_sms] sid:{fake_message_sid}, direction:{direction}, user:{user_id}, '
                    f'{from_number}->{to_number}, push_content={push_content}, images_urls={images_urls}, status:{status}')
        try:
            if not latest_ts:
                latest_ts = TimeUtil.GetNowTsInInt()
            record = SMSRecord(
                sid=fake_message_sid, user_id=user_id, direction=direction.upper(), from_number=from_number,
                to_number=to_number,
                content=db_content, filtered_content='', images=images_urls, latest_ts=latest_ts,
                status=status, price=0, point=0, err_code=0, is_it=1
            )
            record.save()

            # 因为是假短信，只能自己实现推送
            if direction == settings.SMS_DIRECTION_RECEIVE:
                # 系统通知就不要推送了
                if push_content == settings.CUSTOMER_REPLY_FINISH:
                    return
                # 不要给自己推送了
                if user_id == settings.APP_IT_SUPPORT_USERID:
                    return

                PushUtil.notify_by_user(user_id, 1, push_content)

        except Exception:
            logger.error(f"[SmsItSupportTool.add_support_sms] user:{user_id}, from:{from_number}, to:{to_number}, "
                         f"sid:{fake_message_sid}, content:{push_content}, save error: ", exc_info=True)
