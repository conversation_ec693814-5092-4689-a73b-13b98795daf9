import datetime
import json

from django.core.cache import cache
from django.utils.decorators import method_decorator
from twilio.twiml.voice_response import <PERSON><PERSON><PERSON>po<PERSON>, Dial

from Call.callcommon import <PERSON><PERSON><PERSON><PERSON>
from Call.tool_call import CallTool
from Common.err import ErrInfo
from Common.ratelimit.decorators import ratelimit
from Common.rediskey import RedisKey
from Common.util import Util
from Common.views import Sec<PERSON>honeView
from MyTelnyx.my_telnyx import TelnyxUtil, TELNYX_SID_CONNECTION_PREFIX
from Number.tools.number_tool import NumberTool
from Number.tools.number_valid_tool import NumberValidTool
from Order.tools.tool_order import OrderTool
from SecPhone import settings
from SecPhone.settings import logger
from Sms.info import sms_info_bean
from Sms.tools.tool_forbidden_sms_call import SmsCallForbiddenTool
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from Sms.tools.tool_sms import SmsTool
from Sms.tools.tool_sms_add import SmsAddTool
from User.models import User
from User.tools.user_kill_tool import UserKillTool
from User.tools.user_tool import UserTool


class GetTelnyxAccessToken(SecPhoneView):

    @staticmethod
    def access_token_v2(user: User):
        sip_connection_id, sip_username, sip_password = TelnyxUtil.refresh_sip_connection(user.id)

        if not sip_connection_id:
            logger.warning(f"[Telnyx.GetAccessToken.v2] user:{user.id}, sip_connection_id:{sip_connection_id}, "
                           f"sip_username:{sip_username}, sip_password:{sip_password}")
            return {
                "access_token": "",
                "sip_username": "",
                "sip_password": "",
            }

        logger.info(f"[GetAccessToken.v2] user:{user.id}, sip_connection_id:{sip_connection_id}, "
                    f"sip_username:{sip_username}, sip_password:{sip_password}")
        return {
            "access_token": "",
            "sip_username": sip_username,
            "sip_password": sip_password,
        }

    @staticmethod
    def access_token_v3(user: User):
        sip_connection_id, sip_username, sip_password = TelnyxUtil.refresh_sip_connection(user.id)

        if not sip_connection_id:
            logger.warning(f"[Telnyx.GetAccessToken.v3] user:{user.id}, sip_connection_id:{sip_connection_id}, "
                           f"sip_username:{sip_username}, sip_password:{sip_password}")
            return {
                "access_token": "",
                "sip_username": "",
                "sip_password": "",
            }

        encrypt_sip_username = Util.AESEncode(sip_username)
        encrypt_sip_password = Util.AESEncode(sip_password)
        logger.info(f"[GetAccessToken.v3] user:{user.id}, sip_connection_id:{sip_connection_id}, "
                    f"sip_username:{sip_username}, sip_password:{sip_password}, "
                    f"encrypt_sip_username={encrypt_sip_username}, encrypt_sip_password={encrypt_sip_password}")
        return {
            "access_token": "",
            "sip_username": encrypt_sip_username,
            "sip_password": encrypt_sip_password,
        }

    @SecPhoneView.VerifySign
    @SecPhoneView.VerifyToken
    def post(self, request):
        header = self.GetHeaderInRequest(request)
        userid = header['userid']

        user = UserTool.get_user_by_id(userid)
        if not user:
            return self.ReturnError(ErrInfo.USER_NOT_EXIST)

        if OrderTool.is_user_vip_expire(user.id) != ErrInfo.SUCCESS:
            logger.info(f"[GetAccessToken] user is not vip, no need to register: {user.id}")
            return self.ReturnSuccess(data={
                "access_token": "",
                "sip_username": "",
                "sip_password": "",
            })

        headers = self.GetHeaderInRequest(request)
        app_version = headers['app_version']
        if app_version >= 200500:
            return self.ReturnSuccess(data=GetTelnyxAccessToken.access_token_v3(user))

        return self.ReturnSuccess(data=GetTelnyxAccessToken.access_token_v2(user))


class TeXMLWebhook(SecPhoneView):

    def incoming_v2(self, to_user_id: int, from_number: str, to_number: str):
        err = NumberValidTool.is_to_number_ok(to_number)

        addition_content = "You can try to send a message to this number, it may be received"
        if err != ErrInfo.SUCCESS:
            logger.warning(f"[TeXMLWebhook.incoming_v2] {from_number}->{to_number}, but {to_number} unavailable!")
            resp = VoiceResponse()
            if err == ErrInfo.CONTACTED_ACCOUNT_UNAVAILABLE_NO_SUPPORT_NUMBER:
                resp.say("Hello! The number you dialed is not currently supported!" + addition_content, voice='alice')
            elif err == ErrInfo.ONLY_NUMBERS_IN_THE_US_AND_CANADA_ARE_SUPPORTED:
                resp.say("Hello! Only numbers in the US and Canada are supported!" + addition_content, voice='alice')
            elif err == ErrInfo.CONTACTED_ACCOUNT_UNAVAILABLE_NO_VIP:
                resp.say("Hello! Sorry, the number you have dialed cannot be answered because it is not subscribed."
                         + addition_content,
                         voice='alice')
            elif err == ErrInfo.CONTACTED_ACCOUNT_UNAVAILABLE_NO_POINT:
                resp.say(
                    "Sorry, the number you called doesn't have enough points to accept the call. " + addition_content,
                    voice='alice')
            else:
                resp.say("The number you dialed is temporarily unavailable! " + addition_content, voice='alice')

            logger.warning(f"[TeXMLWebhook.incoming_v2] from: {from_number}, to: {to_number}, resp: {resp}")
            # 推送给接收者
            system_content, db_content, push_content = SmsNoticeTool.miss_incoming_call(from_number)
            SmsItSupportTool.add_support_sms_v2(to_user_id, settings.SMS_DIRECTION_RECEIVE,
                                                settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, db_content=db_content,
                                                push_content=push_content)

            return self.ReturnStr(str(resp))

        # 用户 SIP-connection 信息不全
        to_user = UserTool.get_user_by_id(to_user_id)
        if not to_user or not to_user.telnyx_sip_username:
            logger.warning(f"[TeXMLWebhook.incoming_v2] incoming, from: {from_number}, to: {to_number}，"
                           f"to_user_id: {to_user_id}，failed without sip_username")

            # 推送给接收者
            system_content, db_content, push_content = SmsNoticeTool.miss_incoming_call(from_number)
            SmsItSupportTool.add_support_sms_v2(to_user_id, settings.SMS_DIRECTION_RECEIVE,
                                                settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, db_content=db_content,
                                                push_content=push_content)

            resp = VoiceResponse()
            resp.dial()
            resp.say("The number you dialed is not answered for the moment, please call again later" + addition_content,
                     voice='alice')
            return self.ReturnStr(str(resp))

        # 创建响应对象
        resp = VoiceResponse()
        dial = Dial(answerOnBridge=True, callerId=from_number, timeout=60)
        dial.sip(f"{to_user.telnyx_sip_username}@sip.telnyx.com")
        resp.append(dial)
        xml_str = str(resp)

        logger.info(f"[TeXMLWebhook.incoming_v2] incoming, from: {from_number}, to: {to_number}, "
                    f"to_user_id: {to_user_id}, success: {xml_str}")
        return self.ReturnStr(xml_str)

    def get(self, request):
        logger.error("INVALID WEBHOOK: phone/call/texml/webhook/")
        # 打印查询字符串参数（GET参数）
        logger.error("GET parameters: %s", request.GET)

        # 打印表单数据（POST参数），仅在POST请求时有效
        logger.error("POST parameters: %s", request.POST)

        # 打印请求的主体内容（用于JSON等格式的数据）
        logger.error("Body: %s", request.body.decode('utf-8'))

        # 打印请求的meta信息，包含所有请求头和其他元数据
        logger.error("META: %s", request.META)
        return self.ReturnStr("")

    @method_decorator(ratelimit(key='post:From', rate='120/1h', block=True))
    @method_decorator(ratelimit(key='post:From', rate='20/60s', block=True))
    @method_decorator(ratelimit(key='post:To', rate='120/1h', block=True))
    @method_decorator(ratelimit(key='post:To', rate='20/60s', block=True))
    def post(self, request):
        logger.info(f"[TeXMLWebhook] post_data: BODY: {request.POST}")
        from_number = request.POST['From']
        to_number = request.POST['To']
        to_number = Util.FormatNumberV2(to_number)
        if from_number == to_number:
            logger.error(f"[TeXMLWebhook] incoming: {from_number}->{to_number} invalid call, same number!!! "
                         f"give up incoming.")
            resp = VoiceResponse()
            resp.say("Hello, the user you dialed is temporarily unavailable and cannot use phone services. Please try "
                     "contacting them through other means.", voice='alice')
            return self.ReturnStr(str(resp))

        if NumberValidTool.is_to_number_ok(from_number) != ErrInfo.SUCCESS:
            logger.warning(f"[TeXMLWebhook] incoming: {from_number}->{to_number} invalid from! give up incoming.")
            resp = VoiceResponse()
            resp.say("Hello, the user you dialed is temporarily unavailable and cannot use phone services. Please try "
                     "contacting them through other means.", voice='alice')
            return self.ReturnStr(str(resp))

        to_user_id = NumberTool.get_user_id_by_number(to_number)
        if not to_user_id:
            logger.warning(f"[TeXMLWebhook] incoming: {to_number} not in db or expire!!!")
            resp = VoiceResponse()
            resp.say("Hello, the user you dialed is temporarily unavailable and cannot use phone services. Please try "
                     "contacting them through other means.", voice='alice')
            return self.ReturnStr(str(resp))

        # 限制坏人用我们的号码去当客服号码做电炸钓鱼
        allow_person_cnt = 0
        if cache.get(RedisKey.gen_incoming_call_allow(to_user_id)):
            allow_person_cnt = 3
            logger.warning(f"[TeXMLWebhook] incoming: {to_number}, user:{to_user_id}, allow cnt add extra 3")

        user_vip_days = OrderTool.get_user_vip_days(to_user_id)
        logger.info(f"[TeXMLWebhook] incoming: {to_number}, user:{to_user_id}, user_vip_days:{user_vip_days}...")
        if 0 <= user_vip_days <= 3:
            # 试用期内 VIP
            incoming_person_cnt = CallTool.get_all_from_numbers_cnt_when_incoming(to_user_id)
            if incoming_person_cnt >= 3 + allow_person_cnt:
                logger.warning(
                    f"[TeXMLWebhook] incoming: {to_number}, user:{to_user_id}, user_vip_days:{user_vip_days}, "
                    f"incoming_person_cnt:{incoming_person_cnt}, new user can only get 2 + {allow_person_cnt} "
                    f"incoming person calls")
                resp = VoiceResponse()
                resp.say(
                    "Hello, the user you dialed is temporarily unavailable and cannot use phone services. Please try "
                    "contacting them through other means.", voice='alice')
                return self.ReturnStr(str(resp))
        elif 4 <= user_vip_days <= 7:
            # 刚过试用期的 VIP
            incoming_person_cnt = CallTool.get_all_from_numbers_cnt_when_incoming(to_user_id)
            if incoming_person_cnt >= 5 + allow_person_cnt:
                logger.warning(
                    f"[TeXMLWebhook] incoming: {to_number}, user:{to_user_id}, user_vip_days:{user_vip_days}, "
                    f"incoming_person_cnt:{incoming_person_cnt}, new user can only get 5 + {allow_person_cnt} "
                    f"incoming person calls")
                resp = VoiceResponse()
                resp.say(
                    "Hello, the user you dialed is temporarily unavailable and cannot use phone services. Please try "
                    "contacting them through other means.", voice='alice')
                return self.ReturnStr(str(resp))
        elif 8 <= user_vip_days <= 30:
            # 一个月内的 VIP
            incoming_person_cnt = CallTool.get_all_from_numbers_cnt_when_incoming(to_user_id)
            if incoming_person_cnt >= 8 + allow_person_cnt:
                logger.warning(
                    f"[TeXMLWebhook] incoming: {to_number}, user:{to_user_id}, user_vip_days:{user_vip_days}, "
                    f"incoming_person_cnt:{incoming_person_cnt}, new user can only get 15 + {allow_person_cnt} "
                    f"incoming person calls")
                resp = VoiceResponse()
                resp.say(
                    "Hello, the user you dialed is temporarily unavailable and cannot use phone services. Please try "
                    "contacting them through other means.", voice='alice')
                return self.ReturnStr(str(resp))

        # 如果是之前的用户的对话短信，不要骚扰当前用户
        if SmsCallForbiddenTool.check_if_from_number_seeking_before_user(to_user_id, from_number, to_number):
            logger.warning(f"[TeXMLWebhook] from_number is seeking before user, not for user:{to_user_id}, "
                           f"{from_number}->{to_number} give up")
            # 加到未完成记录中，暂时用sms_info来存储
            sms_info = sms_info_bean.create_not_finished_info(user_id=to_user_id, from_number=from_number,
                                                              to_number=to_number,
                                                              direction=settings.SMS_DIRECTION_RECEIVE,
                                                              origin_content='', image_url='')
            sms_info.not_finished_reason = (f"[TeXMLWebhook] from_number:{from_number} is seeking before user, "
                                            f"not for user:{to_user_id}")
            SmsAddTool.add_not_finished_sms_call_record(sms_info=sms_info,
                                                        event_type=settings.EVENT_TYPE_CALL,
                                                        not_finished_code=settings.NOT_FINISHED_SEEKING_BEFORE_USER)

            resp = VoiceResponse()
            resp.say("Hello, the user you dialed is temporarily unavailable and cannot use phone services. Please try "
                     "contacting them through other means.", voice='alice')
            return self.ReturnStr(str(resp))

        # 如果我们给这个号码发送过stop，也不想收到
        if SmsTool.is_stop_someone_incoming(to_user_id, from_number):
            logger.warning(f"[TeXMLWebhook] {to_user_id}:{from_number}->{to_number}, not send due to reply stop.")
            # 加到未完成记录中，暂时用sms_info来存储
            sms_info = sms_info_bean.create_not_finished_info(user_id=to_user_id, from_number=from_number,
                                                              to_number=to_number,
                                                              direction=settings.SMS_DIRECTION_RECEIVE,
                                                              origin_content='', image_url='')
            sms_info.not_finished_reason = (f"[TeXMLWebhook] from_number:{from_number} is being stop by user before, "
                                            f"user:{to_user_id}")
            SmsAddTool.add_not_finished_sms_call_record(sms_info=sms_info,
                                                        event_type=settings.EVENT_TYPE_CALL,
                                                        not_finished_code=settings.NOT_FINISHED_INCOMING_NUMBER_FORBIDDEN)
            resp = VoiceResponse()
            resp.say("Hello, the number you dialed has blocked you. Please try "
                     "contacting them through other means.", voice='alice')
            return self.ReturnStr(str(resp))

        return self.incoming_v2(to_user_id, from_number, to_number)


class TeXMLWebhookFailed(SecPhoneView):

    def post(self, request):
        try:
            logger.warning(f"[TelnyxTeXMLWebhookFailed] post_data: BODY: {request.POST}, {request.body}")
            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[TelnyxTeXMLWebhookFailed] failed: BODY: {request.body}", exc_info=True)
            return self.ReturnSuccess()


class SipConnectionWebhookV2(SecPhoneView):
    @staticmethod
    def get_app_version(from_number: str, to_number: str, direction: str) -> str:
        if direction == settings.CALL_DIRECTION_OUTGOING:
            from_user_id = NumberTool.get_user_id_by_number_even_expire(from_number)
            from_user = UserTool.get_user_by_id(from_user_id) if from_user_id else None
            app_version = from_user.app_version if from_user else 'unknown'
            return app_version
        elif direction == settings.CALL_DIRECTION_INCOMING:
            if Util.has_alphanumeric_chars(to_number):  # to_number:txtnow1013552
                to_user = UserTool.get_user_by_sip_username(to_number)
                to_user_id = to_user.id if to_user else 0
            else:
                to_user_id = NumberTool.get_user_id_by_number_even_expire(to_number)  # to_number: +13331234567

            to_user = UserTool.get_user_by_id(to_user_id) if to_user_id else None
            app_version = to_user.app_version if to_user else 'unknown'
            return app_version
        else:
            return "unknown"

    @staticmethod
    def double_check_number_and_user_binging(number: str, sip_connection_id: str) -> (int, int):
        try:
            # 根据 sip_connection_id 找到这个userid
            sip_username = TelnyxUtil.get_sip_username_connection_by_sip_id(sip_connection_id)
            if not sip_username:
                logger.warning(f"[SIPWebhook][double_check_number_and_user_binging] failed to parse:{number}, "
                               f"sip_connection_id:{sip_connection_id}")
                return 0, 0

            user_id_from_sip = int(sip_username.replace(TELNYX_SID_CONNECTION_PREFIX, ""))

            if number:
                user_id = NumberTool.get_user_id_by_number_even_expire(number)
            else:
                user_id = 0
            return user_id_from_sip, user_id
        except Exception:
            logger.error(f"[SIPWebhook][double_check_number_and_user_binging] failed, from:{number}, "
                         f"sip_connection_id:{sip_connection_id}", exc_info=True)
            return 0, 0

    def post(self, request):
        try:
            post_data = json.loads(request.body.decode('utf-8').replace("\n", ""))
            logger.info(f"[SIPWebhook] post_data: {post_data}")

            call_event = post_data["data"].get("event_type", None)
            payload = post_data["data"].get("payload", {})
            start_time = payload.get("start_time", None)
            end_time = payload.get("end_time", None)
            call_control_id = payload.get("call_control_id", None)
            direction = payload.get("direction", None)
            from_number = payload.get("from", None)
            to_number = payload.get("to", None)
            connection_id = payload.get("connection_id", None)
            app_version = SipConnectionWebhookV2.get_app_version(from_number, to_number, direction)

            if call_event == "call.playback.started":
                return self.ReturnSuccess()
            if call_event == "call.playback.ended":
                return self.ReturnSuccess()

            # 双重检查下大电话的userid和号码是否匹配
            if direction == settings.CALL_DIRECTION_OUTGOING:
                user_id_from_sip, db_user_id = self.double_check_number_and_user_binging(from_number, connection_id)
                if user_id_from_sip != db_user_id and user_id_from_sip > 0:
                    logger.error(f"[SIPWebhook][{direction}][封号处理]为什么分配给你的userid和号码的userid不匹配？"
                                 f"{from_number}>{to_number},"
                                 f"user_id_from_sip:{user_id_from_sip}, db_user_id:{db_user_id}, post_data:{post_data}")
                    UserKillTool.kill_user(user_id_from_sip, -365)
                else:
                    logger.info(f"[SIPWebhook][outgoing] webhook invalid, [{direction}]{from_number}->{to_number}, "
                                f"user_id_from_sip:{user_id_from_sip}, db_user_id:{db_user_id}")
            elif direction == settings.CALL_DIRECTION_INCOMING:
                user_id_from_sip, db_user_id = self.double_check_number_and_user_binging(to_number, connection_id)
            else:
                user_id_from_sip, db_user_id = self.double_check_number_and_user_binging("", connection_id)
                db_user_id = user_id_from_sip

            # 911 也要报警
            if from_number == '911' or to_number == '911':
                logger.error(f"[SIPWebhook] webhook invalid, 911, [{direction}]{from_number}->{to_number}, "
                             f"app_version:{app_version}, post_data:{post_data}")
                logger.error(f"[SIPWebhook][outgoing][封号处理]你理论上不应该可以打911的！"
                             f"[{direction}]{from_number}>{to_number},"
                             f"user_id_from_sip:{user_id_from_sip}, db_user_id:{db_user_id}, post_data:{post_data}")
                UserKillTool.kill_user(user_id_from_sip, -365)
                return self.ReturnSuccess()

            # 相同号码报警
            if from_number == to_number:
                logger.error(f"[SIPWebhook] webhook invalid, same number, [{direction}]{from_number}->{to_number}, "
                             f"app_version:{app_version}. post_data:{post_data}")
                logger.error(f"[SIPWebhook][outgoing][封号处理] 为什么你可以自己打电话给自己？"
                             f"[{direction}]{from_number}->{to_number}, "
                             f" post_data:{post_data}")
                UserKillTool.kill_user(user_id_from_sip, -365)
                return self.ReturnSuccess()

            if call_event == "call.initiated":
                # save to DB
                if direction == settings.CALL_DIRECTION_OUTGOING:
                    from_user_id = NumberTool.get_user_id_by_number_even_expire(from_number)
                    if not from_user_id:
                        # 分配给你的userid为啥你会用别人的号码
                        logger.error(f"[SIPWebhook][outgoing][封号处理] 为什么你的号码甚至不在数据库？"
                                     f"[{direction}]{from_number}->{to_number}, "
                                     f" post_data:{post_data}")
                        UserKillTool.kill_user(user_id_from_sip, -365)
                        return self.ReturnSuccess()

                    # 先存一波 db
                    CallCommon.AddCallRecord(call_control_id, from_user_id, settings.CALL_DIRECTION_OUTGOING,
                                             from_number, to_number)

                    # 检查 from
                    logger.info(f"[SIPWebhook][outgoing] call.initiated outgoing check is_self_number_ok: "
                                f"{from_user_id}:{from_number}->{to_number}, app_version:{app_version}")
                    err = NumberValidTool.is_self_number_ok(from_user_id)
                    if err != ErrInfo.SUCCESS:
                        from_user = UserTool.get_user_by_id(from_user_id)
                        app_version = from_user.app_version if from_user else 'unknown'
                        logger.error(f"[SIPWebhook][outgoing] webhook invalid: from invalid:{from_user_id}, "
                                     f"app_version:{app_version}, [{direction}]{from_number}->{to_number}, "
                                     f"err:{ErrInfo.ErrMsg_EN.get(err, 'unknown')}")
                        return self.ReturnSuccess()

                    # 检查 to
                    err = NumberValidTool.is_to_number_ok(to_number)
                    if err != ErrInfo.SUCCESS:
                        logger.error(f"[SIPWebhook][outgoing] webhook invalid: to invalid, from:{from_user_id}, "
                                     f"app_version:{app_version},  [{direction}]{from_number}->{to_number}, "
                                     f"err:{ErrInfo.ErrMsg_EN.get(err, 'unknown')}")

                        # 为什么你可以绕过检查，打电话给其他国家的人
                        logger.error(f"[SIPWebhook][outgoing][封号处理] 打电话给其他国家！"
                                     f"[{direction}]{from_number}->{to_number}, "
                                     f"user_id_from_sip:{user_id_from_sip}, db_user_id:{db_user_id}")
                        UserKillTool.kill_user(user_id_from_sip, -365)
                        return self.ReturnSuccess()

                elif direction == settings.CALL_DIRECTION_INCOMING:
                    if Util.has_alphanumeric_chars(to_number):  # to_number:txtnow1013552
                        to_user = UserTool.get_user_by_sip_username(to_number)
                        to_user_id = to_user.id
                    else:
                        to_user_id = NumberTool.get_user_id_by_number_even_expire(to_number)  # to_number: +13331234567
                    if not to_user_id:
                        logger.error(f"[SIPWebhook][incoming] [{direction}]{from_number}->{to_number}, "
                                     f"app_version:{app_version} not exists in our db, post_data:{post_data}")
                        return self.ReturnSuccess()
                    else:
                        CallCommon.AddCallRecord(call_control_id, to_user_id, settings.CALL_DIRECTION_INCOMING,
                                                 from_number, to_number)

                    logger.info(f"[SIPWebhook][incoming] call.initiated incoming check is_self_number_ok: "
                                f"{to_user_id}:{to_number}, app_version:{app_version}")
                    err = NumberValidTool.is_self_number_ok(to_user_id)
                    if err != ErrInfo.SUCCESS:
                        logger.error(
                            f"[SIPWebhook][incoming] [{direction}]{from_number}->{to_number}, to:{to_user_id}, "
                            f"app_version:{app_version} not ok, err:{ErrInfo.ErrMsg_EN.get(err, 'unknown')}")

                        # push to notify incoming user
                        system_content, db_content, push_content = SmsNoticeTool.miss_incoming_call(from_number)
                        SmsItSupportTool.add_support_sms_v2(to_user_id, settings.SMS_DIRECTION_RECEIVE,
                                                            settings.APP_IT_SUPPORT_SHOW_PHONE, to_number,
                                                            db_content=db_content, push_content=push_content)

                        return self.ReturnSuccess()

            elif call_event == "call.bridged":
                logger.info(f"[SIPWebhook] [{direction}]{from_number}->{to_number} bridged")
            elif call_event == "call.answered":
                logger.info(f"[SIPWebhook] [{direction}]{from_number}->{to_number} answered")
            elif call_event == "call.speak.ended":
                logger.error(f"[SIPWebhook] [{direction}]{from_number}->{to_number} call.speak.ended")
            elif call_event == "call.hangup":
                # 计算 duration
                start_datetime = datetime.datetime.fromisoformat(start_time[:-1])  # Remove the 'Z' at the end
                end_datetime = datetime.datetime.fromisoformat(end_time[:-1])  # Remove the 'Z' at the end
                duration = int((end_datetime - start_datetime).total_seconds())
                logger.info(f"[SIPWebhook] [{direction}]{from_number}->{to_number}, {duration} s")

                # 更新 点数
                CallCommon.update_call_record_telnyx(call_control_id, duration)

            elif call_event == "call.dtmf.received":
                logger.info(
                    f"[SIPWebhook] [{direction}]{from_number}->{to_number} call.dtmf.received")
            else:
                logger.error(f"[SIPWebhook] [{direction}]{from_number}->{to_number} "
                             f"invalid event: {call_event}")

            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[SIPWebhook] failed: BODY: {request.body}", exc_info=True)
            return self.ReturnSuccess()


class SipConnectionWebhookFailedV2(SecPhoneView):

    def post(self, request):
        try:
            post_data = json.loads(request.body.decode('utf-8').replace("\n", ""))
            logger.warning(f"[SIPWebhookFailed] post_data: {post_data}")
            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[SIPWebhookFailed] failed: BODY: {request.body}", exc_info=True)
            return self.ReturnSuccess()


class CallStatusWebhook(SecPhoneView):

    def post(self, request):
        try:
            logger.info(f"[CallStatusWebhook] post_data: BODY: {request.POST}")
            for k, v in request.POST.items():
                logger.info(f"[CallStatusWebhook] {k}: {v}")
            return self.ReturnSuccess()
        except Exception:
            logger.info(f"[CallStatusWebhook] failed: BODY: {request.body}", exc_info=True)
            return self.ReturnSuccess()


class GetTelnyxAccessTokenV4(SecPhoneView):

    @staticmethod
    def access_token_v4(sip_username: str, sip_password: str):
        sip_connection_id = TelnyxUtil.create_a_sip_connection_v4(sip_username, sip_password)
        return {
            "sip_connection_id": sip_connection_id,
            "sip_username": sip_username,
            "sip_password": sip_password,
        }

    @SecPhoneView.VerifySignV4
    @method_decorator(ratelimit(key='post:sip_username', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='post:sip_username', rate='1/60s', block=True))
    @method_decorator(ratelimit(key='post:sip_password', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='post:sip_password', rate='1/60s', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/1h', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/60s', block=True))
    def post(self, request):
        post_data, err_code, err_msg = self.ReadPostJson(request, check_list=['sip_username', 'sip_password'])
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)
        logger.error(f"[GetTelnyxAccessTokenV4] request json:{post_data}")
        sip_username = post_data('sip_username')
        sip_password = post_data('sip_password')
        return self.ReturnSuccess(data=GetTelnyxAccessTokenV4.access_token_v4(sip_username, sip_password))
