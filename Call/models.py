from django.db import models


class CallRecord(models.Model):
    sid = models.CharField(max_length=100)
    user_id = models.IntegerField()
    direction = models.CharField(max_length=20)  # in / out
    from_number = models.CharField(max_length=50)
    to_number = models.CharField(max_length=50)
    status = models.CharField(max_length=50, blank=True)  # fail, cancel, busy, answer
    duration = models.IntegerField(blank=True)
    cb_duration = models.IntegerField(blank=True)
    cb_call_duration = models.IntegerField(blank=True)
    price = models.FloatField(blank=True)
    point = models.IntegerField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class SMSRecord(models.Model):
    sid = models.CharField(max_length=100)
    user_id = models.IntegerField()
    latest_ts = models.BigIntegerField()
    direction = models.Char<PERSON>ield(max_length=20)  # in / out
    from_number = models.CharField(max_length=50)
    to_number = models.CharField(max_length=50)
    status = models.CharField(max_length=50)  # fail, cancel, complete
    err_code = models.IntegerField(blank=True, default=0)
    price = models.FloatField()
    point = models.IntegerField()
    content = models.TextField(blank=True)
    filtered_content = models.TextField(blank=True)
    images = models.CharField(max_length=2000, blank=True)  # if MMS
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted = models.IntegerField('deleted', blank=True, default=0)
    is_it = models.IntegerField(blank=True, default=0)  # 是否it推送
    is_inner = models.IntegerField(blank=True, default=0)  # 是否内部消化
    is_fake_send = models.IntegerField(blank=True, default=0)  # 是否假装发送
    is_non_english = models.IntegerField(blank=True, default=0)  # 是否非英语
    is_first_check_bad = models.IntegerField(blank=True, default=0)  # 是否初审失败
    is_bot_bad = models.IntegerField(blank=True, default=0)  # 是否机审失败
    ai_rsp = models.IntegerField(blank=True, default=0)  # 是否AI审失败, 0 没审，1正常，2不合格，3不知道
    # 计算parts: https://twiliodeved.github.io/message-segment-calculator/
    parts = models.IntegerField(blank=True, default=0)  # 短信切割分成几块，140个字符为1块
    first_check_reason = models.CharField(max_length=512, blank=True, default="")  # first check的理由
    link = models.CharField(max_length=512, blank=True, default="")  # 可能携带的link
    is_ai_answer = models.IntegerField(blank=True, default=0)  # 是否it ai 回复
    to_carrier = models.CharField(max_length=128, blank=True)  # 运营商
    is_reviewed = models.IntegerField(blank=True, default=0)  # 是否审核过了，审核过了就不展示在 resend admin
    is_fatpo_resend = models.IntegerField(blank=True, default=0)  # 是否 fatpo 重发
    wangyi_task_id = models.CharField(max_length=64, blank=True, default="")  # wangyi_id
    err_reason = models.CharField(max_length=512, blank=True, default="")  # 发送失败的原因
    is_image = models.IntegerField(blank=True, default=0)  # 是否图片
    is_buka_resent = models.IntegerField(blank=True, default=0)  # 是否buka代发
    image_md5 = models.CharField(blank=True, max_length=64)  # 图片md5
    objects = models.Manager()


class SMSCallNotFinishedRecord(models.Model):
    user_id = models.IntegerField()
    direction = models.CharField(max_length=20)  # in / out
    from_number = models.CharField(max_length=20)
    to_number = models.CharField(max_length=20)
    content = models.CharField(max_length=1000, blank=True)
    image_url = models.CharField(max_length=2000, blank=True)  # if MMS
    message_sid = models.CharField(max_length=100, blank=True)
    message_status = models.CharField(max_length=50, blank=True)
    event_type = models.CharField(max_length=10, blank=True)
    not_finished_reason = models.CharField(max_length=10000)
    not_finished_code = models.IntegerField(blank=True, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class SMSRecordWaitForReview(models.Model):
    REVIEW_CHOICES = [
        ('approve', 'Approve'),
        ('approveOriginalText', 'Approve Original Text'),
        ('reject', 'Reject'),
    ]

    JUDGE_CHOICES = [
        (0, 'Fatpo'),
        (1, 'Wangyi'),
        (2, 'Other'),  # 添加其他选项
    ]

    not_send_reason = models.CharField(max_length=256)
    user_id = models.IntegerField()
    latest_ts = models.BigIntegerField()
    ip = models.CharField(max_length=20, blank=True)
    uuid = models.CharField(max_length=32, blank=True)
    wangyi_task_id = models.CharField(max_length=64, blank=True)
    direction = models.CharField(max_length=20)  # in / out
    from_number = models.CharField(max_length=50)
    to_number = models.CharField(max_length=50)
    content = models.CharField(max_length=1000, blank=True)
    content_zh = models.CharField(max_length=1000, blank=True)
    filtered_content = models.CharField(max_length=1000, blank=True)
    images = models.CharField(max_length=2000, blank=True)  # if MMS
    review_result = models.CharField(max_length=64, blank=True, choices=REVIEW_CHOICES)
    user_register_days = models.IntegerField(default=0)
    contact_count = models.IntegerField(default=0)
    sms_count = models.IntegerField(default=0)
    last_n_sms = models.CharField(max_length=10000, blank=True)
    last_n_sms_zh = models.CharField(max_length=10000, blank=True)
    user_vip_expired_at = models.DateTimeField(auto_now_add=True)
    judge_type = models.IntegerField(default=0, choices=JUDGE_CHOICES)  # 0-fatpo, 1-wangyi
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted = models.IntegerField(default=0)

    objects = models.Manager()


class ImageCheck(models.Model):
    user_id = models.IntegerField()
    user_number = models.CharField(max_length=20)
    direction = models.CharField(max_length=20)  # incoming/outgoing
    image_url = models.CharField(max_length=256)
    suggestion = models.IntegerField(blank=True)  # 0: pass, 1: suspend, 2: failed
    #  100：色情，200：广告，260：广告法，300：暴恐，400：违禁，500：涉政，600：谩骂，700：灌水，900：其他，1100：涉价值观
    label = models.IntegerField(blank=True)  # wangyi image check label:
    sub_label = models.IntegerField(blank=True)  # wangyi image check sub_label:
    task_id = models.CharField(max_length=100)  # wangyi task id
    details = models.CharField(max_length=1024, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted = models.IntegerField(default=0)

    objects = models.Manager()


class SmsCheck(models.Model):
    user_id = models.IntegerField('user_id')
    uuid = models.CharField('uuid', max_length=64, default='', blank=True)
    ip = models.CharField(max_length=20, blank=True)
    direction = models.CharField('direction', max_length=12, blank=True)
    from_number = models.CharField('from_number', max_length=12, blank=True)
    to_number = models.CharField('to_number', max_length=12, blank=True)
    appid = models.IntegerField('appid', default=0, blank=True)
    content = models.CharField('content', max_length=2048, default='')
    filtered_content = models.CharField('filtered_content', max_length=2048, default='')
    task_id = models.CharField('task_id', blank=True, max_length=100)  # wangyi task id
    suggestion = models.IntegerField('suggestion', blank=True)  # 0: pass, 1: suspend, 2: failed
    #  100：色情，200：广告，260：广告法，300：暴恐，400：违禁，500：涉政，600：谩骂，700：灌水，900：其他，1100：涉价值观
    label = models.IntegerField('label', blank=True)  # wangyi check label:
    sub_label = models.IntegerField('sub_label', blank=True, default=0)  # wangyi check sub_label:
    details = models.TextField('details', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted = models.IntegerField('deleted', default=0)
    reviewed = models.IntegerField('reviewed', default=0)

    objects = models.Manager()


class MessageDeliveryHour(models.Model):
    mydate = models.DateField(unique=True)
    for hour in range(24):
        locals()[f'hour{hour}_total'] = models.IntegerField(default=0)
        locals()[f'hour{hour}_failed'] = models.IntegerField(default=0)
        locals()[f'hour{hour}_fake'] = models.IntegerField(default=0)

    def __str__(self):
        return f"SMS Delivery Data for {self.mydate}"


class ForbiddenContact(models.Model):
    number = models.CharField(max_length=20)
    direction = models.CharField(max_length=20, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted = models.IntegerField('deleted', blank=True, default=0)

    objects = models.Manager()


class BadImage(models.Model):
    user_id = models.IntegerField()
    image_url = models.CharField(max_length=256)
    image_md5 = models.CharField(max_length=64)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class MaybeBadSms(models.Model):
    user_id = models.IntegerField()
    content = models.CharField(max_length=2048)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()
