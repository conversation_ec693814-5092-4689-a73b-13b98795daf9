from Call.models import CallR<PERSON>ord
from SecPhone import settings


class CallTool:

    @staticmethod
    def get_conversation_cnt_both_direction(user_id: int, number: str) -> int:
        from_cnt = CallRecord.objects.filter(user_id=user_id, from_number=number).count()
        to_cnt = CallRecord.objects.filter(user_id=user_id, to_number=number).count()
        return from_cnt + to_cnt

    @staticmethod
    def get_total_incoming_cnt(user_id: int) -> int:
        count = CallRecord.objects.filter(user_id=user_id, direction=settings.CALL_DIRECTION_INCOMING).count()
        return count

    @staticmethod
    def get_total_outgoing_cnt(user_id: int) -> int:
        count = CallRecord.objects.filter(user_id=user_id, direction=settings.CALL_DIRECTION_OUTGOING).count()
        return count

    @staticmethod
    def get_all_from_numbers_cnt_when_incoming(user_id: int) -> int:
        count = CallRecord.objects.filter(user_id=user_id,
                                          direction=settings.CALL_DIRECTION_INCOMING
                                          ).values('from_number').distinct().count()
        return count

    @staticmethod
    def get_all_to_numbers_cnt_when_outgoing(user_id: int) -> int:
        count = CallRecord.objects.filter(user_id=user_id,
                                          direction=settings.CALL_DIRECTION_OUTGOING
                                          ).values('to_number').distinct().count()
        return count
