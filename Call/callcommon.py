import math

from django.conf import settings

from Call.models import Call<PERSON>ecord
from Point.models import Point<PERSON><PERSON>ord
from Point.pointcommon import <PERSON><PERSON><PERSON><PERSON>
from SecPhone.settings import logger


class CallCommon:

    @staticmethod
    def AddCallRecord(sid, user_id, direction, from_number, to_number):

        call_record = CallRecord(
            sid=sid, user_id=user_id, direction=direction.upper(), from_number=from_number, to_number=to_number
        )
        call_record.save()

    @staticmethod
    def update_call_record_telnyx(sid: str, duration: int):
        record = CallRecord.objects.filter(sid=sid).first()
        if not record:
            logger.warning(f"[CallCommon.update_call_record] sid: {sid}, duration: {duration}, record not exists!")
            return

        point = math.ceil(int(duration) / 60) * settings.POINT_PER_UNIT['CALL']
        point = max(2, point)
        record.point = point
        record.duration = duration
        record.save()

        if PointRecord.objects.filter(user_id=record.user_id, record_id=record.id, event='CALL').count() == 0:
            logger.info(f"[CallCommon.update_call_record] user:{record.user_id}, sid: {sid}, cost:{point}, done.")
            PointCommon.Add(record.user_id, 0 - record.point, 'CALL', record.id)
        else:
            logger.info(f"[CallCommon.update_call_record] user:{record.user_id}, sid: {sid}, already handle.")
