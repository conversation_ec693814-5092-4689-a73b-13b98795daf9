import pytz
from django.contrib import admin
from django.http import HttpResponseRedirect
from django.urls import path
from django.urls import reverse
from django.utils.html import format_html
from django.utils.timezone import now, timedelta, localtime

from Call.models import SMSRecordWaitForReview, SmsCheck, CallRecord, SMSRecord, MessageDeliveryHour, ForbiddenContact, \
    BadImage, MaybeBadSms
from SecPhone import settings
from SecPhone.settings import logger
from Sms.info.sms_info_bean import SMSInfo
from Sms.msg_pipeline import MsgPipeline
from Sms.tools.tool_sms_add import SmsAddTool


@admin.register(CallRecord)
class CallRecordAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'user_id', 'sid', 'direction', 'from_number', 'to_number', 'status', 'duration',
        'cb_duration', 'cb_call_duration', 'price', 'point', 'created_at', 'updated_at',)
    search_fields = []  # 不使用默认搜索框
    list_filter = ('created_at',)
    list_per_page = 50  # 设置每页显示 50 条记录

    def changelist_view(self, request, extra_context=None):
        # 处理额外的上下文
        return super().changelist_view(request, extra_context=extra_context)

    class Media:
        js = ('admin/js/custom_sms_record_admin.js',)  # 引入自定义的 JavaScript 代码


@admin.register(SMSRecord)
class SMSRecordAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'user_id', 'combined_ds_info', 'direction', 'from_number', 'to_number', 'combined_status_info',
        'combined_content_info',)
    search_fields = []  # 不使用默认搜索框
    list_filter = ()
    list_per_page = 100  # 设置每页显示记录

    class Media:
        js = ('admin/js/custom_sms_record_admin.js',)  # 引入自定义的 JavaScript 代码
        css = {
            'all': ('admin/css/custom_sms_record_admin.css',)  # 引入自定义的 CSS 文件
        }

    def changelist_view(self, request, extra_context=None):
        # 判断请求中是否所有相关的过滤条件都为空
        filter_params = ['user_id', 'sid', 'from_number', 'to_number', 'direction', 'wangyi_task_id', ]
        if not any(request.GET.get(param) for param in filter_params):  # 如果所有过滤条件都为空
            # 如果没有时间过滤条件，则默认过滤最近24小时
            if not request.GET.get('created_at__gte'):
                ds_ago = (now() - timedelta(hours=72)).strftime('%Y-%m-%d %H:%M:%S')
                q = request.GET.copy()
                q['created_at__gte'] = ds_ago  # 添加时间过滤条件
                request.GET = q
                request.META['QUERY_STRING'] = request.GET.urlencode()  # 更新 URL 查询字符串

        # 添加默认的 is_it=0 条件
        if not request.GET.get('is_it'):
            q = request.GET.copy()
            q['is_it'] = '0'  # 设置默认的 is_it 值为 0

            # 更新请求的 GET 参数和查询字符串
            request.GET = q
            request.META['QUERY_STRING'] = request.GET.urlencode()

        logger.info(f"[SMSRecordAdmin] query:{request.GET}, meta: {request.META['QUERY_STRING']}")

        return super().changelist_view(request, extra_context=extra_context)

    def combined_ds_info(self, obj):

        price_parts_str = f"{obj.price}_{obj.parts}"

        # 获取当前时间并转换到北京时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        created_at_bj = localtime(obj.created_at, timezone=beijing_tz).strftime('%Y-%m-%d %H:%M:%S')
        updated_at_bj = localtime(obj.updated_at, timezone=beijing_tz).strftime('%Y-%m-%d %H:%M:%S')

        return format_html(
            "sid: {}<br><br>created(BJ):{}<br><br>updated(BJ): {}<br><br>price:{}<br><br>wangyi_taskid: {}",
            obj.sid, created_at_bj, updated_at_bj, price_parts_str, obj.wangyi_task_id)

    combined_ds_info.short_description = 'Ds Info'

    def combined_content_info(self, obj):
        if obj.images:
            if len(obj.first_check_reason) > 0:
                return format_html(
                    """
                    <img src="{}" class="zoomable" width="100" height="100" style="border:1px solid #ccc;">
                    <br><br>why_bad:{}
                    """,
                    obj.images, obj.first_check_reason)
            else:
                # 缩略图支持点击放大、恢复
                return format_html(
                    """
                    <img src="{}" class="zoomable" width="100" height="100" style="border:1px solid #ccc;">
                    """,
                    obj.images)

        if len(obj.first_check_reason) > 0:
            return format_html(
                "content:{}<br><br>filtered_content: {}<br><br>why_bad:{}",
                obj.content, obj.filtered_content, obj.first_check_reason)
        else:
            return format_html(
                "content:{}<br><br>filtered_content: {}",
                obj.content, obj.filtered_content)

    combined_content_info.short_description = 'Content Info'

    def combined_status_info(self, obj):
        if obj.err_reason:
            return format_html("status:{}<br><br>err_reason: {}", obj.status, obj.err_reason)
        else:
            return format_html("{}", obj.status)

    combined_status_info.short_description = 'Status'

    def combined_it_info(self, obj):
        return format_html(
            "is_it: {}<br><br>is_inner: {}<br><br>is_fake_send: {}<br><br>is_non_english: {}<br><br>is_first_check_bad: {}<br><br>is_bot_bad: {}<br><br>ai_rsp: {}<br><br>link:{}",
            obj.is_it, obj.is_inner, obj.is_fake_send, obj.is_non_english, obj.is_first_check_bad, obj.is_bot_bad,
            obj.ai_rsp, obj.link)

    combined_it_info.short_description = 'It Info'


@admin.register(SMSRecordWaitForReview)
class SMSRecordWaitForReviewAdmin(admin.ModelAdmin):
    list_display = ('id', 'not_send_reason', 'combined_user_info', 'combined_sms_info', 'deleted_status',
                    'combined_content_info', 'review_result', 'judge_and_actions', 'judge_type',
                    )
    search_fields = ('user_id', 'from_number', 'to_number', 'content')
    list_filter = ('deleted', 'review_result', 'created_at',)
    readonly_fields = (
        'judge_type',
        'user_id', 'content', 'content_zh', 'filtered_content', 'uuid', 'ip', 'user_register_days', 'sms_count',
        'contact_count', 'latest_ts', 'not_send_reason', 'images', 'last_n_sms', 'last_n_sms_zh',
        'direction', 'from_number', 'to_number', 'wangyi_task_id', 'created_at', 'updated_at')

    actions = ['approve_selected', 'approve_original_text_selected', 'reject_selected']

    def get_queryset(self, request):
        return SMSRecordWaitForReview.objects.all()

    def deleted_status(self, obj):
        if obj.deleted:
            return format_html('<span style="color: blue;">{}</span>', 'Done')
        else:
            return format_html('<span style="color: red;">{}</span>', 'Todo')

    deleted_status.short_description = 'Status'

    def combined_user_info(self, obj):
        return format_html(
            "user_id: {}<br><br>device_id: {}<br><br>username: {}<br><br>user_register_days: {}<br><br>sms_count: {}<br><br>contact_count: {}<br><br>user_vip_expired_at: {}",
            obj.user_id, obj.uuid, obj.ip, obj.user_register_days, obj.sms_count, obj.contact_count,
            obj.user_vip_expired_at)

    combined_user_info.short_description = 'User Info'

    def combined_sms_info(self, obj):
        return format_html(
            "created_at:{}<br><br>latest_ts: {}<br><br>direction: {}<br><br>from_number: {}<br><br>to_number: {}<br><br>wangyi_task_id: {}",
            obj.created_at, obj.latest_ts, obj.direction, obj.from_number, obj.to_number, obj.wangyi_task_id)

    combined_sms_info.short_description = 'Sms Info'

    def combined_content_info(self, obj):
        return format_html("content: {}<br><br>content_zh: {}<br><br>filtered_content: {}",
                           obj.content, obj.content_zh, obj.filtered_content)

    combined_content_info.short_description = 'Content Info'

    def approve_selected(self, request, queryset):
        for record in queryset:
            self.approve_filtered_sms(request, record.id)

    approve_selected.short_description = 'Approve Selected SMS (Filtered)'

    def approve_original_text_selected(self, request, queryset):
        for record in queryset:
            self.approve_original_text_sms(request, record.id)

    approve_original_text_selected.short_description = 'Approve Selected SMS (Original Text)'

    def reject_selected(self, request, queryset):
        for record in queryset:
            self.reject_sms(request, record.id)

    reject_selected.short_description = 'Reject Selected SMS'

    def judge_and_actions(self, obj):
        if obj.review_result != 'approve':
            approve_filtered_button = format_html('<a class="button" href="{}">Approve (Filtered)</a>',
                                                  reverse('admin:approve_filtered_sms', args=[obj.id]))
        else:
            approve_filtered_button = '-'

        if obj.review_result != 'approveOriginalText':
            approve_original_text_button = format_html('<a class="button" href="{}">Approve (Original)</a>',
                                                       reverse('admin:approve_original_text_sms', args=[obj.id]))
        else:
            approve_original_text_button = '-'

        if obj.review_result != 'reject':
            reject_button = format_html('<a class="button" href="{}">Reject</a>',
                                        reverse('admin:reject_sms', args=[obj.id]))
        else:
            reject_button = '-'

        return format_html("{}<br><br> {}<br><br> {}", approve_filtered_button, approve_original_text_button,
                           reject_button)

    judge_and_actions.short_description = 'Judge and Actions'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('approve_filtered_sms/<int:sms_id>/', self.admin_site.admin_view(self.approve_filtered_sms),
                 name='approve_filtered_sms'),
            path('approve_original_text_sms/<int:sms_id>/', self.admin_site.admin_view(self.approve_original_text_sms),
                 name='approve_original_text_sms'),
            path('reject_sms/<int:sms_id>/', self.admin_site.admin_view(self.reject_sms),
                 name='reject_sms'),
        ]
        return custom_urls + urls

    def approve_filtered_sms(self, request, sms_id):
        record = SMSRecordWaitForReview.objects.get(id=sms_id)
        sms_info = SMSInfo(
            user_id=record.user_id, from_number=record.from_number, to_number=record.to_number,
            direction=settings.SMS_DIRECTION_SEND, origin_content=record.content, ip='', uuid='',
            latest_ts=record.latest_ts,
            is_fake_mode=False
        )
        sms_info.filtered_content = record.filtered_content
        MsgPipeline.do_send(sms_info=sms_info)
        record.review_result = "approve"
        record.deleted = 1
        record.save()

        self.message_user(request, "SMS approved as filtered.")
        return HttpResponseRedirect(request.META.get('HTTP_REFERER', '/admin'))

    def approve_original_text_sms(self, request, sms_id):
        record = SMSRecordWaitForReview.objects.get(id=sms_id)
        sms_info = SMSInfo(
            user_id=record.user_id, from_number=record.from_number, to_number=record.to_number,
            direction=settings.SMS_DIRECTION_SEND, origin_content=record.content, ip='', uuid='',
            latest_ts=record.latest_ts,
            is_fake_mode=False
        )
        sms_info.filtered_content = record.content
        MsgPipeline.do_send(sms_info=sms_info)
        record.review_result = "approveOriginalText"
        record.deleted = 1
        record.save()

        self.message_user(request, "SMS approved as original text.")
        return HttpResponseRedirect(request.META.get('HTTP_REFERER', '/admin'))

    def reject_sms(self, request, sms_id):
        record = SMSRecordWaitForReview.objects.get(id=sms_id)
        record.review_result = "reject"
        record.deleted = 1
        record.save()

        sms_info = SMSInfo(
            user_id=record.user_id, from_number=record.from_number, to_number=record.to_number,
            direction=settings.SMS_DIRECTION_SEND, origin_content=record.content, ip='', uuid='',
            latest_ts=record.latest_ts,
            is_fake_mode=False
        )
        sms_info.is_first_check_bad = True
        sms_info.ai_rsp = SMSInfo.AI_UNKNOWN
        sms_info.is_fake_send = True
        sms_info.err_rsp_list = ["fatpo-reject"]
        SmsAddTool.add_fake_sms_record(sms_info)

        self.message_user(request, "SMS rejected.")
        return HttpResponseRedirect(request.META.get('HTTP_REFERER', '/admin'))


@admin.register(SmsCheck)
class SmsCheckAdmin(admin.ModelAdmin):
    list_display = ('id', 'combined_user_info', 'combined_sms_info', 'combined_content_info', 'combined_wangyi_info',
                    'combined_record_info', 'judge_and_actions'
                    )
    search_fields = ('user_id', 'from_number', 'to_number', 'content', 'filtered_content')
    list_filter = ('deleted', 'reviewed', 'created_at')
    readonly_fields = (
        'user_id', 'uuid', 'ip', 'direction', 'from_number', 'to_number', 'appid', 'task_id', 'created_at',
        'updated_at')

    def get_queryset(self, request):
        return SmsCheck.objects.filter(reviewed=0).all()

    def combined_user_info(self, obj):
        return format_html(
            "user_id: {}<br><br>device_id: {}",
            obj.user_id, obj.uuid, obj.ip)

    combined_user_info.short_description = 'User Info'

    def combined_sms_info(self, obj):
        return format_html(
            "direction: {}<br><br>from_number: {}<br><br>to_number: {}",
            obj.direction, obj.from_number, obj.to_number)

    combined_sms_info.short_description = 'Sms Info'

    def combined_content_info(self, obj):
        return format_html(
            "content: {}<br><br>filtered_content: {}",
            obj.content, obj.filtered_content)

    combined_content_info.short_description = 'Content Info'

    def combined_wangyi_info(self, obj):
        return format_html(
            "task_id: {}<br><br>suggestion: {}<br><br>label: {}<br><br>sub_label: {}<br><br>details: {}",
            obj.task_id, obj.suggestion, obj.label, obj.sub_label, obj.details)

    combined_wangyi_info.short_description = 'Wangyi Info'

    def combined_record_info(self, obj):
        return format_html(
            "created_at: {}<br><br>updated_at: {}<br><br>deleted: {}<br><br>reviewed: {}",
            obj.created_at, obj.updated_at, obj.deleted, obj.reviewed)

    combined_record_info.short_description = 'Record Info'

    def bad_sms_check(self, request, queryset):
        for record in queryset:
            self.bad_sms_check(request, record.id)

    bad_sms_check.short_description = 'No. It\'s bad content'

    def good_sms_check(self, request, queryset):
        for record in queryset:
            self.good_sms_check(request, record.id)

    good_sms_check.short_description = 'Yes. It\'s good content'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('bad_sms_check/<int:sms_id>/', self.admin_site.admin_view(self.bad_sms_check),
                 name='bad_sms_check'),
            path('done_sms_check/<int:sms_id>/', self.admin_site.admin_view(self.good_sms_check),
                 name='good_sms_check'),
        ]
        return custom_urls + urls

    def bad_sms_check(self, request, sms_id):
        sms_check = SmsCheck.objects.get(id=sms_id)
        sms_check.reviewed = 1  # 坏短信只要确定就行了
        sms_check.save()
        self.message_user(request, "SMS Reviewed")
        return HttpResponseRedirect(request.META.get('HTTP_REFERER', '/admin'))

    def good_sms_check(self, request, sms_id):
        sms_check = SmsCheck.objects.get(id=sms_id)
        sms_check.deleted = 1  # 因为是好短信，所以要把这个删掉，防止征信异常
        sms_check.reviewed = 1
        sms_check.save()
        self.message_user(request, "SMS Deleted")
        return HttpResponseRedirect(request.META.get('HTTP_REFERER', '/admin'))

    def judge_and_actions(self, obj):
        bad_content_button = format_html('<a class="button" href="{}">Bad Content</a>',
                                         reverse('admin:bad_sms_check', args=[obj.id]))

        good_content_button = format_html('<a class="button" href="{}">Good Content</a>',
                                          reverse('admin:good_sms_check', args=[obj.id]))

        return format_html("{}<br><br> {}<br><br>", bad_content_button, good_content_button)

    judge_and_actions.short_description = 'Judge and Actions'


@admin.register(MessageDeliveryHour)
class MessageDeliveryHourAdmin(admin.ModelAdmin):
    # 设置基础字段和分页
    list_per_page = 30

    # 自定义动态列显示的字段标题
    def get_list_display(self, request):
        base_display = ['mydate']
        for hour in range(24):
            field_name = f"hour{hour}_status"
            base_display.append(field_name)
        return base_display

    # 动态生成 hourX_status 的方法
    def get_field(self, hour):
        def hour_status(instance):
            total_count = getattr(instance, f"hour{hour}_total", 0)
            failed_count = getattr(instance, f"hour{hour}_failed", 0)
            fake_count = getattr(instance, f"hour{hour}_fake", 0)
            return f"{total_count}/{fake_count}/{failed_count}"

        hour_status.short_description = f"Hour {hour}"  # 设置字段标题
        return hour_status

    # 动态生成字段
    def __getattr__(self, name):
        if name.startswith("hour") and name.endswith("_status"):
            try:
                hour = int(name[4:-7])  # 提取 hourX 中的 X
                return self.get_field(hour)
            except ValueError:
                pass
        raise AttributeError(f"{self.__class__.__name__} object has no attribute {name}")


@admin.register(ForbiddenContact)
class AdminForbiddenContact(admin.ModelAdmin):
    list_display = ('id', 'number', 'direction', 'created_at', 'updated_at',)
    search_fields = []  # 不使用默认搜索框
    list_filter = ('number',)
    list_per_page = 50  # 设置每页显示 50 条记录

    def changelist_view(self, request, extra_context=None):
        # 处理额外的上下文
        return super().changelist_view(request, extra_context=extra_context)


@admin.register(BadImage)
class BadImageAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'image_md5', 'image_url', 'created_at', 'updated_at',)
    search_fields = ['image_md5', 'user_id']
    list_filter = ()
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(MaybeBadSms)
class MaybeBadSmsAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'content', 'created_at', 'updated_at',)
    search_fields = ['user_id']
    list_filter = ()
    list_per_page = 50  # 设置每页显示 50 条记录
