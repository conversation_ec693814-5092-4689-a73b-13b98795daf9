from django.conf.urls import url

from Call import Call_telnyx

urlpatterns = [
    # v2 app - telnyx - call
    url(r'^zhphone/phone/call/access_token/$', Call_telnyx.GetTelnyxAccessToken.as_view()),
    url(r'^zhphone/phone/call/texml/webhook/$', Call_telnyx.TeXMLWebhook.as_view()),
    url(r'^zhphone/phone/call/texml/webhook/failed/$', Call_telnyx.TeXMLWebhookFailed.as_view()),
    url(r'^zhphone/phone/call/status/callback/$', Call_telnyx.CallStatusWebhook.as_view()),
    url(r'^zhphone/phone/call/sipconnection/webhook/v2/$', Call_telnyx.SipConnectionWebhookV2.as_view()),
    url(r'^zhphone/phone/call/sipconnection/webhook/failed/v2/$', Call_telnyx.SipConnectionWebhookFailedV2.as_view()),
]
