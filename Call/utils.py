"""
@author: jon
"""
import base64
import hashlib
import os
import random
import string
import time
from datetime import datetime

from django.conf import settings

from SecPhone.settings import logger


class CallUtils:

    @staticmethod
    def gen_random_filename():

        now = int(time.time())
        random_str = ''.join(random.sample(string.ascii_letters + string.digits, 8))
        return str(now) + '_' + random_str

    @staticmethod
    def SaveImage(file_data, file_format):
        try:
            today = datetime.today().strftime("%Y-%m-%d")
            file_dir = settings.BASE_DIR + "/%s/uploads/%s" % ('static', today)
            if not os.path.exists(file_dir):
                os.makedirs(file_dir)

            filename = "%s.%s" % (CallUtils.gen_random_filename(), file_format)
            filename_abs = "%s/%s" % (file_dir, filename)
            logger.info(f"save image: {filename_abs}")

            f = open(filename_abs, 'wb')
            f.write(base64.b64decode(file_data))
            f.close()

            filename_base = "/uploads/%s/%s" % (today, filename)
            url = settings.STATIC_URL_BASE2 + filename_base

            # 4. 新增：计算图片文件的MD5值
            md5_hash = hashlib.md5()  # 初始化MD5哈希对象
            with open(filename_abs, 'rb') as f:  # 以二进制读模式打开文件（MD5计算需二进制数据）
                # 分块读取大文件（避免一次性加载占用过多内存）
                chunk_size = 4096  # 每次读取4KB（可根据图片大小调整，如8192）
                while chunk := f.read(chunk_size):  # walrus运算符（Python 3.8+）：读取块并赋值给chunk
                    md5_hash.update(chunk)  # 累加块数据到MD5哈希
            image_md5 = md5_hash.hexdigest()  # 获取16进制格式的MD5字符串（32位）

            return url, image_md5
        except Exception as e:
            logger.error('[save image] error: ', exc_info=True)
            return None, None
