<!DOCTYPE html>
<html lang="utf-8">
<head>
    <title>User Tickets</title>
    <style>
        .bordered-table {
            border: 1px solid #ccc;
            border-collapse: collapse;
        }

        .bordered-table th, .bordered-table td {
            border: 1px solid #ccc;
            padding: 8px;
        }

        .image-container {
            position: relative;
            display: inline-block;
        }

        .thumbnail {
            max-width: 200px;
            max-height: 200px;
            cursor: pointer;
        }

        .enlarged-image {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .enlarged-image img {
            max-width: 80%;
            max-height: 80%;
        }
    </style>
    <script>
        function enlargeImage(image) {
            var enlargedImage = document.createElement('div');
            enlargedImage.className = 'enlarged-image';
            enlargedImage.innerHTML = '<img src="' + image.src + '" alt="Enlarged Image">';
            enlargedImage.onclick = function () {
                document.body.removeChild(enlargedImage);
            };
            document.body.appendChild(enlargedImage);
        }
    </script>
</head>
<body>
<h1>User Tickets - To Do</h1>
<table class="bordered-table">
    <thead>
    <tr>
        <th>Ticket Submit Time</th>
        <th>Ticket Type</th>
        <th>User ID</th>
        <th>Device ID</th>
        <th>Email</th>
        <th>Current Number</th>
        <th>Ticket</th>
        <th>Username</th>
        <th>Image URL</th>
        <th>Status</th>
    </tr>
    </thead>
    <tbody>
    {% for ticket in tickets %}
    <tr>
        <td>{{ ticket.created_at }}</td>
        <td>{{ ticket.ticket_type }}</td>
        <td>{{ ticket.user_id }}</td>
        <td>{{ ticket.device_id }}</td>
        <td>{{ ticket.email }}</td>
        <td>{{ ticket.current_number }}</td>
        <td>{{ ticket.ticket }}</td>
        <td>{{ ticket.username }}</td>
        <td>{{ ticket.status }}</td>

        <td>
            {% for url in ticket.image_urls %}
            <div class="image-container">
                <img src="{{ url }}" alt="Image" class="thumbnail" onclick="enlargeImage(this)">
            </div>
            {% endfor %}
        </td>
    </tr>
    {% endfor %}
    </tbody>
</table>
</body>
</html>
