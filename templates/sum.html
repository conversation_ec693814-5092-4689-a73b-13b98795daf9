<html>
<script src="/js/jquery-1.8.3.min.js"></script>
<script src="/js/highcharts.js"></script>
<script src="/js/exporting.js"></script>

<script>
    // 函数用于切换图例的显示状态
    function toggleLegend(containerId) {
        var chart = $('#' + containerId).highcharts(); // 获取对应的图表对象
        var series = chart.series[0];
        if (series.visible) {
            $(chart.series).each(function(){
                this.setVisible(false, false);
            });
            chart.redraw();
        } else {
            $(chart.series).each(function(){
                this.setVisible(true, false);
            });
            chart.redraw();
        }
    }
</script>

<script>

$(function () {
    $('#container_subscription').highcharts({
        chart: {
            type: 'line'
        },
        title: {
            text: '用户每日订阅情况'
        },
        xAxis: {
            categories: {{days | safe}}
        },
        yAxis: {
            title: {
                text: 'user count'
            }
        },
        plotOptions: {
            line: {
                dataLabels: {
                    enabled: true          // 开启数据标签
                },
                enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
            }
        },
        series: [
            {% for k, v in  order_data.items %}
                {
                    name: "{{ k }}",
                    data: {{ v }}
                },
            {% endfor %}

            {
            name: "user register",
            data: {{register}}
            }
        ]
    });
});
</script>
<div id="container_subscription" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_subscription')">Toggle Legend</button>
</div>


<script>
$(function () {
    $('#container_subscription_asa').highcharts({
        chart: {
            type: 'line'
        },
        title: {
            text: 'asa用户每日订阅情况'
        },
        xAxis: {
            categories: {{days | safe}}
        },
        yAxis: {
            title: {
                text: 'user count'
            }
        },
        plotOptions: {
            line: {
                dataLabels: {
                    enabled: true          // 开启数据标签
                },
                enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
            }
        },
        series: [
            {% for k, v in  order_data_asa_user.items %}
                {
                    name: "{{ k }}",
                    data: {{ v }}
                },
            {% endfor %}

            {
            name: "asa user register",
            data: {{register_asa_user}}
            }
        ]
    });
});
</script>
<div id="container_subscription_asa" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_subscription_asa')">Toggle Legend</button>
</div>



<script>
    $(function () {
        $('#container_funnel').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '用户漏斗'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: 'user action funnel count'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  funnel_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_funnel" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_funnel')">Toggle Legend</button>
</div>


<script>
$(function () {
    $('#container_number').highcharts({
        chart: {
            type: 'line'
        },
        title: {
            text: '号码池每日情况'
        },
        xAxis: {
            categories: {{days | safe}}
        },
        yAxis: {
            title: {
                text: 'number count'
            }
        },
        plotOptions: {
            line: {
                dataLabels: {
                    enabled: true          // 开启数据标签
                },
                enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
            }
        },
        series: [
            {% for k, v in  snapshot_number_data.items %}
                {
                    name: "{{ k }}",
                    data: {{ v }}
                },
            {% endfor %}
        ]
    });
});
</script>
<div id="container_number" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_number')">Toggle Legend</button>
</div>



<script>
$(function () {
    $('#container_invalid_points').highcharts({
        chart: {
            type: 'line'
        },
        title: {
            text: '异常points统计'
        },
        xAxis: {
            categories: {{days | safe}}
        },
        yAxis: {
            title: {
                text: 'invalid points count'
            }
        },
        plotOptions: {
            line: {
                dataLabels: {
                    enabled: true          // 开启数据标签
                },
                enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
            }
        },
        series: [
            {% for k, v in snapshot_invalid_points_data.items %}
                {
                    name: "{{ k }}",
                    data: {{ v }}
                },
            {% endfor %}
        ]
    });
});
</script>
<div id="container_invalid_points" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_invalid_points')">Toggle Legend</button>
</div>





<script>
$(function () {
    $('#container_income').highcharts({
        chart: {
            type: 'line'
        },
        title: {
            text: '总收入'
        },
        xAxis: {
            categories: {{days | safe}}
        },
        yAxis: {
            title: {
                text: 'number count'
            }
        },
        plotOptions: {
            line: {
                dataLabels: {
                    enabled: true          // 开启数据标签
                },
                enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
            }
        },
        series: [
            {% for k, v in  income_total_data.items %}
                {
                    name: "{{ k }}",
                    data: {{ v }}
                },
            {% endfor %}
        ]
    });
});
</script>
<div id="container_income" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_income')">Toggle Legend</button>
</div>



<script>
$(function () {
    $('#container_income_today').highcharts({
        chart: {
            type: 'line'
        },
        title: {
            text: '今天收入'
        },
        xAxis: {
            categories: {{days | safe}}
        },
        yAxis: {
            title: {
                text: 'income count'
            }
        },
        plotOptions: {
            line: {
                dataLabels: {
                    enabled: true          // 开启数据标签
                },
                enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
            }
        },
        series: [
            {% for k, v in  income_today_data.items %}
                {
                    name: "{{ k }}",
                    data: {{ v }}
                },
            {% endfor %}
        ]
    });
});
</script>
<div id="container_income_today" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_income_today')">Toggle Legend</button>
</div>



<script>
    $(function () {
        $('#container_refund').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '每日退款'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '每日退款统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  refund_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_refund" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_refund')">Toggle Legend</button>
</div>




<script>
    $(function () {
        $('#container_sms_delivery').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '短信送达率'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '短信送达率统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  sms_delivery_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_sms_delivery" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_sms_delivery')">Toggle Legend</button>
</div>




<script>
    $(function () {
        $('#container_sms_cost').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '短信成本'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '短信成本统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  sms_cost_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_sms_cost" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_sms_cost')">Toggle Legend</button>
</div>



<script>
    $(function () {
        $('#container_sms_carrier_normal').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '短信运营商正常状态'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '短信运营商正常状态统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  sms_carrier_normal_status_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_sms_carrier_normal" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_sms_carrier_normal')">Toggle Legend</button>
</div>



<script>
    $(function () {
        $('#container_sms_carrier_not_normal').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '短信运营商不正常状态'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '短信运营商不正常状态统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  sms_carrier_not_normal_status_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_sms_carrier_not_normal" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_sms_carrier_not_normal')">Toggle Legend</button>
</div>


<script>
    $(function () {
        $('#container_sms_exception').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '短信异常状态'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '短信异常状态统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  sms_exception_status_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_sms_exception" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_sms_exception')">Toggle Legend</button>
</div>



<script>
    $(function () {
        $('#container_sms_no_carrier').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '短信不走运营商'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '短信不走运营商统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  sms_no_carrier_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_sms_no_carrier" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_sms_no_carrier')">Toggle Legend</button>
</div>


<script>
    $(function () {
        $('#container_sms_system_notice').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '短信系统通知'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '短信系统通知统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  sms_system_notice_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_sms_system_notice" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_sms_system_notice')">Toggle Legend</button>
</div>



<script>
    $(function () {
        $('#container_sms_call_not_finished').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '短信电话未完成状态'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '短信电话未完成状态统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  sms_call_not_finished_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_sms_call_not_finished" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_sms_call_not_finished')">Toggle Legend</button>
</div>



<script>
    $(function () {
        $('#container_sms_judge').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '短信审核状态'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '短信审核状态统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  sms_judge_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_sms_judge" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_sms_judge')">Toggle Legend</button>
</div>


<script>
    $(function () {
        $('#container_sms_human_judge').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '短信人类审核状态'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '短信人类审核状态统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  sms_human_judge_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_sms_human_judge" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_sms_human_judge')">Toggle Legend</button>
</div>




<script>
    $(function () {
        $('#container_points').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '点数消耗'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '点数消耗统计'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  points_cost_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_points" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_points')">Toggle Legend</button>
</div>


<script>
    $(function () {
        $('#container_points_event').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '点数类型分布'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '点数类型分布'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  points_event_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_points_event" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_points_event')">Toggle Legend</button>
</div>



<script>
    $(function () {
        $('#container_new_user_country_24_hours').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '最近24小时新用户的国家分布'
            },
            xAxis: {
                categories: {{hours | safe}}
            },
            yAxis: {
                title: {
                    text: '最近24小时新用户的国家分布'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  new_user_country_24_hours_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_new_user_country_24_hours" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_new_user_country_24_hours')">Toggle Legend</button>
</div>




<script>
    $(function () {
        $('#container_new_user_country').highcharts({
            chart: {
                type: 'line'
            },
            title: {
                text: '新用户的国家分布'
            },
            xAxis: {
                categories: {{days | safe}}
            },
            yAxis: {
                title: {
                    text: '新用户的国家分布'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true          // 开启数据标签
                    },
                    enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
                }
            },
            series: [
                {% for k, v in  new_user_country_data.items %}
                    {
                        name: "{{ k }}",
                        data: {{ v }}
                    },
                {% endfor %}
            ]
        });
    });
</script>
<div id="container_new_user_country" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_new_user_country')">Toggle Legend</button>
</div>




<script>
$(function () {
    $('#container_app_version').highcharts({
        chart: {
            type: 'line'
        },
        title: {
            text: 'APP Version统计'
        },
        xAxis: {
            categories: {{days | safe}}
        },
        yAxis: {
            title: {
                text: 'app version count'
            }
        },
        plotOptions: {
            line: {
                dataLabels: {
                    enabled: true          // 开启数据标签
                },
                enableMouseTracking: false // 关闭鼠标跟踪，对应的提示框、点击事件会失效
            }
        },
        series: [
            {% for k, v in snapshot_appversion_data.items %}
                {
                    name: "{{ k }}",
                    data: {{ v }}
                },
            {% endfor %}
        ]
    });
});
</script>
<div id="container_app_version" style="min-width:400px;height:400px"></div>
<div style="display: flex; justify-content: center; align-items: center;margin-bottom: 40px;">
    <button onclick="toggleLegend('container_app_version')">Toggle Legend</button>
</div>



</html>
