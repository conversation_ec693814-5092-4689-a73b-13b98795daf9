import firebase_admin
import firebase_admin.exceptions
from django.conf import settings
from django.core.cache import cache
from firebase_admin import _messaging_utils
from firebase_admin._messaging_utils import APNSPayload, Aps
from firebase_admin.messaging import Message

from Common.rediskey import Redis<PERSON>ey
from SecPhone.settings import logger
from Sms.tools.tool_sms import SmsTool
from User.tools.user_tool import UserTool


class PushUtil(object):

    @staticmethod
    def calc_push_badge(user_id: int, appid: int):
        if appid == 0:
            badge = 0
        else:
            latest_ts = cache.get(RedisKey.GenSmsLatestTimestamp(user_id))
            if not latest_ts:
                badge = 1
            else:
                # 否则只拉用户的比latest_ts大的收短信
                no_read_sms_cnt = SmsTool.get_no_read_cnt(user_id, latest_ts)
                badge = no_read_sms_cnt + 1

        return badge

    @staticmethod
    def notify_by_user(user_id: int, appid: int, content: str):
        user = UserTool.get_user_by_id(user_id)
        if not user:
            logger.warning(f"[PushUtil.notify_by_user] appid:{appid}, user:{user_id} not exists")
            return

        if not user.push_id:
            logger.warning(f"[PushUtil.notify_by_user] appid:{appid}, user:{user_id} has no push_id")
            return

        badge = PushUtil.calc_push_badge(user_id, appid)
        PushUtil.notify_by_fcm(user_id=user_id, push_id=user.push_id, appid=appid, content=content, badge=badge)

    @staticmethod
    def notify_by_fcm(user_id: int, push_id: str, appid: int, content: str, badge: int):
        """
        https://developer.apple.com/library/archive/documentation/NetworkingInternet/Conceptual/RemoteNotificationsPG/PayloadKeyReference.html#//apple_ref/doc/uid/TP40008194-CH17-SW1

        :param user_id:
        :param push_id:
        :param appid:
        :param content:
        :param badge:
        :return:
        """
        try:
            if len(push_id) < 30:
                logger.warning(
                    f"[PushUtil.notify_by_fcm] user:{user_id} push_id invalid: {push_id}, content: {content}")
                return

            message = firebase_admin.messaging.Message(
                notification=firebase_admin.messaging.Notification(
                    title=settings.FCM_APP_NAME_MAP[appid],
                    body=content[0:256] if content else "Receive an image",
                    image=None,
                ),
                apns=_messaging_utils.APNSConfig(payload=APNSPayload(aps=Aps(badge=badge, sound="default"))),
                token=push_id,
            )
            fcm_app = firebase_admin.get_app(settings.FCM_APP_NAME_MAP[appid])
            result = firebase_admin.messaging.send(message, app=fcm_app)
            logger.info(f"[PushUtil.notify_by_fcm] user:{user_id}, _notify_by_fcm, badge:{badge}, result: {result}")
        except firebase_admin._messaging_utils.UnregisteredError:
            logger.warning(f"[PushUtil.notify_by_fcm] user:{user_id} Unregistered,  content: {content}, failed")
        except firebase_admin.exceptions.NotFoundError:
            logger.warning(f"[PushUtil.notify_by_fcm] user:{user_id} NotFoundError, content: {content}, failed")
        except Exception:
            logger.error(f"[PushUtil.notify_by_fcm] user:{user_id} _notify_by_fcm, content: {content}, failed",
                         exc_info=True)
