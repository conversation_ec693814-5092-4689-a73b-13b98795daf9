docker pull docker.io/ouyangbro/vsim:1.0

# 一个挂载点/code/logs看日志，请自己调整好位置：~/logs
# 一个挂载点/code/ 用来持续更新git代码，请自己调整好位置：~/VirtualSIM_BackEnd/
docker run -itd  -p 8000:8000  -v ./:/code/ --name vsim  docker.io/ouyangbro/vsim:1.0

# redis
docker run -d -p 6379:6379 --name redis -v ~/dockerdata/redis_home/redis.conf:/etc/redis/redis.conf -v ~/dockerdata/re
dis_home/data/:/data redis redis-server /etc/redis/redis.conf  --appendonly yes
