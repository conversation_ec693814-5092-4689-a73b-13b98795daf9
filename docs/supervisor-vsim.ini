# root@vsim:~# cat /etc/supervisor/vsim.ini
[program:vsim]
user=root
environment= PATH="/usr/bin"
directory=/root/www/VirtualSIM_BackEnd/
command=/usr/local/bin/gunicorn --reload -k gevent -w 1 -b 0.0.0.0:8000 --error-logfile /root/www/VirtualSIM_BackEnd/logs/gunicorn.err --log-file /root/www/VirtualSIM_BackEnd/logs/gunicorn.log  --access-logfile  /root/www/VirtualSIM_BackEnd/logs/gunicorn_access.log  SecPhone.wsgi:application
redirect_stderr=true
stdout_logfile=/root/www/VirtualSIM_BackEnd/logs/vsim-supervisor.log
stderr_logfile=/root/www/VirtualSIM_BackEnd/logs/vsim-supervisor.err

root@vsim:~/www/VirtualSIM_BackEnd/static/callMe#