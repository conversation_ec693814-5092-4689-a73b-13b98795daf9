from twilio.rest import Client

# Find your Account SID and Auth Token at twilio.com/console
# and set the environment variables. See http://twil.io/secure
account_sid = "**********************************"
auth_token = "d703a14afd6342ce8699d3ed8607bfc2"

client = Client(account_sid, auth_token)

"""
注册基础资料（理论基础，但没什么用）：https://www.twilio.com/docs/sms/a2p-10dlc/onboarding-isv#pre-requisite-gather-required-information-for-a2p-10dlc-registration

注册API资料（有用）： https://www.twilio.com/docs/sms/a2p-10dlc/onboarding-isv-api-sole-prop-new

活动用例列表： https://console.twilio.com/us1/develop/sms/regulatory-compliance/campaigns
"""


def step1():
    """
    1.1 获取入门客户资料政策
    :return:
    """
    policies = client.trusthub \
        .v1 \
        .policies('RN806dd6cd175f314e1f96a9727ee271f4') \
        .fetch()

    print(policies.friendly_name)


def step2():
    """
    1.2 创建一个空的入门客户资料包

    BU您将在后面的步骤中使用您创建的这个新客户配置文件包中的 SID（SID 以 开头）。

    :return:
    """
    customer_profiles = client.trusthub \
        .v1 \
        .customer_profiles \
        .create(
        friendly_name='fatpo Starter Customer Profile Bundle',
        email='<EMAIL>',
        policy_sid='RN806dd6cd175f314e1f96a9727ee271f4'
    )

    # BU413adad81636827d95fd56c08dda85d0
    print(customer_profiles.sid)


def step3():
    """
    1.3 创建一个具有类型的最终用户对象

    IT您将在后面的步骤中使用您创建的新最终用户对象的 SID（SID 以 开头）。

    :return:
    """
    end_user = client.trusthub \
        .v1 \
        .end_users \
        .create(
        attributes={
            'email': '<EMAIL>',
            'first_name': 'fatpo',
            'last_name': 'fat',
            'phone_number': '+8615014139212'
        },
        friendly_name='Starter Profile End User',
        type='starter_customer_profile_information'
    )

    # ITacda9ef047a950ecaaad4c7bea58ba19
    print(end_user.sid)


def step4():
    """
    1.4 创建支持文件：customer_profile_address
    在这里，您为客户创建一个 Address 对象，然后将其附加到他们的客户资料中。请注意，如果您要创建独资经营者注册，则只能使用有效的美国或加拿大地址。


    :return:
    """
    address = client.addresses.create(
        # street_secondary='Apt B',
        customer_name='John Doe',
        street='123 Example Street',
        city='Example City',
        region='CA',
        postal_code='12345',
        iso_country='US'
    )
    # ADda2b51bbe5ace2b5b4dde27601cec8e2
    print(address.sid)


if __name__ == '__main__':
    # step1()
    # step2()
    # step3()
    step4()
