# 导入必要的模块和库
import json
import time
import datetime

# 定义一个函数来处理Webhook请求
async def call_webhook(req, res, next):
    try:
        fields = "*"
        if not req.body:  # 检查请求体是否为空
            raise Exception("Something Went Wrong")

        # 解析事件类型
        event_type = req.body.get("data", {}).get("event_type", "")
        if not event_type:
            event_type = req.body.get("event_type", "").split("_")[1]

        received_data = req.body.get("data", req.body)

        where_initiated = f"faxNumber='{received_data['payload']['to']}' LIMIT 1"

        # 在数据库中查找用户
        user = await query_model.find_one(table.INBOUND_SUBSCRIPTION, "*", where_initiated)

        if user:
            block = await query_model.find_one(
                table.NUMBER_BLOCK_USER,
                "*",
                f"userId={user['userId']} AND phoneNumber='{received_data['payload']['from']}'"
            )
            if block:
                await hangup(received_data['payload']['call_control_id'])
                return res.end()

        # 根据事件类型进行不同的处理
        if event_type == 'initiated':
            # 处理呼出
            if received_data['payload']['direction'] == "outgoing":
                con = f"callControlId='{received_data['payload']['call_control_id']}' AND direction='outgoing'"
                check_entry = await query_model.find_one(table.CALL_DETAILS, "*", con)
                if check_entry:
                    return res.end()

                # 检查发送者ID
                where = f"faxNumber='{received_data['payload']['from']}'"
                user = await query_model.find_one(table.INBOUND_SUBSCRIPTION, "*", where)

                if not user:
                    join = "s LEFT JOIN user_info u ON u.id=s.userId LEFT JOIN plan_list p ON p.code=s.productName"
                    user = await query_model.find_one(
                        table.SUBSCRIPTION,
                        "p.planName,u.id As userId,u.deviceId As deviceId",
                        f"u.phoneNumber='{received_data['payload']['from']}'",
                        join
                    )
                    if user and user['planName'] != "FREE":
                        return res.end()

                # 构建请求数据
                data = {
                    "userId": user['userId'],
                    "deviceId": user['deviceId'],
                    "callerId": received_data['payload']['call_leg_id'],
                    "callSessionId": received_data['payload']['call_session_id'],
                    "callControlId": received_data['payload']['call_control_id'],
                    "toNumber": received_data['payload']['to'],
                    "fromNumber": received_data['payload']['from'],
                    "connectionId": received_data['payload']['connection_id'],
                    "callStatus": "initiate",
                    "callbackStatus": '0',
                    "direction": received_data['payload']['direction'],
                    "createdDate": int(time.time() * 1000)
                }

                # 插入数据到数据库
                call_save = await query_model.insert(table.CALL_DETAILS, data)
                if call_save:
                    loggerg(log.CALL_WEBHOOKS).info(
                        f"({SUCCESS}) - Success - Call_initiated/Outgoing- Response data - Webhook data-{json.dumps({'bodyData': req.body}, indent=1)} - Inserted data-{json.dumps(data, indent=1)} - {req.originalUrl} - {req.method} - {req.ip}"
                    )
                    res.end()

            # 处理呼入
            elif received_data['payload']['direction'] == "incoming":
                where = f"callSessionId='{received_data['payload']['call_session_id']}' AND direction='incoming'"
                data_entry_check = await query_model.find_one(table.CALL_DETAILS, "*", where)
                if data_entry_check:
                    return res.end()

                where = f"faxNumber='{received_data['payload']['to']}'"
                user = await query_model.find_one(table.INBOUND_SUBSCRIPTION, "*", where)
                if not user:
                    return res.end()
                if user:
                    block = await query_model.find_one(
                        table.NUMBER_BLOCK_USER,
                        "*",
                        f"userId={user['userId']} AND phoneNumber='{received_data['payload']['from']}'"
                    )
                    if block:
                        hangup(received_data['payload']['call_control_id'])
                        return res.end()

                # 发送通知给设备
                # device_token = await query_model.find_one(table.DEVICES, "pushToken, platformId", f"userId={user['userId']} AND pushToken IS NOT NULL")
                # if device_token['pushToken'] not in ["Undefined", None] and device_token['platformId'] in [2, "2"]:
                #     fcm_data = {
                #         "data": {
                #             "title": received_data['payload']['from'],
                #             "messageType": "Incoming call",
                #             "body": f"Incoming call from {received_data['payload']['from']}",
                #             "callerId": received_data['payload']['call_leg_id'],
                #             "time": int(time.time() * 1000),
                #             "android_channel_id": "call_app_fcm_id",
                #             "channel_id": "call_app_fcm_id",
                #             "number": received_data['payload']['from'],
                #             "displayTime": "Today",
                #             "callType": "Incoming"
                #         },
                #         "android": {
                #             "priority": "high"
                #         },
                #         "priority": 10,
                #         "to": device_token['pushToken']
                #     }
                #     fcm.send(fcm_data, function(err, response){
                #         if (err) {
                #             console.log("Something has gone wrong!");
                #         } else {
                #             console.log("Successfully sent with response: ", response);
                #         }
                #     });

                # 检查是否启用/禁用转发号码
                check_forward_enable = await query_model.find_one(table.USERS, 'callForward,forwardNumber', f"id={user['userId']}")

                # 构建请求数据
                data = {
                    "userId": user['userId'],
                    "deviceId": user['deviceId'],
                    "callerId": received_data['payload']['call_leg_id'],
                    "callSessionId": received_data['payload']['call_session_id'],
                    "callControlId": received_data['payload']['call_control_id'],
                    "toNumber": received_data['payload']['to'],
                    "fromNumber": received_data['payload']['from'],
                    "connectionId": received_data['payload']['connection_id'],
                    "callStatus": "initiate",
                    "callbackStatus": '0',
                    "direction": received_data['payload']['direction'],
                    "createdDate": int(time.time() * 1000)
                }

                # 插入数据到数据库
                call_save = await query_model.insert(table.CALL_DETAILS, data)

                if call_save:
                    loggerg(log.CALL_WEBHOOKS).info(
                        f"({SUCCESS}) - Success - Call_initiated/Incoming - Response data  - Webhook data-{json.dumps({'bodyData': req.body}, indent=1)} - Inserted data-{json.dumps(data, indent=1)} - {req.originalUrl} - {req.method} - {req.ip}"
                    )
                    return res.end()

                res.end()

        elif event_type == 'answered':
            # 获取特定呼叫
            call_detail = await query_model.find_one(table.CALL_DETAILS, fields, f"callControlId='{received_data['payload']['call_control_id']}'")

            loggerg(log.CALL_WEBHOOKS).info(
                f"({SUCCESS}) - Success - Call_Answered/{call_detail['direction']} - Response data -{json.dumps(call_detail, indent=1)} - {req.originalUrl} - {req.method} - {req.ip}"
            )

            if call_detail:
                start_time = datetime.datetime.strptime(received_data.get('occurred_at', received_data['payload']['occurred_at']), "%Y-%m-%dT%H:%M:%SZ")
                call_details = {
                    "initiateTime": int(start_time.timestamp() * 1000),
                    "callbackStatus": '1',
                    "callStatus": "answered"
                }
                # 更新最新的呼叫详情
                update_data = await query_model.update(table.CALL_DETAILS, f"callControlId='{received_data['payload']['call_control_id']}'", call_details)
                loggerg(log.CALL_WEBHOOKS).info(
                    f"({SUCCESS}) - Success - Call Answered - Response data - Webhook data - {json.dumps({'bodyData': req.body}, indent=1)} - Updated data{json.dumps(call_details, indent=1)} - {req.originalUrl} - {req.method} - {req.ip}"
                )

            check_for_user = await query_model.find_one(table.INBOUND_SUBSCRIPTION, 'userId', f"faxNumber = '{received_data['payload']['to']}'")

            if check_for_user:
                check_for_greeting = await query_model.find_one(table.USERS, 'companyGreeting,greetingFile', f"id = {check_for_user['userId']}")
                if check_for_greeting['companyGreeting'] == 1 or check_for_greeting['companyGreeting'] == '1':
                    get_url_of_file = await get_greeting_url(check_for_greeting['greetingFile'])

                    # 播放声音
                    speak = telnyx.Call(call_control_id=received_data['payload']['call_control_id'])
                    speak.playback_start(
                        audio_url=get_url_of_file,
                        target_legs='opposite',
                        stop="all"
                    )

            res.end()

        elif event_type == 'hangup':
            current_call_data = {}
            check_status = 0

            # 如果呼叫失败
            if received_data['payload']['sip_hangup_cause'] == '480':
                delete_incoming = await query_model.delete(
                    table.CALL_DETAILS,
                    f"callSessionId='{received_data['payload']['call_session_id']}' AND direction='incoming'"
                )
                if delete_incoming:
                    check_status = 1

            # 获取呼叫详情
            call_data = await query_model.find_one(table.CALL_DETAILS, fields, f"callControlId='{received_data['payload']['call_control_id']}'")
            if not call_data:
                return res.end()

            loggerg(log.CALL_WEBHOOKS).info(
                f"({SUCCESS}) - Success - callData/hangup/{call_data['direction']} - Response data -{json.dumps(call_data, indent=1)} - {req.originalUrl} - {req.method} - {req.ip}"
            )
            find_usage = await query_model.find_one(table.CALL_USAGE, "*", f"userId={call_data['userId']}")

            if call_data['direction'] == "outgoing":
                # 结束时间
                end_date = datetime.datetime.strptime(received_data['payload']['end_time'], "%Y-%m-%dT%H:%M:%SZ")
                current_call_data['hangUpTime'] = int(end_date.timestamp() * 1000)
                current_call_data['callStatus'] = "failed" if check_status else "hangup"
                current_call_data['callDuration'] = 0 if call_data['initiateTime'] is None else int((current_call_data['hangUpTime'] - call_data['initiateTime']) / 1000)

                # 扣除费用
                deduct_balance, provider_cost = await deduct_charges(current_call_data['callDuration'], call_data['toNumber'])

                # 更新费用
                current_call_data['userCost'] = deduct_balance
                current_call_data['providerCost'] = provider_cost

                cur_date = datetime.datetime.now().strftime('%Y-%m-%d')
                call_min = await query_model.find_one(
                    table.SUBSCRIPTION,
                    "callLimit,id",
                    f"userId={call_data['userId']} AND ((status='1' AND canceled='0') OR (status='0' AND canceled='1' AND DATE(FROM_UNIXTIME(expiryDate/1000)) >= '{cur_date}')) ORDER BY id DESC LIMIT 1"
                )

                # 更新余额和呼叫详情
                query_model.update(table.CALL_DETAILS, f"callControlId='{received_data['payload']['call_control_id']}'", current_call_data)
                query_model.update(table.SUBSCRIPTION, f"id={call_min['id']}", {"callLimit": call_min['callLimit'] - (current_call_data['callDuration'] / 60)})
                query_model.update(table.CALL_USAGE, f"userId={call_data['userId']}", {"callOutgoing": find_usage['callOutgoing'] + (current_call_data['callDuration'])})

                check_user = await query_model.find_one(table.USERS, "*", f"id={call_data['userId']}")
                if check_user['filterSpam'] == 1 or check_user['filterSpam'] == '1':
                    check_number_spam = await query_model.find_one(table.SPAM_CALLS, "COUNT(id) As total", f"number='{call_data['toNumber']}'")
                    if check_number_spam['total'] >= 1:
                        spam_detail = {
                            "number": call_data['toNumber'],
                            "userId": call_data['userId'],
                            "callId": call_data['id']
                        }
                        query_model.insert(table.SPAM_CALLS, spam_detail)

                # 记录日志
                loggerg(log.CALL_WEBHOOKS).info(
                    f"({SUCCESS}) - Success - hangup/{call_data['direction']} - Webhook data -{json.dumps({'bodyData': req.body}, indent=1)} - Updated Data - {json.dumps(current_call_data, indent=1)} - {req.originalUrl} - {req.method} - {req.ip}"
                )
                res.end()

            # 管理呼入方向
            elif call_data['direction'] == "incoming":
                end_date = datetime.datetime.strptime(received_data['payload']['end_time'], "%Y-%m-%dT%H:%M:%SZ")
                current_call_data['hangUpTime'] = int(end_date.timestamp() * 1000)
                current_call_data['callStatus'] = "hangup"
                current_call_data['callDuration'] = 0 if call_data['initiateTime'] is None else int((current_call_data['hangUpTime'] - call_data['initiateTime']) / 1000)

                # 扣除费用
                deduct_balance, providercost = await deduct_incoming_charges(current_call_data['callDuration'])

                # 更新费用
                current_call_data['userCost'] = deduct_balance
                current_call_data['providerCost'] = providercost

                # 更新余额和呼叫详情
                updated = await query_model.update(table.CALL_DETAILS, f"callControlId='{received_data['payload']['call_control_id']}'", current_call_data)
                cur_date = datetime.datetime.now().strftime('%Y-%m-%d')
                call_min = await query_model.find_one(
                    table.SUBSCRIPTION,
                    "callLimit,id",
                    f"userId={call_data['userId']} AND ((status='1' AND canceled='0') OR (status='0' AND canceled='1' AND DATE(FROM_UNIXTIME(expiryDate/1000)) >= '{cur_date}')) ORDER BY id DESC LIMIT 1"
                )

                # 更新余额和呼叫详情
                query_model.update(table.CALL_DETAILS, f"callControlId='{received_data['payload']['call_control_id']}'", current_call_data)
                query_model.update(table.SUBSCRIPTION, f"id={call_min['id']}", {"callLimit": call_min['callLimit'] - (current_call_data['callDuration'] / 60)})
                query_model.update(table.CALL_USAGE, f"userId={call_data['userId']}", {"callIncoming": find_usage['callIncoming'] + (current_call_data['callDuration'])})

                # 检查电子邮件警报
                check_user = await query_model.find_one(table.USERS, "*", f"id={call_data['userId']}")
                if check_user['emailAlert'] == 1 or check_user['emailAlert'] == '1':
                    # 获取呼叫信息
                    get_call_details = await query_model.find_one(table.CALL_DETAILS, "*", f"callControlId='{received_data['payload']['call_control_id']}' AND userId={check_user['id']} AND direction='incoming'")
                    if get_call_details['callbackStatus'] == 0 or get_call_details['callbackStatus'] == '0':
                        # 发送未接来电警报邮件
                        send_email_data = {
                            "from": 'Amplify Team(Call Web) <<EMAIL>>',
                            "to": check_user['email'],
                            "subject": MISSED_CALLS,
                            "templateVariable": MISSED_CALL,
                            "data": {
                                "Missed_call": f"You have missed call from {get_call_details['fromNumber']} this Phone number",
                                "username": check_user['firstName']
                            }
                        }
                        send_email(send_email_data)

                if check_user['filterSpam'] == 1 or check_user['filterSpam'] == 1:
                    check_number_spam = await query_model.find_one(table.SPAM_CALLS, "COUNT(id) As total", f"number='{call_data['fromNumber']}'")
                    if check_number_spam['total'] >= 1:
                        spam_detail = {
                            "number": call_data['fromNumber'],
                            "userId": call_data['userId'],
                            "callId": call_data['id']
                        }
                        query_model.insert(table.SPAM_CALLS, spam_detail)

            res.end()
        else:
            res.end()
    except Exception as error:
        error.statusCode = 200
        next(error)
