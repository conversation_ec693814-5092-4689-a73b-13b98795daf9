server {
    listen      80;
    listen [::]:80;
    listen 443 ssl;


    server_name phone.zehougroup.xyz;

    client_max_body_size 20m;
    ssl_certificate /etc/letsencrypt/live/phone.zehougroup.xyz/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/phone.zehougroup.xyz/privkey.pem; # managed by Certbot

    #ssl_certificate  /root/.acme.sh/phone.zehougroup.xyz_ecc/fullchain.cer;
    #ssl_certificate_key  /root/.acme.sh/phone.zehougroup.xyz_ecc/phone.zehougroup.xyz.key;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;

    location ^~ /static/qa_ds3dn9xey6whx2w1ox/ {
	root /root/www/VirtualSIM_BackEnd/;
    }

    location ^~ /captcha/ {
        alias /tmp/captcha/;
    }

    location ^~ /static/admin/ {
        root /root/www/VirtualSIM_BackEnd/;
    }


    location ~* \.(html|gif|jpg|jpeg|png|css|js|ico)$ {
        root /root/www/VirtualSIM_BackEnd/static/;
    }
    location /static/ {
        root /root/www/VirtualSIM_BackEnd/;
    }

    #location /zhphone/feedback/fatpoBackendTools/ {
    #    rewrite ^/zhphone/feedback/fatpoBackendTools/$ /tools_box/toolsv2.html? permanent;
    #}

    #location /CustomerSupportDALJ1IO3KDS/ {
    #    root /root/www/VirtualSIM_BackEnd/static/;
    #}
    #location /CustomerSupportScripts12E1J3NJNDS/ {
    #    root /root/www/VirtualSIM_BackEnd/static/;
    #}
    location ~* ^/(fatpoadmin|zhphone|user|number|config|order|phone|point|report)/ {
        proxy_set_header        REMOTE_ADDR     $proxy_add_x_forwarded_for;

        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT, X-Mx-ReqToken, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, Authorization';

        if ($request_method = 'OPTIONS') {
            return 204;
        }

        proxy_set_header   Host             $host:8000;
        proxy_set_header   X-Real-IP        $remote_addr;
        proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
        proxy_set_header   Cookies          $http_cookies;
        proxy_pass http://myapp;
    }

}
