<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Zehou Technology</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
        }

        h1 {
            font-size: 2rem;
        }

        h2 {
            font-size: 1.5rem;
        }

        p {
            font-size: 1rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 1rem;
        }

        footer {
            text-align: center;
            padding: 1rem;
            background-color: #f0f0f0;
            margin-top: 2rem;
            font-size: 0.8rem;
        }

        .feedback-form {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background-color: #fff;
            border: 1px solid #ccc;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            width: 300px;
        }

        .feedback-form h2 {
            margin-top: 0;
        }

        .feedback-form label {
            display: block;
            margin-bottom: 5px;
        }

        .feedback-form input,
        .feedback-form textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }

        .feedback-form button {
            background-color: #007BFF;
            color: #fff;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .feedback-form button:hover {
            background-color: #0056b3;
        }

        .code-btn {
            margin-left: 10px;
            padding: 3px 6px; /* 进一步减小内边距 */
            font-size: 0.8rem; /* 进一步减小字体大小 */
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>About Zehou Technology</h1>
        <h2>Discover the Future of Internet Software Development</h2>
        <p>Founded in 2021, Zehou Technology is a vibrant and innovative startup company dedicated to revolutionizing the world of internet software development. With a strong and passionate team of fewer than 50 employees, we have successfully expanded our business to two countries and regions, proudly serving over one million people worldwide.</p>
        <h2>Our Mission</h2>
        <p>At ZehouTech, our mission is to create user - centric software solutions that bring convenience and value to people's lives. Our flagship product, "Second Texting Number," is an internet - telephony app designed to provide our users with an unparalleled communication experience. As we continue to grow, we look forward to developing more cutting - edge products to enrich your digital life.</p>
        <h2>Employee - Owned and Operated</h2>
        <p>ZehouTech is a private company that is 100% employee - owned, implementing an employee stock ownership plan (ESOP) through our labor union. Our ownership structure, which includes fewer than 50 participants, is a testament to our commitment to empowering our team members and maintaining an inclusive and collaborative work environment. We are proud to share that no government departments or institutions hold ZehouTech shares.</p>
        <h2>Get in Touch</h2>
        <p>We are always eager to hear from you and explore new opportunities for collaboration. If you have any questions, suggestions, or simply want to learn more about ZehouTech, please do not hesitate to contact us:</p>
        <p><strong>Company:</strong> Shenzhen ZehouTech Technology Co., Ltd.<br>
        <strong>Email:</strong> <EMAIL></p>
        <p>Join us on our journey to reshape the internet software landscape and make a lasting impact on the lives of millions of people around the globe. Welcome to Zehou Technology, where innovation meets convenience.</p>
    </div>
    <div class="feedback-form">
        <h2>投诉意见反馈</h2>
        <label for="username">用户名:</label>
        <input type="text" id="username" name="username" required>
        <label for="phone">手机号:</label>
        <input type="tel" id="phone" name="phone" required>
        <label for="suggestion">意见或建议:</label>
        <textarea id="suggestion" name="suggestion" rows="4" required></textarea>
        <label for="code">短信验证码:</label>
        <div style="display: flex; align-items: center;">
            <input type="text" id="code" name="code" required>
            <button class="code-btn" type="button">发送</button>
        </div>
        <button type="submit">提交</button>
    </div>
    <footer>
        &copy; 2022 Copyright 2021 - 2022 Shenzhen Zehou Technology Co., Ltd. All Rights Reserved<br>
        <a target="_blank" rel="noopener" href="https://beian.miit.gov.cn/#/Integrated/index">粤ICP备2022007583号 - 1</a>
    </footer>
</body>

</html>
