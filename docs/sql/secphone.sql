CREATE DATABASE IF NOT EXISTS secphone DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_general_ci;

use secphone;

/*
 Navicat Premium Data Transfer

 Source Server         : V-New-Prod
 Source Server Type    : MySQL
 Source Server Version : 50725
 Source Host           : **************:3306
 Source Schema         : secphone

 Target Server Type    : MySQL
 Target Server Version : 50725
 File Encoding         : 65001

 Date: 22/04/2021 17:04:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for Call_callrecord
-- ----------------------------
DROP TABLE IF EXISTS `Call_callrecord`;
CREATE TABLE `Call_callrecord` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sid` varchar(100) NOT NULL,
  `user_id` int(11) NOT NULL,
  `direction` varchar(20) NOT NULL,
  `from_number` varchar(50) NOT NULL,
  `to_number` varchar(50) NOT NULL,
  `status` varchar(50) DEFAULT NULL,
  `duration` int(11) DEFAULT NULL,
  `cb_duration` int(11) DEFAULT NULL,
  `cb_call_duration` int(11) DEFAULT NULL,
  `price` double DEFAULT NULL,
  `point` int(11) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for Call_smsrecord
-- ----------------------------
DROP TABLE IF EXISTS `Call_smsrecord`;
CREATE TABLE `Call_smsrecord` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sid` varchar(100) NOT NULL,
  `user_id` int(11) NOT NULL,
  `latest_ts` bigint(20) DEFAULT '0',
  `direction` varchar(20) NOT NULL,
  `from_number` varchar(50) NOT NULL,
  `to_number` varchar(50) NOT NULL,
  `status` varchar(50) DEFAULT NULL,
  `price` double DEFAULT NULL,
  `point` int(11) DEFAULT NULL,
  `content` varchar(2000) DEFAULT NULL,
  `images` varchar(2000) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userid_ts_idx` (`user_id`,`latest_ts`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;



-- ----------------------------
-- Table structure for Config_config
-- ----------------------------
DROP TABLE IF EXISTS `Config_config`;
CREATE TABLE `Config_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `appid` int(11) NOT NULL,
  `version` int(11) NOT NULL,
  `content` longtext NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for Number_numberinventory
-- ----------------------------
DROP TABLE IF EXISTS `Number_numberinventory`;
CREATE TABLE `Number_numberinventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `number` varchar(50) NOT NULL,
  `friendly_name` varchar(50) NOT NULL,
  `sid` varchar(100) NOT NULL,
  `capabilities` varchar(200) NOT NULL,
  `status` varchar(20) DEFAULT NULL,
  `release_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_sid` (`sid`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for Number_numberused
-- ----------------------------
DROP TABLE IF EXISTS `Number_numberused`;
CREATE TABLE `Number_numberused` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `number_id` int(11) NOT NULL,
  `number` varchar(50) NOT NULL,
  `type` varchar(20) NOT NULL,
  `expired_at` date NOT NULL,
  `status` varchar(20) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `idx_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_number_id` (`number_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for Order_order
-- ----------------------------
DROP TABLE IF EXISTS `Order_order`;
CREATE TABLE `Order_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `number_id` int(11) NOT NULL,
  `renew_status` int(11) DEFAULT NULL,
  `expiration_intent` int(11) DEFAULT NULL,
  `expire_at` datetime(6) DEFAULT NULL,
  `original_transaction_id` varchar(128) DEFAULT '',
  `certificate` longtext NOT NULL,
  `appid` int(11) DEFAULT '0',
  `valid` int(11) DEFAULT '1',
  `order_status` varchar(20) DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `idx_expire_valid_status` (`expire_at`,`valid`,`order_status`),
  KEY `idx_number_id` (`number_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for Point_point
-- ----------------------------
DROP TABLE IF EXISTS `Point_point`;
CREATE TABLE `Point_point` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `point` int(11) NOT NULL,
  `free_point` int(11) DEFAULT '0',
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `idx_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for Point_pointrecord
-- ----------------------------
DROP TABLE IF EXISTS `Point_pointrecord`;
CREATE TABLE `Point_pointrecord` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `point` int(11) NOT NULL,
  `event` varchar(200) NOT NULL,
  `record_id` int(11) DEFAULT '0',
  `created_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for Point_refundrecord
-- ----------------------------
DROP TABLE IF EXISTS `Point_refundrecord`;
CREATE TABLE `Point_refundrecord` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `order_type` varchar(20) DEFAULT NULL,
  `appid` int(11) NOT NULL,
  `next_refund_at` datetime(6) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for User_user
-- ----------------------------
DROP TABLE IF EXISTS `User_user`;
CREATE TABLE `User_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `login_id` varchar(256) DEFAULT '',
  `email` varchar(100) NOT NULL,
  `passwd` varchar(50) NOT NULL,
  `name` varchar(50) NOT NULL,
  `token` varchar(50) NOT NULL,
  `uuid` varchar(128) DEFAULT '',
  `push_id` varchar(256) DEFAULT '',
  `login_type` tinyint(4) DEFAULT '0',
  `is_add_point` tinyint(4) DEFAULT '0',
  `expired_at` datetime(6) DEFAULT NULL,
  `appid` int(11) DEFAULT '0',
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`(20)) USING BTREE,
  KEY `idx_id` (`id`) USING BTREE,
  KEY `idx_login_id` (`login_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

INSERT INTO `secphone`.`User_user` (`id`, `email`, `passwd`, `name`, `token`, `expired_at`, `created_at`, `updated_at`, `appid`)
VALUES ('1', '<EMAIL>', 'ea6207ce9ca4dc4b3948496955ad672e', '', '7cf5c574398022ae40f502d55d58dcf0', NULL, '2021-08-10 16:03:48.401413', '2021-08-10 16:03:48.401480', '0');


CREATE TABLE `User_blackuser` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `uuid` varchar(64) DEFAULT '',
  `appid` tinyint(4) DEFAULT '0',
  `ban_type` tinyint(4) DEFAULT '0',
  `reason` varchar(1024) DEFAULT NULL,
  `ban_times` int(11) DEFAULT '0',
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(4) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_user_id` (`user_id`),
  KEY `idx_uuid` (`uuid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC


-- ----------------------------
-- Table structure for auth_group
-- ----------------------------
DROP TABLE IF EXISTS `auth_group`;
CREATE TABLE `auth_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(80) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for auth_group_permissions
-- ----------------------------
DROP TABLE IF EXISTS `auth_group_permissions`;
CREATE TABLE `auth_group_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_group_permissions_group_id_permission_id_0cd325b0_uniq` (`group_id`,`permission_id`),
  KEY `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` (`permission_id`),
  CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for auth_permission
-- ----------------------------
DROP TABLE IF EXISTS `auth_permission`;
CREATE TABLE `auth_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `content_type_id` int(11) NOT NULL,
  `codename` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_permission_content_type_id_codename_01ab375a_uniq` (`content_type_id`,`codename`),
  CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for auth_user
-- ----------------------------
DROP TABLE IF EXISTS `auth_user`;
CREATE TABLE `auth_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `password` varchar(128) NOT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(150) NOT NULL,
  `first_name` varchar(30) NOT NULL,
  `last_name` varchar(150) NOT NULL,
  `email` varchar(254) NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for auth_user_groups
-- ----------------------------
DROP TABLE IF EXISTS `auth_user_groups`;
CREATE TABLE `auth_user_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_user_groups_user_id_group_id_94350c0c_uniq` (`user_id`,`group_id`),
  KEY `auth_user_groups_group_id_97559544_fk_auth_group_id` (`group_id`),
  CONSTRAINT `auth_user_groups_group_id_97559544_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`),
  CONSTRAINT `auth_user_groups_user_id_6a12ed8b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for auth_user_user_permissions
-- ----------------------------
DROP TABLE IF EXISTS `auth_user_user_permissions`;
CREATE TABLE `auth_user_user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_user_user_permissions_user_id_permission_id_14a6b632_uniq` (`user_id`,`permission_id`),
  KEY `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` (`permission_id`),
  CONSTRAINT `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for django_admin_log
-- ----------------------------
DROP TABLE IF EXISTS `django_admin_log`;
CREATE TABLE `django_admin_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action_time` datetime(6) NOT NULL,
  `object_id` longtext,
  `object_repr` varchar(200) NOT NULL,
  `action_flag` smallint(5) unsigned NOT NULL,
  `change_message` longtext NOT NULL,
  `content_type_id` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `django_admin_log_content_type_id_c4bce8eb_fk_django_co` (`content_type_id`),
  KEY `django_admin_log_user_id_c564eba6_fk_auth_user_id` (`user_id`),
  CONSTRAINT `django_admin_log_content_type_id_c4bce8eb_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`),
  CONSTRAINT `django_admin_log_user_id_c564eba6_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for django_content_type
-- ----------------------------
DROP TABLE IF EXISTS `django_content_type`;
CREATE TABLE `django_content_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_label` varchar(100) NOT NULL,
  `model` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `django_content_type_app_label_model_76bd3d3b_uniq` (`app_label`,`model`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for django_migrations
-- ----------------------------
DROP TABLE IF EXISTS `django_migrations`;
CREATE TABLE `django_migrations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `applied` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for django_session
-- ----------------------------
DROP TABLE IF EXISTS `django_session`;
CREATE TABLE `django_session` (
  `session_key` varchar(40) NOT NULL,
  `session_data` longtext NOT NULL,
  `expire_date` datetime(6) NOT NULL,
  PRIMARY KEY (`session_key`),
  KEY `django_session_expire_date_a5c62663` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

SET FOREIGN_KEY_CHECKS = 1;


-- ----------------------------
-- Table structure for order_orderconsume
-- ----------------------------
DROP TABLE IF EXISTS `order_orderconsume`;
CREATE TABLE `order_orderconsume` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `number_id` int(11) NOT NULL,
  `transaction_id` varchar(128) NOT NULL,
  `appid` int(11) DEFAULT '0',
  `certificate` longtext NOT NULL,
  `valid` int(11) DEFAULT '1',
  `order_status` varchar(20) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_transaction_id` (`transaction_id`) USING BTREE,
  KEY `idx_number_id` (`number_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


-- ----------------------------
-- Table structure for number_numberlocked
-- ----------------------------
DROP TABLE IF EXISTS `number_numberlocked`;
CREATE TABLE `number_numberlocked` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `number` varchar(50) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `idx_userid` (`user_id`) USING BTREE,
  KEY `idx_number` (`number`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;



-- ----------------------------
-- Table structure for Report_funnel
-- ----------------------------
DROP TABLE IF EXISTS `Report_funnel`;
CREATE TABLE `Report_funnel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `day` varchar(50) NOT NULL,
  `login_cnt` int(11) DEFAULT 0 NOT NULL,
  `search_cnt` int(11) DEFAULT 0 NOT NULL,
  `lock_cnt` int(11) DEFAULT 0 NOT NULL,
  `vip_cnt` int(11) DEFAULT 0 NOT NULL,
  `order_cnt` int(11) DEFAULT 0 NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_day` (`day`) USING BTREE,
  UNIQUE KEY `uniq_day` (`day`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


-- ----------------------------
-- Table structure for Report_finance
-- ----------------------------
-- ----------------------------
-- Table structure for Report_finance
-- ----------------------------
DROP TABLE IF EXISTS `Report_finance`;
CREATE TABLE `Report_finance` (
	`id` INT ( 11 ) NOT NULL AUTO_INCREMENT,
	`day` VARCHAR ( 50 ) NOT NULL,
	`total_income` FLOAT DEFAULT 0 NOT NULL,
	`total_vip_income` FLOAT DEFAULT 0 NOT NULL,
	`total_vip_month_income` FLOAT DEFAULT 0 NOT NULL,
	`total_vip_season_income` FLOAT DEFAULT 0 NOT NULL,
	`total_vip_year_income` FLOAT DEFAULT 0 NOT NULL,
	`total_point_income` FLOAT DEFAULT 0 NOT NULL,

	`today_total_income` FLOAT DEFAULT 0 NOT NULL,
	`today_vip_income` FLOAT DEFAULT 0 NOT NULL,
	`today_vip_year_income` FLOAT DEFAULT 0 NOT NULL,
	`today_vip_season_income` FLOAT DEFAULT 0 NOT NULL,
	`today_vip_month_income` FLOAT DEFAULT 0 NOT NULL,
	`today_point_income` FLOAT DEFAULT 0 NOT NULL,

	`total_user_cnt` INT DEFAULT 0 NOT NULL,
	`total_vip_user_cnt` INT DEFAULT 0 NOT NULL,
	`total_vip_month_user_cnt` INT DEFAULT 0 NOT NULL,
	`total_vip_season_user_cnt` INT DEFAULT 0 NOT NULL,
	`total_vip_year_user_cnt` INT DEFAULT 0 NOT NULL,
	`total_baipiao_user_cnt` INT DEFAULT 0 NOT NULL,
	`total_refund_user_cnt` INT DEFAULT 0 NOT NULL,

	`today_vip_user_cnt` INT DEFAULT 0 NOT NULL,
	`today_vip_month_user_cnt` INT DEFAULT 0 NOT NULL,
	`today_vip_season_user_cnt` INT DEFAULT 0 NOT NULL,
	`today_vip_year_user_cnt` INT DEFAULT 0 NOT NULL,
	`today_point_charge_sum` FLOAT DEFAULT 0 NOT NULL,
	`today_point_charge_times` FLOAT DEFAULT 0 NOT NULL,

	created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	PRIMARY KEY ( `id` ),
	KEY `idx_day` ( `day` ) USING BTREE,
UNIQUE KEY `uniq_day` ( `day` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4;


-- ----------------------------
-- Table structure for Feedback_feedback
-- ----------------------------
DROP TABLE IF EXISTS `Feedback_feedback`;
CREATE TABLE `Feedback_feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `email` varchar(128)  NULL,
  `content` varchar(2048) NOT NULL,
  `user_profile` varchar(1024) NULL,
  `image_url` varchar(512) NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `idx_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

