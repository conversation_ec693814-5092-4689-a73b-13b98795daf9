<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport"
        content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <title></title>
    <link rel="stylesheet" href="https://zehou-1300206744.cos.ap-guangzhou.myqcloud.com/phone/css/normalize.css">
    <link rel="stylesheet" href="https://zehou-1300206744.cos.ap-guangzhou.myqcloud.com/phone/css/base.css">
    <link rel="stylesheet" href="https://zehou-1300206744.cos.ap-guangzhou.myqcloud.com/phone/css/index.css">

    <script src="https://zehou-1300206744.cos.ap-guangzhou.myqcloud.com/phone/js/vue.min.js" type="text/javascript"
        charset="utf-8"></script>
    <script src="https://zehou-1300206744.cos.ap-guangzhou.myqcloud.com/phone/js/axios.min.js" type="text/javascript"
        charset="utf-8"></script>
    <script src="https://zehou-1300206744.cos.ap-guangzhou.myqcloud.com/phone/js/moment.min.js" type="text/javascript"
        charset="utf-8"></script>

</head>

<body>


    <div id="app">
        <div id="list" v-show="list">
            <div class="size"><span> <span style="color: red; "> {{unReadSize}} / {{ listSize }} conversations </span>
                </span>
            </div>
            <div v-for="(item, index) in msgList" :key="index" @click="clickMsg(item.number)">
                <div class="head">{{ item.head }}</div>
                <div v-if="item.tips != 0" class="tips">
                    {{ item.tips > 9 ? '9+' : item.tips }}
                </div>
                <div class="content">
                    <div class="number">{{ item.number }}</div>
                    <div class="time">{{ item.time }}</div>
                    <div class="messgae">{{ item.message }}</div>
                </div>
            </div>
        </div>
        <div id="detail" v-show="detail">
            <div class="head">
                <div class="back" @click="back">
                    Messages
                </div>
                <div class="number">{{ msgNumber }}</div>
            </div>
            <div id="scroll" class="content">
                <div v-for="(item, index) in msgDetail" :key="index" class="word" :class="{ send: item.send }">
                    <template v-if="!item.json">{{ item.word }}</template>
                    <pre v-else>{{ item.word }}</pre>
                </div>
            </div>


            <div class="operate">
                <div class="operateBtns">
                    <button class="operateBtn" @click="popupShow = true">快捷回复</button>
                </div>

                <div class="inputWrapper">
                    <textarea class="input" v-model="inputMsg" rows="15" id="sending-msg-id"></textarea>
                    <button class="send" @click="send"><img
                            src="https://zehou-1300206744.cos.ap-guangzhou.myqcloud.com/phone/img/send.png" alt="">
                    </button>
                </div>
            </div>
        </div>
        <div v-show="popupShow" id="replyPopup">
            <div class="closeBtn" @click="popupShow = false">x</div>
            <div class="title">快捷回复</div>
            <div class="main">
                <reply-list-comp :list="replyList" :fill-word="fillInputNew" />
            </div>
        </div>
    </div>
    <script>

        Vue.component('reply-list-comp', {
            template: `
          <div class="replyList">
            <div v-for="(item, idx) in list" :key="item.label">
              <div :style="getStyle(item)" @click="fillInputNew(item)">{{ item.label }}</div>
              <div v-if="item.children">
                <reply-list-comp :list="item.children" :fill-word="fillWord"/>
              </div>
            </div>
          </div>`,
            props: {
                list: {
                    type: Array,
                    default: []
                },
                fillWord: {
                    type: Function,
                }
            },
            methods: {
                fillInputNew(item) {
                    this.fillWord(item)
                },
                getStyle(item) {
                    return {
                        paddingLeft: `${item.level * 2}rem`,
                        lineHeight: '1.5rem'
                    }
                }
            }
        })

        const app = new Vue({
            el: '#app',
            data: {
                list: true,
                listSize: 0,
                unReadSize: 0,
                msgList: [],
                detail: false,
                msgNumber: '',
                msgDetail: [],
                inputMsg: '',
                popupShow: false,
                replyList: [] // 初始化为空数组
            },
            mounted() {
                this.getList()
                console.log(this.fillInputNew)
                const handler = () => {
                    this.getList()
                    setTimeout(handler, 240000)

                    // 回到主页
                    this.detail = false
                    this.list = true
                }
                setTimeout(handler, 240000); // 每4分钟请求一次getList
            }
            ,
            methods: {
                getList() {
                    this.listSize = 0
                    this.unReadSize = 0
                    this.msgList = []
                    axios
                        .post('http://*************:8080/zhphone/feedback/fatpoCustomerServiceSmsList/', {
                            fatpotoken: 'jiejie'
                        })
                        .then(({ data }) => {
                            if (data.err_code == 0) {
                                this.listSize = data.data.conversation_size
                                this.unReadSize = data.data.conversation_unread_size
                                const arr = data.data.conversation_list
                                const list = []
                                for (let key in arr) {
                                    list.push({
                                        head: key.substr(key[0] === '+' ? 1 : 0, 2),
                                        number: key,
                                        time: moment(arr[key].latest_ts).format('DD/MM/YYYY HH:mm'),
                                        message: arr[key].last_content,
                                        tips: arr[key].no_read_cnt ? arr[key].no_read_cnt : 0
                                    })
                                }
                                this.msgList = list
                            }
                        })
                },
                clickMsg(number) {
                    this.list = false
                    this.detail = true
                    this.msgNumber = number
                    this.inputMsg = ''
                    this.inputMsg = document.getElementById("sending-msg-id").value;
                    this.msgDetail = []
                    axios.post('http://*************:8080/zhphone/feedback/fatpoCustomerServiceSmsListByContact/', {
                        fatpotoken: "jiejie",
                        contact: number
                    })
                        .then(({ data }) => {
                            if (data.err_code == 0) {
                                const list = []
                                data.data.sms_list.forEach(item => {
                                    list.push({
                                        send: item.from_number == '+1000009999',
                                        word: item.content,
                                        json: item.content !== null ? item.content.indexOf('用户id') !== -1 : false
                                    })
                                })
                                this.msgDetail = list
                                this.scrollToBottom()
                            }
                        })
                },
                back() {
                    this.detail = false
                    this.list = true
                    this.getList()
                },
                send() {
                    const content = document.getElementById("sending-msg-id").value;
                    if (content.length > 0) {
                        axios.post('http://*************:8080/zhphone/feedback/fatpoCustomerServiceSendSms/', {
                            fatpotoken: "jiejie",
                            to_number: this.msgNumber,
                            content: document.getElementById("sending-msg-id").value,
                        })
                            .then(({ data }) => {
                                if (data.err_code == 0) {
                                    this.msgDetail.push({
                                        send: true,
                                        word: document.getElementById("sending-msg-id").value
                                    })
                                    this.inputMsg = ''
                                    document.getElementById("sending-msg-id").value = ''; // 清空输入框
                                    this.scrollToBottom()

                                    if (data.data.backend_content) {
                                        this.msgDetail.push({
                                            send: false,
                                            word: data.data.backend_content,
                                            json: true,
                                        });
                                        this.scrollToBottom()
                                    }
                                } else {
                                    alert(data.err_msg)
                                }
                            })
                    } else {
                        alert("请输入消息内容");
                    }
                },
                scrollToBottom() {
                    this.$nextTick(() => {
                        let box = this.$el.querySelector("#scroll")
                        box.scrollTop = box.scrollHeight
                    })
                },
                fillInput(word) {
                    const inputElement = document.getElementById("sending-msg-id");
                    inputElement.value = word; // 将单词填充到输入框中

                    this.send(); // 触发发送动作
                },
                fillInputNew(item) {
                    if (item.children) {
                        return
                    }
                    // const inputElement = document.getElementById("sending-msg-id");
                    // inputElement.value = item.label; // 将单词填充到输入框中
                    this.inputMsg = item.code
                    document.getElementById("sending-msg-id").value = item.code
                    this.popupShow = false

                    if (item.code.indexOf('后门') !== -1) {
                        this.send(); // 触发发送动作
                    }
                }
            }
        })

        // 使用 fetch API 异步加载 JSON 文件
        fetch('replylist.json')
            .then(response => response.json())
            .then(data => {
                // JSON 文件加载完成后将数据赋值给 Vue 实例的 replyList 属性
                app.replyList = data;
            })
            .catch(error => console.error('Error loading JSON:', error));
    </script>
</body>

</html>