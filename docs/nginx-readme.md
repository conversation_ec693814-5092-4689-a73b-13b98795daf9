## nginx

cat /etc/nginx/conf.d/vsim.conf

```
upstream myapp{
    server 127.0.0.1:8000;
}

server {
    listen       80;
    listen [::]:80;

    server_name  api.virtualsim.xyz;

    client_max_body_size 20m;

    location ~* \.(html|gif|jpg|jpeg|png|css|js|ico)$ {
        root /root/www/VirtualSIM_BackEnd/static/;
    }
    location /static/ {
        root /root/www/VirtualSIM_BackEnd/;
    }

    location / {

        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT, X-Mx-ReqToken, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, Authorization';

        if ($request_method = 'OPTIONS') {
            return 204;
        }

        proxy_set_header   Host             $host:8000;
        proxy_set_header   X-Real-IP        $remote_addr;
        proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
        proxy_set_header   Cookies          $http_cookies;
        proxy_pass http://myapp;
    }
}

server {
    listen 443;
    listen [::]:443;

    server_name api.virtualsim.xyz;
    ssl on;
    root html;
    index index.html index.htm;
    ssl_certificate   /root/.acme.sh/api.virtualsim.xyz/fullchain.cer;
    ssl_certificate_key  /root/.acme.sh/api.virtualsim.xyz/api.virtualsim.xyz.key;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;

    client_max_body_size 20m;

    location ~* \.(html|gif|jpg|jpeg|png|css|js|ico)$ {
        root /root/www/VirtualSIM_BackEnd/static/;
    }
    location /static/ {
        root /root/www/VirtualSIM_BackEnd/;
    }

    location / {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT, X-Mx-ReqToken, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type, Authorization';

        if ($request_method = 'OPTIONS') {
            return 204;
        }

        proxy_set_header   Host             $host:8000;
        proxy_set_header   X-Real-IP        $remote_addr;
        proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
        proxy_set_header   Cookies          $http_cookies;
        proxy_pass http://myapp;
    }
}
```

然后记得去:

```
/etc/nginx/nginx.conf 把user改成root
```