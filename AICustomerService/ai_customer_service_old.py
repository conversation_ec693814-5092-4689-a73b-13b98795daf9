import json

import requests.adapters
from requests.adapters import HTT<PERSON><PERSON>pter, Retry

from SecPhone.settings import logger

# 体验：https://open.bigmodel.cn/shareapp/v1/?share_code=N8H4iVWQaNTUAGP8J7d1j
# 后台：https://open.bigmodel.cn/

# 创建一个Session对象，它会隐式地使用连接池
session = requests.Session()

# 创建一个Retry对象，配置重试策略
retry_strategy = Retry(
    total=3,  # 最大重试次数
    backoff_factor=1,  # 重试间隔时间的回退因子
    status_forcelist=[500, 502, 503, 504],  # 针对这些 HTTP 状态码进行重试
)

# 设置连接池的大小（例如，最大同时打开的连接数）
# 注意：这个值需要根据你的具体需求和服务器的能力来设置
adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=10, pool_maxsize=20)
session.mount('http://', adapter)
session.mount('https://', adapter)


class AiCustomerServiceUtil:
    @staticmethod
    def get_ai_answer(user_id: int, query: str) -> str:
        try:
            logger.info(f"[AiCustomerServiceUtil] user_id:{user_id}, start handle: {query}")
            query = query.strip()
            query = query.replace("\n", " ")
            if not query:
                logger.error(f"[AiCustomerServiceUtil] user_id:{user_id}, q is empty")
                return ""

            endpoint = "https://open.bigmodel.cn/api/llm-application/open/model-api/1854774869661814785/invoke"
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer 0b995dfd8ac0cba5ff89a24b126a2314.MtROSnLKozaNrN7T"
            }
            payload = {'prompt': [{'role': 'user', 'content': query}]}
            logger.info(f"[AiCustomerServiceUtil] user_id:{user_id}, making request to endpoint")
            response = session.post(endpoint, headers=headers, json=payload, timeout=(30, 60))
            logger.info(f"[AiCustomerServiceUtil] user_id:{user_id}, request completed")

            try:
                rsp_json = response.json()
                if "data" not in rsp_json:
                    logger.error(
                        f"[AiCustomerServiceUtil] user_id:{user_id}, query:{query} failed, rsp: {response.content}")
                    return ""
                answer = rsp_json['data']['content']
            except json.decoder.JSONDecodeError:
                logger.error(f"[AiCustomerServiceUtil] user_id:{user_id}, query:{query}, "
                             f"failed with json: {response.content}", exc_info=True)
                answer = ""
            except Exception:
                logger.error(f"[AiCustomerServiceUtil] user_id:{user_id}, query:{query}", exc_info=True)
                answer = ""

            logger.info(f"[AiCustomerServiceUtil] user_id:{user_id}, q:{query}, a:{answer}")
            return answer
        except Exception:
            query_ = query.replace("\n", " ")
            logger.error(f"[AiCustomerServiceUtil] user_id:{user_id}, q:{query_},failed", exc_info=True)
            return ""


if __name__ == '__main__':
    answer = AiCustomerServiceUtil.get_ai_answer(0, "change number")
    print(answer)
