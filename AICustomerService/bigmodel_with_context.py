import requests
import json
from requests.adapters import H<PERSON><PERSON><PERSON>pter, Retry
from SecPhone.settings import logger
from django.core.cache import cache

session = requests.Session()


retry_strategy = Retry(
    total=3,  # 最大重试次数
    backoff_factor=1,  # 重试间隔时间的回退因子
    status_forcelist=[500, 502, 503, 504],  # 针对这些 HTTP 状态码进行重试
)

# 设置连接池的大小（例如，最大同时打开的连接数）
# 注意：这个值需要根据你的具体需求和服务器的能力来设置
adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=10, pool_maxsize=20)
session.mount('http://', adapter)
session.mount('https://', adapter)

ZHIPU_APP_ID = 1899731350756442112
ZHIPU_TOKEN = 'f60dcf29594f4ce2be844f36247ea49d.w1k4mxx4f48yc7gj'

url_new_chat = 'https://open.bigmodel.cn/api/llm-application/open//v2/application/%s/conversation' % ZHIPU_APP_ID
url_send_question = 'https://open.bigmodel.cn/api/llm-application/open/v2/application/generate_request_id'
headers = {'Authorization': 'Bearer %s' % ZHIPU_TOKEN}


# create chat, can each user has his own chat, better for multi-round chat
def __create_chat():
    r = session.post(url_new_chat, headers=headers, json={})
    logger.info('[ZHIPU]create chat:', r.json())
    chat_id = r.json()['data']['conversation_id']
    return chat_id


def __get_user_chat_id(user_id):

    cache_key = 'CHAT_ID_' + str(user_id)
    chat_id = cache.get(cache_key, None)
    if chat_id is None:
        chat_id = __create_chat()
        cache.set(cache_key, chat_id, 3600 * 24)
    return chat_id


def zhipu_chat_with_context(user_id, question):

    chat_id = __get_user_chat_id(user_id)

    # send question
    data = {
        "app_id": ZHIPU_APP_ID,
        "conversation_id": chat_id,
        "key_value_pairs": [
            {"id": "user", "name": "用户提问", "type": "input", "value": question}
        ]
    }

    r = session.post(url_send_question, headers=headers, json=data)
    logger.info('[ZHIPU]send question:', question, r.json())
    answer_id = r.json()['data']['id']

    # get answer
    url_get_answer = 'https://open.bigmodel.cn/api/llm-application/open/v2/model-api/%s/sse-invoke' % answer_id

    with session.post(url_get_answer, headers=headers, stream=True, timeout=30) as resp:
        try:
            for line in resp.iter_lines():
                line = line.decode('utf8')
                if line.find('block_type":"output"') > 0 and line.find('out_content') > 0:
                    line = line.replace('data:', '')
                    res = json.loads(line)
                    answer = res['extra_input']['block_data']['out_put']['out_content']
                    return answer
        except Exception as e:
            logger.error(f"[ZHIPU]get answer error: {e}", exc_info=True)
            return None


if __name__ == '__main__':


    contents = [
        # "change number",
        # "delete account",
        "why can not receive message?",
        "fuck you scam app!",
    ]
    for c in contents:
        print(zhipu_chat_with_context(1234, c))
