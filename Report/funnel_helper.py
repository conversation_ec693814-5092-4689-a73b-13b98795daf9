# 专门处理用户漏斗的工具包
from datetime import timedelta, datetime

from django.db import connection

from SecPhone.settings import logger


class FunnelHelper:
    def __init__(self):
        self.funnel_types = ('登录', '搜索号码', '锁定号码', '查看VIP', '真正订阅')
        self.today = datetime.utcnow()
        self.begin = self.today - timedelta(days=30)
        self.tmr = self.today + timedelta(days=1)
        self.days = self.__init_days()

    def __init_days(self):

        days = []

        for i in range(30, -1, -1):
            dt = self.today - timedelta(days=i)
            days.append(dt.strftime("%Y-%m-%d"))

        return days

    def __init_days_dict(self):

        return dict([x, 0] for x in self.days)

    def get_funnel_data(self):

        data = {}
        for t in self.funnel_types:
            data[t] = self.__init_days_dict()

        sql = f''' select `day`, sum(login_cnt) as login_cnt, sum(search_cnt) as search_cnt, sum(lock_cnt) as lock_cnt, sum(vip_cnt) as vip_cnt, sum(order_cnt) as order_cnt  from Report_funnel
        where `day` between '{self.begin.strftime('%Y%m%d')}' and '{self.tmr.strftime('%Y%m%d')}' group by day
        '''
        logger.info(f"report sum __get_funnel_data, sql={sql}")
        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, login_cnt, search_cnt, lock_cnt, vip_cnt, order_cnt) in cur.fetchall():
                day = datetime.strptime(day, "%Y%m%d").strftime("%Y-%m-%d")
                data['登录'][day] = int(login_cnt)
                data['搜索号码'][day] = int(search_cnt)
                data['锁定号码'][day] = int(lock_cnt)
                data['查看VIP'][day] = int(vip_cnt)
                data['真正订阅'][day] = int(order_cnt)

        return data

    def dict_2_list(self, data: dict) -> list:

        return [data.get(x, 0) for x in self.days]
