from datetime import timedelta, datetime

from currency_converter import CurrencyConverter
from django.db import connection

from SecPhone import settings
from SecPhone.settings import logger


class RefundHelper:
    def __init__(self):
        self.types = settings.total_product_ids
        self.types.update(['total'])
        self.today = datetime.utcnow()
        self.begin = self.today - timedelta(days=30)
        self.tmr = self.today + timedelta(days=1)
        self.days = self.__init_days()
        self.c = CurrencyConverter()

    def __init_days(self):

        days = []

        for i in range(30, -1, -1):
            dt = self.today - timedelta(days=i)
            days.append(dt.strftime("%Y-%m-%d"))

        return days

    def __init_days_dict(self):

        return dict([x, 0] for x in self.days)

    @staticmethod
    def dict_fetchall(cursor):
        columns = [col[0] for col in cursor.description]
        return [dict(zip(columns, row)) for row in cursor.fetchall()]

    def convert_to_usd(self, amount, currency):
        try:
            if currency == "SAR":
                return amount * 0.27
            elif currency == "AED":
                return amount * 0.27
            elif currency == "NGN":
                return amount * 0.0013
            elif currency == "PKR":
                return amount * 0.0035
            elif currency == "EGP":
                return amount * 0.032
            elif currency == "VND":
                return amount * 0.000039
            elif currency == "QAR":
                # 严格上来说是 1QAR=0.27刀, 但是我们实际上7.99刀=29900 QAR，所以我只能自己换算了
                return amount * 0.0002664221407135712

            converted_amount = self.c.convert(amount, currency, 'USD')
            return converted_amount
        except Exception:
            logger.error(f"[convert_to_usd] failed with {currency}: {amount}")
            return 0

    def get_refund_data(self):

        data = {}
        for t in self.types:
            data[t] = self.__init_days_dict()

        sql = f''' select * from Order_orderrefund
        where `revocation_date` between '{self.begin.strftime('%Y-%m-%d')}' and '{self.tmr.strftime('%Y-%m-%d')}' 
        '''
        logger.info(f"report sum __get_funnel_data, sql={sql}")
        with connection.cursor() as cur:
            cur.execute(sql)
            res = self.dict_fetchall(cur)
            for i in res:
                day = i['revocation_date'].strftime("%Y-%m-%d")
                if i['refund_currency'] != 'USD':
                    refund_price = self.convert_to_usd(i['refund_price'], i['refund_currency'])
                else:
                    refund_price = i['refund_price']

                product_id = i['refund_product_id']
                if product_id in self.types:
                    data[product_id][day] += refund_price
                    data['total'][day] += refund_price
                else:
                    raise Exception(f"invalid product_id: {product_id}")

        # 把0.001单元转成元，再保留小数点2位
        round_dict_values(data, 2)
        return data

    def dict_2_list(self, data: dict) -> list:

        return [data.get(x, 0) for x in self.days]


def round_dict_values(dictionary, decimal_places=2):
    for key, value in dictionary.items():
        if isinstance(value, dict):
            round_dict_values(value, decimal_places)  # 递归处理内部字典
        else:
            dictionary[key] = round(value / 1000, decimal_places)
