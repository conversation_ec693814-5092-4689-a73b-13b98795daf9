from django.db import models


class SnapshotNumber(models.Model):
    released = models.IntegerField('released count')
    using = models.IntegerField('using count')
    expire = models.IntegerField('expire count')
    total = models.IntegerField('total count')
    open_order = models.IntegerField('order open count')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class SnapshotAppVersion(models.Model):
    app_version = models.CharField(max_length=64, default="")
    count = models.IntegerField('count')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class SnapshotInvalidPoints(models.Model):
    total_cnt = models.IntegerField('total_cnt')
    empty_cnt = models.IntegerField('empty_cnt')
    not_empty_cnt = models.IntegerField('not_empty_cnt')
    invalid_cnt = models.IntegerField('invalid_cnt')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
