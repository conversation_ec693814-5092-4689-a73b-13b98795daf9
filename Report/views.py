from datetime import datetime, timedelta

from django.db import connection
from django.db.models import Sum
from django.shortcuts import render

from Call.models import SMSRecord, CallRecord
from Common.err import ErrInfo
from Common.util import Util
from Common.views import SecPhoneView
from Point.models import PointRecord
from Report.funnel_helper import FunnelHelper
from Report.income_helper import IncomeHelper
from Report.models import SnapshotNumber, SnapshotAppVersion, SnapshotInvalidPoints
from Report.refund_helper import RefundHelper
from SecPhone.settings import logger
from User.models import User


# /report/sum/
class ReportSum(SecPhoneView):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        self.order_types = ('TRIAL', 'MONTH', 'SEASON', 'YEAR')
        self.order_asa_user_types = ('TRIAL_asa', 'MONTH_asa', 'SEASON_asa', 'YEAR_asa')
        self.sms_types = ('INCOMING', 'OUTGOING', 'TOTAL', 'TOTAL-PARTS', 'INCOMING-PARTS', 'OUTGOING-PARTS')
        self.sms_system_notice_types = ('System-Notice',)
        self.sms_no_carrier_types = ('Non-English', 'Inner', 'Total')
        self.sms_cost_types = ('INCOMING', 'OUTGOING', 'TOTAL')
        self.points_event_types = (
            'CALL', 'SMS', 'MMS', 'CHARGE', 'FREE', 'CLEAN', 'FATPO-CHARGE', 'REFUND', 'REGISTER',)
        self.points_cost_types = ('TOTAL-COST-POINTS', 'CALL-POINTS', 'TOTAL-SMS-MMS-POINTS', 'SMS-POINTS',
                                  'MMS-POINTS', 'CHARGE-POINTS', 'REFUND-POINTS')
        self.sms_status_types = ()
        self.sms_exception_status_types = ('FAILED-SEND', 'NULL-PRICE', 'NULL-POINTS',)
        self.sms_judge_types = ('step1-TOTAL-BAD', 'step1-BOT-BAD',
                                'step2-AI-VALID', 'step2-AI-INVALID', 'step2-AI-UNKNOWN', 'step3-FAKE-SEND',)
        self.sms_human_judge_types = ('FAKE-SEND', 'HUMAN-APPROVE', 'HUMAN-REJECT',
                                      'HUMAN-NEED-TO-JUDGE', 'HUMAN-TOTAL',)
        self.sms_call_not_finished_types = ('SMS', 'MMS', 'SMS_MMS', 'CALL', 'TOTAL',
                                            'CODE_SEEKING_BEFORE_USER', 'CODE_INVALID_JSON', 'CODE_FORBIDDEN_FROM',
                                            'CODE_CANNOT_LOCATE_USER', 'CODE_SENDING_FAILED',)
        self.today = datetime.utcnow()
        self.begin = self.today - timedelta(days=30)
        self.tmr = self.today + timedelta(days=1)
        self.days = self.__init_days()
        self.hours = self.__init_last_24_hours()

    def __init_days(self):

        days = []

        for i in range(30, -1, -1):
            dt = self.today - timedelta(days=i)
            days.append(dt.strftime("%Y-%m-%d"))

        return days

    def __init_last_24_hours(self):
        hours = []

        for i in range(25):
            dt = self.today - timedelta(hours=i)
            hour_str = dt.strftime("%Y-%m-%d %H:00:00")
            hours.append(hour_str)

        return hours

    def __init_days_dict(self):

        return dict([x, 0] for x in self.days)

    def __init_hours_dict(self):

        return dict([x, 0] for x in self.hours)

    def __get_user_data(self):

        data = self.__init_days_dict()

        sql = '''
               select DATE_FORMAT(created_at, '%%Y-%%m-%%d') as d, count(*)
               from User_user
               where created_at between '%s' and '%s'
               group by d
               ''' % (self.begin.strftime('%Y-%m-%d'), self.tmr.strftime('%Y-%m-%d'))
        logger.info(f"report sum, user data,  sql: {sql}")
        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, count) in cur.fetchall():
                data[day] += count

        return data

    def __get_user_data_register_asa_data(self):

        data = self.__init_days_dict()

        sql = '''
               select DATE_FORMAT(created_at, '%%Y-%%m-%%d') as d, count(*)
               from User_user
               where app_source='asa' and created_at between '%s' and '%s'
               group by d
               ''' % (self.begin.strftime('%Y-%m-%d'), self.tmr.strftime('%Y-%m-%d'))
        logger.info(f"report sum, user data register asa,  sql: {sql}")
        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, count) in cur.fetchall():
                data[day] += count

        return data

    @staticmethod
    def __get_order_type(duration: int) -> str:

        if duration > 300:
            return 'YEAR'
        if duration > 80:
            return 'SEASON'
        if duration > 20:
            return 'MONTH'

        return 'TRIAL'

    def __get_order_data(self):

        data = {}
        for t in self.order_types:
            data[t] = self.__init_days_dict()

        sql = f'''
           select DATE_FORMAT(created_at, '%%Y-%%m-%%d') as d, DATEDIFF(expire_at, created_at) as duration, count(*) 
           from Order_order
           where created_at between '%s' and '%s'
           group by d, duration
           ''' % (self.begin.strftime('%Y-%m-%d'), self.tmr.strftime('%Y-%m-%d'))
        logger.info(f"report sum, order data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, duration, count) in cur.fetchall():
                order_type = self.__get_order_type(duration)
                data[order_type][day] += count

        return data

    def __get_order_data_by_asa(self):

        data = {}
        for t in self.order_asa_user_types:
            data[t] = self.__init_days_dict()

        sql = f'''
           select DATE_FORMAT(t1.created_at, '%%Y-%%m-%%d') as d, DATEDIFF(t1.expire_at, t1.created_at) as duration, count(*) 
           from Order_order t1 left join User_user t2 on t1.user_id=t2.id
           where t2.app_source='asa' and t1.created_at between '%s' and '%s'
           group by d, duration
           ''' % (self.begin.strftime('%Y-%m-%d'), self.tmr.strftime('%Y-%m-%d'))
        logger.info(f"report sum, order data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, duration, count) in cur.fetchall():
                order_type = self.__get_order_type(duration)
                data[order_type + "_asa"][day] += count

        return data

    def __get_sms_data(self):

        data = {}
        for t in self.sms_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql = f'''select DATE_FORMAT(created_at, '%Y-%m-%d') as d, direction, count(*), sum(parts) from Call_smsrecord 
             where is_it=0 and is_fake_send=0 and created_at between '{start}' and '{end}' group by d, direction;
           '''
        logger.info(f"report sum, sms data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, direction, count, parts_cnt) in cur.fetchall():
                if direction == "RECIEVE":
                    direction = "INCOMING"
                    direction_part = "INCOMING-PARTS"
                if direction == "SEND":
                    direction = "OUTGOING"
                    direction_part = "OUTGOING-PARTS"
                data[direction][day] += count
                data['TOTAL'][day] += count
                data[direction_part][day] += int(parts_cnt)
                data['TOTAL-PARTS'][day] += int(parts_cnt)

        return data

    def __get_sms_cost_data(self):

        data = {}
        for t in self.sms_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql = f'''select DATE_FORMAT(created_at, '%Y-%m-%d') as d, direction, sum(price) from Call_smsrecord where err_code = 0 and created_at between '{start}' and '{end}' group by d, direction;'''
        logger.info(f"report sum, sms cost data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, direction, cost) in cur.fetchall():
                if not cost:
                    cost = 0
                if direction == "RECIEVE":
                    direction = "INCOMING"
                if direction == "SEND":
                    direction = "OUTGOING"
                data[direction][day] += cost
                data['TOTAL'][day] += cost

        # 处理小数点
        Util.round_dict_values(data)
        return data

    def __get_points_event_data(self):

        data = {}
        for t in self.points_event_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql = f'''select DATE_FORMAT(created_at, '%Y-%m-%d') as d, event, count(*) from Point_pointrecord where created_at between '{start}' and '{end}' group by d, event;'''
        logger.info(f"report sum, points event cost data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, event, cnt) in cur.fetchall():
                data[event][day] += cnt

        return data

    def __get_points_cost_data(self):

        data = {}
        for t in self.points_cost_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql = f'''select DATE_FORMAT(created_at, '%Y-%m-%d') as d, event, sum(point) from Point_pointrecord where created_at between '{start}' and '{end}' group by d, event;'''
        logger.info(f"report sum, points event cost data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, event, points_sum) in cur.fetchall():
                if event == "CALL":
                    data['CALL-POINTS'][day] += abs(int(points_sum))
                    data['TOTAL-COST-POINTS'][day] += abs(int(points_sum))
                if event == "SMS":
                    data['SMS-POINTS'][day] += abs(int(points_sum))
                    data['TOTAL-SMS-MMS-POINTS'][day] += abs(int(points_sum))
                    data['TOTAL-COST-POINTS'][day] += abs(int(points_sum))
                if event == "MMS":
                    data['MMS-POINTS'][day] += abs(int(points_sum))
                    data['TOTAL-SMS-MMS-POINTS'][day] += abs(int(points_sum))
                    data['TOTAL-COST-POINTS'][day] += abs(int(points_sum))
                if event == "CHARGE":
                    data['CHARGE-POINTS'][day] += abs(int(points_sum))
                if event == "REFUND":
                    data['REFUND-POINTS'][day] += abs(int(points_sum))

        return data

    def __get_new_user_country_data_24_hours(self):

        sql = f'''SELECT 
            DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00') AS time_interval,
            country,
            COUNT(*) AS user_count
        FROM 
            User_user
        WHERE 
            created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY 
            time_interval,
            country
        ORDER BY 
            time_interval,
            country;
        '''
        logger.info(f"report sum, new user country 24 hours data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            data = {'Total': self.__init_hours_dict()}
            for (hour, country, cnt) in cur.fetchall():
                if not country:
                    country = "Unknown"
                if country not in data:
                    data[country] = self.__init_hours_dict()
                data[country][hour] += cnt
                data['Total'][hour] += cnt

        # 删除总的数据不超过n的干扰数据
        del_set = set()
        for country, hour_data in data.items():
            total_count = sum(hour_data.values())
            if total_count < 1:
                del_set.add(country)

        logger.info(f"__get_new_user_country_data_24_hours del {del_set}")
        for country in del_set:
            data.pop(country)

        return data

    def __get_new_user_country_data(self):

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql = f'''select DATE_FORMAT(created_at, '%Y-%m-%d') as d, country, count(*) from User_user 
             where created_at between '{start}' and '{end}' group by d, country;
           '''
        logger.info(f"report sum, new user country data 30 days sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            data = {'Total': self.__init_days_dict()}
            for (day, country, cnt) in cur.fetchall():
                if not country:
                    country = "Unknown"
                if country not in data:
                    data[country] = self.__init_days_dict()
                data[country][day] += cnt
                data['Total'][day] += cnt

        # 删除总的数据不超过10的干扰数据
        del_set = set()
        for country, daily_data in data.items():
            total_count = sum(daily_data.values())
            if total_count < 10:
                del_set.add(country)

        logger.info(f"__get_new_user_country_data del {del_set}")
        for country in del_set:
            data.pop(country)

        return data

    def __get_sms_system_notice_data(self):

        data = {}
        for t in self.sms_system_notice_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql = f'''SELECT DATE_FORMAT(created_at, '%Y-%m-%d') as d,
                    SUM(CASE WHEN is_it = 1 THEN 1 ELSE 0 END) AS is_it_count
                FROM Call_smsrecord  where created_at between '{start}' and '{end}' group by d;'''
        logger.info(f"report sum, sms system notice status data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, it_cnt) in cur.fetchall():
                data['System-Notice'][day] += int(it_cnt)

        return data

    def __get_sms_no_carrier_data(self):

        data = {}
        for t in self.sms_no_carrier_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql = f'''SELECT DATE_FORMAT(created_at, '%Y-%m-%d') as d,
                    SUM(CASE WHEN is_inner = 1 THEN 1 ELSE 0 END) AS is_inner_count,
                    SUM(CASE WHEN is_non_english = 1 THEN 1 ELSE 0 END) AS is_non_english_count
                FROM Call_smsrecord  where created_at between '{start}' and '{end}' group by d;'''
        logger.info(f"report sum, sms no carrier status data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, inner_cnt, non_english_cnt) in cur.fetchall():
                data['Inner'][day] += int(inner_cnt)
                data['Non-English'][day] += int(non_english_cnt)
                data['Total'][day] += int(inner_cnt) + int(non_english_cnt)

        return data

    def __get_sms_status_data(self):

        data = {}
        for t in self.sms_status_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql2 = f'''select DATE_FORMAT(created_at, '%Y-%m-%d') as d, status, count(*) from `Call_smsrecord` where created_at between '{start}' and '{end}' group by d, status;'''
        logger.info(f"report sum, sms status data sql2: {sql2}")
        with connection.cursor() as cur:
            cur.execute(sql2)
            for (day, status, cnt) in cur.fetchall():
                if status not in data:
                    data[status] = self.__init_days_dict()
                data[status][day] += int(cnt)

        # 分成正常状态和非正常状态
        data_normal = {}
        data_not_normal = {}

        for k, v in data.items():
            # 这两个不是运营商发送的
            if k in ('send_fake', 'received'):
                continue
            if k in ("delivered", "received", "webhook_delivered"):
                data_normal[k] = v
            else:
                data_not_normal[k] = v
        return data_normal, data_not_normal

    def __get_sms_exception_status_data(self):

        data = {}
        for t in self.sms_exception_status_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql = f'''SELECT DATE_FORMAT(created_at, '%Y-%m-%d') as d,
                    SUM(CASE WHEN err_code > 1 THEN 1 ELSE 0 END) AS failed_count,
                    SUM(CASE WHEN price is null and err_code=0 and is_fake_send=0 and is_it=0 THEN 1 ELSE 0 END) AS non_price_count,
                    SUM(CASE WHEN point is null THEN 1 ELSE 0 END) AS non_points_count
                FROM Call_smsrecord  where created_at between '{start}' and '{end}' group by d;'''
        logger.info(f"report sum, sms failed status data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, failed_count, non_price_count, non_points_count) in cur.fetchall():
                data['FAILED-SEND'][day] += int(failed_count)
                data['NULL-PRICE'][day] += int(non_price_count)
                data['NULL-POINTS'][day] += int(non_points_count)
        return data

    def __get_sms_judge_data(self):

        data = {}
        for t in self.sms_judge_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql = f'''SELECT DATE_FORMAT(created_at, '%Y-%m-%d') as d,
                    SUM(CASE WHEN is_first_check_bad = 1 THEN 1 ELSE 0 END) AS is_first_check_bad_count,
                    SUM(CASE WHEN is_bot_bad = 1 THEN 1 ELSE 0 END) AS is_bot_bad_count,
                    SUM(CASE WHEN is_fake_send = 1 THEN 1 ELSE 0 END) AS is_fake_send_count,
                    SUM(CASE WHEN ai_rsp = 1 THEN 1 ELSE 0 END) AS ai_ok_count,
                    SUM(CASE WHEN ai_rsp = 2 THEN 1 ELSE 0 END) AS ai_not_ok_count,
                    SUM(CASE WHEN ai_rsp = 3 THEN 1 ELSE 0 END) AS ai_unknown_count
                FROM Call_smsrecord  where created_at between '{start}' and '{end}' group by d;'''
        logger.info(f"report sum, sms status data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, first_check_bad_count, bot_bad_count, fake_send_count, ai_ok_count, ai_not_ok_count,
                 ai_unknown_count) in cur.fetchall():
                data['step1-TOTAL-BAD'][day] += int(first_check_bad_count)
                data['step1-BOT-BAD'][day] += int(bot_bad_count)
                data['step2-AI-VALID'][day] += int(ai_ok_count)
                data['step2-AI-INVALID'][day] += int(ai_not_ok_count)
                data['step2-AI-UNKNOWN'][day] += int(ai_unknown_count)
                data['step3-FAKE-SEND'][day] += int(fake_send_count)
        return data

    def __get_sms_human_judge_data(self):

        data = {}
        for t in self.sms_human_judge_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql2 = f'''select DATE_FORMAT(created_at, '%Y-%m-%d') as d, review_result, count(*) from `Call_smsrecordwaitforreview` where created_at between '{start}' and '{end}' group by d, review_result;'''
        logger.info(f"report sum, sms status data sql2: {sql2}")
        with connection.cursor() as cur:
            cur.execute(sql2)
            for (day, review_result, cnt) in cur.fetchall():
                if review_result == "approve":
                    data["HUMAN-APPROVE"][day] += int(cnt)
                elif review_result == "approveOriginalText":
                    data["HUMAN-APPROVE"][day] += int(cnt)
                elif review_result == "reject":
                    data["HUMAN-REJECT"][day] += int(cnt)
                else:
                    data["HUMAN-NEED-TO-JUDGE"][day] += int(cnt)
                data['HUMAN-TOTAL'][day] += int(cnt)

        return data

    def __get_sms_call_not_finished_data(self):

        data = {}
        for t in self.sms_call_not_finished_types:
            data[t] = self.__init_days_dict()

        start = self.begin.strftime('%Y-%m-%d')
        end = self.tmr.strftime('%Y-%m-%d')

        sql = f'''SELECT DATE_FORMAT(created_at, '%Y-%m-%d') as d,
                    SUM(CASE WHEN event_type = 'CALL' THEN 1 ELSE 0 END) AS call_cnt,
                    SUM(CASE WHEN event_type = 'SMS' THEN 1 ELSE 0 END) AS sms_cnt,
                    SUM(CASE WHEN event_type = 'MMS' THEN 1 ELSE 0 END) AS mms_cnt,
                    SUM(CASE WHEN not_finished_code = 1 THEN 1 ELSE 0 END) AS seeking_before_user_cnt,
                    SUM(CASE WHEN not_finished_code = 2 THEN 1 ELSE 0 END) AS failed_json_cnt,
                    SUM(CASE WHEN not_finished_code = 3 THEN 1 ELSE 0 END) AS forbidden_from_cnt,
                    SUM(CASE WHEN not_finished_code = 4 THEN 1 ELSE 0 END) AS cannot_locate_user_cnt,
                    SUM(CASE WHEN not_finished_code = 5 THEN 1 ELSE 0 END) AS sending_failed_cnt
                FROM Call_smscallnotfinishedrecord  where created_at between '{start}' and '{end}' group by d;'''
        logger.info(f"report sum, sms and call not finished data sql: {sql}")

        with connection.cursor() as cur:
            cur.execute(sql)
            for (day, call_type_cnt, sms_type_cnt, mms_type_cnt, seeking_before_user_cnt, failed_json_cnt,
                 forbidden_from_cnt, cannot_locate_user_cnt, sending_failed_cnt) in cur.fetchall():
                data['SMS'][day] += int(sms_type_cnt)
                data['MMS'][day] += int(mms_type_cnt)
                data['SMS_MMS'][day] += int(sms_type_cnt) + int(mms_type_cnt)
                data['CALL'][day] += int(call_type_cnt)
                data['TOTAL'][day] += int(call_type_cnt) + int(sms_type_cnt) + int(mms_type_cnt)
                data['CODE_SEEKING_BEFORE_USER'][day] += int(seeking_before_user_cnt)
                data['CODE_INVALID_JSON'][day] += int(failed_json_cnt)
                data['CODE_FORBIDDEN_FROM'][day] += int(forbidden_from_cnt)
                data['CODE_CANNOT_LOCATE_USER'][day] += int(cannot_locate_user_cnt)
                data['CODE_SENDING_FAILED'][day] += int(sending_failed_cnt)

        return data

    def __dict_2_list(self, data: dict) -> list:

        return [data.get(x, 0) for x in self.days]

    def __dict_2_list_by_hours(self, data: dict) -> list:

        return [data.get(x, 0) for x in self.hours]

    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, ("token",))
        if err_code != 0:
            logger.error(f"[Report.sum] param error：no token")
            return self.ReturnError(ErrInfo.AUTH_PARAMETER_MISS)

        if data['token'] != 'fatpojon':
            logger.error(f"[Report.sum] param error：invalid token")
            return self.ReturnError(ErrInfo.AUTH_PARAMETER_MISS)

        appid = int(data.get('appid', -1))

        # 用户日活情况
        user_dict = self.__get_user_data()
        user_register_asa_dict = self.__get_user_data_register_asa_data()

        # 订单情况
        order_dict = self.__get_order_data()
        order_asa_user_dict = self.__get_order_data_by_asa()
        # 收入情况
        income_helper = IncomeHelper()
        income_total_dict = income_helper.get_income_total_data()
        income_today_dict = income_helper.get_income_data_today()

        logger.info(f"report sum incoming total: {income_total_dict}")

        # 用户漏斗
        funnel_helper = FunnelHelper()
        funnel_dict = funnel_helper.get_funnel_data()

        order_data = {}
        for t in self.order_types:
            order_data[t] = self.__dict_2_list(order_dict[t])

        order_data_asa_user = {}
        for t in self.order_asa_user_types:
            order_data_asa_user[t] = self.__dict_2_list(order_asa_user_dict[t])

        income_total_data = {}
        for t in income_helper.income_total_types:
            income_total_data[t] = self.__dict_2_list(income_total_dict[t])

        income_today_data = {}
        for t in income_helper.income_today_types:
            income_today_data[t] = self.__dict_2_list(income_today_dict[t])

        funnel_data = {}
        for t in funnel_helper.funnel_types:
            funnel_data[t] = self.__dict_2_list(funnel_dict[t])

        # 退款相关
        refund_helper = RefundHelper()
        refund_dict = refund_helper.get_refund_data()
        refund_data = {}
        for t in refund_helper.types:
            refund_data[t] = self.__dict_2_list(refund_dict[t])

        # 短信送达率
        sms_delivery_dict = self.__get_sms_data()
        sms_delivery_data = {}
        for t in self.sms_types:
            sms_delivery_data[t] = self.__dict_2_list(sms_delivery_dict[t])

        # 短信cost
        sms_cost_data = {}
        sms_cost_dict = self.__get_sms_cost_data()
        for t in self.sms_cost_types:
            sms_cost_data[t] = self.__dict_2_list(sms_cost_dict[t])

        # 短信运营商status-正常状态和非正常状态
        sms_carrier_normal_status_data = {}
        sms_carrier_not_normal_status_data = {}
        sms_carrier_normal_status_dict, sms_carrier_not_normal_status_dict = self.__get_sms_status_data()
        for t in sms_carrier_normal_status_dict.keys():
            sms_carrier_normal_status_data[t] = self.__dict_2_list(sms_carrier_normal_status_dict[t])

        for t in sms_carrier_not_normal_status_dict.keys():
            sms_carrier_not_normal_status_data[t] = self.__dict_2_list(sms_carrier_not_normal_status_dict[t])

        # 短信异常状态
        sms_exception_status_data = {}
        sms_exception_status_dict = self.__get_sms_exception_status_data()
        for t in self.sms_exception_status_types:
            sms_exception_status_data[t] = self.__dict_2_list(sms_exception_status_dict[t])

        # 不走运营商的短信
        sms_no_carrier_data = {}
        sms_no_carrier_dict = self.__get_sms_no_carrier_data()
        for t in self.sms_no_carrier_types:
            sms_no_carrier_data[t] = self.__dict_2_list(sms_no_carrier_dict[t])

        # 消息通知的短信
        sms_system_notice_data = {}
        sms_system_notice_dict = self.__get_sms_system_notice_data()
        for t in self.sms_system_notice_types:
            sms_system_notice_data[t] = self.__dict_2_list(sms_system_notice_dict[t])

        # 短信审核状态
        sms_judge_data = {}
        sms_judge_dict = self.__get_sms_judge_data()
        for t in self.sms_judge_types:
            sms_judge_data[t] = self.__dict_2_list(sms_judge_dict[t])

        # 短信人类审核状态
        sms_human_judge_data = {}
        sms_human_judge_dict = self.__get_sms_human_judge_data()
        for t in self.sms_human_judge_types:
            sms_human_judge_data[t] = self.__dict_2_list(sms_human_judge_dict[t])

        # 短信电话未完成状态
        sms_call_not_finished_data = {}
        sms_call_not_finished_dict = self.__get_sms_call_not_finished_data()
        for t in self.sms_call_not_finished_types:
            sms_call_not_finished_data[t] = self.__dict_2_list(sms_call_not_finished_dict[t])

        # 查看国家分布-24小时
        new_user_country_24_hours_data = {}
        new_user_country_24_hours_dict = self.__get_new_user_country_data_24_hours()
        countries = new_user_country_24_hours_dict.keys()
        for t in countries:
            new_user_country_24_hours_data[t] = self.__dict_2_list_by_hours(new_user_country_24_hours_dict[t])

        # 查看国家分布-30天
        new_user_country_data = {}
        new_user_country_dict = self.__get_new_user_country_data()
        countries = new_user_country_dict.keys()
        for t in countries:
            new_user_country_data[t] = self.__dict_2_list(new_user_country_dict[t])

        # 查看点数消耗
        points_cost_data = {}
        points_cost_dict = self.__get_points_cost_data()
        for t in self.points_cost_types:
            points_cost_data[t] = self.__dict_2_list(points_cost_dict[t])

        # 查看点数分布
        points_event_data = {}
        points_event_dict = self.__get_points_event_data()
        for t in self.points_event_types:
            points_event_data[t] = self.__dict_2_list(points_event_dict[t])

        context = {
            "appid": appid,
            "days": self.days,
            "hours": self.hours,
            "order_data": order_data,
            "order_data_asa_user": order_data_asa_user,
            "income_total_data": income_total_data,
            "income_today_data": income_today_data,
            "funnel_data": funnel_data,
            "register": self.__dict_2_list(user_dict),
            "register_asa_user": self.__dict_2_list(user_register_asa_dict),
            "refund_data": refund_data,
            "sms_delivery_data": sms_delivery_data,
            "sms_cost_data": sms_cost_data,
            "sms_exception_status_data": sms_exception_status_data,
            "sms_no_carrier_data": sms_no_carrier_data,
            "sms_system_notice_data": sms_system_notice_data,
            "sms_judge_data": sms_judge_data,
            "sms_human_judge_data": sms_human_judge_data,
            "sms_call_not_finished_data": sms_call_not_finished_data,
            "sms_carrier_normal_status_data": sms_carrier_normal_status_data,
            "sms_carrier_not_normal_status_data": sms_carrier_not_normal_status_data,
            "new_user_country_24_hours_data": new_user_country_24_hours_data,
            "new_user_country_data": new_user_country_data,
            "points_event_data": points_event_data,
            "points_cost_data": points_cost_data,
        }

        # 号码池情况
        snapshot_number_data = SnapshotNumberUsing().get_serial_data()
        print(f"snapshot_number_data={snapshot_number_data}")

        context.update({
            "snapshot_number_data": snapshot_number_data
        })

        # 版本号情况
        snapshot_appversion_data = SnapshotAppVersionView().get_serial_data()
        print(f"snapshot_appversion_data={snapshot_appversion_data}")

        context.update({
            "snapshot_appversion_data": snapshot_appversion_data
        })

        # 异常points情况
        snapshot_invalid_points_data = SnapshotInvalidPointsView().get_serial_data()
        print(f"snapshot_invalid_points_data={snapshot_invalid_points_data}")

        context.update({
            "snapshot_invalid_points_data": snapshot_invalid_points_data
        })

        return render(request, 'sum.html', context)


class SnapshotNumberUsing(SecPhoneView):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.token = 'ouyang-abo-200'
        self.today = datetime.utcnow()
        self.begin = self.today - timedelta(days=30)
        self.tmr = self.today + timedelta(days=1)
        self.days = self.__init_days()

    def __init_days(self):

        days = []

        for i in range(30, -1, -1):
            dt = self.today - timedelta(days=i)
            days.append(dt.strftime("%Y-%m-%d"))

        return days

    def __init_days_dict(self):

        return dict([x, 0] for x in self.days)

    def __dict_2_list(self, data: dict) -> list:

        return [data.get(x, 0) for x in self.days]

    def get(self, request):

        data, _, _ = self.GetDataInGet(request)
        token = data.get('token', None)
        if token != self.token:
            logger.error(f"[SnapshotNumberUsing] invalid token")
            return self.ReturnSuccess({"error": "wrong token"})

        try:
            result = {}

            sql = '''
            select status, count(*) from Number_numberinventory  where status in ('USING', 'EXPIRE', 'RELEASED') group by status
            '''

            with connection.cursor() as cur:
                cur.execute(sql)
                for (status, count) in cur.fetchall():
                    result[status.lower()] = int(count)

            result['total'] = sum(result.values())

            sql = '''select count(*) from Order_order where order_status='OPEN' and valid = 1'''

            with connection.cursor() as cur:
                cur.execute(sql)
                result['open_order'] = int(cur.fetchone()[0])

            if 'released' not in result:
                result['released'] = 0
            if 'using' not in result:
                result['using'] = 0
            if 'expire' not in result:
                result['expire'] = 0
            if 'total' not in result:
                result['total'] = 0
            if 'open_order' not in result:
                result['open_order'] = 0

            logger.info(f"[SnapshotNumberUsing] snapshot result = {result}")
            SnapshotNumber(**result).save()

            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[SnapshotNumberUsing] failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

    def get_serial_data(self):

        types = ('released', 'using', 'expire', 'open_order')

        data = {}
        for t in types:
            data[t] = self.__init_days_dict()

        sql = '''
        select released, `using`, expire, open_order, DATE_FORMAT(created_at, '%Y-%m-%d') as d from Report_snapshotnumber order by created_at desc limit 31
        '''

        with connection.cursor() as cur:
            cur.execute(sql)
            for (released, using, expire, open_order, day) in cur.fetchall():
                data['released'][day] = released
                data['using'][day] = using
                data['expire'][day] = expire
                data['open_order'][day] = open_order

        for t in types:
            data[t] = self.__dict_2_list(data[t])

        return data


class SnapshotAppVersionView(SecPhoneView):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.token = 'ouyang-abo-200'
        self.today = datetime.utcnow()
        self.begin = self.today - timedelta(days=30)
        self.tmr = self.today + timedelta(days=1)
        self.days = self.__init_days()

    def __init_days(self):

        days = []

        for i in range(30, -1, -1):
            dt = self.today - timedelta(days=i)
            days.append(dt.strftime("%Y-%m-%d"))

        return days

    def __init_days_dict(self):

        return dict([x, 0] for x in self.days)

    def __dict_2_list(self, data: dict) -> list:

        return [data.get(x, 0) for x in self.days]

    def get(self, request):

        data, _, _ = self.GetDataInGet(request)
        token = data.get('token', None)
        if token != self.token:
            logger.error(f"[SnapshotAppVersionView] invalid token")
            return self.ReturnSuccess({"error": "wrong token"})

        try:
            result = {}

            sql = '''select app_version, count(*) from User_user where created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) group by app_version'''
            with connection.cursor() as cur:
                cur.execute(sql)
                for (app_version, count) in cur.fetchall():
                    result[str(app_version).lower()] = int(count)

            result['total'] = sum(result.values())
            logger.info(f"snapshot app_version result = {result}")

            for k, v in result.items():
                tmp_dic = {
                    "app_version": k,
                    "count": v
                }
                SnapshotAppVersion(**tmp_dic).save()

            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[SnapshotAppVersionView] failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

    def get_serial_data(self):
        data = {}

        sql = '''
            SELECT app_version, count, DATE_FORMAT(created_at, '%Y-%m-%d') AS d
            FROM Report_snapshotappversion
            WHERE created_at >= CURDATE() - INTERVAL 30 DAY
            ORDER BY created_at DESC;
        '''

        with connection.cursor() as cur:
            cur.execute(sql)
            for (app_version, count, day) in cur.fetchall():
                if app_version not in data:
                    data[app_version] = self.__init_days_dict()

                data[app_version][day] = count

        types = data.keys()
        for t in types:
            data[t] = self.__dict_2_list(data[t])

        return data


class SnapshotInvalidPointsView(SecPhoneView):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.token = 'ouyang-abo-200'
        self.today = datetime.utcnow()
        self.begin = self.today - timedelta(days=30)
        self.tmr = self.today + timedelta(days=1)
        self.days = self.__init_days()

    def __init_days(self):

        days = []

        for i in range(30, -1, -1):
            dt = self.today - timedelta(days=i)
            days.append(dt.strftime("%Y-%m-%d"))

        return days

    def __init_days_dict(self):

        return dict([x, 0] for x in self.days)

    def __dict_2_list(self, data: dict) -> list:

        return [data.get(x, 0) for x in self.days]

    def get(self, request):

        data, _, _ = self.GetDataInGet(request)
        token = data.get('token', None)
        if token != self.token:
            logger.error(f"[SnapshotInvalidPointsView] invalid token")
            return self.ReturnSuccess({"error": "wrong token"})

        try:
            if 'date_str' in data:
                date_str = data['date_str']
            else:
                yesterday = datetime.utcnow() + timedelta(days=-1)
                date_str = yesterday.strftime("%Y%m%d")

            # Parse the date string
            start_day = datetime.strptime(date_str, "%Y%m%d")
            end_day = start_day + timedelta(days=1)

            # Fetch users created within the specified date range
            users = User.objects.filter(created_at__gte=start_day, created_at__lt=end_day)

            # Initialize counters
            total_cnt = len(users)
            invalid_cnt = 0
            empty_cnt = 0
            invalid_list = []

            for user in users:
                user_id = user.id

                # Calculate the sums for sms, call, and point records
                sms_cnt = SMSRecord.objects.filter(user_id=user_id, is_it=False).aggregate(cnt=Sum('point'))['cnt'] or 0
                call_cnt = CallRecord.objects.filter(user_id=user_id).aggregate(cnt=Sum('point'))['cnt'] or 0
                point_cnt = PointRecord.objects.filter(user_id=user_id, point__lt=0).aggregate(cnt=Sum('point'))[
                                'cnt'] or 0

                if sms_cnt == call_cnt == point_cnt:
                    empty_cnt += 1
                    continue

                if sms_cnt + call_cnt != abs(point_cnt):
                    invalid_cnt += 1
                    invalid_list.append(f"user:{user_id}, sms_cnt:{sms_cnt}, call_cnt:{call_cnt}, point:{point_cnt}")

            dic = {
                "total_cnt": total_cnt,
                "empty_cnt": empty_cnt,
                "not_empty_cnt": total_cnt - empty_cnt,
                "invalid_cnt": invalid_cnt,
            }
            SnapshotInvalidPoints(**dic).save()

            return self.ReturnSuccess(data={"dic": dic, "invalid_list": invalid_list})
        except Exception:
            logger.error(f"[SnapshotInvalidPointsView] failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

    def get_serial_data(self):
        data = {}

        sql = '''
        select invalid_cnt, DATE_FORMAT(created_at, '%Y-%m-%d') as d from Report_snapshotinvalidpoints order by created_at desc limit 31
        '''

        types = ["invalid_cnt"]
        for t in types:
            data[t] = self.__init_days_dict()

        with connection.cursor() as cur:
            cur.execute(sql)
            for (invalid_cnt, day) in cur.fetchall():
                data["invalid_cnt"][day] = invalid_cnt

        types = data.keys()
        for t in types:
            data[t] = self.__dict_2_list(data[t])

        return data
