import json

from django.conf import settings

from Common.views import SecPhoneView
from Feedback.models import UserTicket
from Order.tools.tool_order import OrderTool
from SecPhone.settings import logger


class GetVipConfig(SecPhoneView):

    def get(self, request):
        headers = self.GetHeaderInRequest(request)
        appid = str(headers.get('appid', 0))
        user_id = int(headers.get('userid', 0))

        config = self.__read_config_file()
        res = config.get(appid, None)

        if res is None:
            res = config.get("0")

        # 之前订阅过的用户，要把 is_free_trial 变成 false
        check_user_ids = [user_id]
        duplicate_infos = OrderTool.get_duplicate_info_list(user_id)
        for d in duplicate_infos:
            check_user_ids.append(d["duplicate_user_id"])

        for check_user_id in check_user_ids:
            if OrderTool.get_user_order_without_condition(check_user_id):
                for i in res["list"]:
                    i["is_free_trial"] = False
                logger.warning(f"[GetVipConfig] user:{user_id}, check_user_id:{check_user_id} already has order, "
                               f"remove is_free_trial, config:{res}, check_user_ids:{check_user_ids}")
                break
        else:
            logger.info(f"[GetVipConfig] user:{user_id}, empty order, config:{res}, check_user_ids:{check_user_ids}")

        return self.ReturnSuccess(res)

    def __read_config_file(self):
        file = settings.BASE_DIR + '/Config/vip.json'
        with open(file) as f:
            return json.loads(f.read())


class GetPointConfig(SecPhoneView):

    def get(self, request):
        headers = self.GetHeaderInRequest(request)
        appid = str(headers.get('appid', 0))

        config = self.__read_config_file()
        res = config.get(appid, None)

        if res is None:
            res = config.get("0")

        return self.ReturnSuccess(res)

    def __read_config_file(self):
        file = settings.BASE_DIR + '/Config/points.json'
        with open(file) as f:
            return json.loads(f.read())


class CheckHealth(SecPhoneView):

    def get(self, request):
        UserTicket.objects.first()
        logger.info(f"CheckHealth, success")
        return self.ReturnSuccess()
