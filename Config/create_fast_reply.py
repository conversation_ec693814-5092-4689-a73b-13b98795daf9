import json
import re


def parse_markdown(markdown):
    lines = markdown.strip().split('\n')
    result = []
    skip_lines = True
    current_parent = None
    pass_cnt = 0

    for line in lines:
        stripped_line = line.strip()

        if stripped_line == '<!-- tocstop -->':
            skip_lines = False
            continue

        if skip_lines:
            continue

        if pass_cnt > 0:
            pass_cnt -= 1
            continue

        if re.match(r'^##\s', stripped_line):
            current_level = 0
            current_parent = {'level': current_level, 'label': stripped_line[3:].strip(), 'children': []}
            result.append(current_parent)
        elif re.match(r'^###\s', stripped_line):
            current_level = 1
            current_parent['children'].append({'level': current_level, 'label': stripped_line[4:].strip()})
        elif stripped_line.startswith('```'):
            code_lines = []
            next_line_index = lines.index(line) + 1
            while next_line_index < len(lines) and not lines[next_line_index].startswith('```'):
                code_lines.append(lines[next_line_index])
                next_line_index += 1
            pass_cnt = len(code_lines) + 1  # 包括 最后的```
            current_parent['children'][-1]['code'] = '\n'.join(code_lines)

    return result


def parse_markdown_v2(markdown):
    lines = markdown.strip().split('\n')
    result = []
    current_level = 0
    current_parent = None
    skip_lines = True

    i = 0
    while i < len(lines):
        line = lines[i]
        stripped_line = line.strip()

        # mac
        # if stripped_line == '<!-- tocstop -->':
        #     skip_lines = False
        #     i += 1
        #     continue

        # windows
        if stripped_line == '<!-- TOC -->':
            skip_lines = False
            i += 1
            continue

        if skip_lines:
            i += 1
            continue

        if re.match(r'^##\s', stripped_line):
            current_level = 0
            current_parent = {'level': current_level, 'label': stripped_line[3:].strip(), 'children': []}
            result.append(current_parent)
        elif re.match(r'^###\s', stripped_line):
            current_level = 1
            current_parent['children'].append({'level': current_level, 'label': stripped_line[4:].strip()})
        elif re.match(r'^####\s', stripped_line):
            current_level = 2
            current_parent['children'].append({'level': current_level, 'label': stripped_line[5:].strip()})
        elif stripped_line.startswith('```'):
            code_lines = []
            i += 1
            while i < len(lines) and not lines[i].startswith('```'):
                code_lines.append(lines[i])
                i += 1
            i += 1  # 跳过 ```
            current_parent['children'][-1]['code'] = '\n'.join(code_lines)
            continue

        i += 1

    result.insert(0, {
        "level": 0,
        "label": '后门系列',
        "children": [{
            "level": 1,
            "label": '查看用户画像',
            "code": '后门用户',
        }, {
            "level": 1,
            "label": '查看最近短信（不带it）',
            "code": '后门短信(it=0)',
        }, {
            "level": 1,
            "label": '查看最近短信（纯it）',
            "code": '后门短信(it=1)',
        },  {
            "level": 1,
            "label": "恢复订阅1（随便点，如果它订阅更新延迟）",
            "code": "后门订阅"
        }, {
            "level": 1,
            "label": "恢复订阅2（随便点，如果它有两个设备）",
            "code": "后门恢复订阅"
        }, {
            "level": 1,
            "label": '一键换号',
            "code": '后门换号',
        }, {
            "level": 1,
            "label": '查看用户号码列表',
            "code": '号码列表',
        }, {
            "level": 1,
            "label": '查看各状态号码数量',
            "code": '后门各状态号码数量',
        }]
    })

    return result


if __name__ == '__main__':
    file_path = 'huashu.md'

    with open(file_path, 'r', encoding='utf-8') as file:
        markdown_data = file.read()

    parsed_data = parse_markdown_v2(markdown_data)
    json_data = json.dumps(parsed_data, ensure_ascii=False, indent=4)
    print(json_data)

    with open("./replylist.json", "w") as f:
        f.write(json_data)
