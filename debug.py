import base64
import time

import requests

from Common.util import Util
from SecPhone import settings


class DebugURL:

    def __init__(self, env):

        self.base_url = 'http://localhost:8000'
        self.user_email = "<EMAIL>"
        self.user_pw = "378d20480713fb84a75d91e7c3dfb420fe535aea0529aab40ae4604fff4efeb5"
        self.user_id = 194639  # 37947
        self.token = "c36c20d46f1e7f33dbb20b73bb3191ea"
        self.SECRETKEY = settings.SECRET_KEY
        self.DEFALUT_TOKEN = 'SeCPhONE-TOKEN-DEFAULT-(NxmFK)'

        if env.lower() == 'prd':
            self.base_url = 'http://xxx'
            self.token = '56f1f5b074a4a385af453043a1255185'

    def __gen_headers(self, has_user_info=True):

        header = {
            "x-userid": str(self.user_id),
            "x-token": self.token if has_user_info else self.DEFALUT_TOKEN,
            "x-timestamp": str(int(time.time() * 10 ** 6)),
            # 'x-appid': "0",
        }

        header['x-sign'] = Util.MD5Sum(header['x-timestamp'] + header['x-token'] + self.SECRETKEY)
        print(header)
        return header

    def SignUp(self, email, passwd):

        url = self.base_url + '/user/signup/'
        data = {"email": email, "password": passwd}
        r = requests.post(url, headers=self.__gen_headers(False), json=data)
        print(r.json())

    def SignIn(self):

        url = self.base_url + '/user/signin/'
        data = {"email": self.user_email, "password": self.user_pw}
        r = requests.post(url, headers=self.__gen_headers(False), json=data)
        print(r.json())

    def SearchNumber(self):

        url = self.base_url + '/number/search/'
        data = {
            "country": "US",  ## 号码所属国家，非必传，可选值：US/CA, 默认值US
            "area_code": "808",  ## 地区编号，非必传
            # "contains": "287",     ## 号码包含字段，非必传
            "page_number": 1  ## 页数，翻页用，非必传，默认1
        }
        r = requests.post(url, headers=self.__gen_headers(), json=data)
        print(r.json())

    def BuyNumber(self, number, days=3):

        url = self.base_url + '/number/buy/'
        data = {"number": number, "duration": days}
        r = requests.post(url, headers=self.__gen_headers(), json=data)
        print(r.content)
        print(r.json())

    def GetMyNumber(self):

        url = self.base_url + '/number/my/'
        r = requests.get(url, headers=self.__gen_headers())
        # print(r.content)
        print(r.json())

    def GetPoint(self):

        url = self.base_url + '/point/my/'
        r = requests.get(url, headers=self.__gen_headers())
        print(r.json())

    def AddPoint(self, point):

        url = self.base_url + '/point/add/'
        data = {"point": point}
        r = requests.post(url, headers=self.__gen_headers(), json=data)
        print(r.json())

    def SubPoint(self, unit, event):

        url = self.base_url + '/point/sub/'
        data = {"unit": unit, "event": event}
        r = requests.post(url, headers=self.__gen_headers(), json=data)
        print(r.json())

    def GetPointConfig(self):

        url = self.base_url + '/point/config/'
        r = requests.get(url, headers=self.__gen_headers())
        print(r.json())

    def GetCallToken(self):

        url = self.base_url + '/phone/access_token?userid=%s&token=%s' % (self.user_id, self.token)
        r = requests.get(url)
        print(r.content)

    def MakeCall(self, to):

        url = self.base_url + '/phone/call/'
        r = requests.post(url, headers=self.__gen_headers(), json={"to": to})
        print(r.content)

    def SendMMS(self, to, content, images):

        data = {}
        data['to'] = to
        data['files'] = []
        data['content'] = content
        for img in images:
            f = open(img, 'br')
            file_content = base64.b64encode(f.read()).decode('utf-8')
            file_format = 'png'
            data['files'].append({"content": file_content, "format": file_format})

        url = self.base_url + '/phone/sendmms/'
        r = requests.post(url, headers=self.__gen_headers(), json=data)
        print(r.content)

    def SendSMS(self, to, content):

        data = {}
        data['to'] = to
        data['content'] = content
        url = self.base_url + '/phone/sendsms/'
        r = requests.post(url, headers=self.__gen_headers(), json=data)
        print(r.content)


if __name__ == '__main__':
    debug = DebugURL('prd')
    # debug.SignUp('<EMAIL>', '123456')
    # debug.SignIn()
    # debug.SearchNumber()
    # debug.GetPoint()
    # debug.AddPoint(100)
    # debug.SubPoint(30, "call")
    # debug.SubPoint(10, "sms")
    #
    # debug.GetPoint()
    # debug.GetPoint()
    # debug.GetPoint()
    # debug.GetPoint()
    # debug.BuyNumber('+12056240841', 90)
    debug.GetMyNumber()
    # debug.SendMMS('+13363831493', 'mms from jon', ['/tmp/1.png'])
    # debug.SendSMS('+12054319603', 'sms test 22')
    # debug.GetPointConfig()
    # debug.GetCallToken()
    # debug.MakeCall("+8618612345678")
