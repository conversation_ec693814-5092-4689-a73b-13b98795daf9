{# templates/admin/test_sms.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h1>Test a Sms</h1>

    <form id="custom-form" method="post" action="{% url 'admin:test_sms' %}">
        {% csrf_token %}
        <div class="form-row">
            <br>
            <div class="form-group col-md-6">
                <label for="test_sms">Test Sms:</label>
                <textarea type="text" id="test_sms" name="test_sms" rows="10" cols="50"></textarea>
            </div>
            <br>
        </div>

        <!-- 添加复选框 -->
        <div class="form-group form-check">
            <input type="checkbox" class="form-check-input" id="use_fatpo_ai" name="use_fatpo_ai">
            <label class="form-check-label" for="use_fatpo_ai">是否走fatpoAI</label>
        </div>

        <button type="submit">Submit</button>
    </form>
    <div class="form-group mt-3">
        <label for="response-textbox">Response</label>
        <textarea id="response-textbox" class="form-control" readonly rows="30" cols="100"></textarea>
    </div>
</div>

<script>
    document.getElementById('custom-form').onsubmit = function (event) {
        event.preventDefault();
        var responseTextbox = document.getElementById('response-textbox');
        responseTextbox.value = ""; // 先清空内容

        var formData = new FormData(this);
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        }).then(response => response.json()).then(data => {
            responseTextbox.value = JSON.stringify(data, null, 2);
        }).catch(error => {
            responseTextbox.value = "Error: " + error;
        });
    };
</script>
{% endblock %}
