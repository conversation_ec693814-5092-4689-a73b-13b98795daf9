{# templates/admin/mock_received_sms.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h1>Search Telnyx Numbers</h1>
    <form id="custom-form" method="post" action="{% url 'admin:mock_received_sms' %}">
        {% csrf_token %}
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="user_id">Received UserId:</label>
                <input type="text" id="user_id" name="user_id">
            </div>
            <div class="form-group col-md-6">
                <label for="from_number">From Number:</label>
                <input type="text" id="from_number" name="from_number">
            </div>
            <br>
            <div class="form-group col-md-6">
                <label for="content">content:</label>
                <input type="text" id="content" name="content">
            </div>
            <br>
        </div>
        <button type="submit">Submit</button>
    </form>
    <div class="form-group mt-3">
        <label for="response-textbox">Response</label>
        <textarea id="response-textbox" class="form-control" readonly rows="20" cols="60"></textarea>
    </div>
</div>


<script>
    document.getElementById('custom-form').onsubmit = function (event) {
        event.preventDefault();
        var formData = new FormData(this);
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        }).then(response => response.json()).then(data => {
            var responseTextbox = document.getElementById('response-textbox');
            responseTextbox.value = JSON.stringify(data, null, 2);
        });
    };
</script>
{% endblock %}
