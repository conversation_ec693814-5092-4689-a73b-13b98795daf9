{# templates/admin/check_bad_sms.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h1>Check Bad Sms Every Day</h1>
    <form id="custom-form" method="post" action="{% url 'admin:check_bad_sms' %}">
        {% csrf_token %}
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="day">Day (YYYYMMDD):</label>
                <input type="text" id="day" name="day">
            </div>
        </div>
        <button type="submit">Submit</button>
    </form>

    <h2 class="mt-5">Parsed Data</h2>
    <table class="table table-striped mt-3" id="data-table">
        <thead>
        <tr>
            <th>Index</th>
            <th>Actions</th>
            <th>Category (cate)</th>
            <th>Size / ID</th>
            <th>User ID / Date</th>
            <th>Description</th>
        </tr>
        </thead>
        <tbody id="data-table-body">
        <!-- Dynamic rows will be inserted here -->
        </tbody>
    </table>
</div>

<script>
    // Automatically fill yesterday's date in the input field
    document.addEventListener("DOMContentLoaded", function () {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const formattedDate = yesterday.toISOString().slice(0, 10).replace(/-/g, '');
        document.getElementById('day').value = formattedDate;
    });

    document.getElementById('custom-form').onsubmit = function (event) {
        event.preventDefault();
        var formData = new FormData(this);
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        }).then(response => response.json()).then(data => {
            var tableBody = document.getElementById('data-table-body');
            tableBody.innerHTML = '';

            let globalIndex = 1;

            data.bad_sms.forEach(item => {
                item.details.forEach(detail => {
                    var row = document.createElement('tr');
                    var detailParts = detail.split('___');

                    // Add index
                    var indexCell = document.createElement('td');
                    indexCell.textContent = globalIndex++;
                    row.appendChild(indexCell);

                    // Add delete button
                    var actionCell = document.createElement('td');
                    var deleteButton = document.createElement('button');
                    deleteButton.textContent = 'Delete';
                    deleteButton.className = 'btn btn-danger btn-sm';
                    deleteButton.onclick = function () {
                        const userId = detailParts[2];
                        const rowsToDelete = Array.from(document.querySelectorAll('#data-table-body tr'))
                            .filter(r => r.cells[4].textContent.includes(userId));

                        const deletedCount = rowsToDelete.length;
                        rowsToDelete.forEach(r => r.remove());
                        reindexTable();

                        alert(`Deleted ${deletedCount} rows for User ID: ${userId}`);
                    };
                    actionCell.appendChild(deleteButton);
                    row.appendChild(actionCell);

                    // Add category
                    var cateCell = document.createElement('td');
                    cateCell.textContent = item.cate;
                    row.appendChild(cateCell);

                    // Add size and ID combined
                    var sizeCell = document.createElement('td');
                    sizeCell.textContent = `${item.size} / ${detailParts[0]}`;
                    row.appendChild(sizeCell);

                    // Add User ID and Date combined
                    var userIdDateCell = document.createElement('td');
                    userIdDateCell.innerHTML = `${detailParts[2]}<br><br>${detailParts[3]}`;

                    // Add date coloring logic
                    const oneYearAgo = new Date();
                    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
                    const rowDate = new Date(detailParts[3]);
                    if (rowDate < oneYearAgo) {
                        userIdDateCell.style.color = 'red';
                    }

                    row.appendChild(userIdDateCell);

                    // Add Description
                    var descriptionCell = document.createElement('td');
                    descriptionCell.textContent = detailParts[1];
                    row.appendChild(descriptionCell);

                    tableBody.appendChild(row);
                });
            });
        }).catch(error => {
            console.error('Error:', error);
        });
    };

    // Function to reindex the table after deletion
    function reindexTable() {
        const rows = Array.from(document.getElementById('data-table-body').querySelectorAll('tr'));
        rows.forEach((row, index) => {
            row.cells[0].textContent = index + 1; // Update the index column
        });
    }
</script>
{% endblock %}
