{# templates/admin/push_a_message.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h1>Push a Message</h1>
    <button id="ban-scam-text-btn" type="button" class="btn btn-warning mt-3">加载诈骗</button>
    <button id="ban-threat-text-btn" type="button" class="btn btn-danger mt-3">加载威胁</button>
    <button id="ban-sex-text-btn" type="button" class="btn btn-sex mt-3">加载色情</button>
    <button id="ban-reject-number-text-btn" type="button" class="btn btn-reject-number mt-3">加载婉拒换号</button>
    <button id="ban-money-text-btn" type="button" class="btn btn-money mt-3">加载不要发送钱</button>
    <button id="help-change-number-text-btn" type="button" class="btn help-change-number-text mt-3">加载劝他换号</button>

    <form id="custom-form" method="post" action="{% url 'admin:push_message' %}">
        {% csrf_token %}
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="userid">User Id:</label>
                <input type="text" id="userid" name="userid">
            </div>
            <br>
            <div class="form-group col-md-6">
                <label for="push_content">Push Content:</label>
                <textarea type="text" id="push_content" name="push_content" rows="10" cols="50"></textarea>
            </div>
            <br>
        </div>
        <button type="submit">Submit</button>
    </form>
    <div class="form-group mt-3">
        <label for="response-textbox">Response</label>
        <textarea id="response-textbox" class="form-control" readonly rows="20" cols="60"></textarea>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 自动填充 "诈骗通知"
        document.getElementById('ban-scam-text-btn').addEventListener('click', function () {
            const contentTextarea = document.getElementById('push_content');
            contentTextarea.value = `Dear User,\n\nWe regret to inform you that your number has been reported by the telecom operator for alleged involvement in fraudulent or scam activities. As a result, the number has been reclaimed.\n\nWe sincerely apologize for any inconvenience caused. Should there be any updates, we will notify you promptly.\n\nIf you have any questions or require further assistance, please feel free to contact our customer support team.\n\nThank you for your understanding and cooperation.`;
        });

        // 自动填充 "威胁通知"
        document.getElementById('ban-threat-text-btn').addEventListener('click', function () {
            const contentTextarea = document.getElementById('push_content');
            contentTextarea.value = `Dear User,\n\nWe have received a report from a telecommunications provider alleging that your account has been involved in threatening behavior. Such actions are a violation of our app's community guidelines and terms of use.\n\nAs a result, we have taken action to suspend your account in accordance with our user policy.\n\nIf you have any questions or believe this action was taken in error, please feel free to contact our customer support team for further assistance.\n\nThank you for your understanding.`;
        });

        // 自动填充 "色情通知"
        document.getElementById('ban-sex-text-btn').addEventListener('click', function () {
            const contentTextarea = document.getElementById('push_content');
            contentTextarea.value = `Dear User,

We have detected that your account has been involved in activities promoting obscene content or solicitation, which violates our app's community guidelines and terms of use.

As such, your account has been suspended in accordance with our user policy. This action is taken to maintain a safe and respectful environment for all users.

If you believe this suspension was made in error or if you have any questions, please contact our customer support team for assistance.

Thank you for your cooperation.`;
        });

        // 自动填充 "婉拒换号"
        document.getElementById('ban-reject-number-text-btn').addEventListener('click', function () {
            const contentTextarea = document.getElementById('push_content');
            contentTextarea.value = `Dear User,
I have submitted a request to change your number. However, due to a shortage in current number inventory, we are coordinating with the procurement department to resolve the issue. I will keep you updated on any progress.

You may also consider submitting another ticket within the next two days to help expedite the process.`;
        });

        //  不要钱
        document.getElementById('ban-money-text-btn').addEventListener('click', function () {

            const contentTextarea = document.getElementById('push_content');
            contentTextarea.value = `Dear User,

Please refrain from sending messages involving money, as this may be flagged as potential financial fraud. Repeated violations may result in your account being temporarily suspended by our AI system.

If you believe this suspension was made in error or if you have any questions, please contact our customer support team for assistance.

Thank you for your cooperation.`;
        });


        //  帮你换个号
        document.getElementById('help-change-number-text-btn').addEventListener('click', function () {

            const contentTextarea = document.getElementById('push_content');
            contentTextarea.value = `Dear User,

Hello, the telecommunications provider has reported an issue with your current number. There may be communication problems in the future. Our engineers will conduct emergency repairs for you and now help you switch to a number that can be used normally.

We're truly sorry for the trouble and inconvenience caused.`;
        });

    });

    document.getElementById('custom-form').onsubmit = function (event) {
        event.preventDefault();
        var formData = new FormData(this);
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        }).then(response => response.json()).then(data => {
            var responseTextbox = document.getElementById('response-textbox');
            responseTextbox.value = JSON.stringify(data, null, 2);
        });
    };
</script>
{% endblock %}
