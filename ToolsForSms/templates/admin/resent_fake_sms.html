{% extends "admin/change_list.html" %}
{% load static %}

{% block content %}
<div class="container">
    <div class="filter-box" style="margin-bottom: 15px;">
        <input type="number" id="replyCount" placeholder="输入回复数" style="margin-right: 10px;">
        <button onclick="filterResults()" style="padding: 5px 10px;">过滤</button>
    </div>

    <div class="filter-box" style="margin-bottom: 15px;">
        <input type="text" id="reviewed_userid" placeholder="输入干掉的userid" style="margin-right: 10px;">
        <button onclick="filterResultsByUserId()" style="padding: 5px 10px;">过滤</button>
        <button onclick="clearUserId()" style="padding: 5px 10px;">清空</button>
    </div>

    <div class="filter-box" style="margin-bottom: 15px;">
        <input type="text" id="select_user_id" placeholder="输入user_id" style="margin-right: 10px;">
        <button onclick="selectUserId()" style="padding: 5px 10px;">选择</button>
        <button onclick="clearSelectUserId()" style="padding: 5px 10px;">清空</button>
    </div>

    <div class="filter-box" style="margin-bottom: 15px;">
        <input type="text" id="reviewed_content" placeholder="输入干掉的content" style="margin-right: 10px;">
        <button onclick="filterResultsByContent()" style="padding: 5px 10px;">过滤</button>
        <button onclick="clearUserId()" style="padding: 5px 10px;">清空</button>
    </div>


    {% if total %}
    <div class="total-summary" style="margin-bottom: 15px;">
        <strong>Total:</strong> {{ total }}
    </div>
    {% endif %}
</div>

{{ block.super }}

<script>
    // 清空 User ID 输入框
    function clearUserId() {
        document.getElementById('reviewed_userid').value = '';
        document.getElementById('reviewed_content').value = '';
    };

    document.addEventListener('DOMContentLoaded', function () {
        // 从模板变量中获取 total
        const total = {{ total|default:0 }};
    });

    document.addEventListener('DOMContentLoaded', function () {
        // 从模板变量中获取 total
        const total = {{ total|default:0 }};
    });


  // 获取CSRF令牌
  function getCsrfToken() {
      return document.querySelector('[name=csrfmiddlewaretoken]').value;
  }

  // 安全地显示对话框
  function showModal(content) {
      const existingModal = document.getElementById('customModal');
      if (existingModal) existingModal.remove();

      const modal = document.createElement('div');
      modal.id = 'customModal';
      modal.style = `
          position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
          background-color: #fff; border-radius: 8px; padding: 20px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); z-index: 1000;
          width: 80%; max-height: 80vh; overflow-y: auto;
      `;
      modal.innerHTML = `
          ${content}
          <button id="closeModal" style="margin-top: 15px; padding: 8px 16px; background: #007BFF; color: #fff; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
      `;
      document.body.appendChild(modal);
      document.getElementById('closeModal').addEventListener('click', () => modal.remove());
  }

  // 处理AJAX请求
  function handleAjaxRequest(url, method = 'GET', body = null, successCallback, errorCallback) {
      fetch(url, {
          method,
          headers: {
              'X-CSRFToken': getCsrfToken(),
              'Content-Type': 'application/json'
          },
          body: body ? JSON.stringify(body) : null
      })
      .then(response => response.json())
      .then(data => {
          if (data.status === 'success') {
              successCallback(data);
          } else {
              alert(data.message || errorCallback);
          }
      })
      .catch(() => alert(errorCallback));
  }

    function getQueryParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    };

    // 过滤结果
    function filterResults() {
        const replyCount = document.getElementById('replyCount').value.trim();

        if (!replyCount || isNaN(replyCount) || parseInt(replyCount) < 0) {
            alert('请输入有效的回复数');
            return;
        }

        // 获取 URL 参数中的 hours（如果存在）
        let hours = getQueryParam('hours');
        if (!hours) {
            hours = 2; // 如果 URL 中没有 hours，使用默认值 2
        }

        // 拼接带有 hours 参数的 URL
        const url = `/fatpoadmin/ToolsForSms/smsfakerecordproxy/filter_by_replies/${replyCount}/?hours=${hours}`;

        handleAjaxRequest(url, 'GET', null,
            () => location.reload(),
            '过滤失败，请稍后重试！'
        );
    }

  // 过滤结果
  function filterResultsByUserId() {
      const user_id = document.getElementById('reviewed_userid').value.replace(/^\s+|\s+$/g, ''); // 手动去除空格
       if (!user_id || isNaN(user_id) || parseInt(user_id) < 0) {
           alert('请输入有效的userid');
           return;
       }

        // 获取 URL 参数中的 hours（如果存在）
        let hours = getQueryParam('hours');
        if (!hours) {
            hours = 24; // 如果 URL 中没有 hours，使用默认值 24
        }

        handleAjaxRequest(`/fatpoadmin/ToolsForSms/smsfakerecordproxy/filter_replies_by_userid/${user_id}/?hours=${hours}`, 'GET', null,
          () => location.reload(),
          '过滤失败，请稍后重试！'
        );

        clearUserId();
    };

     // 过滤结果
      function filterResultsByContent() {
          const content = document.getElementById('reviewed_content').value; // 手动去除空格
           if (!content) {
               alert('请输入有效的content');
               return;
           }

            // 获取 URL 参数中的 hours（如果存在）
            let hours = getQueryParam('hours');
            if (!hours) {
                hours = 24; // 如果 URL 中没有 hours，使用默认值 24
            }

            handleAjaxRequest(`/fatpoadmin/ToolsForSms/smsfakerecordproxy/filter_replies_by_content/${content}/?hours=${hours}`, 'GET', null,
              () => location.reload(),
              '过滤失败，请稍后重试！'
            );

            clearUserId();
        };

    // 选择user_id并添加到URL
    function selectUserId() {
        const user_id = document.getElementById('select_user_id').value.trim();
        if (!user_id) {
            alert('请输入有效的user_id');
            return;
        }

        // 获取当前URL参数中的hours（如果存在）
        let hours = getQueryParam('hours');
        if (!hours) {
            hours = 2; // 默认值
        }

        // 更新URL并刷新页面
        const url = new URL(window.location.href);
        url.searchParams.set('user_id', user_id);
        url.searchParams.set('hours', hours);
        window.location.href = url.toString();
    }

    // 清空select user_id ID输入框
    function clearSelectUserId() {
        document.getElementById('select_user_id').value = '';
        // 清除URL中的user_id参数
        const url = new URL(window.location.href);
        url.searchParams.delete('user_id');
        // 刷新页面
        window.location.href = url.toString();
    }

    // 页面加载时，从URL参数中获取user_id并填充到输入框
    document.addEventListener('DOMContentLoaded', function() {
        const userId = getQueryParam('user_id');
        if (userId) {
            document.getElementById('select_user_id').value = userId;
        }
    });

  // 更新总数
  function updateTotal() {
      const totalElement = document.querySelector('.total-summary strong');
      if (totalElement) {
          let total = parseInt(totalElement.nextSibling.textContent.trim(), 10) - 1;
          totalElement.nextSibling.textContent = ` ${total}`;
          if (total <= 0) {
             clearSelectUserId(); // 新增逻辑：总数为 0 时清空 URL 中的 user_id
            // location.reload();
          }
      }
  }

  // 处理通用动作
  function handleAction(url, id, successMessage, errorMessage) {
      handleAjaxRequest(url, 'POST', { id },
          () => {
              const row = document.querySelector(`tr input[value='${id}']`)?.closest('tr');
              if (row) row.remove();
              updateTotal();
          },
          errorMessage
      );
  }

  // 具体动作函数
  function resendFakeSms(id) {
      handleAction(`/fatpoadmin/ToolsForSms/smsfakerecordproxy/resend_fake_sms/${id}/`, id, '重发成功', '重发失败，请稍后重试！');
  }

 function resendOriginalSms(id) {
      if (confirm('确定要重发原始短信吗？')) {
          handleAction(`/fatpoadmin/ToolsForSms/smsfakerecordproxy/resend_fake_sms_original/${id}/`, id, '重发原始成功', '重发原始失败，请稍后重试！');
      }
 }

  function reviewFakeSms(id) {
      handleAction(`/fatpoadmin/ToolsForSms/smsfakerecordproxy/review_fake_sms/${id}/`, id, '审核成功', '审核失败，请稍后重试！');
  }

  function showConversation(id) {
      handleAjaxRequest(`/fatpoadmin/ToolsForSms/smsfakerecordproxy/show_conversation_action/${id}/`, 'GET', null,
          data => {
              const conversationHtml = `
                  <h3>会话详情</h3>
                  <table border="1" style="width: 100%; text-align: left;">
                      <tr><th>时间</th><th>方向</th><th>状态</th><th>内容</th></tr>
                      ${data.data.map(item => `
                          <tr class="${item.is_hl ? 'highlight-row' : ''}">
                              <td>${escapeHtml(item.ts)}</td>
                              <td>${escapeHtml(item.direction)}</td>
                              <td>${escapeHtml(item.status)}</td>
                              <td>${escapeHtml(item.content)}<br>${escapeHtml(item.filtered_content)}</td>
                          </tr>
                      `).join('')}
                  </table>
              `;
              showModal(conversationHtml);
          },
          '获取会话失败，请稍后重试！'
      );
  }

  // 显示大图
  function showImage(imageUrl) {
      const modal = document.createElement('div');
      modal.id = 'imageModal';
      modal.style = `
          display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%;
          background-color: rgba(0,0,0,0.9); z-index: 1000; text-align: center;
      `;
      modal.innerHTML = `
          <img src="${imageUrl}" style="max-width: 90%; max-height: 90%; margin-top: 5%; cursor: pointer;">
          <span id="closeImageModal" style="position: absolute; top: 15px; right: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; cursor: pointer;">&times;</span>
      `;
      document.body.appendChild(modal);
      modal.style.display = 'block';

      document.getElementById('closeImageModal').addEventListener('click', () => modal.remove());
      modal.querySelector('img').addEventListener('click', () => modal.remove());
  }

  // HTML转义函数
  function escapeHtml(unsafe) {
      return unsafe
          .replace(/&/g, "&amp;")
          .replace(/</g, "&lt;")
          .replace(/>/g, "&gt;")
          .replace(/"/g, "&quot;")
          .replace(/'/g, "&#039;");
  }

  const style = document.createElement('style');
  style.innerHTML = `.highlight-row { background-color: #ffcccb !important; }`;
  document.head.appendChild(style);
</script>
{% endblock %}
