from django.db import models

from Call.models import SMSRecord


class Tool(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    objects = models.Manager()


class PushMessageTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Push a message'
        verbose_name_plural = 'Push a message'


class TestSmsTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Test a message'
        verbose_name_plural = 'Test a message'


class CustomerServiceTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'AI - Customer Service'
        verbose_name_plural = 'AI - Customer Service'


class SmsFakeRecordProxy(SMSRecord):
    class Meta:
        proxy = True
        verbose_name = 'A - Sms - Admin resent fake sms'
        verbose_name_plural = 'A - Sms - Admin resent fake sms'


class MockReceivedSmsTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'AI - <PERSON><PERSON> Mock Received SMS'
        verbose_name_plural = 'AI - <PERSON><PERSON> Mock Received SMS'


class CheckBadSmsTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'A - Check Bad Sms'
        verbose_name_plural = 'A - Check Bad Sms'
