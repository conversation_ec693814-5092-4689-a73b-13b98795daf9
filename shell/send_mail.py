# -*- coding: utf-8 -*-
import smtplib
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


# 邮箱定义
smtp_server = 'smtp.qq.com'
smtp_port = 587
from_addr = '<EMAIL>'
password = 'geodowcfakrsbhhi'
to_addr = ['<EMAIL>']
# 邮件对象
msg = MIMEMultipart()
msg['From'] = _format_addr('发件人 <%s>' % from_addr)
msg['To'] = _format_addr('收件人 <%s>' % to_addr)
msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}网盾Warning:单ip请求次数异常', 'utf-8').encode()
# 获取系统中要发送的文本内容
txt = ""
with open('/var/log/nginx/log_ip_top10', 'r') as f:
    lines = f.readlines()
    txt += '</br> '.join(lines)
# 邮件正文是MIMEText:
html = '<html><body><h2>一分钟内单ip请求次数超过阀值</h2>' + \
       '<p>ip:请求数\分\n :%s</p>' % txt + \
       '</body></html>'
msg.attach(MIMEText(html, 'html', 'utf-8'))
server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
server.login(from_addr, password)
server.sendmail(from_addr, to_addr, msg.as_string())
server.quit()
