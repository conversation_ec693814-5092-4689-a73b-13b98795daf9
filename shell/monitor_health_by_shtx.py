import json
import logging
import smtplib
import socket
import ssl
import time
import traceback
from datetime import datetime, timezone
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import requests

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', encoding='utf-8')

MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 5  # 重试延迟，单位：秒
TIMEOUT = (60, 60)  # 连接超时与读取超时 (连接30秒，读取60秒)
FAIL_WINDOW_TIME = 3 * 60  # 失败时间窗口（3分钟），单位秒
CHECK_INTERVAL = 60  # 每分钟检查一次
STATE_FILE = "health_check_state.json"  # 存储检查状态的文件

# 可以把 URL 列表移到配置文件中或从环境变量读取
urls = [
    "https://phone.zehougroup.xyz/config/checkHealth/",
    "https://phone2.zehougroup.xyz/config/checkHealth/",
    "https://api.v3phone.xyz/v3sim/config/checkHealth/",
    "https://api.gongbaojiding.xyz/lpapi/config/checkHealth",
    "https://ai.zehoutech.com/smartai/config/checkHealth",
]


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(message_list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>', '<EMAIL>', '<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr(f'发件人 <{from_addr}>')
        msg['To'] = ", ".join(to_addr)  # 修正收件人格式
        msg['Subject'] = Header(f'电话健康检查报警:{len(message_list)}！！！',
                                '                                    utf-8').encode()

        txt = "告警详情 <br>"
        for t in message_list:
            txt += f"{t} <br>"

        msg.attach(MIMEText(txt, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()  # 启用 TLS
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception as e:
        logging.error("发送邮件失败", exc_info=True)
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


def check_health(url, retries=MAX_RETRIES):
    is_server_healthy, err_code = check_server_health(url, retries=retries)
    if not is_server_healthy:
        logging.error(f"{url} server is not healthy: {err_code}")
        return False, err_code

    domain = get_domain_from_url(url)
    is_https_health, err_code = check_cert_expiry(domain, 443, 30)
    if not is_https_health:
        logging.error(f"{domain} SSL certificate is not healthy: {err_code}")
        return False, err_code

    return True, None


def check_server_health(url, retries=MAX_RETRIES):
    attempt = 0
    while attempt < retries:
        attempt += 1
        try:
            logging.info(f"Checking {url}, attempt {attempt}/{retries}")
            response = requests.get(url, timeout=30)
            response.raise_for_status()  # 如果响应状态码不是 2xx，会抛出异常
            rsp = response.json()
            print(response.content)
            if rsp.get("err_code") != 0:
                return False, rsp.get("err_msg", "Unknown error")
            return True, response.status_code
        except requests.exceptions.Timeout:
            logging.warning(f"Timeout occurred while checking {url}. Retrying...                                    ")
            time.sleep(RETRY_DELAY)  # 等待一段时间再重试
        except requests.exceptions.RequestException as e:
            return False, f"Request error: {str(e)}"
    return False, "Exceeded maximum retries"


def get_domain_from_url(url):
    """新增：从URL中提取域名（用于证书检查）"""
    from urllib.parse import urlparse
    parsed_url = urlparse(url)
    return parsed_url.netloc  # 提取域名（如：phone.zehougroup.xyz）


def check_cert_expiry(domain, port=443, CERT_EXPIRE_THRESHOLD=100, max_retries=3, retry_delay=2):
    """检查指定域名的HTTPS证书到期时间，内部重试max_retries次，全部失败才算最终失败"""
    for attempt in range(1, max_retries + 1):
        try:
            logging.info(f"检查证书到期时间（第{attempt}/{max_retries}次）：{domain}:{port}")
            # 建立SSL连接并获取证书
            context = ssl.create_default_context()
            with socket.create_connection((domain, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssl_sock:
                    cert = ssl_sock.getpeercert()  # 获取证书信息（字典格式）

            # 解析证书到期时间（证书时间为UTC时区，需转换为本地时间）
            cert_expire_utc = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
            cert_expire_utc = cert_expire_utc.replace(tzinfo=timezone.utc)  # 标记UTC时区
            cert_expire_local = cert_expire_utc.astimezone()  # 转换为本地时区

            # 计算距离到期的天数
            now_local = datetime.now(timezone.utc).astimezone()  # 当前本地时间
            days_until_expire = (cert_expire_local - now_local).days

            # 格式化返回信息
            expire_str = cert_expire_local.strftime('%Y-%m-%d %H:%M:%S')
            status_msg = f"证书有效期：{days_until_expire}天（到期时间：{expire_str}）"

            # 检查是否触发告警阈值
            if days_until_expire < 0:
                return False, f"⚠️ {domain} 证书已过期！{status_msg}"
            elif days_until_expire < CERT_EXPIRE_THRESHOLD:
                return False, f"⚠️ {domain} 证书即将到期（{days_until_expire}天），请及时更新！{status_msg}"
            else:
                return True, f"{domain} 证书正常 {status_msg}"

        except Exception as e:
            # 本次尝试失败
            logging.warning(f"第{attempt}/{max_retries}次检查失败：{str(e)}")
            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries:
                time.sleep(retry_delay)

    # 所有重试都失败，返回最终失败结果
    return False, f"❌ {domain} 经过{max_retries}次重试后仍检查失败，请检查域名或网络"


def load_state():
    """从文件加载失败状态"""
    try:
        with open(STATE_FILE, "r") as f:
            state = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        state = {}
    return state


def save_state(state):
    """保存状态到文件"""
    with open(STATE_FILE, "w") as f:
        json.dump(state, f)


def track_failures(url, is_healthy, state):
    """跟踪每个 URL 的失败时间戳，如果连续 3 次失败，触发告警。"""
    current_time = time.time()

    if url not in state:
        state[url] = []

    # 如果健康检查成功，清空失败记录
    if is_healthy:
        state[url] = []
    else:
        # 如果失败，将当前时间戳添加到失败时间窗口
        state[url].append(current_time)

    # 清除超过 FAIL_WINDOW_TIME 的过期记录
    state[url] = [t for t in state[url] if current_time - t <= FAIL_WINDOW_TIME]

    # 如果连续失败次数达到 6 次，触发告警
    if len(state[url]) >= 6:
        return True
    return False


if __name__ == '__main__':
    state = load_state()  # 加载状态
    err_msg_list = []

    for url in urls:
        try:
            is_healthy, message = check_health(url)
            if is_healthy:
                logging.info(f"{url} is healthy")
            else:
                logging.error(f"{url} is not healthy. Error: {message}")
                err_msg_list.append(f"{url} is not healthy. Error: {message}")
                # 检查是否连续失败达到阈值
                if track_failures(url, is_healthy=False, state=state):
                    err_msg_list.append(f"{url} 连续失败达到 3 次，已触发告警。")
        except Exception as e:
            logging.error(f"{url} 检查失败: {str(e)}", exc_info=True)

    # 如果有失败记录，发送邮件
    if err_msg_list:
        send_mail(err_msg_list)

    # 保存更新后的状态
    save_state(state)
