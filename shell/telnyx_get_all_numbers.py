import time

import telnyx

if __name__ == '__main__':
    # 设置Telnyx API认证信息
    telnyx.api_key = "**********************************************************"
    api_key = telnyx.api_key
    numbers_set = set()
    for i in range(20):
        numbers = telnyx.PhoneNumber.list(page={"number": i + 1, "size": 100})
        print("####")
        print(numbers)

        if len(numbers) == 0 or not numbers or ("data" in numbers and len(numbers['data']) == 0):
            break
        for index, n in enumerate(numbers):
            print(index, n)
            numbers_set.add(n['phone_number'])
        print("number set size:", len(numbers_set))
        time.sleep(1)
        break
