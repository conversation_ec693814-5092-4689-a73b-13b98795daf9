import csv

if __name__ == '__main__':

    rows = []
    with open("/Users/<USER>/Desktop/1.txt") as f:
        while True:
            text = f.readline()
            if not text:
                break
            print(text)
            result = text.split("[SmsReviewWangyiManualCallback]")[1].split(",")[0]
            suggestion = text.split("[SmsReviewWangyiManualCallback]")[1].split(",")[1].split("-")[0]
            task_id = text.split("[SmsReviewWangyiManualCallback]")[1].split(",")[1].split("-")[1]
            if "ERROR" in text:
                timestamp = text.split("][smsviews.py]")[0].split("[ERROR][")[1]
            else:
                timestamp = text.split("][smsviews.py]")[0].split("[WARNING][")[1]
            if "origin_content=" in text:
                content = text.split("origin_content=")[1]
            else:
                content = text.split("filtered_content:")[1]

            rows.append([result, suggestion, task_id, content, timestamp])

    # Write the extracted information to a CSV file
    csv_filename = "audit_logs.csv"
    with open(csv_filename, "w", newline="") as out_file:
        writer = csv.writer(out_file)
        writer.writerow(["Result", "Suggestion", "Task ID", "Content", "Timestamp"])
        writer.writerows(rows)

    print(f"CSV file '{csv_filename}' has been created successfully.")
