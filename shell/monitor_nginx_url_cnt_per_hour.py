# coding=utf-8
import datetime
import smtplib
import subprocess
import sys
import traceback
from collections import defaultdict
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_last_hour_str() -> str:
    # 上一个小时
    dt = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=-1)
    t1 = dt.strftime("%d/%b/%Y:%H:%M:%S")
    t2 = t1.split(":")
    return ':'.join(t2[0:2])


def get_access_log() -> list:
    hour_str = get_last_hour_str()
    cmd = f'cat /var/log/nginx/access.log  | grep {hour_str}'
    print(f"cmd1: {cmd}")

    output, error = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    lines1 = output.decode("utf-8").split("\n")
    return lines1


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(url_cnt_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        # header
        default_header = Header(f'{env_config["EMAIL_PREFIX"]}一小时接口访问量统计', 'utf-8').encode()

        # 判断是否需要进一步报警
        is_need_more_alert = False

        html = f'<html><body><h2>一小时接口访问量统计：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
        <table border="1">
        <caption>按照请求量排序</caption>
         <tr>
            <th>接口</th>
            <th>访问量</th>
            
            <th>upstream_response_time平均耗时</th>
            <th>upstream_response_time最大耗时</th>
            
            <th>request_times平均耗时</th>
            <th>request_times最大耗时</th>

            <th>upstream_connect_time平均耗时</th>
            <th>upstream_connect_time最大耗时</th>

            <th>upstream_header_time平均耗时</th>
            <th>upstream_header_time最大耗时</th>
        </tr>
        """

        alert_url_list = ['user', 'number', 'config', 'order', 'phone', 'point', 'report', 'feedback']

        for url_cnt_item in url_cnt_list:
            url = url_cnt_item[0]

            #  出现这个说明我们的图片有问题
            if url == '404 page for images' and url_cnt_item[1] > 0:
                is_need_more_alert = True
                break

            # 不是所有的接口都要 alert，有些是爬虫接口、前端资源接口
            upstream_response_time = url_cnt_item[8]
            for i in alert_url_list:
                if i in url:
                    # 这3个接口就是很慢
                    if '/report/sum' in url:
                        if upstream_response_time >= 5:
                            is_need_more_alert = True
                            break
                    elif '/phone/sendmms/' in url:
                        if upstream_response_time >= 4:
                            is_need_more_alert = True
                            break
                    elif '/order/cert/' in url:
                        if upstream_response_time >= 3:
                            is_need_more_alert = True
                            break
                    else:
                        if upstream_response_time >= 1:
                            is_need_more_alert = True
                            break

            html += f"""
            <tr>
                <td>{url_cnt_item[0][0:50]}</td>
                <td>{url_cnt_item[1]}</td>
                
                <td>{url_cnt_item[8]}</td>
                <td>{url_cnt_item[9]}</td>
                
                <td>{url_cnt_item[2]}</td>
                <td>{url_cnt_item[3]}</td>
                <td>{url_cnt_item[4]}</td>
                <td>{url_cnt_item[5]}</td>
                <td>{url_cnt_item[6]}</td>
                <td>{url_cnt_item[7]}</td>
            </tr>
            """

        if is_need_more_alert:
            alert_header = Header('[!!!有问题!!!]一小时接口访问量统计 ！！！', 'utf-8').encode()
            msg['Subject'] = alert_header

            # 增加额外一个统计纬度
            url_cnt_list = sorted(url_cnt_list, key=lambda k: k[8], reverse=True)
            html += """
            </table>
            <table border="1">
            <caption>按照整体平均耗时排序</caption>
             <tr>
                <th>接口</th>
                <th>访问量</th>
                
                <th>upstream_response_time平均耗时</th>
                <th>upstream_response_time最大耗时</th>
                
                <th>request_times平均耗时</th>
                <th>request_times最大耗时</th>

                <th>upstream_connect_time平均耗时</th>
                <th>upstream_connect_time最大耗时</th>

                <th>upstream_header_time平均耗时</th>
                <th>upstream_header_time最大耗时</th>

            </tr>
            """

            for url_cnt_item in url_cnt_list:
                html += f"""
                       <tr>
                           <td>{url_cnt_item[0][0:50]}</td>
                           <td>{url_cnt_item[1]}</td>
                            
                           <td>{url_cnt_item[8]}</td>
                           <td>{url_cnt_item[9]}</td>
                           
                           <td>{url_cnt_item[2]}</td>
                           <td>{url_cnt_item[3]}</td>
                           <td>{url_cnt_item[4]}</td>
                           <td>{url_cnt_item[5]}</td>
                           <td>{url_cnt_item[6]}</td>
                           <td>{url_cnt_item[7]}</td>
                       </tr>
                       """
        else:
            msg['Subject'] = default_header

        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def work():
    """
    3.81.94.218 - - [19/Oct/2021:08:48:18 +0000] POST /phone/incoming/sms/ HTTP/1.1 "200" 0 "-" "TwilioProxy/1.1" "-"  0.000 3.440 3.440 3.448

    四个时间： $upstream_connect_time $upstream_header_time  $upstream_response_time $request_time';

    $upstream_connect_time 是建立连接的时间
    $upstream_header_time 从建立连接到发送第一个响应头字节的时间
    $request_times 是从请求到建立连接到发送完最后一个内容字节的时间
    $upstream_response_time 是从建立连接到发送完最后一个内容字节的时间,这个是我们需要关注的,因为客户端的请求和客户所在网络有关
    """
    lines = get_access_log()
    url_cnt = defaultdict(int)
    total_request_time = defaultdict(float)
    total_upstream_response_time = defaultdict(float)
    total_upstream_connect_time = defaultdict(float)
    total_upstream_header_time = defaultdict(float)

    max_request_time_map = defaultdict(float)
    max_upstream_response_time = defaultdict(float)
    max_upstream_connect_time = defaultdict(float)
    max_upstream_header_time = defaultdict(float)

    cnt_for_404 = 0
    cnt_for_404_image = 0
    cnt_for_403 = 0

    for line in lines:
        if not line:
            continue
        a = line.split()
        url = a[6].split("?")[0]

        # 不合法的url不要管了
        if a[8] == '"404"':
            cnt_for_404 += 1
            if "/uploads/20" in url:
                cnt_for_404_image += 1
            continue

        # 不合法的url不要管了
        if a[8] == '"403"':
            cnt_for_403 += 1
            continue

        url_cnt[url] += 1

        # 4个时间字符串
        upstream_connect_time = a[-4]
        upstream_header_time = a[-3]
        upstream_response_time = a[-2]
        request_time = a[-1]
        try:
            if request_time.replace('.', '', 1).isdigit():
                tmp_request_time = float(request_time)
                total_request_time[url] += tmp_request_time

                # 更新最大 request_time
                if tmp_request_time > max_request_time_map[url]:
                    max_request_time_map[url] = tmp_request_time
            else:
                total_request_time[url] += 0
        except Exception as e:
            traceback.print_exc()
            total_request_time[url] += 0

        try:
            if upstream_response_time.replace('.', '', 1).isdigit():
                tmp_upstream_response_time = float(upstream_response_time)
                total_upstream_response_time[url] += tmp_upstream_response_time

                # 更新最大 upstream_response_time
                if tmp_upstream_response_time > max_upstream_response_time[url]:
                    max_upstream_response_time[url] = tmp_upstream_response_time
            else:
                total_upstream_response_time[url] += 0
        except Exception as e:
            traceback.print_exc()
            total_upstream_response_time[url] += 0

        try:
            if upstream_connect_time.replace('.', '', 1).isdigit():
                tmp_upstream_connect_time = float(upstream_connect_time)
                total_upstream_connect_time[url] += tmp_upstream_connect_time

                # 更新最大 upstream_connect_time
                if tmp_upstream_connect_time > max_upstream_connect_time[url]:
                    max_upstream_connect_time[url] = tmp_upstream_connect_time
            else:
                total_upstream_connect_time[url] += 0
        except Exception as e:
            traceback.print_exc()
            total_upstream_connect_time[url] += 0

        try:
            if upstream_header_time.replace('.', '', 1).isdigit():
                tmp_upstream_header_time = float(upstream_header_time)
                total_upstream_header_time[url] += tmp_upstream_header_time

                # 更新最大 upstream_header_time
                if tmp_upstream_header_time > max_upstream_header_time[url]:
                    max_upstream_header_time[url] = tmp_upstream_header_time
            else:
                total_upstream_header_time[url] += 0
        except Exception as e:
            traceback.print_exc()
            total_upstream_header_time[url] += 0

    if not url_cnt:
        print("has no url count, return")
        return

    res = []
    sorted_res = sorted(url_cnt.items(), key=lambda d: -d[1])
    sorted_res = sorted_res[0:50]

    for i in sorted_res:
        url = i[0]
        cnt = i[1]
        request_time_avg = round(total_request_time[url] / cnt, 3)
        upstream_response_time_avg = round(total_upstream_response_time[url] / cnt, 3)
        upstream_header_time_avg = round(total_upstream_header_time[url] / cnt, 3)
        upstream_connect_time_avg = round(total_upstream_connect_time[url] / cnt, 3)

        res.append([url, cnt,
                    request_time_avg, max_request_time_map[url],
                    upstream_connect_time_avg, max_upstream_connect_time[url],
                    upstream_header_time_avg, max_upstream_header_time[url],
                    upstream_response_time_avg, max_upstream_response_time[url]
                    ])

    # 顺便统计下404 403页面
    res.append(["404 page for images", cnt_for_404_image, 0, 0, 0, 0, 0, 0, 0, 0])
    res.append(["404 page", cnt_for_404, 0, 0, 0, 0, 0, 0, 0, 0])
    res.append(["403 page", cnt_for_403, 0, 0, 0, 0, 0, 0, 0, 0])

    send_mail(res)


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"url cnt per hour finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
