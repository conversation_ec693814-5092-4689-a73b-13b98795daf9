# coding=utf-8
import sys

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "TWILIO_ACCOUNT_SID": env("TWILIO_ACCOUNT_SID"),
        "TWILIO_ACCOUNT_TOKEN": env("TWILIO_ACCOUNT_TOKEN"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(userid: int, reason: str, is_release_tw: bool):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = f"select * from User_blackuser where user_id={userid}"
    print(sql)
    cursor.execute(sql)
    black_users = cursor.fetchall()
    if len(black_users) > 0:
        print(f"user already handle: {userid}")
        return

    sql = f"select * from Number_numberused where user_id={userid} and status='USING'"
    print(sql)
    cursor.execute(sql)
    users_numbers = cursor.fetchall()
    if not users_numbers or len(users_numbers) == 0:
        print(f"user not exist: {userid}")
        return

    sql = f"select * from User_user where id={userid}"
    print(sql)
    cursor.execute(sql)
    users = cursor.fetchall()
    if not users or len(users) == 0:
        print(f"user not exist: {userid}")
        return
    print(users)
    uuid = users[0]['uuid']
    appid = users[0]['appid']

    print(users_numbers)
    number_id = users_numbers[0]['number_id']

    sql = f"select * from Number_numberused where user_id={userid} and number_id={number_id} and status='USING'"
    print(sql)
    cursor.execute(sql)
    users_numbers = cursor.fetchall()
    print(users_numbers)

    sql = f"select * from Number_numberinventory where id={number_id}"
    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()
    print(f"res: {res}")
    number_sid = res[0]['sid']

    sql = f"update Number_numberinventory set status='RELEASED' where id={number_id}"
    print(sql)
    res = cursor.execute(sql)
    print(f"res: {res}")

    sql = f"update Number_numberused set status='EXPIRE' where user_id={userid} and number_id={number_id} and status='USING'"
    print(sql)
    res = cursor.execute(sql)
    print(f"res: {res}")

    if reason:
        sql = f"INSERT INTO `secphone`.`User_blackuser`(`user_id`, `uuid`, `appid`, `reason`, `ban_type`,  `created_at`, `updated_at`) VALUES ({userid}, '{uuid}', {appid}, '{reason}', 0, now(), now());"
    else:
        sql = f"INSERT INTO `secphone`.`User_blackuser`(`user_id`, `uuid`, `appid`, `ban_type`, `created_at`, `updated_at`) VALUES ({userid}, '{uuid}', {appid}, 0, now(), now());"
    print(sql)
    res = cursor.execute(sql)
    print(f"res: {res}")

    db.commit()
    cursor.close()
    db.close()

    # 从 tw 释放号码
    if is_release_tw:
        print(f"is_release_tw: {is_release_tw}")
        release_tw_number(number_sid)


def release_tw_number(sid: str):
    from twilio.rest import Client

    account_sid = env_config["TWILIO_ACCOUNT_SID"]
    auth_token = env_config["TWILIO_ACCOUNT_TOKEN"]

    client = Client(account_sid, auth_token)
    res = client.incoming_phone_numbers(sid).delete()
    print(res)


if __name__ == '__main__':
    userid = int(sys.argv[1])
    if len(sys.argv) == 3:
        reason = str(sys.argv[2])
        is_release_tw = True
    else:
        reason = None
        is_release_tw = True

    if len(sys.argv) == 4:
        reason = str(sys.argv[2])
        is_release_tw = bool(sys.argv[3])
    else:
        reason = None
        is_release_tw = True
    work(userid, reason, is_release_tw)
