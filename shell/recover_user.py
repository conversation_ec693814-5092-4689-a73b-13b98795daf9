# coding=utf-8
import sys

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(user_id: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)
    sql1 = f"select * from Call_imagecheck where user_id={user_id} and deleted=0"
    sql2 = f"select * from Call_smscheck where user_id={user_id} and deleted=0"
    sql3 = f"select * from User_blackuser where user_id={user_id} and deleted=0"
    sql4 = f"select * from User_contactcontrol where user_id={user_id} and deleted=0"
    print("mms checking ...")
    cursor.execute(sql1)
    res1 = cursor.fetchall()
    for i in res1:
        print(f"{i['suggestion']},{i['label']},{i['image_url']}")
    print("sms checking ...")
    cursor.execute(sql2)
    res2 = cursor.fetchall()
    for i in res2:
        print(f"{i['suggestion']},{i['label']},{i['content']}")
    print("black user checking ...")
    cursor.execute(sql3)
    res3 = cursor.fetchall()
    for i in res3:
        print(i)
    print("contact control checking ...")
    cursor.execute(sql4)
    res4 = cursor.fetchall()
    for i in res4:
        print(i)

    sql5 = f"update Call_imagecheck set deleted=1 where user_id={user_id}"
    sql6 = f"update Call_smscheck set deleted=1 where user_id={user_id}"
    sql7 = f"insert into User_contactcontrol(user_id, status, created_at) values({user_id}, 1, now())"
    if len(res1) > 0:
        print(sql5)
        cursor.execute(sql5)
    else:
        print("mms no need to handle")
    if len(res2) > 0:
        print(sql6)
        cursor.execute(sql6)
    else:
        print("sms no need to handle")
    if len(res4) == 0:
        print(sql7)
        cursor.execute(sql6)
    else:
        print("contact control no need to handle")

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    user_id = int(sys.argv[1])
    work(user_id)
