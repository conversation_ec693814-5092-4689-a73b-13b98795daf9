import pymysql
from telnyx.http_client import requests


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def delete_a_sip(sid: str):
    url = "https://api.telnyx.com/v2/credential_connections/" + sid

    headers = {"Authorization": "Bearer KEY0193D036537A4B8864C8C5751E010685_OmTXhITKI4JA0jBUzKRhMh"}

    response = requests.delete(url, headers=headers)
    data = response.json()
    # print(data)
    return data


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = "select t1.id, t1.telnyx_sip_connection_id from User_user t1 left join Order_order t2 on t1.id=t2.user_id where (t1.telnyx_sip_connection_id is not null and t1.telnyx_sip_connection_id <> '') and t2.id is null;"
    cursor.execute(sql)
    res = cursor.fetchall()
    user_ids = []
    sip_map = {}
    for i in res:
        user_id = i['id']
        sid = i['telnyx_sip_connection_id']
        user_ids.append(user_id)
        sip_map[user_id] = sid
    print(user_ids)

    for user_id in user_ids:
        print(user_id)
        sql1 = f"update User_user set telnyx_sip_connection_id=null,telnyx_sip_username=null,telnyx_sip_password=null where id={user_id}"
        print(sql1)
        cursor.execute(sql1)

        delete_a_sip(sip_map[user_id])

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    work()
