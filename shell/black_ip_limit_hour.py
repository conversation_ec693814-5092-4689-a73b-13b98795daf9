# encoding=utf-8
import datetime
import smtplib
import subprocess
import sys
import time
import traceback
from collections import defaultdict
from email.header import Header
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

log_path = "/var/log/nginx/access.log"
black_ip_file_path = '/etc/nginx/ip.black'

today = time.strftime("%Y-%m-%d", time.localtime(time.time()))
now = datetime.datetime.now(datetime.timezone.utc)


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_current_hour_log():
    hour_str = get_this_hour_str()

    cmd = f'cat /var/log/nginx/access.log  | grep {hour_str}'
    print(f"cmd1: {cmd}")

    output, error = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    lines1 = output.decode("utf-8").split("\n")

    # 可能发生了日志切割
    if len(lines1) < 1000:
        cmd = f'cat /var/log/nginx/access1.log  | grep {hour_str}'
        print(f"cmd2: {cmd}")

        output, error = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
        lines2 = output.decode("utf-8").split("\n")
        if lines2:
            lines1.extend(lines2)

    return lines1


def get_current_blacks():
    # 更新黑名单列表
    with open(black_ip_file_path, 'r') as black_f:
        current_black_ips = black_f.readlines()
        return current_black_ips


def work(limit_cnt: int):
    ip_cnt = defaultdict(int)
    lines = get_current_hour_log()
    print(f"line size: {len(lines)}")
    for line in lines:
        if not line:
            continue

        # TW的ip我们要放过
        if 'TwilioProxy' in line and '/phone/' in line:
            continue

        a = line.split()
        ip = a[0]
        ip_cnt[ip] += 1

    # 只要超过阈值的ip
    ip_cnt = {k: v for k, v in ip_cnt.items() if v >= limit_cnt}
    if len(ip_cnt) == 0:
        return

    write_cnt = 0
    send_list = []
    with open(black_ip_file_path, 'a') as black_f:
        _current_black_ips = get_current_blacks()
        for bip, v in ip_cnt.items():
            for line in _current_black_ips:
                if bip in line:
                    print(f"{bip} already in black, pass")
                    break
            else:
                send_list.append((bip, v))
                write_cnt += 1
                print(f"black ip: {bip} set in /etc/nginx/ip.black...")
                black_f.write(f'deny {bip}; # limit hour cnt {v} at {now} \n')

    # 重启nginx
    if write_cnt > 0:
        print(f"write ip cnt: {write_cnt},  reload nginx...")
        subprocess.call('/usr/sbin/nginx -s reload', shell=True)
        send_mail(send_list)


def get_this_hour_str() -> str:
    dt = datetime.datetime.now(datetime.timezone.utc)
    t1 = dt.strftime("%d/%b/%Y:%H")
    return t1


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}因为当前小时内请求次数超过100被封IP ！！！',
                                'utf-8').encode()

        html = f'<html><body><h2>因为当前小时内请求次数超过100被封IP：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
        <table border="1">
         <tr>
            <th>IP</th>
            <th>请求次数</th>
        </tr>
        """
        for send_item in send_list:
            html += f"""
            <tr>
                <td>{send_item[0]}</td>
                <td>{send_item[1]}</td>
            </tr>
            """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"limit ip cnt, time: {now}")
    print("*" * 20)
    try:
        work(limit_cnt=500)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"limit ip cnt, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
