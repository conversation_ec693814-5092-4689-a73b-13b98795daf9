from twilio.rest import Client


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "TWILIO_ACCOUNT_SID": env("TWILIO_ACCOUNT_SID"),
        "TWILIO_ACCOUNT_TOKEN": env("TWILIO_ACCOUNT_TOKEN"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

account_sid = env_config["TWILIO_ACCOUNT_SID"]
auth_token = env_config["TWILIO_ACCOUNT_TOKEN"]
client = Client(account_sid, auth_token)


def get_tw_numbers() -> list:
    tw_numbers = []

    for t in client.incoming_phone_numbers.list(limit=2800):
        tw_numbers.append(t.phone_number)

    return tw_numbers


number_sid_map = {}


def get_from_msg_pool(sid: str):
    phone_numbers = client.messaging \
        .v1 \
        .services(sid) \
        .phone_numbers \
        .list(limit=2000)

    for record in phone_numbers:
        # print(record.phone_number)
        number_sid_map[record.phone_number] = record.sid

    return [v.phone_number for v in phone_numbers]


def add_to_msg(message_sid: str, phone_sid: str):
    phone_number = client.messaging \
        .v1 \
        .services(message_sid) \
        .phone_numbers \
        .create(
        phone_number_sid=phone_sid
    )

    print(phone_number.sid)


if __name__ == '__main__':
    tw_numbers = get_tw_numbers()
    print(f"tw_numbers: {len(tw_numbers)}")
    print(f"map_size : {len(number_sid_map)}")
    msg1_numbers = get_from_msg_pool("MGdd082ba25c1602029b1b0a94da712f83")
    print(f"msg1_numbers: {len(msg1_numbers)}")
    msg2_numbers = get_from_msg_pool("MG1364926db36d3fb434dc1d9af5bb661c")
    print(f"msg2_numbers: {len(msg2_numbers)}")
    msg3_numbers = get_from_msg_pool("MG6cc38b1f321fa15ff0de251b2f7b1a8f")
    print(f"msg3_numbers: {len(msg3_numbers)}")
    msg4_numbers = get_from_msg_pool("MGd0688906757043c12c2e59adf63bfbe6")
    print(f"msg4_numbers: {len(msg4_numbers)}")
    msg5_numbers = get_from_msg_pool("MG81a4920c2d7690d927f24ff5af19a45e")
    print(f"msg5_numbers: {len(msg5_numbers)}")

    all_msg_numbers = list(set(msg1_numbers + msg2_numbers + msg3_numbers + msg4_numbers + msg5_numbers))

    print("not in msg service:")
    not_in_msg_numbers = [v for v in tw_numbers if v not in all_msg_numbers]
    for i in not_in_msg_numbers:
        print(i)
