import pprint

import requests

TELNYX_API_KEY = "**********************************************************"

if __name__ == '__main__':
    number = "+13125300145"
    #number = "+16063801207"

    url = "https://api.telnyx.com/v2/phone_numbers"
    query = {
        "filter[phone_number]": number,
    }

    headers = {"Authorization": f"Bearer {TELNYX_API_KEY}"}
    response = requests.get(url, headers=headers, params=query)

    data = response.json()
    pprint.pprint(data)
