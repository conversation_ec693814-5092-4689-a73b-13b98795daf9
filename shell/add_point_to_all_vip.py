# coding=utf-8
import datetime
import random
import time

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    now = datetime.datetime.now(datetime.timezone.utc)
    now_str = f"{now.year}-{now.month}-{now.day} 00:00:00"

    sql1 = f""" select user_id, expire_at, created_at from Order_order where expire_at > '{now_str}' and valid=1 and order_status = 'OPEN' ; """
    print(sql1)
    cursor.execute(sql1)
    res1 = cursor.fetchall()
    print(len(res1))
    for index, i in enumerate(res1):
        user_id = i['user_id']

        try:
            sql2 = f"select * from Number_numberused where user_id={user_id} and status='USING' limit 1"
            cursor.execute(sql2)
            res2 = cursor.fetchall()
            number = res2[0]["number"]
        except Exception as e:
            print(e)
            number = ""

        print(index, i)
        insert_sql2 = f"insert into Point_pointrecord(user_id, event, point, created_at) values ({user_id}, 'FATPO-CHARGE', 100, now());"
        print(insert_sql2)
        cursor.execute(insert_sql2)

        update_sql = f"update Point_point set point=point + 100 where user_id={user_id};"
        print(update_sql)
        cursor.execute(update_sql)

        mock_sid = str(time.time()) + str(random.randint(10000, 99999))
        latest_ts = int(time.time() * 1000)
        sql3 = f"""INSERT INTO `secphone`.`Call_smsrecord`(`sid`, `user_id`, `latest_ts`, `ip`, `direction`, `from_number`, `to_number`, `status`, `err_code`, `price`, `point`, `content`, `filtered_content`, `images`, `created_at`, `updated_at`) 
        VALUES ('{mock_sid}', {user_id}, {latest_ts}, NULL, 'RECIEVE', '+1000009999', '{number}', 'delivered', NULL, 0, 0, 'Hello, thank you for your unwavering support and dedication to our platform. On behalf of the platform, we would like to offer you 100 credits as a token of appreciation. Please feel free to contact customer service if you have any questions.', NULL, NULL, now(), now());"""
        cursor.execute(sql3)

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    work()
