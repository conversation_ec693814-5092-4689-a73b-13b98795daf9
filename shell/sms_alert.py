import threading
import time
from datetime import datetime

import requests

PROJECT = [
    {"name": "VirtualSim", "url": "https://api.virtualsim.xyz/config/checkHealth/",
     "to_numbers": ["18688949202", "15014139212", ]},

    {"name": "VirtualSim2", "url": "https://phone2.zehougroup.xyz/config/checkHealth/",
     "to_numbers": ["18688949202", "15014139212", ]},

    {"name": "VirtualSim3", "url": "https://api.v3phone.xyz/config/checkHealth/",
     "to_numbers": ["18688949202", "15014139212", ]},
]


class ProjectMonitor:

    def __init__(self, name, url, to_numbers):
        self.name = name
        self.url = url
        self.to_numbers = to_numbers
        self.ua = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36'

    def __is_ok(self):

        count = 0
        while count < 3:
            try:
                r = requests.get(self.url, headers={'User-Agent': self.ua}, timeout=60, verify=False)
                if r.status_code == 200:
                    return True
                else:
                    count += 1
                    print('retring, project=%s, count=%s, status_code=%s' % (self.name, count, r.status_code))
                    continue

            except Exception as e:
                count += 1
                print("error, try again, count=%s" % count)

        return False

    def __send_alert(self):

        url = 'http://smssh1.253.com/msg/send/json'

        for to_number in self.to_numbers:
            params = {
                'account': 'YZM4765575',
                'password': 'KkTaWFPgUn5e37',
                'msg': '验证码：999999',
                'phone': to_number,
                'report': 'false'
            }

            try:
                print("send to number=%s" % to_number)
                r = requests.post(url, json=params)
                print(r.json())
                print("send alert done")
            except Exception as e:
                continue

    def run(self):

        while True:

            is_ok = self.__is_ok()
            print('%s --> %s' % (self.__datetime_to_str(), 'OK' if is_ok else 'ERROR'))
            if is_ok is False:
                self.__send_alert()
            time.sleep(300)

    def __datetime_to_str(self) -> str:
        date = datetime.now()
        return date.strftime("%Y-%m-%dT%H:%M:%S.000Z")


if __name__ == '__main__':

    threads = []
    for project in PROJECT:
        monitor = ProjectMonitor(project['name'], project['url'], project['to_numbers'])
        t = threading.Thread(target=monitor.run)
        threads.append(t)
        t.start()
        time.sleep(1)

    for t in threads:
        t.join()
