# coding=utf-8
import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(fake_cnt: int, err_cnt: int, total_cnt: int):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'ktawydeyracacabb'
        to_addr = ['<EMAIL>']

        fake_rate = 0.0
        if fake_cnt > 0:
            fake_rate = fake_cnt * 1.0 / total_cnt
        fake_rate = round(fake_rate, 2)

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}送达率fake={fake_cnt}/err={err_cnt}/all={total_cnt}/'
                                f'fr={fake_rate}', 'utf-8').encode()

        txt = f"fake/失败/总/fake率：{fake_cnt}/{err_cnt}/{total_cnt}/{fake_rate} <br>"
        msg.attach(MIMEText(txt, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def is_delivery_ok(cnt1: int, cnt2: int, limit: int):
    if cnt1 >= limit or (cnt2 > 0 and (cnt1 / cnt2 > 0.05)):
        return False
    return True


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    now = datetime.datetime.now(datetime.timezone.utc)
    now_str = f"{now.year}-{now.month}-{now.day} 00:00:00"

    sql1 = f"""select count(*) as cnt from Call_smsrecord where created_at >= '{now_str}' and direction='SEND' and is_it=0 and status <> 'delivered' and status <> 'send_fake';"""
    sql2 = f"""select count(*) as cnt from Call_smsrecord where created_at >= '{now_str}' and direction='SEND' and is_it=0;"""
    sql3 = f"""select count(*) as cnt from Call_smsrecord where created_at >= '{now_str}' and direction='SEND' and is_it=0 and status = 'send_fake';"""
    print(sql1)
    print(sql2)
    print(sql3)
    cursor.execute(sql1)
    res1 = cursor.fetchone()

    cursor.execute(sql2)
    res2 = cursor.fetchone()

    cursor.execute(sql3)
    res3 = cursor.fetchone()

    print(res1, res2, res3)
    err_cnt = res1['cnt']
    total_cnt = res2['cnt']
    fake_cnt = res3['cnt']

    # 存 DB
    cur_date = datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d")
    cur_hour = datetime.datetime.now(datetime.timezone.utc).hour
    col_name_total = f"hour{cur_hour}_total"
    col_name_failed = f"hour{cur_hour}_failed"
    col_name_fake = f"hour{cur_hour}_fake"

    sql = f"select * from Call_messagedeliveryhour where mydate='{cur_date}'"
    print(sql)
    cursor.execute(sql)
    if cursor.fetchone():
        sql = f"update Call_messagedeliveryhour set {col_name_total}={total_cnt}, {col_name_failed}={err_cnt}, {col_name_fake}={fake_cnt} where mydate='{cur_date}'"
        print(sql)
        cursor.execute(sql)
    else:
        sql = f"insert into Call_messagedeliveryhour(mydate, {col_name_total}, {col_name_failed}, {col_name_fake}) " \
              f"values ('{cur_date}', {total_cnt}, {err_cnt}, {fake_cnt})"
        print(sql)
        cursor.execute(sql)

    db.commit()
    cursor.close()
    db.close()

    if err_cnt >= 100:
        print(f"start sending email...err_cnt >= 100, fake:{fake_cnt},err:{err_cnt},total:{total_cnt}")
        send_mail(fake_cnt, err_cnt, total_cnt)
        return
    if total_cnt > 0 and float(fake_cnt) / total_cnt >= 0.4:
        print(f"start sending email... fake_cnt/total_cnt >= 0.4, fake:{fake_cnt},err:{err_cnt},total:{total_cnt}")
        send_mail(fake_cnt, err_cnt, total_cnt)
        return
    if total_cnt > 0 and err_cnt / total_cnt >= 0.4:
        print(f"start sending email... err_cnt / total_cnt >= 0.4, fake:{fake_cnt},err:{err_cnt},total:{total_cnt}")
        send_mail(fake_cnt, err_cnt, total_cnt)
        return

    print("no need send email")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail(-1, -1, -1)
    finally:
        print(f"message delivery finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
