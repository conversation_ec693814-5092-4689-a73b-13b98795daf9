import pandas as pd


def sum_every_day_and_total(origin_df):
    print("#" * 50)
    print("#" * 50)

    total_cost = origin_df['Cost'].sum()
    print(f"total_cost: {total_cost}")

    df_txtnow = origin_df[origin_df['Full Terminating number'].str.contains('txtnow')]
    print(df_txtnow['Call duration'].count())

    origin_df = origin_df[~origin_df['Full Terminating number'].str.contains('txtnow')]

    df_short = origin_df[origin_df['Call duration'] <= 6]
    df_short = df_short[df_short['Call duration'] > 0]
    df_long = origin_df[origin_df['Call duration'] > 6]
    df_sip = origin_df[origin_df['Terminating number'].notna() & origin_df['Terminating number'].str.contains('sip')]
    df_0_duration = origin_df[origin_df['Call duration'] == 0]

    sip_cnt = df_sip['Call duration'].count()
    duration_0_cnt = df_0_duration['Call duration'].count()

    short_cnt = df_short['Call duration'].count()
    long_cnt = df_long['Call duration'].count()

    print(f"short_cnt: {short_cnt}")
    print(f"long_cnt: {long_cnt}")
    print(f"total_cnt: {short_cnt + long_cnt}")
    print(f"short ratio: {float(short_cnt) / (short_cnt + long_cnt)}")

    print(f"sip_cnt: {sip_cnt}")
    print(f"duration_0_cnt: {duration_0_cnt}")

    print(f"sip_txntow cost: {df_txtnow['Cost'].sum()}")
    print(f"sip_txntow duration : {df_txtnow['Call duration'].sum()}")
    # assert sip_cnt == duration_0_cnt

    # 遍历'Full Terminating number'列
    # for index, row in df_short.iterrows():
    #     print(
    #         f"{index}, {row['Originating Number']} -> {row['Full Terminating number']} : {row['Call duration']}  : {row['Start Timestamp(UTC)']} -> {row['Answer Timestamp']} -> {row['End Timestamp']}")
    #     if index > 10000:
    #         break


if __name__ == '__main__':
    # 3 月
    # df = pd.read_csv('~/Downloads/cdr_customer_request-f59314e2-5e61-4acd-b384-e7ec853e95a1.csv')

    # 2 月
    df = pd.read_csv('~/Downloads/calls_customer_request-88f05456-927f-4e2d-9ba4-79ec87e3b5d8.csv')
    """
    total_cost: 465.6194
    16448
    short_cnt: 29611
    long_cnt: 17389
    total_cnt: 47000
    short ratio: 0.6300212765957447
    sip_cnt: 1758
    duration_0_cnt: 13974
    sip_txntow cost: 0.0
    sip_txntow duration : 739305
    """

    sum_every_day_and_total(df)
