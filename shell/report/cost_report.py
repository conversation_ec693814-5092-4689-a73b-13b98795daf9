import pandas as pd


def sum_every_day_and_total(origin_df):
    print("#" * 50)
    print("#" * 50)
    df = origin_df[origin_df['amount'] <= 0]

    # 将 'event_date' 列转换为日期类型
    df['event_date'] = pd.to_datetime(df['event_date'], format='%Y-%m-%d')

    # 按照 'event_date' 列对 'amount' 列进行求和
    sum_amount_by_date = df.groupby('event_date')['amount'].sum()

    print(sum_amount_by_date)

    sum_total = df['amount'].sum()
    print(f"sum_total:{sum_total}")


def sum_event_amount(df):
    print("#" * 50)
    print("#" * 50)
    sum_amount_by_date = df.groupby('cost_type')['amount'].sum()
    print(sum_amount_by_date)


def sum_event_details_amount(df):
    print("#" * 50)
    print("#" * 50)

    prefix_to_group = {
        '10DLC-SMS-ORIGINATION-UNREGISTERED-CARRIER-FEE-': '10DLC-SMS-ORIGINATION-UNREGISTERED-CARRIER-FEE',
        '10DLC-SMS-TERMINATION-UNREGISTERED-CARRIER-FEE-': '10DLC-SMS-TERMINATION-UNREGISTERED-CARRIER-FEE',
        '10DLC-MMS-TERMINATION-UNREGISTERED-CARRIER-FEE-': '10DLC-MMS-TERMINATION-UNREGISTERED-CARRIER-FEE',
        '10DLC-MMS-ORIGINATION-UNREGISTERED-CARRIER-FEE-': '10DLC-MMS-ORIGINATION-UNREGISTERED-CARRIER-FEE',
        'MMS-RATE0-TERMINATION-CARRIER-FEE-': 'MMS-RATE0-TERMINATION-CARRIER-FEE',
        'SMS-RATE0-ORIGINATION-CARRIER-FEE-': 'SMS-RATE0-ORIGINATION-CARRIER-FEE',
        'SMS-RATE0-TERMINATION-CARRIER-FEE-': 'SMS-RATE0-TERMINATION-CARRIER-FEE',
        # ... 其他前缀到组的映射 ...
    }
    # 定义您想要根据前缀分组的映射

    # 使用 apply 函数和 lambda 表达式来根据前缀创建分组列
    df['cost_code_group'] = df['cost_code'].apply(
        lambda x: next((group for prefix, group in prefix_to_group.items() if x.startswith(prefix)), x))

    # 现在，您可以根据新的分组列进行 groupby 操作
    sum_amount_by_date = df.groupby('cost_code_group')['amount'].sum()
    print(sum_amount_by_date)


def sum_big_event_amount(df):
    print("#" * 50)
    print("#" * 50)

    contains_to_group = {
        'SMS-RATE0': 'SMS',
        'UNREGISTERED': 'UNREGISTERED-SMS',
        'MMS-RATE0': 'MMS',
        'CALL-': 'CALL',
        'DID-': 'NUMBER',
        '10DLC-MMS-ORIGINATION-UNREGISTERED-CARRIER-FEE-': '10DLC-MMS-ORIGINATION-UNREGISTERED-CARRIER-FEE',
    }
    # 定义您想要根据前缀分组的映射

    # 使用 apply 函数和 lambda 表达式来根据前缀创建分组列
    df['cost_code_group'] = df['cost_code'].apply(
        lambda x: next((group for prefix, group in contains_to_group.items() if prefix in x), x))

    # 现在，您可以根据新的分组列进行 groupby 操作
    sum_amount_by_date = df.groupby('cost_code_group')['amount'].sum()
    print(sum_amount_by_date)


if __name__ == '__main__':
    """
    术语：
    MRC = Monthly Recurring Charge
    OTC = One Time Charge
    GLOBAL-CONV-SDC-SURCHARGE = 过短的通话会额外收费， refers to the surcharge applied to customers who have a high percentage of short duration calls (SDC) on the Global Conversational product. The surcharge is calculated based on the total number of SDC calls and is applied at a rate of .01 per call.,  This product code refers to short duration calls, please read more about this here: https://support.telnyx.com/en/articles/1130707-what-are-short-duration-calls
    DID-VANITY-TIER1-MRC = 虚荣号码额外收费， Vanity numbers operate in different tiers, where there are repeating numbers which make it easy to remember what the number is like:
**************

    """
    # 读取Excel文件
    # df = pd.read_csv('/Users/<USER>/Downloads/invoice_ledger_49fd2029-0129-4b96-914f-30b72209eb42_2024-02.csv')
    df = pd.read_csv('/Users/<USER>/Downloads/invoice_ledger_a26d560a-fbcf-41c4-b406-7ce6ef982536_2024-02.csv')

    sum_every_day_and_total(df)
    sum_event_amount(df)
    sum_event_details_amount(df)
    sum_big_event_amount(df)
