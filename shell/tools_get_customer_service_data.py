# coding=utf-8
import csv

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql1 = f"""select direction, from_number, to_number, content, created_at from Call_smsrecord where created_at>='2024-05-01' and (to_number = '+1000009999' or from_number='+1000009999') and direction='SEND'; """
    print(sql1)
    cursor.execute(sql1)
    res1 = cursor.fetchall()
    print(len(res1))
    group_map = {}
    for index, i in enumerate(res1):
        from_number = i['from_number']
        to_number = i['to_number']
        content = i['content']

        if from_number != '+1000009999':
            if f"{from_number}_{to_number}" not in group_map:
                group_map[f"{from_number}_{to_number}"] = [f"[{from_number}->{to_number}] {content}"]
            else:
                group_map[f"{from_number}_{to_number}"].append(f"[{from_number}->{to_number}] {content}")
        else:
            if f"{to_number}_{from_number}" in group_map:
                group_map[f"{to_number}_{from_number}"].append(f"[{from_number}->{to_number}] {content}")

    # CSV文件名
    csv_file = 'output.csv'

    # 将字典写入CSV文件
    with open(csv_file, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        # 写入CSV的标题行
        writer.writerow(["Number", "Contents"])

        for key, contents in group_map.items():
            # 将内容列表转换为带换行符的字符串
            contents_str = "\n".join(contents)
            # 写入CSV文件
            writer.writerow([key.split("_")[0], contents_str])

    print(f"数据已写入 {csv_file}")
    # for k, v in group_map.items():
    #     print(k, v)

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    work()
