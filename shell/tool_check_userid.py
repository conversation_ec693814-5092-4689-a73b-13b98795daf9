# coding=utf-8
import sys
import time

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(userid: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = f"select * from User_user where id={userid}"
    cursor.execute(sql)
    users = cursor.fetchall()
    if not users or len(users) == 0:
        print(f"user not exist: {userid}")
        return

    print(f"user: ")
    for i in users:
        for k, v in i.items():
            print(f"\t {k}: {v}")

    sql = f"select * from Number_numberused where user_id={userid}"
    cursor.execute(sql)
    print("user numbers: ")
    users_numbers = cursor.fetchall()
    for i in users_numbers:
        for k, v in i.items():
            print(f"\t {k}: {v}")

    sql = f"select order_status, expire_at, expiration_intent, created_at from Order_order where user_id={userid}"
    cursor.execute(sql)
    orders = cursor.fetchall()
    print(f"user order:")
    for i in orders:
        for k, v in i.items():
            print(f"\t {k}: {v}")

    sql = f"select *  from User_blackuser where user_id={userid}"
    cursor.execute(sql)
    black_infos = cursor.fetchall()
    print(f"user black or grey:")
    for i in black_infos:
        for k, v in i.items():
            print(f"\t {k}: {v}")

    sql = f"select *  from Call_smscheck where user_id={userid}"
    cursor.execute(sql)
    invalid_sms = cursor.fetchall()
    print(f"user invalid sms cnt: {len(invalid_sms)}:")
    for i in invalid_sms:
        for k, v in i.items():
            if isinstance(v, str):
                v = v.replace("\n", "\t")
            print(f"\t {k}: {v}")

    ts = int(time.time() * 1000) - 7 * 24 * 3600 * 1000
    sql = f"select count(distinct(to_number)) as cnt from Call_smsrecord where user_id={userid} and direction='SEND' and latest_ts >= {ts}"
    cursor.execute(sql)
    sms_contact_infos = cursor.fetchall()
    print(f"user contact: {sms_contact_infos}")

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    userid = int(sys.argv[1])
    work(userid)
