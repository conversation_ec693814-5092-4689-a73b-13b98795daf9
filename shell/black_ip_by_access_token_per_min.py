# encoding=utf-8
import datetime
import smtplib
import subprocess
import sys
import time
import traceback
from collections import defaultdict
from email.header import Header
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

log_path = "/var/log/nginx/access.log"
black_ip_file_path = '/etc/nginx/ip.black'

today = time.strftime("%Y-%m-%d", time.localtime(time.time()))
now = datetime.datetime.now(datetime.timezone.utc)


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_last_min_str() -> str:
    # 上一分钟
    dt = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(minutes=-1)
    t1 = dt.strftime("%d/%b/%Y:%H:%M:%S")
    t2 = t1.split(":")
    return ':'.join(t2[0:3])


def get_access_log():
    min_str = get_last_min_str()
    cmd = f'cat /var/log/nginx/access.log  | grep {min_str} | grep "/phone/access_token?userid"'
    print(f"cmd1: {cmd}")

    output, error = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    lines1 = output.decode("utf-8").split("\n")

    # 可能发生了日志切割
    if len(lines1) < 100:
        cmd = f'cat /var/log/nginx/access1.log  | grep {min_str} | grep "/phone/access_token?userid"'
        print(f"cmd2: {cmd}")

        output, error = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
        lines2 = output.decode("utf-8").split("\n")
        if lines2:
            lines1.extend(lines2)

    return lines1


def work(lines: list):
    while_user_list = ["?userid=37&", "?userid=36&", "?userid=1&", "?userid=2&", "?userid=41&", "?userid=397&"]

    min_cnt_dict = defaultdict(int)
    for line in lines:
        if line:
            a = line.split()
            if a:
                for w in while_user_list:
                    if w in a:
                        print(f"白名单用户: {w} 放行 line: {line}...")
                        break
                else:
                    ip = a[0]
                    min_cnt_dict[ip] += 1

    # 只要超过阈值的ip，一分钟给5个access_token的机会，已经是接入层的宽容了
    min_cnt_dict = {k: v for k, v in min_cnt_dict.items() if v > 5}

    for k, v in min_cnt_dict.items():
        print(f"k: {k}, cnt: {v}")

    send_list = []

    if len(min_cnt_dict) > 0:
        # 更新黑名单列表
        with open(black_ip_file_path, 'r') as black_f:
            current_black_ips = black_f.readlines()

        write_cnt = 0
        with open(black_ip_file_path, 'a') as black_f:
            _current_black_ips = current_black_ips

            for bip, v in min_cnt_dict.items():
                for deny_ip in _current_black_ips:
                    if bip in deny_ip:
                        print(f"{bip} already in black, pass")
                        break
                else:
                    send_list.append((bip, v))
                    write_cnt += 1
                    print(f"black ip: {bip} set in /etc/nginx/ip.black...")
                    black_f.write(f'deny {bip}; # limit access token cnt {v} at {now} \n')

        # 重启nginx
        if write_cnt > 0:
            print(f"write ip cnt: {write_cnt},  reload nginx...")
            subprocess.call('/usr/sbin/nginx -s reload', shell=True)

            send_mail(send_list)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}因为access_token访问频率过高被封IP ！！！', 'utf-8').encode()

        html = f'<html><body><h2>因为access_token访问频率过高被封IP：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
        <table border="1">
         <tr>
            <th>IP</th>
            <th>访问ACCESS_TOKEN次数</th>
        </tr>
        """
        for send_item in send_list:
            html += f"""
            <tr>
                <td>{send_item[0]}</td>
                <td>{send_item[1]}</td>
            </tr>
            """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"block ip by access token: {now}")
    print("*" * 20)
    try:
        lines = get_access_log()
        work(lines)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"block ip by access token, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
