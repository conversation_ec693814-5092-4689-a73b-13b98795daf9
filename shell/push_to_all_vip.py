# coding=utf-8
import datetime
import json
import os
import random
import time

import firebase_admin
import pymysql
from firebase_admin import _messaging_utils
from firebase_admin import initialize_app, credentials
from firebase_admin._messaging_utils import APNSPayload, Aps
from firebase_admin.messaging import Message


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

FCM_APP_NAME = env_config["FCM_TITLE"]
FCM_RENEW_VIP_TITLE = env_config["FCM_TITLE"]
FCM_RENEW_VIP_CONTENT = "[Notice] Hello, in order to ensure that you can use our phone and messaging functions smoothly, we need to switch to a new carrier platform. You will need to go to the number selection page again to choose your number. We apologize for any inconvenience caused and would like to offer you a compensation of 30 points. We sincerely appreciate your continued support and understanding."

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
FCM_CERT_FILEPATH = BASE_DIR + env_config["FCM_CERT_FILEPATH"]
print(FCM_CERT_FILEPATH)
with open(FCM_CERT_FILEPATH) as f:
    credential = credentials.Certificate(json.load(f))

FIREBASE_APP = initialize_app(credential=credential, name=FCM_APP_NAME)


def do_add_points_and_sms_record():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    now = datetime.datetime.now(datetime.timezone.utc)
    now_str = f"{now.year}-{now.month}-{now.day} 00:00:00"

    sql1 = f""" select user_id, expire_at, created_at from Order_order where expire_at > '{now_str}' and valid=1 and order_status = 'OPEN' ; """
    print(sql1)
    cursor.execute(sql1)
    res1 = cursor.fetchall()
    print(len(res1))
    for index, i in enumerate(res1):
        user_id = i['user_id']

        try:
            sql2 = f"select * from Number_numberused where user_id={user_id} and status='USING' limit 1"
            cursor.execute(sql2)
            res2 = cursor.fetchall()
            number = res2[0]["number"]
        except Exception as e:
            print(e)
            number = ""

        print(index, i)
        insert_sql2 = f"insert into Point_pointrecord(user_id, event, point, created_at) values ({user_id}, 'FATPO-CHARGE', 30, now());"
        print(insert_sql2)
        cursor.execute(insert_sql2)

        update_sql = f"update Point_point set point=point + 30 where user_id={user_id};"
        print(update_sql)
        cursor.execute(update_sql)

        mock_sid = str(time.time()) + str(random.randint(10000, 99999))
        latest_ts = int(time.time() * 1000)

        push_msg = "[NOTICE] Hello, in order to ensure that you can use our phone and messaging functions smoothly, we need to switch to a new carrier platform. You will need to go to the number selection page again to choose your number. We apologize for any inconvenience caused and would like to offer you a compensation of 30 points. We sincerely appreciate your continued support and understanding."
        sql3 = f"""INSERT INTO `secphone`.`Call_smsrecord`(`sid`, `user_id`, `latest_ts`, `ip`, `direction`, `from_number`, `to_number`, `status`, `err_code`, `price`, `point`, `content`, `filtered_content`, `images`, `created_at`, `updated_at`) 
        VALUES ('{mock_sid}', {user_id}, {latest_ts}, NULL, 'RECIEVE', '+1000009999', '{number}', 'delivered', NULL, 0, 0, '{push_msg}', NULL, NULL, now(), now());"""
        cursor.execute(sql3)

    db.commit()
    cursor.close()
    db.close()


def do_push():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = f"select t1.user_id, t2.push_id, t2.created_at, t1.created_at, t1.expire_at  from Order_order t1 left join User_user t2 on t1.user_id=t2.id where t2.push_id is not null and t2.push_id <> '' and  t1.valid=1 and t1.expire_at >= now()"
    cursor.execute(sql)
    res = cursor.fetchall()
    assert isinstance(res, list)

    db.commit()
    cursor.close()
    db.close()

    print(sql)
    print(f"size: {len(res)}")

    push_ids = [v['push_id'] for v in res]
    fcm_push(push_ids)


def fcm_push(push_ids):
    if not push_ids:
        return

    # 加上 fatpo 的 pushid
    push_ids.append(env_config["FCM_FATPO_PUSHID"])

    # 将 push_ids 分成多个子列表，每个子列表最多包含 100 个推送目标
    push_ids = [v for v in push_ids if v and len(v) > 0]
    push_id_chunks = [push_ids[i:i + 100] for i in range(0, len(push_ids), 100)]

    # 构造多个 Message 对象，每个 Message 对象包含一个子列表中的推送目标
    fcm_app = firebase_admin.get_app(FCM_APP_NAME)
    for index, push_id_chunk in enumerate(push_id_chunks):
        print(f"fcm push batch: {index}, size:{len(push_id_chunk)}")
        m = firebase_admin.messaging.MulticastMessage(
            notification=firebase_admin.messaging.Notification(
                title=FCM_RENEW_VIP_TITLE,
                body=FCM_RENEW_VIP_CONTENT,
                image=None,
            ),
            apns=_messaging_utils.APNSConfig(payload=APNSPayload(aps=Aps(badge=0))),
            tokens=push_id_chunk,
        )
        response = firebase_admin.messaging.send_multicast(m, app=fcm_app)
        print(response)


if __name__ == '__main__':
    do_add_points_and_sms_record()
    do_push()
