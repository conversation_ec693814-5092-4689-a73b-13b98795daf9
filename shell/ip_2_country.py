import geoip2.database
import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    cursor.execute(
        "select id, ip from User_user where created_at >= '2024-03-01' and (country is null or country = '')")
    users = cursor.fetchall()
    print(f"size: {len(users)}")

    for u in users:
        user_id = u['id']
        country = get_country_from_ip(u['ip'])
        print(user_id, u['ip'], country)
        sql2 = f"update User_user set country='{country}' where id={user_id}"
        cursor.execute(sql2)

    db.commit()
    cursor.close()
    db.close()


def get_country_from_ip(ip_address):
    if "," in ip_address:
        return "Unknown"

    reader = geoip2.database.Reader(f'/root/www/VirtualSIM_BackEnd/GeoLite2-Country.mmdb')
    try:
        response = reader.country(ip_address)
        country_name = response.country.name
        return country_name
    except geoip2.errors.AddressNotFoundError:
        return "Unknown"


if __name__ == '__main__':
    work()
