import datetime
import sys
from collections import defaultdict

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def is_user_vip(user_dict, order) -> bool:
    expired_at_ = user_dict['expired_at']
    if not expired_at_:
        return False

    created_at_ = user_dict['created_at']
    if expired_at_ - created_at_ >= datetime.timedelta(days=10) \
            and order['expiration_intent'] not in [5, 6] and order['valid'] == 1:
        return True
    else:
        return False


def get_user_callrecord(cursor, user_id: int) -> list:
    cursor.execute(
        f"select * from Call_callrecord where user_id={user_id};")
    return cursor.fetchall()


def get_user_smsrecord(cursor, user_id: int) -> list:
    cursor.execute(
        f"select * from Call_smsrecord where user_id={user_id};")
    return cursor.fetchall()


def get_user_point(cursor, user_id: int):
    cursor.execute(
        f"select * from Point_point where user_id={user_id};")
    return cursor.fetchall()[0]


def get_user_pointrecords(cursor, user_id: int) -> list:
    cursor.execute(
        f"select * from Point_pointrecord where user_id={user_id};")
    return cursor.fetchall()


def get_user(cursor, user_id: int) -> dict:
    cursor.execute(f"select * from User_user where id={user_id};")
    users = cursor.fetchall()
    print(f"all user size: {len(users)}")
    if len(users) >= 1:
        return users[0]
    return None


def get_user_phone(cursor, user_id: int) -> dict:
    cursor.execute(f"select * from Number_numberused where user_id={user_id};")
    users = cursor.fetchall()
    print(f"Number_numberused size: {len(users)}")
    if len(users) >= 1:
        return users[0]
    return None


def get_user_order(cursor, user_id: int) -> dict:
    cursor.execute(f"select id, user_id, number_id, renew_status, expiration_intent, expire_at, valid, order_status, "
                   f"created_at, updated_at "
                   f"from Order_order where user_id={user_id};")
    orders = cursor.fetchall()
    print(f"Order_order size: {len(orders)}")
    if len(orders) >= 1:
        return orders[0]
    return None


def work(user_id: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')

    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    user = get_user(cursor, user_id)
    user_point = get_user_point(cursor, user_id)
    user_calls = get_user_callrecord(cursor, user_id)
    user_sms = get_user_smsrecord(cursor, user_id)
    user_pointrecords = get_user_pointrecords(cursor, user_id)
    phone = get_user_phone(cursor, user_id)
    order = get_user_order(cursor, user_id)

    print("*" * 20 + ' [total info] ' + '*' * 20)
    print(f"user: {user_id}, created_at: {user['created_at']}, expired_at: {user['expired_at']}")
    for k, v in user.items():
        print(f"{k}: {v}")

    print("*" * 20 + ' [order info] ' + '*' * 20)
    if order:
        for k, v in order.items():
            print(f"{k}: {v}")
    else:
        print(f"user has no order !!!")

    print("*" * 20 + ' [phone info] ' + '*' * 20)
    if phone:
        for k, v in phone.items():
            print(f"{k}: {v}")
    else:
        print(f"没有买号！！")

    print("*" * 20 + ' [point total info] ' + '*' * 20)
    is_vip = is_user_vip(user, order)
    begin_point = 20 if not is_vip else 50
    charge_points = [v['point'] for v in user_pointrecords if v['event'] == 'CHARGE']
    sms_points = [v['point'] for v in user_pointrecords if v['event'] in ['SMS', 'SMM']]
    call_points = [v['point'] for v in user_pointrecords if v['event'] == 'CALL']
    if len(charge_points) > 0:
        charge_cnt = sum(charge_points)
    else:
        charge_cnt = 0

    print(f"begin point : {begin_point}, after charge, point: {charge_cnt + begin_point}")
    print(f"current point : {user_point['point']}")
    print(f"point records size: {len(user_pointrecords)}, sum point : {sum([v['point'] for v in user_pointrecords])}")
    print(f"\tsms point records size: {len(sms_points)}, sum : {sum(sms_points)}")
    print(f"\tcall point records size: {len(call_points)}, sum : {sum(call_points)}")
    print(f"\tcharge point records size: {len(charge_points)}, sum : {sum(charge_points)}")
    print(f"call records size: {len(user_calls)}")
    print(f"sms records size: {len(user_sms)}")

    print("*" * 20 + ' [point details] ' + '*' * 20)
    point_records_event_map = defaultdict(int)
    for index, user_pointrecord in enumerate(user_pointrecords):
        print(f"index:{index} event:{user_pointrecord['event']}, point: {user_pointrecord['point']}, "
              f"create:{user_pointrecord['created_at']}, record_id: {user_pointrecord['record_id']}")
        point_records_event_map[user_pointrecord['event']] += user_pointrecord['point']

    print("*" * 20 + ' [point record total] ' + '*' * 20)
    for k, v in point_records_event_map.items():
        print(f"point record event:{k}, sum point cost: {v}")

    print("*" * 20 + ' [call details] ' + '*' * 20)
    for index, call in enumerate(user_calls):
        print(
            f"idx: {index},id:{call['id']},direct:{call['direction']},from:{call['from_number']},to:{call['to_number']}, "
            f"status:{call['status']}, price:{call['price']}, dur:{call['duration']}, cb_dur:{call['cb_duration']}, "
            f"cb_call_dur:{call['cb_call_duration']}, point:{call['point']},"
            f"at: {call['created_at'].strftime('%Y-%m-%d %H:%M:%S')}")

    print("*" * 20 + ' [sms details] ' + '*' * 20)
    for index, sms in enumerate(user_sms):
        print(f"index: {index}, id:{sms['id']}, direction:{sms['direction']}, from:{sms['from_number']}, "
              f"to:{sms['to_number']}, status={sms['status']}, price:{sms['price']}, point:{sms['point']}, "
              f"sms at: {sms['created_at'].strftime('%Y-%m-%d %H:%M:%S')}")

    cursor.close()
    db.close()


if __name__ == '__main__':
    user_id = sys.argv[1]

    work(int(user_id))
