# encoding=utf-8

import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    cursor.execute("select * from User_user t1 left join Order_order t2 on t1.id=t2.user_id "
                   "where DATEDIFF(t1.expired_at, now()) >= 10 and t1.is_add_point = 0 and t2.valid=1;")
    user_db_list = cursor.fetchall()
    print(f"handle size: {len(user_db_list)}")

    send_list = []
    for user_db in user_db_list:
        print("*" * 20)
        send_list.append(user_db)
        cmd1 = f"update User_user set is_add_point=1, updated_at=now() where id={user_db['id']}"
        print(cmd1)
        cursor.execute(cmd1)
        cursor.fetchall()

    db.commit()
    cursor.close()
    db.close()

    if send_list:
        send_mail(send_list)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}遇到贵人', 'utf-8').encode()

        html = f'<html><body><h2>贵人：{datetime.datetime.now(datetime.timezone.utc)}</h2>'

        for send_item in send_list:
            order_type = ''
            if (send_item['expired_at'] - datetime.datetime.now(datetime.timezone.utc)).days >= 300:
                order_type = '年度'
            elif (send_item['expired_at'] - datetime.datetime.now(datetime.timezone.utc)).days >= 70:
                order_type = '季度'
            elif (send_item['expired_at'] - datetime.datetime.now(datetime.timezone.utc)).days >= 20:
                order_type = '月度'

            html += f"""
             <h3> 贵人类型：{order_type}, 用户:{send_item['id']} </h3>
             """
        html += '</body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"add point, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"add point, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
