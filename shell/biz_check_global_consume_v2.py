# coding=utf-8
import datetime
import json
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql

# 是否必须马上发送邮件
must_send_flag = False
must_send_flag_reason = []

# 发送的东西
send_mail_list = []
rate_data = {}


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_all_user(cursor) -> list:
    sql = """
    select t1.id as user_id, t2.expire_at as order_expired_at, t2.created_at as order_created_at, t2.expiration_intent
    from User_user t1 left join Order_order t2 on t1.id=t2.user_id 
    where (expired_at >= now() or DATEDIFF(now(), expired_at) <= 3) and `expiration_intent`<>5 and valid=1
    and t1.id not in (1, 2, 3, 4, 5, 6, 37, 36, 41, 52, 397, 784, 6675, 6685, 26, 120, 217, 224, 227, 229, 230, 232, 234, 235, 237, 244, 246,
    200980,204563,205141,1013552);
    """

    cursor.execute(sql)
    users = cursor.fetchall()
    print(f"all user size: {len(users)}")
    return users


def get_user_info(cursor, user_id: int) -> dict:
    sql1 = f"select event, count(*) as count, sum(point) as sum from Point_pointrecord where user_id={user_id} group by event;"
    cursor.execute(sql1)
    res = cursor.fetchall()

    info = {
        'call_point_records_count': 0,
        'call_point_sum': 0,
        'sms_point_records_count': 0,
        'sms_point_sum': 0,
        'mms_point_records_count': 0,
        'mms_point_sum': 0,
        'charge_point_records_count': 0,
        'charge_point_sum': 0,
        'free_point_records_count': 0,
        'free_point_sum': 0,
    }
    for i in res:
        if i['event'] == 'CALL':
            info['call_point_records_count'] = i['count']
            info['call_point_sum'] = i['sum']
        elif i['event'] == 'SMS':
            info['sms_point_records_count'] = i['count']
            info['sms_point_sum'] = i['sum']
        elif i['event'] == 'MMS':
            info['mms_point_records_count'] = i['count']
            info['mms_point_sum'] = i['sum']
        elif i['event'] == 'CHARGE':
            info['charge_point_records_count'] = i['count']
            info['charge_point_sum'] = i['sum']
        elif i['event'] == 'FREE':
            info['free_point_records_count'] = i['count']
            info['free_point_sum'] = i['sum']

    info['total_point_records_count'] = sum([v['count'] for v in res])
    info['total_point_sum'] = sum([v['sum'] for v in res])

    sql2 = f"select count(*) as count from Call_callrecord where user_id={user_id} " \
           f"and `status` not in ('failed', 'canceled');"
    cursor.execute(sql2)
    res = cursor.fetchall()[0]
    info['call_records_count'] = res['count']

    sql3 = f"select count(*) as count from Call_smsrecord where user_id={user_id} and (from_number <> '+1000009999' and to_number <> '+1000009999');"
    cursor.execute(sql3)
    res = cursor.fetchall()[0]
    info['sms_records_count'] = res['count']

    sql4 = f"select point, free_point from Point_point where user_id={user_id};"
    cursor.execute(sql4)
    res = cursor.fetchall()[0]
    info['current_point'] = res['point'] + res['free_point']

    return info


def is_user_vip(order_expired_at, order_created_at, expiration_intent, order_valid) -> bool:
    expired_at_ = order_expired_at
    if not expired_at_:
        return False

    created_at_ = order_created_at
    # 0 - 正常订单，1-试用期到了
    if expired_at_ - created_at_ > datetime.timedelta(days=26) \
            and expiration_intent not in [5, 6] and order_valid == 1:
        return True
    else:
        return False


def check_user_normal_consume_rate(userid: int,
                                   order_expired_at: datetime,
                                   call_len: int,
                                   sms_len: int):
    global send_mail_list
    global must_send_flag

    if call_len >= rate_data['alert_limit']['call']:
        for k, v in rate_data["call"].items():
            if userid in v and call_len <= int(k):
                msg = f"USER：{userid}, 白名单 expire_at:{order_expired_at} " \
                      f"has more than {call_len} call records, while rate: {k}."
                print(msg)
                break
        else:
            msg = f"USER：{userid}, expire_at:{order_expired_at} has more than {call_len} call records."
            print(msg)
            send_mail_list.append(msg)
            must_send_flag = True
            must_send_flag_reason.append(msg)

    if sms_len >= rate_data['alert_limit']['sms']:
        for k, v in rate_data["sms"].items():
            if userid in v and sms_len <= int(k):
                msg = f"USER：{userid}, 白名单 expire_at:{order_expired_at} " \
                      f"has more than {sms_len} sms records, while rate: {k}."
                print(msg)
                break
        else:
            msg = f"USER：{userid}, expire_at:{order_expired_at} has more than {sms_len} sms records."
            send_mail_list.append(msg)
            print(msg)
            must_send_flag = True
            must_send_flag_reason.append(msg)


def work(send_mail_cnt_alert: int, un_save_call_user_counts_alert: int, un_save_call_total_counts_alert: int):
    """
    send_mail_cnt_alert: 发送阈值
    un_save_call_user_counts_alert: 发送阈值: 没落库的通话人数
    un_save_call_total_counts_alert: 发送阈值：没落库的通话总数
    :return:
    """
    global must_send_flag
    global send_mail_list

    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 还没入账通话数
    un_save_call_user_counts = 0
    un_save_call_total_counts = 0

    for index, user in enumerate(get_all_user(cursor)):
        # 打印下进度条
        if index % 100 == 0:
            print(f"check 进度: {index}")

        user_id = user['user_id']
        user_info = get_user_info(cursor, user_id)

        # 点数对账
        begin_point = 50 if user_id < 204576 else 100  # 2022年10月02日09:53:38 把 start_point从 50 提升到 100
        if begin_point + user_info['total_point_sum'] != user_info['current_point']:
            msg = f"用户:{user_id}, 初始点数:{begin_point}, 流水账点数:{begin_point + user_info['total_point_sum']} != " \
                  f"用户当前点数 {user_info['current_point']}"
            print(msg)
            send_mail_list.append(msg)
            must_send_flag = True
            must_send_flag_reason.append(msg)

        # 点数数量对账
        if user_info['call_point_records_count'] != user_info['call_records_count']:
            msg = f"用户:{user_id}, 打了{user_info['call_records_count']}个电话, 但是点数只记录了{user_info['call_point_records_count']}个电话"
            print(msg)
            send_mail_list.append(msg)
            un_save_call_user_counts += 1
            un_save_call_total_counts += user_info['call_records_count'] - user_info['call_point_records_count']

        # 短信数量对账，不用太精准，因为过3秒后全部会刷回去的，这里给10条的宽容空间
        if abs(user_info['sms_point_records_count'] + user_info['mms_point_records_count'] - user_info[
            'sms_records_count']) > 10:
            msg = f"用户:{user_id}, 发了{user_info['sms_records_count']}个短信, 但是点数只记录了{user_info['sms_point_records_count']}条短信"
            print(msg)
            send_mail_list.append(msg)
            must_send_flag = True
            must_send_flag_reason.append(msg)

        # 检查消费频率异常
        check_user_normal_consume_rate(user_id, user['order_expired_at'], user_info['call_records_count'],
                                       user_info['sms_records_count'])

    if un_save_call_user_counts >= un_save_call_user_counts_alert:
        msg = f"已经有{un_save_call_user_counts}用户的通话没入账，超过了设置的阈值：{un_save_call_user_counts_alert}"
        print(msg)
        send_mail_list.append(msg)
        must_send_flag = True
        must_send_flag_reason.append(msg)
    if un_save_call_total_counts >= un_save_call_total_counts_alert:
        msg = f"已经有{un_save_call_total_counts}的通话没入账，超过了设置的阈值：{un_save_call_total_counts_alert}"
        print(msg)
        send_mail_list.append(msg)
        must_send_flag = True
        must_send_flag_reason.append(msg)

    db.commit()
    cursor.close()
    db.close()

    do_send(send_mail_list, send_mail_cnt_alert)


def do_send(send_mail_list: list, send_limit_cnt: int):
    # 看情况发送邮件
    for index, send_item in enumerate(send_mail_list):
        print(f"index: {index}, msg: {send_item}")

    # 有必须要发送的情况，马上发送
    if must_send_flag:
        msg = f"有必须要发送的情况，马上发送！"
        print(msg)
        print('<br>'.join(must_send_flag_reason))
        send_mail_list.append(msg)
        send_mail_list.append('<br>'.join(must_send_flag_reason))
        send_mail(send_mail_list)
        return

    # 差不多稳定下来了，只是异常量太大
    if len(send_mail_list) >= send_limit_cnt:
        msg = f"有{send_limit_cnt}个人消费异常，马上报警！"
        print(msg)
        send_mail_list.append(msg)
        send_mail(send_mail_list)
        return

    print("暂时达不到报警阈值！")


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}统计整体消费流程异常情况 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>统计整体消费流程异常情况：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
         <table border="1">
          <tr>
             <th>统计整体消费流程异常情况</th>
         </tr>
         """
        for index, send_item in enumerate(send_list):
            html += f"""
             <tr>
                 <td>{index}: {send_item}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"check global consume, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        # 读取配置
        with open('/root/www/VirtualSIM_BackEnd/shell/white_user_rete.json') as f:
            rate_data = json.loads(f.read())

        send_limit_cnt = int(sys.argv[1])
        un_save_call_user_counts_alert = int(sys.argv[2])
        un_save_call_total_counts_alert = int(sys.argv[3])
        work(send_mail_cnt_alert=send_limit_cnt,
             un_save_call_user_counts_alert=un_save_call_user_counts_alert,
             un_save_call_total_counts_alert=un_save_call_total_counts_alert)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"check global consume, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
