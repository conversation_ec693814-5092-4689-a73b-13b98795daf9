import datetime
import smtplib
import sys
import time
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
import telnyx


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

non_config_number_list = []


def get_all_numbers() -> (set, dict):
    # 设置Telnyx API认证信息
    telnyx.api_key = "**********************************************************"
    numbers_set = set()
    number_sid = {}
    for i in range(1000):
        numbers = telnyx.PhoneNumber.list(page={"number": i + 1, "size": 100})

        if len(numbers) == 0 or not numbers or ("data" in numbers and len(numbers['data']) == 0):
            break
        for index, n in enumerate(numbers):
            if not n['connection_name']:
                print(f"[get_all_numbers] {index} item is nil")
                non_config_number_list.append(n)
                continue
            if not n['messaging_profile_id']:
                print(f"[get_all_numbers] {index} item is nil")
                non_config_number_list.append(n)
                continue

            if 'txtnow' in n['connection_name'] or n['messaging_profile_id'] == '4001891c-3745-4477-bf69-90749dd81d0d':
                numbers_set.add(n['phone_number'])
                number_sid[n['phone_number']] = n['id']
        print("index:", i, "number set size:", len(numbers_set))
        time.sleep(10)
    return numbers_set, number_sid


def get_db_numbers_v2(cursor) -> list:
    sql = "select number from Number_numberinventory where status in ('EXPIRE', 'USING')"
    cursor.execute(sql)
    res = cursor.fetchall()
    numbers_list = []
    for i in res:
        numbers_list.append(i['number'])
    return numbers_list


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    telnyx_numbers, number_sid = get_all_numbers()
    db_numbers = get_db_numbers_v2(cursor)

    in_telnyx_not_in_db = []
    in_db_not_in_telnyx = []
    insert_sql_list = []
    for i in telnyx_numbers:
        if i not in db_numbers:
            in_telnyx_not_in_db.append(i)
            # number = i
            # region = number[2:5]
            # friendly_name = f"({region}){number[5:8]}-{number[8:]}"
            # sid = number_sid[i]
            # capabilities = '[{\"name\": \"sms\"}, {\"name\": \"mms\"}, {\"name\": \"voice\"}, {\"name\": \"fax\"}, {\"name\": \"emergency\"}]'
            # sql = f"""INSERT INTO `Number_numberinventory` (`number`,`friendly_name`,`sid`,`created_at`,`updated_at`,`release_at`,`capabilities`,`status`,`platform`,`tw_version`) VALUES ('{number}','{friendly_name}','{sid}',now(), now(), null, '{capabilities}' ,'EXPIRE',1,0);"""
            # insert_sql_list.append(sql)
    for i in db_numbers:
        if i not in telnyx_numbers:
            in_db_not_in_telnyx.append(i)

    insert_cnt = len(insert_sql_list)
    for sql in insert_sql_list:
        print(sql)
        res = cursor.execute(sql)
        print(res)

    db.commit()
    cursor.close()
    db.close()

    if len(in_telnyx_not_in_db) > 0 or len(in_db_not_in_telnyx) > 0 or insert_cnt > 0 \
            or len(telnyx_numbers) != len(db_numbers):
        send_mail(len(telnyx_numbers), len(db_numbers), insert_cnt, in_telnyx_not_in_db, in_db_not_in_telnyx,
                  non_config_number_list)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(telnyx_number_cnt: int, db_number_cnt: int, insert_cnt: int, in_telnyx_not_in_db: list,
              in_db_not_in_telnyx: list, non_config_number_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}telnyx和数据库号码映射情况', 'utf-8').encode()

        txt = f"<br>telnyx_number_cnt: {telnyx_number_cnt}"
        txt += f"<br>db_number_cnt: {db_number_cnt} <br>"
        txt += f"<br>insert db cnt: {insert_cnt} <br>"
        txt += f"<br>in_telnyx_not_in_db: <br>"
        for i in in_telnyx_not_in_db:
            txt += f"<br>   {i}"
        txt += f"<br>in_db_not_in_telnyx: <br>"
        for i in in_db_not_in_telnyx:
            txt += f"<br>   {i}"

        if non_config_number_list:
            txt += f"<br> 未配置的号码列表"
            for i in non_config_number_list:
                txt += f"<br>   {i}"

        msg.attach(MIMEText(txt, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print(f"telnyx compare db numbers start time: {datetime.datetime.now(datetime.timezone.utc)}")

    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail(-1, -1, -1, [], [], [])
    finally:
        print(f"telnyx compare db numbers finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
