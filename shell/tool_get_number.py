import sys
from collections import defaultdict

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_user_by_number(cursor, number_str: str):
    cmd = f"select * from Number_numberinventory where number like '%{number_str}%' ;"
    print(cmd)
    cursor.execute(cmd)
    res = cursor.fetchall()
    if not res:
        print(f"number: {number_str} not in Number_numberinventory!!!")
        return None
    if len(res) > 1:
        print(f"ERROR: why has two row in Number_numberinventory for {number_str} ???")

    number_obj = res[0]
    if number_obj['status'] == 'RELEASED':
        print(f"number: {number_str} in Number_numberinventory but status is RELEASED!!!")
        return None

    cmd = f"select * from Number_numberused where number_id={number_obj['id']};"
    print(cmd)
    cursor.execute(cmd)
    res2 = cursor.fetchall()
    if not res2:
        print(f"number: {number_str} not in Number_numberused  !!!")
        return None
    if len(res2) > 1:
        print(f"ERROR: why has two row in Number_numberused for {number_str} ???")
    number_used = res2[0]
    if number_used['status'] != 'USING':
        print(f"number: {number_str}  in Number_numberused but status is {number_used['status']}, "
              f"expired_at: {number_used['expired_at']} not USING!!!")
        return None

    user = get_user(cursor, number_used['user_id'])
    return user


def get_user_callrecord(cursor, user_id: int) -> list:
    cmd = f"select * from Call_callrecord where user_id={user_id};"
    print(cmd)
    cursor.execute(cmd)
    return cursor.fetchall()


def get_user_smsrecord(cursor, user_id: int) -> list:
    cmd = f"select * from Call_smsrecord where user_id={user_id};"
    print(cmd)
    cursor.execute(cmd)
    return cursor.fetchall()


def get_user_point(cursor, user_id: int):
    cmd = f"select * from Point_point where user_id={user_id};"
    print(cmd)
    cursor.execute(cmd)
    return cursor.fetchall()[0]


def get_user_pointrecords(cursor, user_id: int) -> list:
    cmd = f"select * from Point_pointrecord where user_id={user_id};"
    print(cmd)
    cursor.execute(cmd)
    return cursor.fetchall()


def get_user(cursor, user_id: int) -> dict:
    cmd = f"select * from User_user where id={user_id};"
    print(cmd)
    cursor.execute(cmd)
    users = cursor.fetchall()
    print(f"all user size: {len(users)}")
    if len(users) >= 1:
        return users[0]
    return None


def work(number: str):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    user = get_user_by_number(cursor, number)
    if not user:
        print("number not in Number_numberinventory or not in Number_numberuserd")
        return

    user_id = user['id']

    user_point = get_user_point(cursor, user_id)
    user_calls = get_user_callrecord(cursor, user_id)
    user_sms = get_user_smsrecord(cursor, user_id)
    user_pointrecords = get_user_pointrecords(cursor, user_id)

    print("*" * 20 + ' [total info] ' + '*' * 20)
    print(f"user: {user_id}, created_at: {user['created_at']}, expired_at: {user['expired_at']}")
    print(f"point : {user_point['point']}")
    print(f"point records size: {len(user_pointrecords)}, sum point : {sum([v['point'] for v in user_pointrecords])}")
    print(f"call records size: {len(user_calls)}")
    print(f"sms records size: {len(user_sms)}")

    print("*" * 20 + ' [point details] ' + '*' * 20)
    point_records_event_map = defaultdict(int)
    for index, user_pointrecord in enumerate(user_pointrecords):
        print(f"index:{index}, event:{user_pointrecord['event']}, point: {user_pointrecord['point']}, "
              f"create:{user_pointrecord['created_at']}, record_id: {user_pointrecord['record_id']}")
        point_records_event_map[user_pointrecord['event']] += user_pointrecord['point']

    print("*" * 20 + ' [point total] ' + '*' * 20)
    for k, v in point_records_event_map.items():
        print(f"point record event:{k}, sum point cost: {v}")

    print("*" * 20 + ' [call details] ' + '*' * 20)
    for index, call in enumerate(user_calls):
        print(
            f"idx: {index},id:{call['id']},direct:{call['direction']},from:{call['from_number']},to:{call['to_number']}, "
            f"status:{call['status']}, price:{call['price']}, dur:{call['duration']}, cb_dur:{call['cb_duration']}, "
            f"cb_call_dur:{call['cb_call_duration']}, point:{call['point']},"
            f"at: {call['created_at'].strftime('%Y-%m-%d %H:%M:%S')}")

    print("*" * 20 + ' [sms details] ' + '*' * 20)
    for index, sms in enumerate(user_sms):
        print(f"index: {index}, id:{sms['id']}, direction:{sms['direction']}, from:{sms['from_number']}, "
              f"to:{sms['to_number']}, status={sms['status']}, price:{sms['price']}, point:{sms['point']},"
              f" call at: {sms['created_at'].strftime('%Y-%m-%d %H:%M:%S')}")

    cursor.close()
    db.close()


if __name__ == '__main__':
    number = sys.argv[1]
    work(number)
