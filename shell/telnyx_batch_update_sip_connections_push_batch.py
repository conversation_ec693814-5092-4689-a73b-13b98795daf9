import threading
from queue import Queue

import requests


# 获取数据函数
def get_sip(page_number: int, page_size: int):
    url = "https://api.telnyx.com/v2/credential_connections"
    query = {
        "page[number]": f"{page_number}",
        "page[size]": f"{page_size}",
        "filter[outbound.outbound_voice_profile_id]": "2201593832611513488",
        "sort": "connection_name",
    }
    headers = {"Authorization": "Bearer KEY0193D036537A4B8864C8C5751E010685_OmTXhITKI4JA0jBUzKRhMh"}
    response = requests.get(url, headers=headers, params=query)
    return response.json()


# 更新数据函数
def update_a_sip(sid: str, failed_queue: Queue):
    url = f"https://api.telnyx.com/v2/credential_connections/{sid}"
    headers = {"Authorization": "Bearer KEY0193D036537A4B8864C8C5751E010685_OmTXhITKI4JA0jBUzKRhMh"}
    payload = {"ios_push_credential_id": "0303fa44-0bcd-484f-bbdb-b0a98a279ccc"}

    try:
        response = requests.patch(url, json=payload, headers=headers)
        data = response.json()
        print(data)
    except Exception as e:
        print(f"Failed to update {sid}: {e}")
        failed_queue.put(sid)


# 线程工作函数
def worker(update_queue: Queue, failed_queue: Queue):
    while not update_queue.empty():
        sid = update_queue.get()
        update_a_sip(sid, failed_queue)
        update_queue.task_done()


if __name__ == '__main__':
    # 设置线程数
    num_threads = 3

    # 队列
    update_queue = Queue()
    failed_queue = Queue()

    # 获取数据并填充队列
    for i in range(100):
        print("load data batch index:", i)
        data = get_sip(i + 1, 100)
        if len(data.get('data', [])) == 0:
            break
        for d in data['data']:
            if 'txtnow' in d['user_name'] and d["ios_push_credential_id"] != "0303fa44-0bcd-484f-bbdb-b0a98a279ccc":
                update_queue.put(d['id'])

    print("update_queue size:", update_queue.qsize())

    # 启动线程
    threads = []
    for _ in range(num_threads):
        t = threading.Thread(target=worker, args=(update_queue, failed_queue))
        t.start()
        threads.append(t)

    # 等待线程完成
    for t in threads:
        t.join()

    # 输出失败记录
    with open("failed_update_push_v2_20250814.txt", "w") as f:
        while not failed_queue.empty():
            failed_id = failed_queue.get()
            f.write(f"{failed_id}\n")

    print("Update completed.")
