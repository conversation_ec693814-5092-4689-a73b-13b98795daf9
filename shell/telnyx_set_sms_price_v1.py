import sys


def mdr_request():
    import requests
    import json

    url = "https://api.telnyx.com/reporting/mdr_requests"

    payload = json.dumps({
        "start_time": "2024-03-01T00:00:00+00:00",
        "end_time": "2024-03-17T00:00:00+00:00",
        "call_types": [
            0
        ],
        "record_types": [
            0
        ],
        "connections": [
            "string"
        ],
        "profiles": [
            "string"
        ],
        "report_name": "string",
        "filters": [
            "string"
        ],
        "directions": [
            "string"
        ],
        "timezone": "UTC"
    })
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-user': "<EMAIL>",
        "x-api-token": "SrB-94oARdeAUFzSseDONg"
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)


def work(limit: int):
    mdr_request()


if __name__ == '__main__':
    if len(sys.argv) != 2:
        limit = 100
    else:
        limit = int(sys.argv[1])

    work(limit)
