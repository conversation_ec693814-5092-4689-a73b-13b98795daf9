# coding=utf-8
import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_month_ago_date_str(month_ago: int) -> str:
    import datetime

    # 获取当前日期
    current_date = datetime.datetime.now().date()

    # 计算六个月前的日期
    months_ago = current_date - datetime.timedelta(days=30 * month_ago)

    # 将日期转换为字符串格式
    months_ago_str = months_ago.strftime('%Y-%m-%d')

    print(months_ago_str)  # 输出：2022-10-01
    return months_ago_str


def work(month_ago: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)
    month_ago_str = get_month_ago_date_str(month_ago)

    # 找到三个月前创建，正在使用的号码
    sql = f"select id, number from Number_numberinventory where status='USING' and created_at <= '{month_ago_str}'"
    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()

    release_cnt = 0
    release_numbers = []
    for index, i in enumerate(res):
        number_id = i['id']
        number = i['number']
        # 三个月内有记录
        sql1 = f"select count(1) from Call_smsrecord where from_number='{number}' and created_at >= '{month_ago_str}'"
        sql2 = f"select count(1) from Call_smsrecord where (to_number='{number}' or to_number='{number.replace('+', '')}')  and created_at >= '{month_ago_str}'"
        sql3 = f"select count(1) from Call_callrecord where from_number='{number}' and created_at >= '{month_ago_str}'"
        sql4 = f"select count(1) from Call_callrecord where (to_number='{number}' or to_number='{number.replace('+', '')}')  and created_at >= '{month_ago_str}'"
        cursor.execute(sql1)
        res1 = cursor.fetchall()
        if res1[0]['count(1)'] > 0:
            continue
        cursor.execute(sql2)
        res2 = cursor.fetchall()
        if res2[0]['count(1)'] > 0:
            continue
        cursor.execute(sql3)
        res3 = cursor.fetchall()
        if res3[0]['count(1)'] > 0:
            continue
        cursor.execute(sql4)
        res4 = cursor.fetchall()
        if res4[0]['count(1)'] > 0:
            continue
        release_cnt += 1
        release_numbers.append({"number_id": number_id, "number": number})
        sql5 = f"update Number_numberused set status='EXPIRE' where number_id={number_id}"
        sql6 = f"update Number_numberinventory set status='EXPIRE' where id={number_id}"
        cursor.execute(sql5)
        cursor.execute(sql6)

    print(f"long time no use numbers size: {len(res)}")
    print(f"release_cnt: {release_cnt}")
    print(f"release_numbers: {release_numbers}")
    db.commit()
    cursor.close()
    db.close()

    # 看情况发送邮件
    if release_cnt > 0:
        send_mail(release_numbers)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(release_numbers: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}释放VIP的号码', 'utf-8').encode()

        html = f'<html><body><h2>释放VIP的号码：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += f"""
         <table border="1">
        <tr>
             <th>index</th>
             <th>号码 ID</th>
             <th>号码</th>
         </tr>
         """
        for index, item in enumerate(release_numbers):
            html += f"""
             <tr>
                 <td>{index}</td><td>{item["number_id"]}</td><td>{item["number"]}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"release vip numbers, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        month_ago = int(sys.argv[1])
        if month_ago < 3:
            print("month_ago must be >= 3")
            month_ago = 3
        work(month_ago=month_ago)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail([])
    finally:
        print(f"release vip numbers, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
