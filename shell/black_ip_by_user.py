# encoding=utf-8
import datetime
import smtplib
import subprocess
import sys
import time
import traceback
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

log_path = "/var/log/nginx/access.log"
black_ip_file_path = '/etc/nginx/ip.black'

today = time.strftime("%Y-%m-%d", time.localtime(time.time()))
now = datetime.datetime.now(datetime.timezone.utc)

black_userids = ['26', '120', '217', '224', '227', '229', '230', '232', '234', '235', '237', '244', '246']
black_ip = set()
black_ip_userid_map = {}


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_black_ip():
    with open(log_path) as log_f:
        while True:
            line = log_f.readline()
            if not line:
                break

            a = line.split(" ")
            ip = a[0]

            for black_userid in black_userids:
                black_userid_str = 'userid=%s&' % black_userid
                if black_userid_str in line:
                    black_ip.add(ip)
                    black_ip_userid_map[ip] = black_userid
                    break


def work():
    send_list = []
    if black_ip:
        # 更新黑名单列表
        with open(black_ip_file_path, 'r') as black_f:
            current_black_ips = black_f.readlines()

        write_cnt = 0
        with open(black_ip_file_path, 'a') as black_f:
            _current_black_ips = current_black_ips

            for bip in black_ip:
                for line in _current_black_ips:
                    if bip in line:
                        print(f"{bip} already in black, pass")
                        break
                else:
                    write_cnt += 1
                    send_list.append((bip, black_ip_userid_map[bip]))
                    print(f"black ip: {bip} set in /etc/nginx/ip.black...")
                    black_f.write(f'deny {bip}; # black userid {black_ip_userid_map[bip]} at {now} \n')

        # 重启nginx
        if write_cnt > 0:
            print(f"write ip cnt: {write_cnt},  reload nginx...")
            subprocess.call('/usr/sbin/nginx -s reload', shell=True)

            send_mail(send_list)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}因为userid上了黑名单被封禁IP ！！！', 'utf-8').encode()

        html = f'<html><body><h2>因为userid上了黑名单被封禁IP：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
        <table border="1">
         <tr>
            <th>IP</th>
            <th>黑名单USERID</th>
        </tr>
        """
        for send_item in send_list:
            html += f"""
            <tr>
                <td>{send_item[0]}</td>
                <td>{send_item[1]}</td>
            </tr>
            """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"black user time: {now}")
    print("*" * 20)
    try:
        get_black_ip()
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail(["服务器执行脚本异常，快去看看吧"])
    finally:
        print(f"block user, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
