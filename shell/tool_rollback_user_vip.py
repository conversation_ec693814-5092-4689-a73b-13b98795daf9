import sys

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(user_id: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)
    cmd = f"update User_user set expired_at=now() where id={user_id};"
    print(cmd)
    affected_rows = cursor.execute(cmd)
    db.commit()
    print(f"更新用户的 is_add_point，影响行数 : {affected_rows}")

    # valid = 0 就不用update了，一定是不可以的
    cmd = f"select valid from Order_order where user_id={user_id};"
    cursor.execute(cmd)
    print(cmd)
    orders = cursor.fetchall()
    assert len(orders) == 1

    if orders[0]['valid'] == 0:
        cmd = f"update Order_order set order_status='CLOSED' where user_id={user_id};"
    elif orders[0]['valid'] == 1:
        cmd = f"update Order_order set expiration_intent=5, expire_at=now(), order_status='CLOSED' " \
              f"where user_id={user_id};"
    print(cmd)
    affected_rows = cursor.execute(cmd)
    db.commit()
    print(f"更新订单的 expiration_intent，影响行数 : {affected_rows}")

    cmd = f"update Point_point set point=point-30 where user_id={user_id};"
    print(cmd)
    affected_rows = cursor.execute(cmd)
    db.commit()
    print(f"更新点数的 point，影响行数: {affected_rows}")

    cmd = f"select * from Number_numberused where user_id={user_id} and status='USING';"
    cursor.execute(cmd)
    print(cmd)
    numbers = cursor.fetchall()
    for n in numbers:
        print(f"该用户占用的号码：{n}")
    if len(numbers) > 0:
        cmd = f"update Number_numberused set status='EXPIRE' where user_id={user_id};"
        print(cmd)
        affected_rows = cursor.execute(cmd)
        db.commit()
        print(f"更新号码的使用，影响行数: {affected_rows}")

        for n in numbers:
            cmd = f"update Number_numberinventory set status='EXPIRE' where `number`='{n['number']}' and status='USING';"
            print(cmd)
            affected_rows = cursor.execute(cmd)
            db.commit()
            print(f"更新号码池子的使用，影响行数: {affected_rows}")

    cursor.close()
    db.close()


if __name__ == '__main__':
    user_id = sys.argv[1]

    work(int(user_id))
