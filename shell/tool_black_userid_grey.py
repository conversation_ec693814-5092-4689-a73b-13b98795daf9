# coding=utf-8
import sys

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(userid: int, reason: str):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = f"select * from User_blackuser where user_id={userid}"
    print(sql)
    cursor.execute(sql)
    black_users = cursor.fetchall()
    if len(black_users) > 0:
        print(f"user already handle: {userid}")
        return

    sql = f"select * from User_user where id={userid}"
    print(sql)
    cursor.execute(sql)
    users = cursor.fetchall()
    if not users or len(users) == 0:
        print(f"user not exist: {userid}")
        return
    print(users)
    uuid = users[0]['uuid']
    appid = users[0]['appid']

    if reason:
        sql = f"INSERT INTO `secphone`.`User_blackuser`(`user_id`, `uuid`, `appid`, `reason`, `ban_type`,  `created_at`, `updated_at`) VALUES ({userid}, '{uuid}', {appid}, '{reason}', 1, now(), now());"
    else:
        sql = f"INSERT INTO `secphone`.`User_blackuser`(`user_id`, `uuid`, `appid`, `ban_type`, `created_at`, `updated_at`) VALUES ({userid}, '{uuid}', {appid}, 1, now(), now());"
    print(sql)
    res = cursor.execute(sql)
    print(f"res: {res}")

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    userid = int(sys.argv[1])
    if len(sys.argv) == 3:
        reason = str(sys.argv[2])
    else:
        reason = None
    work(userid, reason)
