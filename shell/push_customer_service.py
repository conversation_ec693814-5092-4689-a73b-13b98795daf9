# coding=utf-8
import datetime
import json
import smtplib
import sys
import traceback
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pytz
import requests

# 邮箱定义
smtp_server = 'smtp.qq.com'
smtp_port = 587
from_addr = '<EMAIL>'
password = 'lrnnuiufajuwbhed'
to_addr = ['<EMAIL>']


def get_no_reply_cnt() -> int:
    url = "http://127.0.0.1:8000/zhphone/tools/checkCustomerServiceNoRelayCnt/"
    r = requests.get(url)
    data = json.loads(r.content)
    print(r.content)
    if data["err_code"] != 0:
        raise Exception("get pending customer support failed")
    no_reply_cnt = data["data"]["conversation_unread_size"]
    return no_reply_cnt


def work():
    no_reply_cnt = get_no_reply_cnt()
    if no_reply_cnt > 3:
        send_mail(no_reply_cnt)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(no_reply_cnt: int):
    try:
        beijing_tz = pytz.timezone('Asia/Shanghai')
        now = datetime.datetime.now(beijing_tz)
        print("current beijing time hour:", now.hour)

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'[TXTNOW]客服未处理数量:{no_reply_cnt}', 'utf-8').encode()
        html = f"""
             <html>
            <body><h2>客服未处理反馈数量:{no_reply_cnt}：{datetime.datetime.now(datetime.timezone.utc)}</h2>
            </body></html>
        """
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"customer service push, start time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail(-1)
    finally:
        print(f"customer service push, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
