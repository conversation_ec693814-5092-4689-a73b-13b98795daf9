# coding=utf-8
import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(send_limit_cnt: int, point_limit_cnt: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    hour_str = datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d %H") + ":00:00"

    sql = f"select * from Point_point where point <= -10  and updated_at >= '{hour_str}' ;"
    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()
    print(f"low point size: {len(res)}")

    db.commit()
    cursor.close()
    db.close()

    # 看情况发送邮件
    if res:
        if len(res) >= send_limit_cnt \
                or min([v['point'] for v in res]) <= point_limit_cnt:
            for send_item in res:
                print(send_item)
            send_mail(res, send_limit_cnt, point_limit_cnt)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list, send_limit_cnt: int, point_limit_cnt: int):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'[{env_config["EMAIL_PREFIX"]}]低点数报警 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>低点数报警：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += f"""
         <table border="1">
          <tr>
             <th>低点数</th>
         </tr>
           <tr>
             <th>报警条数limit_cnt: {send_limit_cnt}</th>
         </tr>
           <tr>
             <th>报警点数point_cnt: {point_limit_cnt}</th>
         </tr>
         """
        for index, send_item in enumerate(send_list):
            html += f"""
             <tr>
                 <td>{index}: {send_item}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"check low point, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        send_limit_cnt = int(sys.argv[1])
        point_limit_cnt = int(sys.argv[2])
        work(send_limit_cnt=send_limit_cnt, point_limit_cnt=point_limit_cnt)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([], 0, 0)
    finally:
        print(f"check low point, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
