import datetime
import smtplib
import sys
import time
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
import requests
import telnyx


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "TELNYX_API_KEY": env("TELNYX_API_KEY"),
        "TELNYX_MESSAGING_PROFILE_ID": env("TELNYX_MESSAGING_PROFILE_ID"),
        "TELNYX_TEXML_APPLICATION_ID": env("TELNYX_TEXML_APPLICATION_ID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

TELNYX_API_KEY = env_config.get('TELNYX_API_KEY')
TELNYX_MESSAGING_PROFILE_ID = env_config.get('TELNYX_MESSAGING_PROFILE_ID')
TELNYX_TEXML_APPLICATION_ID = env_config.get('TELNYX_TEXML_APPLICATION_ID')

const_txtnow_message_profile_id = TELNYX_MESSAGING_PROFILE_ID
const_txtnow_texml_id = TELNYX_TEXML_APPLICATION_ID
const_v3phone_message_profile_id = '40018738-5681-4919-baf4-1102289f5621'
const_v3phone_texml_id = '2152182937829770519'

const_v4phone_message_profile_id = '4001931a-a917-436e-a736-345599df6d80'
const_v4phone_texml_id = '2561660108996085587'

txtnow_success_message_profile_list = []
v3phone_success_message_profile_list = []


def update_number_message_profile(number: str, messaging_profile_id: str) -> bool:
    # https://developers.telnyx.com/openapi/phonenumbers/tag/Number-Configurations/#tag/Number-Configurations/operation/updatePhoneNumberWithMessagingSettings
    try:
        print(f"[TelnyxUtil.update_number_message_profile] {number}, message_profile:{messaging_profile_id}")
        url = "https://api.telnyx.com/v2/phone_numbers"
        query = {
            "filter[phone_number]": number,
        }

        headers = {"Authorization": f"Bearer {TELNYX_API_KEY}"}
        response = requests.get(url, headers=headers, params=query)
        data = response.json()
        print(f"[TelnyxUtil.update_number_message_profile] {number}, rsp:{data}")
        if len(data['data']) == 0:
            print(f"[TelnyxUtil.update_number_message_profile] {number} not in Telnyx...")
            return False

        for i in data['data']:
            phone_number = telnyx.PhoneNumber.retrieve(i['id'])
            messaging_resource = phone_number.messaging()
            messaging_resource.messaging_profile_id = messaging_profile_id
            messaging_resource.save()

            if messaging_profile_id == const_txtnow_message_profile_id:
                txtnow_success_message_profile_list.append(number)
            else:
                v3phone_success_message_profile_list.append(number)

            return True

        print(f"[TelnyxUtil.update_number_message_profile] {number}, update failed!")
        return False
    except Exception:
        print(f"[TelnyxUtil.update_number_message_profile] {number} failed")
        traceback.print_exc()
        return False


def get_db_numbers_v2() -> list:
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = "select number from Number_numberinventory where status in ('EXPIRE', 'USING')"
    cursor.execute(sql)
    res = cursor.fetchall()
    numbers_list = []
    for i in res:
        numbers_list.append(i['number'])

    db.commit()
    cursor.close()
    db.close()

    return numbers_list


def work():
    # 设置Telnyx API认证信息
    telnyx.api_key = TELNYX_API_KEY

    numbers_set = set()

    txtnow_cnt = 0
    txtnow_numbers = []
    txtnow_invalid_cnt = 0
    txtnow_invalid_list = []

    v3phone_cnt = 0
    v3phone_numbers = []
    v3phone_invalid_cnt = 0
    v3phone_invalid_list = []

    v4phone_cnt = 0
    v4phone_numbers = []
    v4phone_invalid_cnt = 0
    v4phone_invalid_list = []

    invalid_cnt = 0
    invalid_number_list = []
    invalid_number_reason = {}

    v2_db_numbers = get_db_numbers_v2()

    for i in range(1000):
        numbers = telnyx.PhoneNumber.list(page={"number": i + 1, "size": 100})
        if len(numbers) == 0 or not numbers or ("data" in numbers and len(numbers['data']) == 0):
            break
        for index, n in enumerate(numbers):
            numbers_set.add(n['phone_number'])
            if (n['messaging_profile_id'] == const_txtnow_message_profile_id
                    or n['connection_id'] == const_txtnow_texml_id):
                txtnow_cnt += 1
                txtnow_numbers.append(n['phone_number'])
            elif (n['messaging_profile_id'] == const_v3phone_message_profile_id
                  or n['connection_id'] == const_v3phone_texml_id):
                v3phone_cnt += 1
                v3phone_numbers.append(n['phone_number'])
            elif (n['messaging_profile_id'] == const_v4phone_message_profile_id
                  or n['connection_id'] == const_v4phone_texml_id):
                v4phone_cnt += 1
                v4phone_numbers.append(n['phone_number'])
            else:
                invalid_cnt += 1
                invalid_number_list.append(n)
                invalid_number_reason[n['phone_number']] = ['no messaging_profile_id']

            # txtnow
            if (n['connection_id'] == const_txtnow_texml_id and
                    n['messaging_profile_id'] != const_txtnow_message_profile_id):
                txtnow_invalid_cnt += 1
                txtnow_invalid_list.append({n['phone_number']: 'v2 no msg profile'})
                update_number_message_profile(n['phone_number'], const_txtnow_message_profile_id)
            if (n['connection_id'] != const_txtnow_texml_id and
                    n['messaging_profile_id'] == const_txtnow_message_profile_id):
                txtnow_invalid_cnt += 1
                txtnow_invalid_list.append({n['phone_number']: 'v2 no voice profile'})

            # v3phone
            if (n['connection_id'] == const_v3phone_texml_id and
                    n['messaging_profile_id'] != const_v3phone_message_profile_id):
                v3phone_invalid_cnt += 1
                v3phone_invalid_list.append({n['phone_number']: 'v3 no msg profile'})
                update_number_message_profile(n['phone_number'], const_v3phone_message_profile_id)
            if (n['connection_id'] != const_v3phone_texml_id and
                    n['messaging_profile_id'] == const_v3phone_message_profile_id):
                v3phone_invalid_cnt += 1
                v3phone_invalid_list.append({n['phone_number']: 'v3 no voice profile'})

            # v4phone
            if (n['connection_id'] == const_v4phone_texml_id and
                    n['messaging_profile_id'] != const_v4phone_message_profile_id):
                v4phone_invalid_cnt += 1
                v4phone_invalid_list.append({n['phone_number']: 'v4 no msg profile'})
                update_number_message_profile(n['phone_number'], const_v4phone_message_profile_id)
            if (n['connection_id'] != const_v4phone_texml_id and
                    n['messaging_profile_id'] == const_v4phone_message_profile_id):
                v4phone_invalid_cnt += 1
                v4phone_invalid_list.append({n['phone_number']: 'v4 no voice profile'})

            if n['status'] != 'active':
                if n['phone_number'] in invalid_number_reason:
                    invalid_number_reason[n['phone_number']].append('status invalid: ' + n['status'])
                else:
                    invalid_cnt += 1
                    invalid_number_list.append(n)
                    invalid_number_reason[n['phone_number']] = ['status invalid: ' + n['status']]

        print("index:", i, "number set size:", len(numbers_set))
        time.sleep(1)

    print("v2_db_number size:", len(v2_db_numbers))
    print("total number size:", len(numbers_set))
    print("txtnow_cnt:", txtnow_cnt)
    print("txtnow_invalid_cnt:", txtnow_invalid_cnt)
    print("v3phone_cnt:", v3phone_cnt)
    print("v3phone_invalid_cnt:", v3phone_invalid_cnt)
    print("v4phone_cnt:", v4phone_cnt)
    print("v4phone_invalid_cnt:", v4phone_invalid_cnt)
    print("invalid_cnt:", invalid_cnt)

    context = {
        "v2_db_number size:": len(v2_db_numbers),
        "telnyx total number size:": len(numbers_set),
        "telnyx txtnow_cnt:": txtnow_cnt,
        "telnyx txtnow_numbers cnt:": len(txtnow_numbers),
        "telnyx txtnow_invalid_cnt:": txtnow_invalid_cnt,
        "telnyx v3phone_cnt:": v3phone_cnt,
        "telnyx v4phone_cnt:": v4phone_cnt,
        "telnyx v3phone_invalid_cnt:": v3phone_invalid_cnt,
        "telnyx v4phone_invalid_cnt:": v4phone_invalid_cnt,
        "telnyx invalid_cnt:": invalid_cnt,
        "telnyx txtnow_invalid_list:": txtnow_invalid_list,
        "telnyx v3phone_invalid_list:": v3phone_invalid_list,
        "telnyx v4phone_invalid_list:": v4phone_invalid_list,
        "telnyx invalid_number_reason:": invalid_number_reason,
        "telnyx txtnow_success_message_profile_list:": txtnow_success_message_profile_list,
        "telnyx v3phone_success_message_profile_list:": v3phone_success_message_profile_list,
    }

    if len(v2_db_numbers) != txtnow_cnt:
        context["v2_in_db_not_in_telnyx"] = [v for v in v2_db_numbers if v not in txtnow_numbers]
        context["v2_in_telnyx_not_in_db"] = [v for v in txtnow_numbers if v not in v2_db_numbers]

    for k, v in context.items():
        print(f"#### {k} ####")
        if isinstance(v, list):
            for i in v:
                print(i)
        elif isinstance(v, dict):
            for q, p in v.items():
                print(q, v)
        else:
            print(v)

    if len(v2_db_numbers) != txtnow_cnt or len(txtnow_invalid_list) > 0 or len(v3phone_invalid_list) > 0:
        send_mail(context)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(context: dict):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}telnyx号码配置不正确', 'utf-8').encode()

        txt = ""
        for k, v in context.items():
            txt += "<br>#################"
            txt += f"<br>{k}"
            if isinstance(v, list):
                for index, i in enumerate(v):
                    txt += f"<br> {index}: {i}"
            elif isinstance(v, dict):
                cnt = 0
                for q, p in v.items():
                    txt += f"<br> {cnt}: {q}={p}"
                    cnt += 1
            else:
                txt += f"={v}"

        msg.attach(MIMEText(txt, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print(f"telnyx monitor number configuration start time: {datetime.datetime.now(datetime.timezone.utc)}")

    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail({})
    finally:
        print(f"telnyx monitor number configuration finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
