# coding=utf-8
import pymysql
import requests


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

APPLE_VERIFY_URL = 'https://buy.itunes.apple.com/verifyReceipt'
APPLE_VERIFY_URL_SANDBOX = 'https://sandbox.itunes.apple.com/verifyReceipt'

APPLE_VERIFY_PASSWORD = {
    0: '1e92fd6e7e5f40678fd25c8a8b99f71e',
    1: 'e76da754616a40c3a254f2acd8401136',
}


def get_cert_rsp_from_apple(cert, appid) -> (dict, bool):
    # 先过一次生产校验
    data = {"receipt-data": cert, "password": APPLE_VERIFY_PASSWORD[int(appid)]}
    r = requests.post(APPLE_VERIFY_URL, json=data)
    res = r.json()

    # 如果苹果返回21007，就要去沙箱校验
    apple_return_status = res['status']
    if apple_return_status == 21007:
        data = {"receipt-data": cert, "password": APPLE_VERIFY_PASSWORD[int(appid)]}
        r = requests.post(APPLE_VERIFY_URL_SANDBOX, json=data)
        res = r.json()
        return res, True

    return res, False


def get_purchase_points_item(in_app_list: list, db_transaction_id: str):
    for j in in_app_list:
        if j['transaction_id'] == db_transaction_id:
            return j
    return None


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = f"""select * from Order_orderconsume order by id desc limit 300"""
    cursor.execute(sql)
    result = cursor.fetchall()
    for index, db_order in enumerate(result):
        # print(db_order)
        apple_res, is_sandbox = get_cert_rsp_from_apple(db_order['certificate'], 1)
        if is_sandbox:
            sql = f"update Order_orderconsume set order_status='checked_sandbox' where id={db_order['id']}"
            print(index, sql)
            cursor.execute(sql)
            continue

        if apple_res['environment'] != 'Production':
            sql = f"update Order_orderconsume set order_status='checked_not_production' where id={db_order['id']}"
            print(index, sql)
            cursor.execute(sql)
            continue

        in_app_list = apple_res['receipt']['in_app']
        purchase_item = get_purchase_points_item(in_app_list, db_order['transaction_id'])
        if not purchase_item:
            sql = f"update Order_orderconsume set order_status='checked_no_item' where id={db_order['id']}"
            print(index, sql)
            cursor.execute(sql)
            continue

        print(purchase_item)
        for k, v in purchase_item.items():
            if 'cancel' in str(k).lower() or 'cancel' in str(v).lower() \
                    or 'refund' in str(k).lower() or 'refund' in str(v).lower():
                print("\t", k, v)
                print("#######")
                print("#######")
                print("#######")
                print("#######")
                sql = f"update Order_orderconsume set order_status='checked_refund' where id={db_order['id']}"
                print(index, sql)
                cursor.execute(sql)
                break
        else:
            sql = f"update Order_orderconsume set order_status='checked_ok' where id={db_order['id']}"
            print(index, sql)
            cursor.execute(sql)

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    work()
