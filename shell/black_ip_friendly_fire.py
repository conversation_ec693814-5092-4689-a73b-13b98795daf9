# encoding=utf-8
import datetime
import smtplib
import subprocess
import sys
import time
import traceback
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

log_path = "/var/log/nginx/access.log"
black_ip_file_path = '/etc/nginx/ip.black'

today = time.strftime("%Y-%m-%d", time.localtime(time.time()))
now = datetime.datetime.now(datetime.timezone.utc)


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_this_hour_str() -> str:
    # 上一个小时
    dt = datetime.datetime.now(datetime.timezone.utc)
    t1 = dt.strftime("%d/%b/%Y:%H:%M:%S")
    t2 = t1.split(":")
    return ':'.join(t2[0:2])


def get_access_log() -> list:
    hour_str = get_this_hour_str()
    cmd = f'cat /var/log/nginx/access.log  | grep {hour_str}'
    print(f"cmd1: {cmd}")

    output, error = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    lines1 = output.decode("utf-8").split("\n")
    return lines1


def work():
    lines = get_access_log()
    print(f"line size: {len(lines)}")

    valid_urls = ["/phone/", "/user/checkUser/", "/user/unifyLogin/", "/user/getProfile/",
                  "/number/", "/config/points/", "/config/vip/", "/point/", "/order/"]
    send_list = []

    for line in lines:
        if not line:
            continue

        if "\"403\"" in line:
            for t in valid_urls:
                if t in line:
                    send_list.append(line)

    if send_list:
        send_mail(send_list)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}可能误伤友军的封IP', 'utf-8').encode()

        html = f'<html><body><h2>当前小时可能误伤友军的封IP：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
        <table border="1">
         <tr>
            <th>误伤日志</th>
        </tr>
        """
        for send_item in send_list:
            html += f"""
            <tr>
                <td>{send_item}</td>
            </tr>
            """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"friendly fire ip, time: {now}")
    print("*" * 20)
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"friendly fire ip, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
