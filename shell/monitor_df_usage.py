# coding=utf-8
import datetime
import smtplib
import subprocess
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(ip, df_usage_rate, mem_rate, cpu_rate):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}Warning:服务器硬盘和内存使用率报警！！！', 'utf-8').encode()

        txt = f"IP：{ip} <br>"
        txt += f"硬盘使用率：{round(df_usage_rate, 2)} <br>"
        txt += f"内存使用率：{round(mem_rate, 2)} <br>"
        txt += f"CPU使用率：{round(cpu_rate, 2)} <br>"

        msg.attach(MIMEText(txt, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    print("send a slack message ... ")
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    try:
        cmd = "df -h | sed -n '4p' | awk '{print $5}'"
        output, error = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
        usage_rate = output.decode('utf-8').replace("%", "")
        usage_rate = int(usage_rate)

        print(f"df usage rate: {usage_rate}")

        cmd = "free | awk 'NR==2{print $NF, $2, $3}'"
        output2, error2 = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE,
                                           stderr=subprocess.PIPE).communicate()
        mem_rate_str = output2.decode('utf-8').replace("%", "")
        mem_rate_split = str(mem_rate_str).split()
        avail_mem = int(mem_rate_split[0])
        full_mem = int(mem_rate_split[1])
        used_mem = int(mem_rate_split[2])
        mem_rate = (used_mem / full_mem) * 100
        print(f"mem used: {mem_rate}")

        cmd = "vmstat | awk '{print $13, $14}' | sed -n '$p'"
        output3, error3 = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE,
                                           stderr=subprocess.PIPE).communicate()
        cpu_rate_str = output3.decode('utf-8').replace("%", "")
        cpu_rate_split = str(cpu_rate_str).split()
        cpu_us = int(cpu_rate_split[0])
        cpu_sy = int(cpu_rate_split[1])
        cpu_rate = cpu_us + cpu_sy
        print(f"cpu rate: {cpu_rate}")

        if len(sys.argv) == 1:
            df_limit = 50
            mem_limit = 60
            cpu_limit = 40
        elif len(sys.argv) == 2:
            df_limit = int(sys.argv[1])
            mem_limit = 60
            cpu_limit = 40
        elif len(sys.argv) == 3:
            df_limit = int(sys.argv[1])
            mem_limit = int(sys.argv[2])
            cpu_limit = 40
        else:
            df_limit = int(sys.argv[1])
            mem_limit = int(sys.argv[2])
            cpu_limit = int(sys.argv[3])

        cmd = """ifconfig -a|grep inet|grep -v 127.0.0.1|grep -v inet6|awk '{print $2}'|tr -d "addr:" """
        output4, error4 = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE,
                                           stderr=subprocess.PIPE).communicate()
        ip = output4.decode('utf-8').replace("%", "").replace("\n", ", ")
        print(f"ip: {ip}")

        if usage_rate >= df_limit or mem_rate >= mem_limit or cpu_rate >= cpu_limit:
            send_mail(ip, usage_rate, mem_rate, cpu_rate)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail('', -1, -1, -1)
    finally:
        print(f"df usage rate finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
