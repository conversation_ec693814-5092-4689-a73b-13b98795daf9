import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql

white_user_list = [1, 2, 3, 4, 5, 6, 37, 36, 41, 52, 397, 784, 6675, 6685]


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_all_baipiao_users(cursor, appid: int) -> list:
    cmd = f"""
    select user_id, expire_at, created_at as order_created_at from Order_order 
    where valid=1 and expiration_intent not in (5,6) and DATEDIFF(expire_at, created_at) <= 5 and appid={appid}
    """
    print(cmd)
    cursor.execute(cmd)
    users = cursor.fetchall()
    print(f"all baipiao size: {len(users)}")
    return users


def get_all_user_cnt(cursor, appid: int) -> int:
    cursor.execute(f"select count(*) as user_cnt from User_user where appid={appid}")
    users = cursor.fetchall()
    return users[0]['user_cnt']


def get_all_pay_user(cursor, appid: int) -> list:
    cmd = f"""select user_id, expire_at as expired_at, created_at, created_at as order_created_at from Order_order 
    where valid=1 and expiration_intent not in (5,6) 
    and DATEDIFF(expire_at, created_at) >= 10 and appid={appid} 
    and user_id not in ({','.join([str(v) for v in white_user_list])});
    """
    print(cmd)
    cursor.execute(cmd)
    users = cursor.fetchall()
    print(f"all pay user size: {len(users)}")
    return users


def get_all_consume(cursor, appid: int) -> list:
    cursor.execute(
        f"select point, t1.created_at from Point_pointrecord t1 left join User_user t2  on  t1.user_id=t2.id "
        f"where event='CHARGE' and t2.appid={appid} and user_id not in ({','.join([str(v) for v in white_user_list])}); ")
    orders = cursor.fetchall()
    print(f"all consumer size: {len(orders)}")
    return orders


def get_all_refund_users(cursor, appid: int) -> list:
    cursor.execute(f"select user_id, created_at from Order_order where expiration_intent=5 and appid={appid};")
    users = cursor.fetchall()
    print(f"all refund size: {len(users)}")
    return users


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(db, cursor,
              total_user_cnt: int,
              user_list: list,
              baipiao_users: list,
              refund_users: list,
              user_charges: list,
              appid: int):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>', '<EMAIL>', '<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}今日VIP用户汇总', 'utf-8').encode()

        month_pay_users = [v for v in user_list if 25 < (v['expired_at'] - v['order_created_at']).days < 85]
        season_pay_users = [v for v in user_list if 85 <= (v['expired_at'] - v['order_created_at']).days < 300]
        year_pay_users = [v for v in user_list if (v['expired_at'] - v['order_created_at']).days >= 300]

        month_pay_count = len(month_pay_users)
        season_pay_count = len(season_pay_users)
        year_pay_count = len(year_pay_users)

        html = f'<html><body>'
        html += f"""
        <h2>VIP总览</h2>
        <table border="1">
         <tr>
            <th>总用户</th>
            <th>总VIP用户</th>
            <th>月度用户数</th>
            <th>季度用户数</th>
            <th>年度用户数</th>
            <th>总充点次数</th>
            <th>总充点点数</th>
            <th>我们自己测试用户</th>
            <th>白嫖用户</th>
            <th>退款用户</th>
        </tr>
        <tr>
            <td>{total_user_cnt}</td>
            <td>{month_pay_count + season_pay_count + year_pay_count}</td>
            <td>{month_pay_count}</td>
            <td>{season_pay_count}</td>
            <td>{year_pay_count}</td>
            <td>{len(user_charges)}</td>
            <td>{sum([v['point'] for v in user_charges])}</td>
            <td>{len(white_user_list)}</td>
            <td>{len(baipiao_users)}</td>
            <td>{len(refund_users)}</td>
        </tr>
        </table>
        """
        now = datetime.datetime.now(datetime.timezone.utc)
        yesterday = now + datetime.timedelta(days=-1)
        yesterday_str = yesterday.strftime("%Y%m%d")
        today_str = now.strftime("%Y%m%d")
        yesterday_dt = datetime.datetime.strptime(yesterday_str, "%Y%m%d")
        today_dt = datetime.datetime.strptime(today_str, "%Y%m%d")

        today_charges = [v for v in user_charges if yesterday_dt <= v['created_at'] < today_dt]
        # today_month_pay_count = len([v for v in month_pay_users if yesterday_dt <= v['order_created_at'] < today_dt])
        # today_season_pay_count = len([v for v in season_pay_users if yesterday_dt <= v['order_created_at'] < today_dt])
        # today_year_pay_count = len(
        #     [v for v in year_pay_users if
        #      yesterday_dt + datetime.timedelta(days=-3) <= v['order_created_at'] < today_dt + datetime.timedelta(
        #          days=-3)])

        today_month_pay_count = len([v for v in month_pay_users
                                     if yesterday_dt + datetime.timedelta(days=-3) <= v['order_created_at']
                                     < today_dt + datetime.timedelta(days=-3)])
        today_season_pay_count = len([v for v in season_pay_users if yesterday_dt <= v['order_created_at'] < today_dt])
        today_year_pay_count = len([v for v in year_pay_users if yesterday_dt <= v['order_created_at'] < today_dt])

        print(f"昨天：{yesterday_dt}")
        print(f"今天：{today_dt}")

        print(f"today_charges cnt: {len(today_charges)}")
        print(f"today_month_pay_count: {today_month_pay_count}")
        print(f"today_season_pay_count: {today_season_pay_count}")
        print(f"today_year_pay_count: {today_year_pay_count}")

        today_month_pay_sum = round(today_month_pay_count * 7.99, 2)
        today_season_pay_sum = round(today_season_pay_count * 14.99, 2)
        today_year_pay_sum = round(today_year_pay_count * 49.99, 2)

        today_charge_sum = 0
        for charge in today_charges:
            if charge['point'] == 100:
                today_charge_sum += 2.99
            if charge['point'] == 500:
                today_charge_sum += 12.99
            if charge['point'] == 1000:
                today_charge_sum += 15.99
            if charge['point'] == 2000:
                today_charge_sum += 28.99
        today_charge_sum = round(today_charge_sum, 2)

        html += f"""
        <h2>今日VIP总览 </h2>
        <table border="1">
         <tr>
            <th>今日总</th>
            <th>今日VIP</th>
            <th>今日年度VIP</th>
            <th>今日季度VIP</th>
            <th>今日月度VIP</th>
            <th>今日点数</th>
            <th>今日月度用户数</th>
            <th>今日季度用户数</th>
            <th>今日年度用户数</th>
            <th>今日充点次数</th>
            <th>今日充点点数</th>
        </tr>
        <tr>
            <td>{round(today_month_pay_sum + today_season_pay_sum + today_year_pay_sum + today_charge_sum, 2)}</td>
            <td>{round(today_month_pay_sum + today_season_pay_sum + today_year_pay_sum, 2)}</td>
            <td>{today_year_pay_sum}</td>
            <td>{today_season_pay_sum}</td>
            <td>{today_month_pay_sum}</td>
            <td>{today_charge_sum}</td>
            <td>{today_month_pay_count}</td>
            <td>{today_season_pay_count}</td>
            <td>{today_year_pay_count}</td>
            <td>{len(today_charges)}</td>
            <td>{sum([v['point'] for v in today_charges])}</td>
        </tr>
        </table>
        """

        month_pay_sum = round(month_pay_count * 7.99, 2)
        season_pay_sum = round(season_pay_count * 14.99, 2)
        year_pay_sum = round(year_pay_count * 49.99, 2)

        charge_sum = 0
        for charge in user_charges:
            if charge['point'] == 100:
                charge_sum += 3.99
            if charge['point'] == 500:
                charge_sum += 8.99
            if charge['point'] == 1000:
                charge_sum += 15.99
            if charge['point'] == 2000:
                charge_sum += 28.99
        charge_sum = round(charge_sum, 2)

        html += f"""
            <h2></h2>
            <table border="1">
             <tr>
                <th>总</th>
                <th>VIP总</th>
                <th>VIP月度</th>
                <th>VIP季度</th>
                <th>VIP年度</th>
                <th>充点总</th>
                <th>自己测试支出</th>
            </tr>
            <tr>
                <td>{round(month_pay_sum + season_pay_sum + year_pay_sum + charge_sum, 2)}</td>
                <td>{round(month_pay_sum + season_pay_sum + year_pay_sum, 2)}</td>
                <td>{month_pay_sum}</td>
                <td>{season_pay_sum}</td>
                <td>{year_pay_sum}</td>
                <td>{charge_sum}</td>
                <td>{len(white_user_list)}</td>
            </tr>
            </table>
        """

        html += '</body></html>'

        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()

        #  发完邮件把数据保存到DB
        try:
            data_list = [total_user_cnt, month_pay_count + season_pay_count + year_pay_count, month_pay_count,
                         season_pay_count, year_pay_count, len(user_charges),
                         sum([v['point'] for v in user_charges]), len(white_user_list), len(baipiao_users),
                         len(refund_users),
                         round(today_month_pay_sum + today_season_pay_sum + today_year_pay_sum + today_charge_sum, 2),
                         round(today_month_pay_sum + today_season_pay_sum + today_year_pay_sum, 2),
                         today_year_pay_sum, today_season_pay_sum, today_month_pay_sum, today_charge_sum,
                         today_month_pay_count, today_season_pay_count, today_year_pay_count,
                         len(today_charges), sum([v['point'] for v in today_charges]),
                         round(month_pay_sum + season_pay_sum + year_pay_sum + charge_sum, 2),
                         round(month_pay_sum + season_pay_sum + year_pay_sum, 2), month_pay_sum, season_pay_sum,
                         year_pay_sum, charge_sum, len(white_user_list)
                         ]
            save_db(db, cursor, data_list, appid)
        except Exception:
            traceback.print_exc()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def save_db(db, cursor, data_list: list, appid: int):
    total_income = data_list[21]
    total_vip_income = data_list[22]
    total_vip_month_income = data_list[23]
    total_vip_season_income = data_list[24]
    total_vip_year_income = data_list[25]
    total_point_income = data_list[26]

    today_total_income = data_list[10]
    today_vip_income = data_list[11]
    today_vip_year_income = data_list[12]
    today_vip_season_income = data_list[13]
    today_vip_month_income = data_list[14]
    today_point_income = data_list[15]

    total_user_cnt = data_list[0]
    total_vip_user_cnt = data_list[1]
    total_vip_month_user_cnt = data_list[2]
    total_vip_season_user_cnt = data_list[3]
    total_vip_year_user_cnt = data_list[4]
    total_baipiao_user_cnt = data_list[8]
    total_refund_user_cnt = data_list[9]

    today_vip_user_cnt = int(data_list[16]) + int(data_list[17]) + int(data_list[18])
    today_vip_month_user_cnt = data_list[16]
    today_vip_season_user_cnt = data_list[17]
    today_vip_year_user_cnt = data_list[18]
    today_point_charge_sum = data_list[20]
    today_point_charge_times = data_list[19]

    sql = f"""
               INSERT INTO `secphone`.`Report_finance` (
               `day`,
               `total_income`,
               `total_vip_income`,
               `total_vip_month_income`,
               `total_vip_season_income`,
               `total_vip_year_income`,
               `total_point_income`,

               `today_total_income`,
               `today_vip_income`,
               `today_vip_year_income`,
               `today_vip_season_income`,
               `today_vip_month_income`,
               `today_point_income`,

               `total_user_cnt`,
               `total_vip_user_cnt`,
               `total_vip_month_user_cnt`,
               `total_vip_season_user_cnt`,
               `total_vip_year_user_cnt`,
               `total_baipiao_user_cnt`,
               `total_refund_user_cnt`,

               `today_vip_user_cnt`,
               `today_vip_month_user_cnt`,
               `today_vip_season_user_cnt`,
               `today_vip_year_user_cnt`,
               `today_point_charge_sum`,
               `today_point_charge_times`,
               `appid`
           )
           VALUES
               (
                   '{datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d")}',
                   {total_income},
                   {total_vip_income},
                   {total_vip_month_income},
                   {total_vip_season_income},
                   {total_vip_year_income},
                   {total_point_income},
                   {today_total_income},
                   {today_vip_income},
                   {today_vip_year_income},
                   {today_vip_season_income},
                   {today_vip_month_income},
                   {today_point_income},
                   {total_user_cnt},
                   {total_vip_user_cnt},
                   {total_vip_month_user_cnt},
                   {total_vip_season_user_cnt},
                   {total_vip_year_user_cnt},
                   {total_baipiao_user_cnt},
                   {total_refund_user_cnt},
                   {today_vip_user_cnt},
                   {today_vip_month_user_cnt},
                   {today_vip_season_user_cnt},
                   {today_vip_year_user_cnt},
                   {today_point_charge_sum},
                   {today_point_charge_times},
                   {appid}
               );
       """
    print(sql)
    cursor.execute(sql)
    db.commit()


def work(appid: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    send_mail_list = []
    total_user_cnt = get_all_user_cnt(cursor, appid)
    users = get_all_pay_user(cursor, appid)
    baipiao_users = get_all_baipiao_users(cursor, appid)
    user_charges = get_all_consume(cursor, appid)
    refund_users = get_all_refund_users(cursor, appid)
    for user in users:
        days = (user['expired_at'] - user['created_at']).days
        msg = f"user: {user['user_id']},\t create:{user['created_at']},\t" \
              f"expire:{str(user['expired_at']).split('.')[0]},\tdelta_day:{days}"
        print(msg)

        send_mail_list.append([v.replace(',', '') for v in msg.strip().split('\t')])

    if send_mail_list:
        send_mail(db, cursor, total_user_cnt, users, baipiao_users, refund_users, user_charges, appid)

    # 关闭连接
    cursor.close()
    db.close()


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    try:
        if len(sys.argv) == 2:
            appid = int(sys.argv[1])
        else:
            appid = 1
        print(f"start get all pay user: {datetime.datetime.now(datetime.timezone.utc)}")
        work(appid)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail(None, None, 0, [], [], [], [], 0)
    finally:
        print(f"finished get all pay user: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
