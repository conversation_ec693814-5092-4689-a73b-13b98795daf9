# coding=utf-8
import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def is_run(year: int) -> bool:
    if (year % 4 == 0) and (year % 100 != 0) or (year % 400) == 0:
        return True
    else:
        return False


def helper(cursor, now_str, now_day, is_greater_than=False) -> list:
    """
        where  expire_at > '{now_str}' and valid=1 and order_status = 'OPEN' and DATEDIFF('{now_str}', created_at) >= 10

        # -- 筛选合法的订单
        # -- 今天的日期是25号 == expire的日期也是25号
        #  -- 订单创建日期至少要大于10天，否则有些白嫖用户也被抓出来

    :param cursor:
    :param now_str:
    :param now_day:
    :param is_greater_than:
    :return:
    """
    if is_greater_than:
        sql = f""" select user_id, expire_at, created_at  from Order_order where  expire_at > '{now_str}' and valid=1 and order_status = 'OPEN' and DATEDIFF('{now_str}', created_at) >= 10 and DAY(expire_at) >= {now_day}; """
    else:
        sql = f""" select user_id, expire_at, created_at  from Order_order where  expire_at > '{now_str}' and valid=1 and order_status = 'OPEN' and DATEDIFF('{now_str}', created_at) >= 10 and DAY(expire_at) = {now_day}; """
    print(sql)
    cursor.execute(sql)
    vip_list = cursor.fetchall()
    print(f"vip_list size: {len(vip_list)}")
    return vip_list


def get_today_vips(cursor):
    now = datetime.datetime.now(datetime.timezone.utc)
    now_str = f"{now.year}-{now.month}-{now.day} 00:00:00"

    big_month_list = [1, 3, 5, 7, 8, 10, 12]
    little_month_list = [4, 6, 9, 11]
    vip_list = []

    # 大月
    if now.month in big_month_list:
        vip_list = helper(cursor, now_str, now.day, is_greater_than=False)
    # 小月
    elif now.month in little_month_list:
        if now.day < 30:
            vip_list = helper(cursor, now_str, now.day, is_greater_than=False)
        elif now.day == 30:
            vip_list = helper(cursor, now_str, now.day, is_greater_than=True)
    # 二月单独处理
    elif now.month == 2:
        if is_run(now.year):
            if now.day < 29:
                vip_list = helper(cursor, now_str, now.day, is_greater_than=False)
            elif now.day == 29:
                vip_list = helper(cursor, now_str, now.day, is_greater_than=True)
        else:
            if now.day < 28:
                vip_list = helper(cursor, now_str, now.day, is_greater_than=False)
            elif now.day == 28:
                vip_list = helper(cursor, now_str, now.day, is_greater_than=True)

    return vip_list


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    err_list = []
    send_list = []
    cursor = db.cursor(pymysql.cursors.DictCursor)
    for vip in get_today_vips(cursor):
        user_id = vip['user_id']
        sql1 = f"select user_id, point, free_point, created_at from Point_point where user_id={user_id};"
        print(sql1)
        cursor.execute(sql1)
        res1 = cursor.fetchall()

        res1[0]["expired_at"] = vip["expire_at"]
        send_list.append(res1)

        free_point = res1[0]['free_point']
        if free_point > 0:
            insert_sql1 = f"insert into Point_pointrecord(user_id, event, point, created_at) values ({user_id}, 'CLEAN', {0 - free_point}, now());"
            print(insert_sql1)
            cursor.execute(insert_sql1)
        elif free_point < 0:
            err_list.append(f"{user_id}, {res1} 为什么免费点数 < 0")

        point = 100
        if user_id in [1144621, 1177613]:
            point = 200
        insert_sql2 = f"insert into Point_pointrecord(user_id, event, point, created_at) values ({user_id}, 'FREE', {point}, now());"
        print(insert_sql2)
        cursor.execute(insert_sql2)

        update_sql = f"update Point_point set free_point={point} where user_id={user_id};"
        print(update_sql)
        cursor.execute(update_sql)

    if len(err_list) > 0:
        send_err_mail(err_list)
    if len(send_list) > 0:
        send_mail(send_list)

    db.commit()
    cursor.close()
    db.close()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(items: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}给贵人加点', 'utf-8').encode()

        html = f'<html><body><h2>给订阅的贵人们加点：{datetime.datetime.now(datetime.timezone.utc)}</h2>'

        html += f"<p>一共有{len(items)}个贵人要免费加点！</p>"
        for item in items:
            html += f"<p>贵人{item} 加点50</p>"

        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()


def send_err_mail(err_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}给贵人加点的错误', 'utf-8').encode()

        html = f'<html><body><h2>给贵人加点的错误：{datetime.datetime.now(datetime.timezone.utc)}</h2>'

        for item in err_list:
            html += f"<p>{item}</p>"

        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"add point every month, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        work()
    except Exception as e:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_err_mail([str(e)])
    finally:
        print(f"add point every month, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
