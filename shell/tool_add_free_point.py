# coding=utf-8
import sys

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(user_id: int, point: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql1 = f"select * from Point_point where user_id={user_id} ;"
    sql2 = f"update Point_point set point=point+{point} where user_id={user_id} ;"
    sql3 = f"insert into Point_pointrecord(user_id, event, point, created_at)  values ({user_id}, 'FATPO-CHARGE', {point}, now()) ;"

    cursor.execute(sql1)
    res1 = cursor.fetchall()
    print(res1)

    cursor.execute(sql2)
    res2 = cursor.fetchall()
    print(res2)

    cursor.execute(sql3)
    res3 = cursor.fetchall()
    print(res3)

    cursor.execute(sql1)
    res1 = cursor.fetchall()
    print(res1)

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    user_id = int(sys.argv[1])
    point = int(sys.argv[2])
    work(user_id, point)
