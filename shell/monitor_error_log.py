# coding=utf-8
import datetime
import os
import smtplib
import subprocess
import sys
import traceback
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

bin_file = '/root/www/VirtualSIM_BackEnd/shell/monitor_error_log.bin'


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def set_newest_row_number(row_number: int):
    print(f"set current row number: {row_number}")
    with open(bin_file, 'w') as f:
        f.write(f'{row_number}')


def get_last_handle_row_number() -> int:
    if not os.path.exists(bin_file):
        with open(bin_file, 'w') as f:
            f.write('1')

    with open(bin_file, 'r') as f:
        line = f.readline()
        line = line.strip()
        return int(line)


def get_current_row_number() -> int:
    cmd = "wc -l /root/www/VirtualSIM_BackEnd/logs/django.err"
    print(cmd)
    out = subprocess.getoutput(cmd)
    return int(out.split()[0])


def get_new_logs(begin_index: int, end_index: int) -> list:
    cmd = f"sed -n '{begin_index},{end_index}p' /root/www/VirtualSIM_BackEnd/logs/django.err"
    print(cmd)
    output, error = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    lines = output.decode("utf-8").split("\n")
    lines = [v for v in lines if v.strip()]
    return lines


def work(err_cnt_limit: int, warn_cnt_limit: int):
    now = datetime.datetime.now(datetime.timezone.utc)

    current_row_number = get_current_row_number()
    last_row_number = get_last_handle_row_number()
    print(f"last_row_number: {last_row_number}, current_row_number: {current_row_number} ")
    if current_row_number != last_row_number or current_row_number == 1:
        # 一般不会颠倒，如果倒了，我们就重新开始
        if current_row_number < last_row_number:
            print(f"reset last_row_number = 1")
            last_row_number = 1

        lines = get_new_logs(last_row_number + 1, current_row_number)
        warn_lines = []
        error_lines = []

        # 需要不需要在标题增加cancel字样，方便邮件分类
        cancel_order_flag = False

        for line in lines:
            # registerPushId 不要报警
            if '[WARNING]' in line:
                if 'path:/user/registerPushId/, nice token=None' in line:
                    pass
                # 7点的时候在统计那个退款脚本，所以很多 cancelled，这种情况要pass
                elif 'cancelled' in line and now.hour in [7, 8]:
                    pass
                else:
                    warn_lines.append(line)
            elif '[ERROR]' in line:
                if '贵人加点' in line or '用户反馈' in line \
                        or '订单有多个' in line \
                        or '封号' in line \
                        or '杀猪盘警告' in line \
                        or '可疑诈骗' in line \
                        or '对方怀疑' in line \
                        or '图片告警！可能诈骗短信！' in line \
                        or 'but new user limit!' in line \
                        or ('filter_before' in line) \
                        or ('send sms' in line and 'grey' in line) \
                        or ('_check_by_third_party' in line) \
                        or ('is_sms_contact_limit' in line) \
                        or ('temp suspended' in line) \
                        or ('traffic_search_for_single_line' in line and 'es error' not in line) \
                        or ('SmsItSupportTool' in line) \
                        or ('CommitFeedback' in line) \
                        or ('is_sms_phishing' in line) \
                        or ('check_traffic_violations' in line) \
                        or ('_notify_by_fcm' in line):
                    pass
                else:
                    error_lines.append(line)

        print(f"warn error: {len(warn_lines)}")
        print(f"error error: {len(error_lines)}")
        print(f"err_cnt_limit: {err_cnt_limit}")
        print(f"warn_cnt_limit: {warn_cnt_limit}")

        # 重点关注的 fcm 和 sms 发送
        fatpo_handle_limit = 20
        fcm_limit = 20
        sms_limit = 40
        order_limit = 5
        baidufanyi_limit = 10
        baidu_ai_limit = 20
        es_limit = 10
        telnyx_limit = 20
        search_number_limit = 10
        sms_fake_limit = 15
        sms_invalid_limit = 20

        fatpo_handle_cnt = 0
        fcm_cnt = 0
        sms_cnt = 0
        order_cnt = 0
        baidufanyi_cnt = 0
        baidu_ai_cnt = 0
        es_cnt = 0
        telnyx_cnt = 0
        search_number_cnt = 0
        sms_fake_cnt = 0
        sms_invalid_cnt = 0

        for tmp_warn in warn_lines:
            if 'fcm' in tmp_warn:
                fcm_cnt += 1
            if 'SendSms' in tmp_warn and 'add_fake_sms_record' not in tmp_warn and 'baiduAI' not in tmp_warn and 'invalid sms limit' not in tmp_warn:
                sms_cnt += 1
            if 'UpdateOrderStatus' in tmp_warn:
                order_cnt += 1
            if 'baidu_tran_util' in tmp_warn:
                baidufanyi_cnt += 1
            if 'Fatpo' in tmp_warn:
                fatpo_handle_cnt += 1
            if 'Telnyx' in tmp_warn:
                telnyx_cnt += 1
            if 'SearchNumber' in tmp_warn or 'search_number' in tmp_warn:
                search_number_cnt += 1
            if 'add_fake_sms_record' in tmp_warn:
                sms_fake_cnt += 1
            if 'baiduAI' in tmp_warn:
                baidu_ai_cnt += 1
            if 'invalid sms limit' in tmp_warn:
                sms_invalid_cnt += 1

        if len(error_lines) > err_cnt_limit or len(warn_lines) >= warn_cnt_limit \
                or order_cnt >= order_limit \
                or fcm_cnt >= fcm_limit \
                or baidufanyi_cnt >= baidufanyi_limit \
                or es_cnt >= es_limit \
                or fcm_cnt >= fcm_limit \
                or fatpo_handle_cnt >= fatpo_handle_limit \
                or telnyx_cnt >= telnyx_limit \
                or search_number_cnt >= search_number_limit \
                or sms_fake_cnt >= sms_fake_limit \
                or baidu_ai_cnt >= baidu_ai_limit \
                or sms_invalid_cnt >= sms_invalid_limit \
                or search_number_cnt >= search_number_limit \
                or sms_cnt >= sms_limit:
            print("send mail...")
            send_mail(error_lines, warn_lines, cancel_order_flag)
            print("send mail finished.")

        # 更新row_number
        set_newest_row_number(current_row_number)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(err_log_lines: list, warn_log_lines: list, cancel_order_flag: bool):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(
            f'{env_config["EMAIL_PREFIX"]}今天ERROR日志汇总 ！！！' + ('cancel order' if cancel_order_flag else ''),
            'utf-8').encode()

        html = f'<html><body><h2>今天ERROR日志汇总：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
        <table border="1">
        <tr>
            <th>序号</th>
            <th>ERROR日志</th>
        </tr>
        """
        for index, err_log in enumerate(err_log_lines):
            html += f"""
            <tr>
                <td>{index}</td>
                <td>{err_log}</td>
            </tr>
            """
        html += '</table>'

        html += """
        <table border="1">
        <tr>
            <th>序号</th>
            <th>WARN日志</th>
        </tr>
        """
        for index, warn_log in enumerate(warn_log_lines):
            html += f"""
            <tr>
                <td>{index}</td>
                <td>{warn_log}</td>
            </tr>
            """
        html += '</table></body></html>'

        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(datetime.datetime.now(datetime.timezone.utc))
    print("*" * 20)
    try:
        error_cnt_limit = int(sys.argv[1])
        warn_cnt_limit = int(sys.argv[2])
        work(error_cnt_limit, warn_cnt_limit)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail([], [], False)
    finally:
        print(f"monitor error log finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
