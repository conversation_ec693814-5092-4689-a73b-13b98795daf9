# coding=utf-8
import traceback

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def do_tran_user(from_user_id: int, from_cursor, to_cursor, to_db) -> id:
    sql = f"select * from User_user where id={from_user_id}"
    from_cursor.execute(sql)
    from_user = from_cursor.fetchone()
    if not from_user:
        print("\t\t", from_user_id, "do_tran_user failed, from user not exists")
        return

    sql2 = f"select * from User_user where old_user_id={from_user_id}"
    to_cursor.execute(sql2)
    to_user = to_cursor.fetchone()
    if to_user:
        return to_user['id']

    sql3 = f"select * from User_user where login_id='{from_user['login_id']}' and login_type={from_user['login_type']}"
    to_cursor.execute(sql3)
    to_user = to_cursor.fetchone()
    if to_user:
        sql35 = f"update User_user set old_user_id={from_user_id} where id={to_user['id']}"
        to_cursor.execute(sql35)
        return to_user['id']

    sql4 = f"INSERT INTO `User_user` (`login_id`,`email`,`ip`,`ip_segment1`,`ip_segment2`,`passwd`,`name`,`token`,`uuid`,`push_id`,`tw_identify`," \
           f"`tw_address`,`tw_endpoint`,`tw_binding_sid`,`login_type`,`is_add_point`,`expired_at`,`created_at`,`updated_at`,`appid`,`deleted`, old_user_id) "
    sql4 += f"""VALUES ('{from_user['login_id']}',
           '{from_user['email']}',
           '{from_user['ip']}',
           '{from_user['ip_segment1']}',
           '{from_user['ip_segment2']}',
           '{from_user['passwd']}',
           '{from_user['name']}',
           '{from_user['token']}',
           '{from_user['uuid']}',
           '{from_user['push_id']}',
           '{from_user['tw_identify']}',
           '{from_user['tw_address']}',
           '{from_user['tw_endpoint']}',
           '{from_user['tw_binding_sid']}',
           {from_user['login_type']},
           {from_user['is_add_point']},
           '{from_user['expired_at']}',
           '{from_user['created_at']}',
           '{from_user['updated_at']}',
           {from_user['appid']},
           {from_user['deleted']},
           {from_user_id}
           );
    """
    print("\t\t", sql4)

    to_cursor.execute(sql4)

    # 获取插入的自增主键值
    insert_id = to_db.insert_id()
    print("\t\t", "from userId: ", from_user_id, " to userId: ", insert_id)
    return insert_id


def do_tran_point(from_user_id: int, to_user_id: int, from_cursor, to_cursor):
    from_sql = f"select * from Point_point where user_id={from_user_id}"
    from_cursor.execute(from_sql)
    from_point = from_cursor.fetchone()
    if not from_point:
        print(f"\t\tdo_tran_point failed, from user:{from_user_id} not exists points")
        return

    to_sql = f"select * from Point_point where user_id={to_user_id}"
    to_cursor.execute(to_sql)
    to_point = from_cursor.fetchone()
    if to_point:
        print(f"\t\tdo_tran_point failed, to user:{to_user_id} exists points")
        return


def do_tran_number(from_user_id: int, from_cursor, to_cursor, to_db, user_id_mappings, from_has_no_numberused: list,
                   from_has_no_inventory: list, mutil_used: list, failed_numbers_users: list):
    sql = f"select * from Number_numberused where user_id={from_user_id} and status='USING'"
    from_cursor.execute(sql)
    number = from_cursor.fetchone()
    if not number:
        print("\t\t", from_user_id, "do_tran_number failed, from number used not exists")
        from_has_no_numberused.append(from_user_id)
        return

    sql2 = f"select * from Number_numberinventory where id={number['number_id']}"
    from_cursor.execute(sql2)
    from_number_inventory = from_cursor.fetchone()
    if not from_number_inventory:
        print("\t\t", from_user_id, number, "do_tran_number failed, from number inventory not exists")
        from_has_no_inventory.append(from_user_id)
        return

    sql3 = f"select * from Number_numberinventory where number={number['number']}"
    to_cursor.execute(sql3)
    to_number_inventory = to_cursor.fetchone()
    if to_number_inventory:
        print("\t\t", from_user_id, number, "do_tran_number failed, to number inventory is exists", to_number_inventory)
        print("\t\t 大冲突！！！！", from_user_id, number['number'], "两边都在用！！")
        mutil_used.append(from_user_id)
        return
    else:
        sql4 = "insert into Number_numberinventory(number, friendly_name, sid, created_at, updated_at, release_at, capabilities, tw_version) values"

        if from_number_inventory['release_at']:
            sql4 += f"""(
                        '{from_number_inventory['number']}',
                        '{from_number_inventory['friendly_name']}',
                        '{from_number_inventory['sid']}',
                        '{from_number_inventory['created_at']}',
                        '{from_number_inventory['updated_at']}',
                        '{from_number_inventory['release_at']}',
                        '{from_number_inventory['capabilities']}',
                        1
                        )
                    """
        else:
            sql4 += f"""(
                        '{from_number_inventory['number']}',
                        '{from_number_inventory['friendly_name']}',
                        '{from_number_inventory['sid']}',
                        '{from_number_inventory['created_at']}',
                        '{from_number_inventory['updated_at']}',
                        null,
                        '{from_number_inventory['capabilities']}',
                        1
                        )
                    """
        print("\t\t", sql4)
        to_cursor.execute(sql4)
        number_id = to_db.insert_id()

        sql5 = "insert into Number_numberused(user_id, number_id, number, type, expired_at, status, created_at, updated_at, tw_version) values"
        to_user_id = user_id_mappings[from_user_id]
        sql5 += f"""(
                    {to_user_id},
                    {number_id},
                    '{number['number']}',
                    '{number['type']}',
                    '{number['expired_at']}',
                    '{number['status']}',
                    '{number['created_at']}',
                    '{number['updated_at']}',
                    1
                    )
                """
        print("\t\t", sql5)
        to_cursor.execute(sql5)


def migrate_user_points(from_cursor, to_cursor, user_id_mappings: dict, failed_point_users: list):
    from_user_ids = [str(v) for v in user_id_mappings.keys()]
    sql = f"SELECT * FROM Point_point WHERE user_id IN ({','.join(from_user_ids)});"
    print("migrate_user_points", sql)
    from_cursor.execute(sql)
    points = from_cursor.fetchall()
    for point in points:
        try:
            to_userid = user_id_mappings[point['user_id']]
            # insert_sql = f"INSERT INTO Point_point (user_id, point, free_point, predict_point, created_at, updated_at) VALUES ({to_userid}, {point['point']}, {point['free_point']}, {point['predict_point']}, '{point['created_at']}', '{point['updated_at']}') " \
            #              f"ON DUPLICATE KEY UPDATE free_point={point['free_point']}, point={point['point']};"
            #
            insert_sql = f"INSERT INTO Point_point (user_id, point, free_point, predict_point, created_at, updated_at) VALUES ({to_userid}, {point['point']}, {point['free_point']}, {point['predict_point']}, '{point['created_at']}', '{point['updated_at']}') "
            print(insert_sql)
            to_cursor.execute(insert_sql)
        except Exception:
            failed_point_users.append(point['user_id'])


def migrate_user_orders(from_cursor, to_cursor, user_id_mappings: dict, failed_order_users: list):
    from_user_ids = [str(v) for v in user_id_mappings.keys()]
    sql = f"SELECT * FROM Order_order WHERE user_id IN ({','.join(from_user_ids)});"
    from_cursor.execute(sql)
    orders = from_cursor.fetchall()
    for order in orders:
        try:
            to_userid = user_id_mappings[order['user_id']]
            insert_sql = "INSERT INTO Order_order (user_id, cert_md5, number_id, renew_status, expiration_intent, expire_at, original_transaction_id, certificate, appid, valid, order_status, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);"
            print(insert_sql)
            to_cursor.execute(insert_sql, (
                to_userid, order['cert_md5'], 0, order['renew_status'], order['expiration_intent'],
                order['expire_at'], order['original_transaction_id'], order['certificate'], order['appid'],
                order['valid'],
                order['order_status'], order['created_at'], order['updated_at']
            ))
        except Exception:
            print(order['user_id'], "failed to migrate_user_order")
            failed_order_users.append(order['user_id'])


def migrate_sms_records(from_cursor, to_cursor, user_id_mappings: dict, ):
    from_user_ids = [str(v) for v in user_id_mappings.keys()]
    sql = f"SELECT * FROM Call_smsrecord WHERE user_id IN ({','.join(from_user_ids)});"

    from_cursor.execute(sql)
    sms_records = from_cursor.fetchall()
    for index, record in enumerate(sms_records):
        to_user_id = user_id_mappings[record['user_id']]
        insert_sql = "INSERT INTO Call_smsrecord (sid, user_id, latest_ts, ip, direction, from_number, to_number, status, err_code, price, point, content, filtered_content, images, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);"
        try:
            content = record['content'].encode('utf-8') if record['content'] else None
            filtered_content = record['filtered_content'].encode('utf-8') if record['filtered_content'] else None
            to_cursor.execute(insert_sql, (
                record['sid'], to_user_id, record['latest_ts'], record['ip'], record['direction'],
                record['from_number'],
                record['to_number'], record['status'], record['err_code'], record['price'], record['point'],
                content,
                filtered_content, record['images'], record['created_at'], record['updated_at']
            ))
        except Exception:
            traceback.print_exc()
            print(record['user_id'], index, insert_sql)


def get_need_to_migrate_users(from_cursor):
    sql1 = f"select user_id from Order_order where expire_at > now() and appid=1 and order_status='OPEN' and valid=1;"
    print(sql1)
    from_cursor.execute(sql1)
    total_users = from_cursor.fetchall()
    return total_users


def get_user_mappings(to_cursor):
    # return {
    #     205141: 1000002,
    #     228317: 1086079,
    #     216332: 1086033,
    # }
    sql1 = f"select id, old_user_id from User_user where old_user_id>0;"
    print(sql1)
    to_cursor.execute(sql1)
    total_users = to_cursor.fetchall()
    dic = {}
    for i in total_users:
        dic[i['old_user_id']] = i['id']
    print(dic)
    return dic


def work():
    to_db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"],
                            user=env_config["DB_USER_KEY"],
                            passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')

    from_db = pymysql.connect(host="*************", port=3306, user="root", passwd="123qweQWE*", db="secphone",
                              charset='utf8')

    from_cursor = from_db.cursor(pymysql.cursors.DictCursor)
    to_cursor = to_db.cursor(pymysql.cursors.DictCursor)

    user_id_mappings = get_user_mappings(to_cursor)
    failed_order_users = []
    failed_point_users = []

    from_has_no_numberused = []
    from_has_no_inventory = []
    mutil_used = []
    failed_numbers_users = []

    # for index, from_user_id in enumerate(user_id_mappings.keys()):
    #     print("index: ", index, "user_id:", from_user_id)
    #     do_tran_number(from_user_id, from_cursor, to_cursor, to_db, user_id_mappings, from_has_no_numberused,
    #                    from_has_no_inventory, mutil_used, failed_numbers_users)

    migrate_sms_records(from_cursor, to_cursor, user_id_mappings)

    # # 订单
    # migrate_user_orders(from_cursor, to_cursor, user_id_mappings, failed_order_users)
    # # 点数
    # migrate_user_points(from_cursor, to_cursor, user_id_mappings, failed_point_users)

    from_db.commit()
    from_cursor.close()
    from_db.close()

    to_db.commit()
    to_cursor.close()
    to_db.close()

    print("faield orders", failed_order_users)
    print("faield points", failed_point_users)
    print("faield from_has_no_numberused", from_has_no_numberused)
    print("faield from_has_no_inventory", from_has_no_inventory)
    print("faield mutil_used", mutil_used)
    print("faield failed_numbers_users", failed_numbers_users)


if __name__ == '__main__':
    work()
