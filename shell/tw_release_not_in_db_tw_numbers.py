import pymysql
from twilio.rest import Client


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "TWILIO_ACCOUNT_SID": env("TWILIO_ACCOUNT_SID"),
        "TWILIO_ACCOUNT_TOKEN": env("TWILIO_ACCOUNT_TOKEN"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

client_map = {
    1: Client("**********************************", "d703a14afd6342ce8699d3ed8607bfc2"),
    2: Client("**********************************", "7901ab6886b148460b16edc337d1343a"),
}


def get_local_numbers() -> list:
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    cursor = db.cursor(pymysql.cursors.DictCursor)
    cursor.execute("select number from Number_numberinventory where status in ('USING', 'EXPIRE')")
    local_numbers = []
    for row in cursor.fetchall():
        local_numbers.append(row['number'])
    return local_numbers


def get_tw_numbers(version: int) -> list:
    tw_numbers = []

    for t in client_map[version].incoming_phone_numbers.list(limit=10000):
        tw_numbers.append({"number": t.phone_number, "version": version, "sid": t.sid})

    return tw_numbers


def compare_local_and_tw_and_release(local: list, tw: list) -> list:
    res = []
    print('number in local but not in tw:')
    cnt1 = 0
    tw_numbers = [v['number'] for v in tw]
    for t in local:
        if t not in tw_numbers:
            cnt1 += 1
            print("\t%s" % t)
            res.append(f"number in local but not in tw: {t}")

    print('number in tw but not in local:')
    cnt2 = 0
    for t in tw:
        if t['number'] not in local:
            print("\t%s" % t)
            cnt2 += 1
            res.append(f"number in tw but not in local: {t}")
            release_tw_number(tw_version=1, number=t['number'], sid=t['sid'])

    print("number in local but not in tw", cnt1)
    print("number in tw but not in local", cnt2)
    return res


number_message_mappings = {}


def init_service_numbers(tw_version: int):
    services = client_map[tw_version].messaging.services.list()
    for service in services:
        phone_numbers = service.phone_numbers.list()
        for number in phone_numbers:
            number_message_mappings[number.phone_number] = service.sid

    for k, v in number_message_mappings.items():
        print(k, v)


def release_tw_number(tw_version: int, number: str, sid: str):
    # 移除 message sender
    if number in number_message_mappings:
        print(number, sid, "message:", number_message_mappings[number])
        client_map[tw_version].messaging \
            .services(number_message_mappings[number]) \
            .phone_numbers(sid) \
            .delete()
    else:
        print(number, "not in message...")

    # 移除号码
    res = client_map[tw_version].incoming_phone_numbers(sid).delete()
    print(res)


if __name__ == '__main__':
    init_service_numbers(1)

    local_numbers = get_local_numbers()
    tw_numbers_1 = get_tw_numbers(1)
    print("tw_numbers_1 size:", len(tw_numbers_1))
    tw_numbers = tw_numbers_1
    res = compare_local_and_tw_and_release(local_numbers, tw_numbers)
    print("len:", len(res))
