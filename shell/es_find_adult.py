import datetime
import json
import smtplib
import sys
import time
import traceback
from email.header import Header
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
from elasticsearch import Elasticsearch


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


class EsAdultTrafficFinder:
    def __init__(self):
        self.es = Elasticsearch(
            ['http://*********:9200']
        )

    @staticmethod
    def get_index_name():
        today = datetime.datetime.today()
        # 担心还没创建索引，给一分钟的时间
        if today.hour == 0 and today.minute <= 1:
            yesterday = (today - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
            index_name = f"{yesterday}"
            return index_name
        else:
            yesterday = (today - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
            index_today = today.strftime('%Y-%m-%d')
            index_name = f"{yesterday},{index_today}"
            return index_name

    def check_by_available(self, send_list: list, latest_ts: int):
        body = {
            "from": 0,
            "size": 500,
            "query":
                {
                    "bool":
                        {
                            "filter": {
                                "range": {"latest_ts": {"gte": latest_ts}}
                            },
                            "must_not":
                                [
                                    {
                                        "match":
                                            {
                                                "content":
                                                    {
                                                        "operator": "or",
                                                        "query": "it’s its it is interview"
                                                    }
                                            }
                                    }
                                ],
                            "minimum_should_match": 1,
                            "should":
                                [
                                    {
                                        "match":
                                            {
                                                "content":
                                                    {
                                                        "query": "available interested",
                                                        "operator": "and"
                                                    }
                                            }
                                    },
                                    {
                                        "bool":
                                            {
                                                "must":
                                                    [
                                                        {
                                                            "bool": {
                                                                "should":
                                                                    [
                                                                        {
                                                                            "match":
                                                                                {
                                                                                    "content":
                                                                                        {
                                                                                            "query": "i‘am am iam im I’m I'm",
                                                                                            "operator": "or"
                                                                                        }
                                                                                }
                                                                        },
                                                                        {
                                                                            "match_phrase":
                                                                                {
                                                                                    "content": "i am"
                                                                                }
                                                                        },
                                                                        {
                                                                            "match_phrase":
                                                                                {
                                                                                    "content": "i m"
                                                                                }
                                                                        }
                                                                    ]
                                                            }
                                                        },
                                                        {
                                                            "match":
                                                                {
                                                                    "content":
                                                                        {
                                                                            "query": "available avail availa",
                                                                            "operator": "or"
                                                                        }
                                                                }
                                                        }
                                                    ]

                                            }
                                    }
                                ]
                        }
                }
        }

        response = self.es.search(
            index=EsAdultTrafficFinder.get_index_name(),  # 索引名
            body=body,
            filter_path=["took", "hits.hits._source", "hits.hits._score"]
        )
        if 'hits' not in response:
            return set()

        user_ids = set()
        long_words_user_ids = set()
        for doc in response['hits']['hits']:
            if len(doc['_source']['content']) < 100:
                user_ids.add(doc['_source']['user_id'])
            else:
                long_words_user_ids.add(doc['_source']['user_id'])

        if len(user_ids) > 0:
            print(f"available users: {[v for v in user_ids if v not in long_words_user_ids]}")
            send_list.append(json.dumps(response['hits'], ensure_ascii=False))
            send_list.append(f"available users: {[v for v in user_ids if v not in long_words_user_ids]}")
        if len(long_words_user_ids) > 0:
            if len(user_ids) == 0:
                send_list.append(json.dumps(response['hits'], ensure_ascii=False))
            print(f"available users need to check: {long_words_user_ids}")
            send_list.append(f"available users need to check: {long_words_user_ids}")
        return user_ids

    def check_by_in_out_call(self, send_list: list, latest_ts: int):
        body = {
            "from": 0,
            "size": 500,
            "query": {
                "bool": {
                    "filter": {
                        "range": {"latest_ts": {"gte": latest_ts}}
                    },
                    "minimum_should_match": 1,
                    "should": [
                        {
                            "match": {
                                "content": {
                                    "query": "incall outcall",
                                    "operator": "and"
                                }
                            }
                        },
                        {
                            "match_phrase": {
                                "content": "in call"
                            }
                        },
                        {
                            "match_phrase": {
                                "content": "out call"
                            }
                        }
                    ]
                }
            }
        }

        response = self.es.search(
            index=EsAdultTrafficFinder.get_index_name(),
            body=body,
            filter_path=["took", "hits.hits._source", "hits.hits._score"]
        )
        if 'hits' not in response:
            return set()

        user_ids = set()
        for doc in response['hits']['hits']:
            user_ids.add(doc['_source']['user_id'])

        if len(user_ids) > 0:
            print(f"incall or outcall users: {user_ids}")
            send_list.append(json.dumps(response['hits'], ensure_ascii=False))
            send_list.append(f"incall or outcall users: {user_ids}")
        return user_ids

    def check_by_need_service(self, send_list: list, latest_ts: int):
        body = {
            "from": 0,
            "size": 500,
            "query":
                {
                    "bool":
                        {
                            "filter": {
                                "range": {"latest_ts": {"gte": latest_ts}}
                            },
                            "minimum_should_match": 1,
                            "should":
                                [
                                    {
                                        "bool":
                                            {
                                                "must":
                                                    [
                                                        {
                                                            "match":
                                                                {
                                                                    "content":
                                                                        {
                                                                            "query": "hour hours",
                                                                            "operator": "or"
                                                                        }
                                                                }
                                                        },
                                                        {
                                                            "match":
                                                                {
                                                                    "content":
                                                                        {
                                                                            "query": "need service",
                                                                            "operator": "and"
                                                                        }
                                                                }
                                                        }
                                                    ]
                                            }
                                    },
                                    {
                                        "bool":
                                            {
                                                "must":
                                                    [
                                                        {
                                                            "match":
                                                                {
                                                                    "content": "available"
                                                                }
                                                        },
                                                        {
                                                            "match":
                                                                {
                                                                    "content":
                                                                        {
                                                                            "query": "need service",
                                                                            "operator": "and"
                                                                        }
                                                                }
                                                        }
                                                    ]
                                            }
                                    },
                                    {
                                        "match_phrase":
                                            {
                                                "content": "need my service"
                                            }
                                    },
                                    {
                                        "match_phrase":
                                            {
                                                "content": "service do you need"
                                            }
                                    }
                                ]
                        }
                }
        }

        response = self.es.search(
            index=EsAdultTrafficFinder.get_index_name(),
            body=body,
            filter_path=["took", "hits.hits._source", "hits.hits._score"]
        )
        if 'hits' not in response:
            return set()

        user_ids = set()
        for doc in response['hits']['hits']:
            user_ids.add(doc['_source']['user_id'])

        if len(user_ids) > 0:
            print(f"need service users: {user_ids}")
            send_list.append(json.dumps(response['hits'], ensure_ascii=False))
            send_list.append(f"need service users: {user_ids}")
        return user_ids

    def check_by_price(self, send_list: list, latest_ts: int):
        body = {
            "from": 0,
            "size": 500,
            "query":
                {
                    "bool":
                        {
                            "filter": {
                                "range": {"latest_ts": {"gte": latest_ts}}
                            },
                            "minimum_should_match": 2,
                            "should":
                                [
                                    {
                                        "match_phrase":
                                            {
                                                "content": "per hour"
                                            }
                                    },
                                    {
                                        "match":
                                            {
                                                "content": {
                                                    "query": "$80 $100 $150 $170 $200 $250 $300 $400 $450 $500 $550 $600 $650 $700 $750 $800 $850 $900 $950 $1000 overnight",
                                                    "operator": "or"
                                                }

                                            }
                                    }
                                ]
                        }
                }
        }

        response = self.es.search(
            index=EsAdultTrafficFinder.get_index_name(),
            body=body,
            filter_path=["took", "hits.hits._source", "hits.hits._score"]
        )
        if 'hits' not in response:
            return set()

        user_ids = set()
        for doc in response['hits']['hits']:
            user_ids.add(doc['_source']['user_id'])

        if len(user_ids) > 0:
            print(f"price users: {user_ids}")
            send_list.append(json.dumps(response['hits'], ensure_ascii=False))
            send_list.append(f"price users: {user_ids}")
        return user_ids

    def check(self):
        send_mail_list = []
        latest_ts = int(time.time() * 1000)

        suspend_users = set()
        suspend_users.update(self.check_by_available(send_mail_list, latest_ts))
        suspend_users.update(self.check_by_in_out_call(send_mail_list, latest_ts))
        suspend_users.update(self.check_by_need_service(send_mail_list, latest_ts))
        suspend_users.update(self.check_by_price(send_mail_list, latest_ts))

        print(f"bad guys: {suspend_users}")

        db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"],
                             user=env_config["DB_USER_KEY"],
                             passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"],
                             charset='utf8')

        # 使用cursor方法创建一个游标
        cursor = db.cursor(pymysql.cursors.DictCursor)

        send_flag = False
        for userid in suspend_users:
            sql = f"select * from User_user where id={userid}"
            cursor.execute(sql)
            user = cursor.fetchone()
            if not user:
                print(f"user not exist: {userid}")
                continue

            sql = f"select * from User_blackuser where user_id ={userid}"
            cursor.execute(sql)
            res = cursor.fetchone()
            if not res:
                sql = f"INSERT INTO `secphone`.`User_blackuser`(`user_id`, `uuid`, `appid`, `reason`, `ban_type`, `ban_times`," \
                      f" `created_at`, `updated_at`) VALUES ({userid}, '{user['uuid']}', {user['appid']}, " \
                      f"'es_crontab_check', 1, 1, now(), now());"
                print(sql)
                res = cursor.execute(sql)
                print(f"insert res: {res}")
            else:
                sql = f"UPDATE `secphone`.`User_blackuser` set ban_times = ban_times +1 where user_id={userid} and appid={user['appid']};"
                print(sql)
                res = cursor.execute(sql)
                print(f"update res: {res}")

            send_flag = True
            send_mail_list.append(sql)

        if send_flag:
            send_mail(send_mail_list)

        db.commit()
        cursor.close()
        db.close()


def main():
    finder = EsAdultTrafficFinder()
    finder.check()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_items: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>', ]

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}ESAdultTraffic定时检查!!!', 'utf-8').encode()

        html = f'<html><body>'
        html += """
               <table border="1">
                <tr>
                   <th>item</th>
               </tr>
               """
        for send_item in send_items:
            html += f"""
             <tr>
                 <td>{send_item}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print(f"Es Crontab find bad guys, time: {datetime.datetime.now(datetime.timezone.utc)}")
    try:
        main()
    except Exception as ex:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"Es Crontab find bad guys, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
