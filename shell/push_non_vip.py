# coding=utf-8
import datetime
import json
import os
import smtplib
import sys
import time
import traceback
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import firebase_admin
import pymysql
from firebase_admin import _messaging_utils
from firebase_admin import initialize_app, credentials
from firebase_admin._messaging_utils import APNSPayload, Aps
from firebase_admin.messaging import Message
import requests
from google.oauth2.service_account import Credentials
from google.auth.transport.requests import Request

topic_name = "v2_non_vip_topic"
fatpo_user_id = 1214162
FCM_RENEW_VIP_TITLE = "Your Private Number is Waiting for You!"
FCM_RENEW_VIP_CONTENT = "Enjoy private calls & messages with VIP! Renew now for a safer, more private experience."


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

FCM_APP_NAME = env_config["FCM_TITLE"]

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
FCM_CERT_FILEPATH = BASE_DIR + env_config["FCM_CERT_FILEPATH"]
print(FCM_CERT_FILEPATH)
with open(FCM_CERT_FILEPATH) as f:
    credential = credentials.Certificate(json.load(f))

FIREBASE_APP = initialize_app(credential=credential, name=FCM_APP_NAME)


def subscribe_non_vip_users_to_topic():
    """
    将所有未订阅或订阅过期的用户添加到 v2_non_vip_topic
    """
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"],
                         user=env_config["DB_USER_KEY"], passwd=env_config["DB_PASSWORD_KEY"],
                         db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = """
    select t1.id, t1.push_id
    from User_user t1
    left join Order_order t2 on t1.id=t2.user_id
    where (t2.`order_status` <> 'OPEN' or t2.order_status is null)
    and t1.push_id is not null
    and t1.push_id <> ''
    and t1.deleted = 0
    """

    try:
        cursor.execute(sql)
        users = cursor.fetchall()

        # 添加统计
        total_tokens = 0
        total_success = 0
        total_failures = 0
        failed_tokens = []  # 记录失败的tokens

        # 将用户分批处理，每批1000个（FCM限制）
        push_ids = [user['push_id'] for user in users if user['push_id']]
        chunks = [push_ids[i:i + 1000] for i in range(0, len(push_ids), 1000)]
        total_batches = len(chunks)

        print(f"\n开始处理订阅，共 {total_batches} 批次")
        fcm_app = firebase_admin.get_app(FCM_APP_NAME)
        for batch_index, chunk in enumerate(chunks, 1):
            try:
                total_tokens += len(chunk)
                response = firebase_admin.messaging.subscribe_to_topic(
                    tokens=chunk,
                    topic=topic_name,
                    app=fcm_app
                )
                success_count = len(chunk) - response.failure_count
                total_success += success_count
                total_failures += response.failure_count

                print(
                    f"Batch {batch_index}/{total_batches} - Total: {len(chunk)}, Success: {success_count}, Failures: {response.failure_count}")

                # 记录失败的tokens和原因
                if response.failure_count > 0 and hasattr(response, 'errors') and response.errors:
                    # print(f"Batch {batch_index}/{total_batches} failure details:")
                    for error in response.errors:
                        token = error.get('token', 'unknown')
                        error_msg = error.get('error', 'unknown error')
                        failed_tokens.append((token, error_msg))
                        # print(f"- Token: {token}, Error: {error_msg}")

                time.sleep(0.1)
            except Exception as e:
                retry_count = 3
                while retry_count > 0:
                    try:
                        print(f"Batch {batch_index}/{total_batches} failed, retrying... ({retry_count} attempts left)")
                        time.sleep(1)
                        response = firebase_admin.messaging.subscribe_to_topic(
                            tokens=chunk,
                            topic=topic_name,
                            app=fcm_app
                        )
                        success_count = len(chunk) - response.failure_count
                        total_success += success_count
                        total_failures += response.failure_count
                        print(
                            f"Retry successful - Batch {batch_index}/{total_batches} - Success: {success_count}, Failures: {response.failure_count}")
                        retry_success = True
                        break
                    except Exception as retry_e:
                        retry_count -= 1
                        if retry_count == 0:
                            total_failures += len(chunk)
                            print(f"Batch {batch_index}/{total_batches} failed after all retries: {str(retry_e)}")
                            # 记录整批失败的tokens
                            for token in chunk:
                                failed_tokens.append((token, str(retry_e)))
                            traceback.print_exc()

        # 打印最终统计
        success_rate = (total_success / total_tokens * 100) if total_tokens > 0 else 0
        print(f"\n订阅统计:")
        print(f"处理完成 {total_batches} 批次")
        print(f"总token数: {total_tokens}")
        print(f"成功数: {total_success}")
        print(f"失败数: {total_failures}")
        print(f"成功率: {success_rate:.2f}%")

        # 如果有失败的tokens，输出汇总
        if failed_tokens:
            print(f"\n失败token汇总 (共 {len(failed_tokens)} 个):")
            error_counts = {}
            for _, error in failed_tokens:
                error_counts[error] = error_counts.get(error, 0) + 1
            for error, count in error_counts.items():
                print(f"- {error}: {count} 个")

    finally:
        cursor.close()
        db.close()


def unsubscribe_vip_users_from_topic():
    """
    将所有有效订阅用户从 v2_non_vip_topic 移除
    """
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"],
                         user=env_config["DB_USER_KEY"], passwd=env_config["DB_PASSWORD_KEY"],
                         db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = """
    select t1.id, t1.push_id
    from User_user t1 
    left join Order_order t2 on t1.id=t2.user_id 
    where t2.`order_status` = 'OPEN'
    and t1.push_id is not null 
    and t1.push_id <> '' 
    and t1.deleted = 0
    """
    try:
        cursor.execute(sql)
        users = cursor.fetchall()

        # 将用户分批处理，每批1000个
        push_ids = [user['push_id'] for user in users if user['push_id'] and user['id'] != fatpo_user_id]
        chunks = [push_ids[i:i + 1000] for i in range(0, len(push_ids), 1000)]

        fcm_app = firebase_admin.get_app(FCM_APP_NAME)
        for chunk in chunks:
            try:
                response = firebase_admin.messaging.unsubscribe_from_topic(
                    tokens=chunk,
                    topic=topic_name,
                    app=fcm_app
                )
                print(f"Successfully unsubscribed {len(chunk)} tokens from topic. Failures: {response.failure_count}")
                time.sleep(0.5)
            except Exception as e:
                print(f"Failed to unsubscribe batch from topic: {str(e)}")
                traceback.print_exc()
    finally:
        cursor.close()
        db.close()


def subscribe_expired_users_to_topic():
    """
    将三天前订单失效的用户添加到 v2_non_vip_topic
    """
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"],
                         user=env_config["DB_USER_KEY"], passwd=env_config["DB_PASSWORD_KEY"],
                         db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 获取三天前的日期
    three_days_ago = (datetime.datetime.now() - datetime.timedelta(days=3)).strftime('%Y-%m-%d')

    sql = f"""
    SELECT t1.id, t1.push_id
    FROM User_user t1
    LEFT JOIN Order_order t2 ON t1.id = t2.user_id
    WHERE t1.created_at>='{three_days_ago}' and t2.order_status='CLOSED'
    AND t1.push_id IS NOT NULL
    AND t1.push_id <> ''
    AND t1.deleted = 0
    """

    try:
        cursor.execute(sql)
        users = cursor.fetchall()

        # 添加统计
        total_tokens = 0
        total_success = 0
        total_failures = 0
        failed_tokens = []

        # 将用户分批处理，每批1000个（FCM限制）
        push_ids = [user['push_id'] for user in users if user['push_id']]
        chunks = [push_ids[i:i + 1000] for i in range(0, len(push_ids), 1000)]
        total_batches = len(chunks)

        print(f"\n开始处理三天前失效订单用户订阅，共 {total_batches} 批次")
        fcm_app = firebase_admin.get_app(FCM_APP_NAME)
        for batch_index, chunk in enumerate(chunks, 1):
            try:
                total_tokens += len(chunk)
                response = firebase_admin.messaging.subscribe_to_topic(
                    tokens=chunk,
                    topic=topic_name,
                    app=fcm_app
                )
                success_count = len(chunk) - response.failure_count
                total_success += success_count
                total_failures += response.failure_count

                print(
                    f"Batch {batch_index}/{total_batches} - Total: {len(chunk)}, Success: {success_count}, Failures: {response.failure_count}")

                if response.failure_count > 0 and hasattr(response, 'errors') and response.errors:
                    for error in response.errors:
                        token = error.get('token', 'unknown')
                        error_msg = error.get('error', 'unknown error')
                        failed_tokens.append((token, error_msg))

                time.sleep(0.1)
            except Exception as e:
                retry_count = 3
                while retry_count > 0:
                    try:
                        print(f"Batch {batch_index}/{total_batches} failed, retrying... ({retry_count} attempts left)")
                        time.sleep(1)
                        response = firebase_admin.messaging.subscribe_to_topic(
                            tokens=chunk,
                            topic=topic_name,
                            app=fcm_app
                        )
                        success_count = len(chunk) - response.failure_count
                        total_success += success_count
                        total_failures += response.failure_count
                        print(
                            f"Retry successful - Batch {batch_index}/{total_batches} - Success: {success_count}, Failures: {response.failure_count}")
                        retry_success = True
                        break
                    except Exception as retry_e:
                        retry_count -= 1
                        if retry_count == 0:
                            total_failures += len(chunk)
                            print(f"Batch {batch_index}/{total_batches} failed after all retries: {str(retry_e)}")
                            for token in chunk:
                                failed_tokens.append((token, str(retry_e)))
                            traceback.print_exc()

        # 打印最终统计
        success_rate = (total_success / total_tokens * 100) if total_tokens > 0 else 0
        print(f"\n三天前失效订单用户订阅统计:")
        print(f"处理完成 {total_batches} 批次")
        print(f"总token数: {total_tokens}")
        print(f"成功数: {total_success}")
        print(f"失败数: {total_failures}")
        print(f"成功率: {success_rate:.2f}%")

        if failed_tokens:
            print(f"\n失败token汇总 (共 {len(failed_tokens)} 个):")
            error_counts = {}
            for _, error in failed_tokens:
                error_counts[error] = error_counts.get(error, 0) + 1
            for error, count in error_counts.items():
                print(f"- {error}: {count} 个")

    finally:
        cursor.close()
        db.close()


def send_non_vip_message():
    """
    向非VIP topic发送消息
    """
    try:
        fcm_app = firebase_admin.get_app(FCM_APP_NAME)
        message = firebase_admin.messaging.Message(
            notification=firebase_admin.messaging.Notification(
                title=FCM_RENEW_VIP_TITLE,
                body=FCM_RENEW_VIP_CONTENT
            ),
            topic=topic_name
        )

        response = firebase_admin.messaging.send(message, app=fcm_app)
        print(f"Topic消息发送成功，message_id: {response}")

    except Exception as e:
        print(f"Topic消息发送失败: {str(e)}")
        traceback.print_exc()


def work():
    try:
        # 做一次就ok了
        # print("\n开始处理非VIP用户订阅...")
        # subscribe_non_vip_users_to_topic()

        print("\n开始处理三天前失效订单用户...")
        subscribe_expired_users_to_topic()

        print("\n开始处理VIP用户退订...")
        unsubscribe_vip_users_from_topic()

        print("\n开始发送消息到topic...")
        send_non_vip_message()

    except Exception as e:
        print(f"Error in topic subscription management: {str(e)}")
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail([])


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}维护非VIP订阅topic统计', 'utf-8').encode()

        html = f'<html><body><h2>Topic订阅统计：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += f"""
         <table border="1">
          <tr>
             <th>日期</th>
             <th>处理前订阅数</th>
             <th>处理后订阅数</th>
             <th>变化数量</th>
         </tr>
         """
        for send_item in send_list:
            if "before_count" in send_item:
                change = send_item["after_count"] - send_item["before_count"] if send_item["after_count"] and send_item[
                    "before_count"] else "N/A"
                html += f"""
                 <tr>
                     <td>{send_item["date"]}</td>
                     <td>{send_item["before_count"] if send_item["before_count"] is not None else "获取失败"}</td>
                     <td>{send_item["after_count"] if send_item["after_count"] is not None else "获取失败"}</td>
                     <td>{change if change != "N/A" else "N/A"}</td>
                 </tr>
                 """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"non-vip fcm topic, start time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail([])
    finally:
        print(f"non-vip fcm topic, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
