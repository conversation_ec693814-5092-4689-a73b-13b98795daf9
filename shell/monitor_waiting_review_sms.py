# coding=utf-8
import datetime
import json
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
import pymysql.cursors
import requests


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

Token = "*********************************************************"


def sendMessage(message):
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 修改SQL查询，筛选只在31分钟到60分钟之间的记录
    sql = """
       SELECT * 
       FROM Call_smsrecordwaitforreview 
       WHERE deleted = 0 
         AND created_at >= DATE_SUB(NOW(), INTERVAL 60 MINUTE)
         AND created_at < DATE_SUB(NOW(), INTERVAL 31 MINUTE)
       """

    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()
    print("todo size:", len(res))

    if len(res) > 0:
        print("previous_status != current_status, current_status == 1")
        link = "https://tw.zehougroup.xyz/zhphone/phone/sms/review/?fatpotoken=jiejie"
        sendMessage(f"TXTNOW 待审核短信: {len(res)}: " + link)
        send_mail(f"待审核短信: {len(res)}", link)

    db.commit()
    cursor.close()
    db.close()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(push_str: str, link: str):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)

        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]} {push_str}', 'utf-8').encode()
        html = f'<html><body><h2>有新的短信审核review：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += f""" 
        <p>{push_str}<a href="{link}</a> {link}</p>
         """

        html += '</body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"review sms, start time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail("failed...", "")
    finally:
        print(f"review sms, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
