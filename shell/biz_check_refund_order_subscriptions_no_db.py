# encoding=utf-8
import datetime
import json
import time

import jwt
import requests

"""
教程：https://cloud.tencent.com/developer/article/1939304
"""


def get_jwt_token_for_in_app():
    # 读取密钥文件证书内容
    # f = open("/root/www/VirtualSIM_BackEnd/shell/order/SubscriptionKey_CLS57B4XSY.p8")
    f = open("order/SubscriptionKey_CLS57B4XSY.p8")
    key_data = f.read()
    f.close()

    # JWT Header
    header = {
        "alg": "ES256",
        "kid": "CLS57B4XSY",
        "typ": "JWT"
    }

    # JWT Payload
    payload = {
        "iss": "e46a3bfd-1a50-4a96-9e65-2fbd8a742301",
        "aud": "appstoreconnect-v1",
        "iat": int(time.time()),
        "exp": int(time.time()) + 60 * 60,  # 60 minutes timestamp
        "nonce": "6edffe66-b482-11eb-8529-0242ac130003",
        "bid": "com.zehoutech.secondphone"
    }

    # JWT token
    token = jwt.encode(headers=header, payload=payload, key=key_data, algorithm="ES256")

    print("JWT Token:", token)
    return token


def check_refund_order(jwt_token: str, original_transaction_id: str):
    url = f"https://api.storekit.itunes.apple.com/inApps/v1/subscriptions/{original_transaction_id}"
    headers = {
        'Authorization': f'Bearer {jwt_token}'
    }

    response = requests.get(url, headers=headers)
    print(response.text)
    rsp = json.loads(response.text)

    for i in rsp['data'][0]['lastTransactions']:
        """
         是每个订阅项目的最后的订阅状态，status 类型：
            1：有效
            2：过期
            3：账号扣费重试
            4：账号宽限期(这个是开发者设置，比如到期扣费失败时，可以给用户延期多长时间。)
            5：已经撤销。
        """
        print("status:", i['status'])
        signed_transaction_info = i['signedTransactionInfo']
        order_rsp = jwt.decode(signed_transaction_info, options={"verify_signature": False})
        print(json.dumps(order_rsp, ensure_ascii=True, indent=4))

        print('purchaseDate:', datetime.datetime.fromtimestamp(order_rsp['purchaseDate'] / 1000))
        print('expiresDate:', datetime.datetime.fromtimestamp(order_rsp['expiresDate'] / 1000))


def check_refund_order_v1(user_id: int, jwt_token: str, original_transaction_id: str) -> list:
    url = f"https://api.storekit.itunes.apple.com/inApps/v1/refund/lookup/{original_transaction_id}"
    headers = {
        'Authorization': f'Bearer {jwt_token}'
    }

    response = requests.get(url, headers=headers)
    print(response.text)
    rsp = json.loads(response.text)
    refund_rsp_list = []

    if 'signedTransactions' not in rsp:
        print("##### signedTransactions not in rsp", user_id, original_transaction_id, response.text)
        return []

    if len(rsp['signedTransactions']) > 0:
        for i in rsp['signedTransactions']:
            refund_rsp = jwt.decode(i, options={"verify_signature": False})
            print(refund_rsp)
            if refund_rsp['type'] != 'Consumable' and refund_rsp['originalTransactionId'] == original_transaction_id:
                refund_rsp_list.append(refund_rsp)
                print(
                    f'user:{user_id}, originalPurchaseDate: {datetime.datetime.fromtimestamp(refund_rsp["originalPurchaseDate"] / 1000)}, revocationDate: {datetime.datetime.fromtimestamp(refund_rsp["revocationDate"] / 1000)}\n')
                print(json.dumps(refund_rsp, ensure_ascii=True) + '\n')
            else:
                print(
                    f'user:{user_id}, originalPurchaseDate: {datetime.datetime.fromtimestamp(refund_rsp["originalPurchaseDate"] / 1000)}, revocationDate: {datetime.datetime.fromtimestamp(refund_rsp["revocationDate"] / 1000)}\n')
                print(json.dumps(refund_rsp, ensure_ascii=True) + '\n')

    return refund_rsp_list


if __name__ == '__main__':
    jwt_token = get_jwt_token_for_in_app()
    print("#" * 100)
    check_refund_order(jwt_token, "410001724413589")
    print("#" * 100)
    check_refund_order_v1(0, jwt_token, "410001724413589")
