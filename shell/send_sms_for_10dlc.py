import datetime
import random

import phonenumbers
from twilio.rest import Client

total_area_code_list = [str(number) for number in range(200, 1000)]
not_us_canada_area_code_list = ['242', '246', '264', '268', '284', '340', '345', '441', '473', '649', '658', '664',
                                '670', '671', '684', '721', '758', '767', '784', '787', '809', '829', '849', '868',
                                '869', '876', '939']
us_canada_area_code_list = [v for v in total_area_code_list if v not in not_us_canada_area_code_list]


def generate_valid_phone_number():
    cnt = 0
    while True:
        # 生成前九位数字
        prefix = random.choice(us_canada_area_code_list)
        suffix = ''.join(random.choices('**********', k=7))

        # 生成电话号码
        phone_number = '+1' + prefix + suffix

        if phonenumbers.is_valid_number(phonenumbers.parse(phone_number, "US")):
            return phone_number

        cnt += 1
        if cnt > 1000:
            return None


account_sid = "**********************************"
account_token = "d703a14afd6342ce8699d3ed8607bfc2"
client = Client(account_sid, account_token)

if __name__ == '__main__':
    print("*" * 20)
    print(f"time: {datetime.datetime.now(datetime.timezone.utc)}")
    if random.random() <= 0.8:
        print("random to exit")
        exit(0)

    number_map = {
        "+***********": "low-5",
        "+***********": "low-6",
    }
    number_txt_map = {
        "+***********": f"Congrats on today's achievements! You have keep {random.randint(10, 100)} days! Keep it up!",
        "+***********": "Share to boost VIP! Friends join, you both win with extended VIP access.",
    }
    for k, v in number_txt_map.items():
        from_number = k
        content = v
        to_number = generate_valid_phone_number()
        print(f"{from_number} -> {to_number} [ {number_map[k]} ]: {content}")
        message = client.messages.create(
            body=content,
            from_=from_number,
            to=to_number,
            status_callback="https://tw.zehougroup.xyz/phone/sms/status/",
        )
        print(message.sid)
        print("done")
