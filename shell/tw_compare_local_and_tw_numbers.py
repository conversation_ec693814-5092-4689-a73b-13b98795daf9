import datetime
import smtplib
import sys
import traceback
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
from twilio.rest import Client


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "TWILIO_ACCOUNT_SID": env("TWILIO_ACCOUNT_SID"),
        "TWILIO_ACCOUNT_TOKEN": env("TWILIO_ACCOUNT_TOKEN"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_local_numbers() -> list:
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    cursor = db.cursor(pymysql.cursors.DictCursor)
    cursor.execute("select number,sid from Number_numberinventory where status in ('USING', 'EXPIRE') and platform=0")
    local_numbers = []
    for row in cursor.fetchall():
        local_numbers.append({"number": row['number'], "sid": row['sid']})
    return local_numbers


def get_tw_numbers(version: int) -> list:
    version_map = {
        1: {
            "account_sid": "**********************************",
            "account_token": "d703a14afd6342ce8699d3ed8607bfc2",
        },
        2: {
            "account_sid": "**********************************",
            "account_token": "7901ab6886b148460b16edc337d1343a",
        }
    }
    account_sid = version_map[version]["account_sid"]
    auth_token = version_map[version]["account_token"]

    client = Client(account_sid, auth_token)
    tw_numbers = []

    for t in client.incoming_phone_numbers.list(limit=10000):
        tw_numbers.append({"number": t.phone_number, "version": version, "sid": t.sid})

    return tw_numbers


def compare_local_and_tw(local: list, tw: list) -> list:
    res = []
    print('number in local but not in tw:')
    cnt1 = 0
    local_numbers = [v['number'] for v in local]
    local_sids = [v['sid'] for v in local]

    tw_numbers = [v['number'] for v in tw]
    tw_sids = [v['sid'] for v in tw]
    for t in local:
        if t['number'] not in tw_numbers:
            cnt1 += 1
            print("\t%s" % t)
            res.append(f"number in local but not in tw: {t}")
        if t['sid'] not in tw_sids:
            cnt1 += 1
            print("\t%s" % t)
            res.append(f"sid in local but not in tw: {t}")

    print('number in tw but not in local:')
    cnt2 = 0
    for t in tw:
        if t['number'] not in local_numbers:
            print("\t%s" % t)
            cnt2 += 1
            res.append(f"number in tw but not in local: {t}")
        if t['sid'] not in local_sids:
            print("\t%s" % t)
            cnt2 += 1
            res.append(f"sid in tw but not in local: {t}")

    print("number in local but not in tw", cnt1)
    print("number in tw but not in local", cnt2)
    return res


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}本地号码池对不上tw报警 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>本地号码池对不上tw报警：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += f"""
         <table border="1">
         """
        for index, send_item in enumerate(send_list):
            html += f"""
             <tr>
                 <td>{index}: {send_item}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"compare_local_and_tw_numbers, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        local_numbers = get_local_numbers()
        tw_numbers_1 = get_tw_numbers(1)
        # tw_numbers_2 = get_tw_numbers(2)
        print("tw_numbers_1 size:", len(tw_numbers_1))
        # print("tw_numbers_2 size:", len(tw_numbers_2))
        tw_numbers = tw_numbers_1  # + tw_numbers_2
        res = compare_local_and_tw(local_numbers, tw_numbers)
        print("len:", len(res))
        if len(res) > 0:
            send_mail(res)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"compare_local_and_tw_numbers, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
