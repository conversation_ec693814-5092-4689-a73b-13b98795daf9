# coding=utf-8
import datetime
import smtplib
import sys
import traceback
from collections import defaultdict
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import environ
import pymysql

access_log = '/var/log/nginx/access.log'

# 发邮件列表
send_list = []

handel_dt = datetime.datetime.today() + datetime.timedelta(days=-1)
handel_dt_str = handel_dt.strftime("%Y%m%d")


def get_env_config():
    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_today_access_logs(date_str=None):
    # 可以指定日期，默认是昨天
    if not date_str:
        file_name = access_log + '.' + handel_dt_str
    else:
        file_name = access_log + '.' + date_str

    send_list.append(f"本次分析日志：{file_name}")
    print(f"filename: {file_name}")
    lines = []
    with open(file_name, 'r') as f:
        while True:
            line = f.readline()
            if not line:
                break
            lines.append(line)
    return lines


def get_new_users(cursor, date_str: str) -> list:
    dt = datetime.datetime.strptime(date_str, "%Y%m%d")
    end_dt = dt + datetime.timedelta(days=1)
    sql = f"""select uuid from User_user where created_at>='{dt.strftime("%Y-%m-%d")}' and created_at < '{end_dt.strftime("%Y-%m-%d")}'"""
    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()
    device_ids = []
    for i in res:
        device_ids.append(i['uuid'])
    return device_ids


def handle_for_new(userid_url_cnt_map: dict) -> dict:
    print("handle_for_new...")

    new_get_user_profile_user_cnt = 0
    new_vip_user_cnt = 0
    new_number_search_user_cnt = 0
    new_number_lock_user_cnt = 0
    new_order_user_cnt = 0

    # 只处理新用户
    for k, v in userid_url_cnt_map.items():
        if "new" in k:
            if '/user/profileByDevice/' in k:
                new_get_user_profile_user_cnt += 1
            elif '/config/vip/' in k:
                new_vip_user_cnt += 1
            elif '/number/search/' in k:
                new_number_search_user_cnt += 1
            elif '/number/lock/' in k:
                new_number_lock_user_cnt += 1
            elif '/order/cert/' in k:
                new_order_user_cnt += 1

    print(f"去重新用户数 : 获取用户信息：{new_get_user_profile_user_cnt}")
    print(f"去重新用户数 : 获取电话号码列表：{new_number_search_user_cnt}")
    print(f"去重新用户数 : 锁定号码：{new_number_lock_user_cnt}")
    print(f"去重新用户数 : 获取vip列表：{new_vip_user_cnt}")
    print(f"去重新用户数 : 下单：{new_order_user_cnt}")

    return {
        "login_cnt": new_get_user_profile_user_cnt,
        "search_cnt": new_number_search_user_cnt,
        "lock_cnt": new_number_lock_user_cnt,
        "vip_cnt": new_vip_user_cnt,
        "order_cnt": new_order_user_cnt,
    }


def work(date_str: str):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    lines = get_today_access_logs(date_str)
    print(f"lines size: {len(lines)}")
    send_list.append(f"lines size: {len(lines)}")

    # 不指定日期就默认过滤今天的内容
    if date_str:
        dt1 = datetime.datetime.strptime(date_str, "%Y%m%d")
        _date_str = dt1.strftime("%d/%b/%Y")
    else:
        date_str = handel_dt.strftime("%Y%m%d")
        _date_str = handel_dt.strftime("%d/%b/%Y")
    send_list.append(f"过滤日期：{_date_str}")
    print(f"过滤日期：{_date_str}")
    lines = [v for v in lines if _date_str in v]
    print(f"after date filter, lines size: {len(lines)}")
    send_list.append(f"after date filter, lines size: {len(lines)}")

    valid_urls = ["zhphone/user/profileByDevice/", "/zhphone/number/search/", "/zhphone/number/lock/",
                  "/zhphone/config/vip/", "/zhphone/order/cert/"]

    userid_url_cnt_map = defaultdict(int)

    new_device_ids = get_new_users(cursor, date_str)
    print("new_device_ids size:", len(new_device_ids))

    for index, line in enumerate(lines):
        if index % 10000 == 0:
            print(f"index: {index}")

        a = line.split()
        url = a[7].split("?")[0]
        status = a[9]

        # 必须在合法的url中
        for i in valid_urls:
            if i in line:
                break
        else:
            continue

        # 如果不是200，则记录下
        if status != '"200"':
            continue

        uuid = a[-5].strip('"').replace("uuid:", '')
        if uuid in new_device_ids:
            userid_url = f"new_{url}_{uuid}"
            userid_url_cnt_map[userid_url] = 1
        else:
            userid_url = f"old_{url}_{uuid}"
            userid_url_cnt_map[userid_url] = 1

    data = handle_for_new(userid_url_cnt_map)

    sql = f"INSERT INTO Report_funnel (`day`, login_cnt, search_cnt, lock_cnt, vip_cnt, order_cnt) " \
          f"VALUES({date_str}, {data['login_cnt']}, {data['search_cnt']}, " \
          f"{data['lock_cnt']}, {data['vip_cnt']}, {data['order_cnt']}) " \
          f"ON DUPLICATE KEY UPDATE login_cnt={data['login_cnt']}, " \
          f"search_cnt={data['search_cnt']}, lock_cnt={data['lock_cnt']}, " \
          f"vip_cnt={data['vip_cnt']}, order_cnt={data['order_cnt']}"

    print(f"更新漏斗数据, sql={sql}")
    cursor.execute(sql)
    db.commit()

    cursor.close()
    db.close()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(err_msg: str):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}更新用户漏斗出错 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>更新用户漏斗出错：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += f"{err_msg}"
        html += '</body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"check user funnel, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        if len(sys.argv) == 1:
            yesterday = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=-1)
            date_str = yesterday.strftime("%Y%m%d")
            work(date_str)
        elif len(sys.argv) == 2:
            date_str = sys.argv[1]
            work(date_str)
        else:
            print("invalid param")
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail(traceback.format_exc())
    finally:
        print(f"check user funnel, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
