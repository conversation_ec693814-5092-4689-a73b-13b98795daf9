import sys

import pymysql
import telnyx

# 设置Telnyx API认证信息
telnyx.api_key = "**********************************************************"


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_sms_cost_by_id(sms_id: str) -> float:
    sms = telnyx.Message.retrieve(sms_id)
    print(f"[TelnyxUtil.get_sms_cost_by_id] sms_id:{sms_id}, json: {sms}")
    if not sms:
        print(f"[TelnyxUtil.get_sms_cost_by_id] sms_id:{sms_id}, sms is nil")
        return 0
    if "cost" not in sms or not sms['cost']:
        print(f"[TelnyxUtil.get_sms_cost_by_id] sms_id:{sms_id}, sms has no price, {sms}")
        return 0

    return float(sms.get("cost").get("cost", 0))


def work(limit: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = f"select id, sid from Call_smsrecord where price is null and err_code is null and status not in ('send_fake', 'sent_failed') order by id desc limit {limit}"
    print(sql)
    cursor.execute(sql)
    all_records = cursor.fetchall()
    all_size = len(all_records)
    for index, i in enumerate(all_records):
        record_id = i['id']
        sid = i['sid']
        print(f"record_id={record_id}, sid={sid}")
        if index == 0 or index % 100 == 0:
            print(f"=========> {index}/{all_size}")
        cost = get_sms_cost_by_id(sid)
        if cost > 0:
            sql2 = f"update Call_smsrecord set price={cost} where id={record_id}"
            cursor.execute(sql2)

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    if len(sys.argv) != 2:
        limit = 100
    else:
        limit = int(sys.argv[1])

    work(limit)
