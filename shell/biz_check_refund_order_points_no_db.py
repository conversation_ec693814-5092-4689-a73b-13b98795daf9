# encoding=utf-8
import datetime
import json
import time

import jwt
import requests

"""
教程：https://cloud.tencent.com/developer/article/1939304
"""


def get_jwt_token_for_in_app():
    # 读取密钥文件证书内容
    f = open("order/AuthKey_8J425X2QDA.p8")
    key_data = f.read()
    f.close()

    # JWT Header
    header = {
        "alg": "ES256",
        "kid": "8J425X2QDA",
        "typ": "JWT"
    }

    # JWT Payload
    payload = {
        "iss": "e46a3bfd-1a50-4a96-9e65-2fbd8a742301",
        "aud": "appstoreconnect-v1",
        "iat": int(time.time()),
        "exp": int(time.time()) + 60 * 60,  # 60 minutes timestamp
        "nonce": "6edffe66-b482-11eb-8529-0242ac130003",
        "bid": "com.zehoutech.secondphone"
    }

    # JWT token
    token = jwt.encode(headers=header, payload=payload, key=key_data, algorithm="ES256")

    print("JWT Token:", token)
    return token


def check_refund_order(jwt_token: str, original_transaction_id: str):
    url = f"https://api.storekit.itunes.apple.com/inApps/v1/refund/lookup/{original_transaction_id}"
    print(url)
    headers = {
        'Authorization': f'Bearer {jwt_token}'
    }

    response = requests.get(url, headers=headers)

    print(response.text)
    rsp = json.loads(response.text)
    if len(rsp['signedTransactions']) > 0:
        print(f"\t {original_transaction_id} get a refund")
        for i in rsp['signedTransactions']:
            refund_rsp = jwt.decode(i, options={"verify_signature": False})
            print(json.dumps(refund_rsp, ensure_ascii=True, indent=4))
            print('type', refund_rsp['type'])
            print('originalPurchaseDate:', datetime.datetime.fromtimestamp(refund_rsp['originalPurchaseDate'] / 1000))
            print('expiresDate:', datetime.datetime.fromtimestamp(refund_rsp['expiresDate'] / 1000))


if __name__ == '__main__':
    jwt_token = get_jwt_token_for_in_app()

    check_refund_order(jwt_token, "480001412807389")
