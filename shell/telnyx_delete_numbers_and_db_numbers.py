import pymysql
import requests


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def delete_a_number(number: str) -> bool:
    # https://developers.telnyx.com/openapi/spec/tag/Number-Configurations-API/#tag/Number-Configurations-API/operation/deletePhoneNumber
    try:
        url = "https://api.telnyx.com/v2/phone_numbers"
        query = {
            "filter[phone_number]": number,
        }

        headers = {"Authorization": f"Bearer KEY0193D036537A4B8864C8C5751E010685_OmTXhITKI4JA0jBUzKRhMh"}
        response = requests.get(url, headers=headers, params=query)

        data = response.json()
        print(f"[TelnyxUtil.delete_a_number] {number},  status_code:{response.status_code}, rsp:{data}")
        if len(data['data']) == 0:
            print(f"[TelnyxUtil.delete_a_number] {number} not in Telnyx...")
            return False

        for i in data['data']:
            delete_url = "https://api.telnyx.com/v2/phone_numbers/" + i['id']
            print(f"[TelnyxUtil.delete_a_number] {number}, url:{delete_url}")
            response = requests.delete(delete_url, headers=headers)
            delete_data = response.json()
            print(f"[TelnyxUtil.delete_a_number] {number}, delete_data:{delete_data}")
            if delete_data['data']['status'] == 'deleted' and delete_data['data']['phone_number'] == number:
                print(f"[TelnyxUtil.delete_a_number] {number}, delete success!")
                return True

        print(f"[TelnyxUtil.delete_a_number] {number}, delete failed!")
        return False
    except Exception:
        print(f"[TelnyxUtil.delete_a_number] {number} failed")
        return False


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = "select id,number from Number_numberinventory where status='EXPIRE'"
    cursor.execute(sql)
    all_expire_res = cursor.fetchall()

    all_admin_lock_sql = "select * from Number_numberadminlock where deleted=0 and expired_at > now();"
    cursor.execute(all_admin_lock_sql)
    all_admin_lock_res = cursor.fetchall()
    all_admin_lock__numbers = [v['number'] for v in all_admin_lock_res]
    print("all_admin_lock__numbers:", all_admin_lock__numbers)

    # 留下最后的五十保本
    for index, i in enumerate(all_expire_res[:-50]):
        print("#################")
        print("#################")
        number_id = i['id']
        number = i['number']
        print(index, number_id, number)
        is_used_sql = f"select * from Number_numberused where number_id={number_id} and status='USING'"
        cursor.execute(is_used_sql)
        is_number_used = cursor.fetchall()
        if len(is_number_used) > 0:
            print(f"number: {number} is used, cannot delete")
            continue
        if number in all_admin_lock__numbers:
            print(f"number: {number} is admin lock, cannot delete")
            continue

        delete_sql = f"update Number_numberinventory set status='RELEASED', release_at=now() where id={number_id}"
        print(delete_sql)
        cursor.execute(delete_sql)
        delete_a_number(number)

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    work()
