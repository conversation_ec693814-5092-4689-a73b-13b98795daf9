import traceback

import requests


def get_sip(page_number: int, page_size: int):
    url = "https://api.telnyx.com/v2/credential_connections"

    query = {
        "page[number]": f"{page_number}",
        "page[size]": f"{page_size}",
        "filter[outbound.outbound_voice_profile_id]": "2201593832611513488",
        "sort": "connection_name"
    }

    headers = {"Authorization": "Bearer KEY0193D036537A4B8864C8C5751E010685_OmTXhITKI4JA0jBUzKRhMh"}

    response = requests.get(url, headers=headers, params=query)

    data = response.json()
    return data


def update_a_sip(sid: str):
    url = "https://api.telnyx.com/v2/credential_connections/" + sid

    headers = {"Authorization": "Bearer KEY0193D036537A4B8864C8C5751E010685_OmTXhITKI4JA0jBUzKRhMh"}

    payload = {
        "ios_push_credential_id": "0303fa44-0bcd-484f-bbdb-b0a98a279ccc",
    }

    response = requests.patch(url, json=payload, headers=headers)

    data = response.json()
    print(data)


if __name__ == '__main__':

    with open("failed_push.txt", "w") as f:
        cnt = 0
        for i in range(1000):
            print("#" * 50 + f"{i}")
            print("#" * 50 + f"{i}")

            data = get_sip(i + 1, 100)

            if len(data['data']) == 0:
                break

            for index, d in enumerate(data['data']):
                print("batch:", i, "index:", index)
                if 'txtnow' not in d['user_name']:
                    continue

                if d["ios_push_credential_id"] != "0303fa44-0bcd-484f-bbdb-b0a98a279ccc":
                    try:
                        print(d['id'], d['user_name'], d['registration_status'], d["ios_push_credential_id"])
                        update_a_sip(d['id'])
                        cnt += 1
                    except Exception:
                        traceback.print_exc()
                        f.write(d['id'] + "\t" + d['user_name'] + "\n")

            print("batch:", i, "##" * 10, "update cnt:", cnt)
