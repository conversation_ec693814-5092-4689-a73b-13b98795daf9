import json

if __name__ == '__main__':
    ans = {}
    with open("1.txt") as f:
        while True:
            line = f.readline()
            if not line:
                break

            if 'post_data' not in line:
                continue
            line = line.strip()
            # print(line)
            data = line.split("post_data:")[1].replace("'", '"').replace("None", "null")
            json_data = json.loads(data)
            # print(json_data)
            call_control_id = json_data['data']['payload']['call_control_id']
            event_type = json_data['data']['event_type']
            # print(call_control_id, event_type)
            if call_control_id not in ans:
                ans[call_control_id] = [event_type]
            else:
                ans[call_control_id].append(event_type)

    cnt = 0
    event_type_cnt_1 = 0
    event_type_cnt_2 = 0
    event_type_cnt_lower_than_4 = 0
    for k, v in ans.items():
        print(k, v)
        cnt += 1
        if len(v) == 1:
            event_type_cnt_1 += 1
        if len(v) == 2:
            event_type_cnt_2 += 1

        if len(v) < 4:
            event_type_cnt_lower_than_4 += 1

    print("cnt:", cnt)
    print("event_type_cnt_1:", event_type_cnt_1)
    print("event_type_cnt_2:", event_type_cnt_2)
    print("event_type_cnt_lower_than_4:", event_type_cnt_lower_than_4)
