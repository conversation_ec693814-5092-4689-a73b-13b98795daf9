import traceback
import pymysql
import logging
from datetime import datetime, timedelta
from contextlib import contextmanager
import environ

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@contextmanager
def get_db_connection():
    db = pymysql.connect(
        host=env_config["DB_HOST_KEY"],
        port=env_config["DB_PORT_KEY"],
        user=env_config["DB_USER_KEY"],
        passwd=env_config["DB_PASSWORD_KEY"],
        db=env_config["DB_DATABASE_NAME_KEY"],
        charset='utf8'
    )
    try:
        yield db
    finally:
        db.close()


def get_env_config():
    env = environ.Env()
    environ.Env.read_env(env_file="/root/www/VirtualSIM_BackEnd/SecPhone/.env")
    return {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY"))
    }


env_config = get_env_config()


def fetch_sms_data():
    data = {}
    end_date = datetime.now()
    start_date = end_date - timedelta(days=180)

    for day_offset in range(181):
        date = (start_date + timedelta(days=day_offset)).strftime('%Y-%m-%d')
        cumulative_total, cumulative_failed, cumulative_fake, cumulative_delivered = 0, 0, 0, 0

        for hour in range(24):
            start_time = f"{date} {hour}:00:00"
            end_time = f"{date} {hour}:59:59"
            sql = """
                SELECT 
                    SUM(CASE WHEN status='delivered' THEN 1 ELSE 0 END) AS delivered_count,
                    SUM(CASE WHEN status='send_fake' THEN 1 ELSE 0 END) AS fake_count,
                    COUNT(*) AS total_count
                FROM Call_smsrecord
                WHERE direction='SEND' AND is_it=0
                AND created_at BETWEEN %s AND %s;
            """
            with get_db_connection() as db:
                cursor = db.cursor(pymysql.cursors.DictCursor)
                cursor.execute(sql, (start_time, end_time))
                result = cursor.fetchone()

            if result:
                cumulative_total += result.get('total_count', 0) if result.get('total_count', 0) else 0
                cumulative_delivered += result.get('delivered_count', 0) if result.get('delivered_count', 0) else 0
                cumulative_fake += result.get('fake_count', 0) if result.get('fake_count', 0) else 0
                cumulative_failed = cumulative_total - cumulative_delivered - cumulative_fake

                if date not in data:
                    data[date] = {}
                data[date][hour] = {
                    'total': cumulative_total,
                    'failed': cumulative_failed,
                    'fake': cumulative_fake
                }
    return data


def insert_into_delivery_hour(data):
    for date, daily_data in data.items():
        columns = []
        values = []
        updates = []

        for hour, counts in daily_data.items():
            columns.extend([f"hour{hour}_total", f"hour{hour}_failed", f"hour{hour}_fake"])
            values.extend([counts['total'], counts['failed'], counts['fake']])
            updates.extend([f"hour{hour}_total = VALUES(hour{hour}_total)",
                            f"hour{hour}_failed = VALUES(hour{hour}_failed)",
                            f"hour{hour}_fake = VALUES(hour{hour}_fake)"])

        sql = f"""
            INSERT INTO Call_messagedeliveryhour (mydate, {', '.join(columns)})
            VALUES (%s, {', '.join(['%s'] * len(values))})
            ON DUPLICATE KEY UPDATE {', '.join(updates)};
        """
        with get_db_connection() as db:
            cursor = db.cursor()
            cursor.execute(sql, [date] + values)
            db.commit()


if __name__ == "__main__":
    try:
        sms_data = fetch_sms_data()
        insert_into_delivery_hour(sms_data)
    except Exception as e:
        logger.error(f"Error occurred: {e}")
        traceback.print_exc()
