import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

from elasticsearch import Elasticsearch


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def main():
    yesterday = datetime.datetime.today() + datetime.timedelta(days=-1)
    yesterday_str = yesterday.strftime('%Y-%m-%d')
    print(f"index: {yesterday_str}")
    es = Elasticsearch(
        ['http://*********:9200']
    )

    words = ['congratulations', 'power', '150$', '200$', '300$', '350$', '400$', '500$', '600$', 'available', 'incall',
             'outcall', 'service', 'hookup', 'amount', 'afford', 'hhr', 'hrs', 'hour', 'hours', 'cashapp',
             'zelle', 'cash', 'paypal', 'payment', 'how much', 'pay', 'picture', 'horny']
    should_list = []
    for w in words:
        should_list.append({
            "match": {  # 关键字，表示需要匹配的元素
                "content": w
            }
        })
    body = {  # 请求体
        "query": {  # 关键字，把查询语句给 query
            "bool": {  # 关键字，表示使用 filter 查询，没有匹配度
                "filter": [
                    {
                        "term": {
                            "direction": "send"
                        }
                    }
                ],
                "minimum_should_match": 1,
                "should": should_list
            }
        },

        # 下面是对返回的结果继续排序
        "sort": [{"created_at": {"order": "desc"}}],
        "from": 0,  # 从匹配到的结果中的第几条数据开始返回，值是匹配到的数据的下标，从 0 开始
        "size": 500  # 返回多少条数据
    }
    response = es.search(
        index=yesterday_str,  # 索引名
        body=body,
    )
    cnt = response['hits']['total']['value']
    print(f"total: {cnt}")
    if cnt > 0:
        send_mail_list = []
        for index, i in enumerate(response['hits']['hits']):
            print(f"{index + 1}: {i['_source']}")
            send_mail_list.append(i['_source'])
        send_mail(send_mail_list)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_items: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>', ]

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}找到猪!!!', 'utf-8').encode()

        html = f'<html><body><h2>找到猪：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
               <table border="1">
                <tr>
                   <th>index</th>
                   <th>user</th>
                   <th>direction</th>
                   <th>from</th>
                   <th>to</th>
                   <th>content</th>
                   <th>time</th>
               </tr>
               """
        for index, send_item in enumerate(send_items):
            html += f"""
             <tr>
                 <td>{index}</td>
                 <td>{send_item['user_id']}</td>
                 <td>{send_item['direction']}</td>
                 <td>{send_item['from_number']}</td>
                 <td>{send_item['to_number']}</td>
                 <td>{send_item['content']}</td>
                 <td>{send_item['created_at']}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"FindPigs, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        main()
    except Exception as ex:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"FindPigs, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
