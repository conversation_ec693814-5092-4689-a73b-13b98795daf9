# encoding=utf-8
import traceback

import pymysql
import requests


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "TWILIO_ACCOUNT_SID": env("TWILIO_ACCOUNT_SID"),
        "TWILIO_ACCOUNT_TOKEN": env("TWILIO_ACCOUNT_TOKEN"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_local_numbers(cursor) -> list:
    cursor.execute("select count(*) from Number_numberinventory where status = 'TW_HAND_RELEASED'")
    res = cursor.fetchone()
    print(res)

    cursor.execute(
        "select number, sid from Number_numberinventory where status = 'TW_HAND_RELEASED' order by id desc limit 1000")
    local_numbers = []
    for row in cursor.fetchall():
        local_numbers.append({"number": row['number'], "sid": row['sid']})
    return local_numbers


def delete_tw_number(sid: str):
    url = f'https://www.twilio.com/console/phone-numbers/api/v1/incoming-phone-numbers/numbers/{sid}'
    headers = {
        'authority': 'www.twilio.com',
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'cookie': '_cq_duid=1.**********.k8SKFFbvu8AiKz2X; _rdt_uuid=1663643862429.349d1f7e-465b-4968-ab78-fdaa26515ea7; _fbp=fb.1.1663643864612.1004490986; _mkto_trk=id:294-TKB-300&token:_mch-twilio.com-1663643866319-11915; _hjSessionUser_1359002=eyJpZCI6IjhlMDFjMDI0LTdiMDUtNTRlNC05OGU1LTM5MjMzYzZjOTNiNyIsImNyZWF0ZWQiOjE2NjM2NDM4NjYxNTksImV4aXN0aW5nIjp0cnVlfQ==; __stripe_mid=1ff24d7a-bd05-4636-ab10-9f377f3bf4f77edc52; tw-code-language=Python; ki_r=; notice_preferences=2:; notice_gdpr_prefs=0,1,2:; cmapi_gtm_bl=; cmapi_cookie_privacy=permit 1,2,3; oribili_user_guid=************************************; ko_id=e016b790-cd5d-46e5-8f95-4a534f25dee2; mp_f71c19735fa6ecc5225ff563285e1794_mixpanel=%7B%22distinct_id%22%3A%20%2218358f240cb5bb-0c47e63afe0284-1a525635-1ea000-18358f240ccc6b%22%2C%22%24device_id%22%3A%20%2218358f240cb5bb-0c47e63afe0284-1a525635-1ea000-18358f240ccc6b%22%2C%22%24initial_referrer%22%3A%20%22%24direct%22%2C%22%24initial_referring_domain%22%3A%20%22%24direct%22%2C%22%24search_engine%22%3A%20%22google%22%7D; nmstat=a3344404-4b6a-d7d4-267f-b019b0ab5c6c; userty.core.p.246ab3=__2VySWQiOiI1ZjEzYWNkZTQ3ZDRkYmE1OTRjYjNkYTU1ODgyZWMxNSJ9eyJ1c; AMCV_32523BB96217F7B60A495CB6%40AdobeOrg=1176715910%7CMCIDTS%7C19573%7CMCMID%7C70031298370485381622350761829944387332%7CMCAAMLH-1691658207%7C11%7CMCAAMB-1691658207%7CRKhpRz8krg2tLO6pguXWp5olkAcUniQYPHaMWWgdJ3xzPWQmdj0y%7CMCOPTOUT-1691060607s%7CNONE%7CvVersion%7C5.4.0; ki_t=1663643871370%3B1693275699007%3B1693275699007%3B77%3B170; _ga_DT2RYP51K7=GS1.2.1694238982.1.1.1694238982.0.0.0; _uetvid=cdcdabc0389211edb922732e22c16a6e; ajs_user_id=US93598573723e09c2abf18761d52bebaf; ajs_group_id=**********************************; _gcl_au=1.1.1624635319.1695625713; mp_b36aa4f2a42867e23d8f9907ea741d91_mixpanel=%7B%22distinct_id%22%3A%20%222adaac56-edf4-43ec-afb5-1505cde0d955%22%2C%22%24initial_referrer%22%3A%20%22https%3A%2F%2Fconsole.twilio.com%2F%22%2C%22%24initial_referring_domain%22%3A%20%22console.twilio.com%22%7D; sa-user-id=s%253A0-72e4dc30-b1a3-4413-4376-f2ca3534f2fd.5qLqj3MWMJiq7Ky98dcL5IdJ9xGX9bhiDT4N0RHro2U; sa-user-id-v2=s%253AcuTcMLGjRBNDdvLKNTTy_Y9cdhM.TZ7Y7vea6cfITt6r9%252Fxu2mS9xEKqGWaTIjSgGmBGj1w; sa-user-id-v3=s%253AAQAKINokpaeDy80uuQKXV7dH7X_BKWIejoen1Wju46gIixKvEMABGAQgyYiQqAYwAToEJM4hpkIEEnzb9A.CBdO1HdmhJ2L8Tj9Xf0v3xNkMH5Q1mw8fRkkxl5IZ%252BA; _gd_visitor=e65052ed-8890-467e-8cf9-7c7db37135d8; _gd_svisitor=a0b31bb839150000db3029631500000047e70000; _hp2_id.150403558=%7B%22userId%22%3A%222326984716206341%22%2C%22pageviewId%22%3A%222252668583193136%22%2C%22sessionId%22%3A%221899839653489186%22%2C%22identity%22%3A%22US93598573723e09c2abf18761d52bebaf%22%2C%22trackerVersion%22%3A%224.0%22%2C%22identityField%22%3Anull%2C%22isIdentified%22%3A1%7D; check=true; _gid=GA1.2.2138271245.1699670240; _an_uid=3233351422457214394; _gd_session=15fab0fc-5358-43cd-82fd-5ef1e8c98c34; _hjIncludedInSessionSample_1359002=0; _hjSession_1359002=eyJpZCI6IjBkMTY0ZjQ0LTNlMGMtNDljMy04NGNkLTcwMDg5YmFjMmE4MiIsImNyZWF0ZWQiOjE2OTk2NzAyNDI4OTMsImluU2FtcGxlIjpmYWxzZSwic2Vzc2lvbml6ZXJCZXRhRW5hYmxlZCI6ZmFsc2V9; _hjAbsoluteSessionInProgress=0; _hp2_ses_props.1541905715=%7B%22ts%22%3A1699670241413%2C%22d%22%3A%22www.twilio.com%22%2C%22h%22%3A%22%2Fen-us%22%7D; at_check=true; mboxEdgeCluster=38; mbox=PC#********************************.38_0#1762915048|session#49cd02c9221047c09302098342e975d7#1699672108; _clck=1kuuxk2|2|fgm|0|1232; _clsk=1manlss|1699670251330|1|1|v.clarity.ms/collect; identity=; _ga_RRP8K4M4F3=GS1.1.1699670240.147.0.1699670792.0.0.0; _ga_B1EHG633YE=GS1.1.1699670246.3.0.1699670792.0.0.0; tw-auth0-code-verifier=2Z4dxSDEGZUP63wHsCZtMCKUlqkzwFRq5gwg__ju8FE; tw-auth0-nonce=lQxa1xjpAhNQMBise0uWXw; tw-auth0-state=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJVQVMiLCJkZWVwbGlua19nIjoiIiwiZXhwIjoxNjk5NjcyNjU3LCJkZWVwbGlua190IjoiIiwiaWF0IjoxNjk5NjcwODU3fQ.HcmKYN0q-QJqv4DSNDXuynbFamucrIdPS4aO2REpENc; ajs_anonymous_id=c9fa81cc-1ad4-47e6-8fa6-71e12363312a; _ga=GA1.2.1586184113.1663643859; _dd_s=rum=1&id=94254c5f-70fd-411f-a381-fe00d5618aec&created=1699670241597&expire=1699671979738; _hp2_id.1541905715=%7B%22userId%22%3A%22888645701585655%22%2C%22pageviewId%22%3A%224240562697134530%22%2C%22sessionId%22%3A%222263192600971056%22%2C%22identity%22%3A%22US93598573723e09c2abf18761d52bebaf%22%2C%22trackerVersion%22%3A%224.0%22%2C%22identityField%22%3Anull%2C%22isIdentified%22%3A1%7D; _gat=1; server-identity=ZTE=LA==MQ==LA==UJc9rWxurrVEAdbDLA==uGFRBnpETOq7nCItiw-2cb68L3u-a3ewosEVC77GhbFfOY8xVvXIQoV_mdAvv4G9qiER4JTAAM-lUb5AcDA=; tw-visitor=eyJrZXlJZCI6InZpc2l0b3JFbmNyeXB0aW9uS2V5Iiwibm9uY2UiOiJwZWdYR0pMTEdYeWI5aHIxIiwicGF5bG9hZCI6Ill4WmhtR1hrZERjZ2pmNzhwMmlkZWpFYUZScHZITUw5VUE4R2ozR005NG1DQXFXMzVmTjF5UjVmamtaMmJwV3NudFE9IiwiY3J5cHRvSWQiOjQsImFkZGl0aW9uYWxEYXRhIjoiZEc5aFdWazNkMkpMVG5kQmNFOXJWalp0WjNCUWFrNHpSMVZYVlZSUFRXMD0ifQ==',
        'origin': 'https://console.twilio.com',
        'pragma': 'no-cache',
        'referer': 'https://console.twilio.com/',
        'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
        'sec-ch-ua-mobile': '?1',
        'sec-ch-ua-platform': '"Android"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Mobile Safari/537.36',
        'x-twilio-csrf': 'e3dac58d28a2ed49b8ba0e133e4e39e82cb49fb68e58bdad68d0eb20dd0bfbfb',
    }

    data = '{"phoneNumberSid":"' + sid + '"}'

    response = requests.request("DELETE", url, headers=headers, data=data)
    print(response.text)


def update_number_status(cursor, number, sid):
    sql = f"update Number_numberinventory set status='TW_HAND_DEL_DONE' where sid='{sid}' and number='{number}'"
    res = cursor.execute(sql)
    print(res)


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    cursor = db.cursor(pymysql.cursors.DictCursor)
    try:
        numbers = get_local_numbers(cursor)
        for index, n in enumerate(numbers):
            number = n['number']
            sid = n['sid']
            print(index, number, sid)
            delete_tw_number(sid)
            update_number_status(cursor, number, sid)
    except Exception:
        traceback.print_exc()
    finally:
        db.commit()
        cursor.close()
        db.close()


if __name__ == '__main__':
    work()
