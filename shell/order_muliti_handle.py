# coding=utf-8

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # sql = f"select id,user_id,original_transaction_id,expire_at from Order_order where original_transaction_id in " \
    #       f"('1190000037216864','200001523835701', '420001508405545', '60001366286588', '60001377307080', '70001529542120', '730001028649810');"
    #
    # cursor.execute(sql)
    # res = cursor.fetchall()
    # my_dic = {}
    # for i in res:
    #     user_id = i['user_id']
    #     original_transaction_id = i['original_transaction_id']
    #     if original_transaction_id not in my_dic:
    #         my_dic[original_transaction_id] = [user_id]
    #     else:
    #         my_dic[original_transaction_id].append(user_id)

    transaction_ids = ['1190000037216864', '200001523835701', '420001508405545', '60001366286588',
                       '60001377307080', '70001529542120', '730001028649810']

    transaction_dict = {}  # 存储每个transaction_id对应的最新user_id

    for transaction_id in transaction_ids:
        # 查询每个transaction_id对应的最新user_id
        sql = f"""SELECT user_id
                  FROM Point_pointrecord
                  WHERE user_id IN (
                      SELECT user_id
                      FROM Order_order
                      WHERE original_transaction_id = '{transaction_id}'
                  )
                  ORDER BY created_at DESC
                  LIMIT 1"""

        cursor.execute(sql)
        result = cursor.fetchone()

        if result:
            transaction_dict[transaction_id] = result['user_id']

    # 输出结果
    for transaction_id, user_id in transaction_dict.items():
        print(f"original_transaction_id: {transaction_id}, 最新的userid: {user_id}")

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    work()
