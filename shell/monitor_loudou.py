# coding=utf-8
import datetime
import re
import smtplib
import sys
import traceback
from collections import defaultdict
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql

access_log = '/var/log/nginx/access.log'

ERROR_UUID_NOT_IN_DB = -1
ERROR_PHONE_NOT_IN_DB = -2
ERROR_PHONE_IN_DB_BUT_NOT_IN_USE = -3
ERROR_CANNOT_GET_USERID = -4

# 发邮件列表
send_list = []

handel_dt = datetime.datetime.today() + datetime.timedelta(days=-1)
handel_dt_str = handel_dt.strftime("%Y%m%d")


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_today_access_logs(date_str=None):
    # 可以指定日期，默认是昨天
    if not date_str:
        file_name = access_log + '.' + handel_dt_str
    else:
        file_name = access_log + '.' + date_str

    send_list.append(f"本次分析日志：{file_name}")
    print(f"filename: {file_name}")
    lines = []
    with open(file_name, 'r') as f:
        while True:
            line = f.readline()
            if not line:
                break
            lines.append(line)
    return lines


def is_new_user(cursor, userid: int, date_str: str = None) -> int:  # -1找不到用户, 0 不是新用户，1是新用户
    cursor.execute(f"select created_at from User_user where id={userid}")
    res = cursor.fetchall()
    if len(res) == 0:
        return ERROR_CANNOT_GET_USERID

    created_at_str = res[0]["created_at"].strftime("%Y%m%d")
    if not date_str:
        now_str = handel_dt_str
    else:
        now_str = date_str

    if created_at_str == now_str:
        return 1
    return 0


def get_userid_by_uuid(cursor, line: str, uuid: str) -> int:
    cursor.execute(f"select * from User_user where uuid='{uuid}'")
    res = cursor.fetchall()
    if len(res) == 0:
        print(f"ERROR uuid: {uuid} not in our app DB. line: {line}")
        return ERROR_UUID_NOT_IN_DB
    return res[0]['id']


def get_phone_by_userid(cursor, line: str, userid: int) -> str:
    cursor.execute(f"select number from Number_numberused where user_id={userid} and status='USING';")
    res = cursor.fetchall()
    if len(res) == 0:
        print(f"ERROR userid: {userid} has no vaild phone in our app DB. line: {line}")
        return ""

    return res[0]['number']


def get_userid_by_phone(cursor, line: str, phone: str) -> int:
    cursor.execute(f"select * from Number_numberused where number='{phone}' and status='USING';")
    res = cursor.fetchall()
    if len(res) == 0:
        cursor.execute(f"select * from Number_numberinventory where number='{phone}';")
        res2 = cursor.fetchall()
        if len(res2) == 0:
            print(f"ERROR number: {phone} not in our app DB. line: {line}")
            return ERROR_PHONE_NOT_IN_DB
        else:
            print(f"ERROR number: {phone} in our app DB, but no in use. line: {line}")
            return ERROR_PHONE_IN_DB_BUT_NOT_IN_USE
    return res[0]['user_id']


def get_userid_by_re(cursor, line: str) -> int:
    p = re.compile(r".*(userid:(\d)+).*")
    r = p.match(line)
    if r:
        try:
            return int(r.groups()[0].replace("userid:", ""))
        except Exception as e:
            print(f"{e}:  {line}")
            raise e
    else:
        p = re.compile(r".*(uuid:([0-9a-zA-Z\-])+).*")
        r = p.match(line)
        if r:
            try:
                uuid = r.groups()[0].replace("uuid:", "")
                return get_userid_by_uuid(cursor, line, uuid)
            except Exception as e:
                print(f"{e}:  {line}")
                raise e
    return ERROR_CANNOT_GET_USERID


def get_tophone_by_re(line: str) -> str:
    line = line.encode('utf-8').decode('unicode-escape')
    p = re.compile(r'.*to":"([+()0-9]*)')
    r = p.match(line)
    if r:
        try:
            to_phone = r.groups()[0]
            return to_phone
        except Exception as e:
            print(f"{e}:  {line}")
            raise e
    return ""


def get_userid(cursor, line: str) -> tuple:
    try:
        a = line.split()

        # ************* - - [21/Oct/2021:15:22:31 +0000] POST /user/unifyLogin/ HTTP/1.1 "200" 407 "-" "CallMe/1.1.2 (xyz.virtualsim.app; build:101021; iOS 14.2.0) Alamofire/1.1.2" "-" "body={\x22email\x22:\<EMAIL>\x22,\x22type\x22:1,\x22login_id\x22:\x22109509433102738019473\x22}" "userid:-" "uuid:3eac45fd-aa55-4e4d-af1e-11c464eab98a"  0.000 0.032  0.032 0.300
        if "unifyLogin" in line:
            uuid = a[20].strip('"').replace("uuid:", '')
            return get_userid_by_uuid(cursor, line, uuid), None, None, None

        # ************* - - [21/Oct/2021:15:22:32 +0000] GET /phone/access_token?userid=719&token=e6556f2f567b2dcba60ae828888cb3ec&sign=1d4f98950234a01c47e1a4c9e5b5fecd&timestamp=1634829751936857&cm_uuid=3eac45fd-aa55-4e4d-af1e-11c464eab98a HTTP/1.1 "200" 66 "-" "CallMe/101021 CFNetwork/1206 Darwin/20.1.0" "-" "body=-" "userid:-" "uuid:-"  0.000 0.012  0.012 0.015
        elif "access_token" in line:
            if "?userid=" in line:
                return int(a[6].split('?userid=')[1].split('&')[0]), None, None, None
            else:
                return ERROR_CANNOT_GET_USERID, None, None, None

        # *********** - - [21/Oct/2021:15:14:10 +0000] POST /phone/incoming/sms/ HTTP/1.1 "200" 0 "-" "TwilioProxy/1.1" "-" "body=ToCountry=US&ToState=MN&SmsMessageSid=SM960618a40b6d92612f41f59ea5fcc545&NumMedia=0&ToCity=WADENA&FromZip=08045&SmsSid=SM960618a40b6d92612f41f59ea5fcc545&FromState=NJ&SmsStatus=received&FromCity=HADDON+HEIGHTS&Body=%E4%B8%8D%E6%98%AF%E5%90%A7%E8%BF%99%E4%B9%88%E6%83%A8&FromCountry=US&To=%2B12184026580&ToZip=56482&NumSegments=1&MessageSid=SM960618a40b6d92612f41f59ea5fcc545&AccountSid=**********************************&From=%2B18566727500&ApiVersion=2010-04-01" "userid:-" "uuid:-"  0.000 3.396  3.396 3.406
        elif "TwilioProxy" in line:
            if "/phone/incoming/sms/" in line or "/phone/incoming/call/" in line or "/phone/call/status/" in line \
                    or "/phone/sms/status/" in line:
                body = a[13]
                t = body.split("&")
                from_phone = ""
                to_phone = ""
                for i in t:
                    if 'To=' in i:
                        to_phone = i.split("=")[1].replace("%2B", "+").replace("%28", "(").replace("%29", ")")
                    elif 'From=' in i:
                        from_phone = i.split("=")[1].replace("%2B", "+").replace("%28", "(").replace("%29", ")")
                from_userid = get_userid_by_phone(cursor, line, from_phone)
                to_userid = get_userid_by_phone(cursor, line, to_phone)
                return int(from_userid), int(to_userid), from_phone, to_phone
            elif "POST /phone/call/ HTTP/1.1" in line:
                body = a[13]
                t = body.split("&")
                from_userid = -999
                from_phone = ""
                to_phone = ""
                for i in t:
                    if "user_id" in i:
                        from_userid = int(i.replace("user_id=", "").replace('"', ''))
                    elif 'To=' in i:
                        to_phone = i.split("=")[1].replace("%2B", "+").replace("%28", "(").replace("%29", ")")
                    elif 'From=' in i:
                        from_phone = i.split("=")[1].replace("%2B", "+").replace("%28", "(").replace("%29", ")")
                to_userid = get_userid_by_phone(cursor, line, to_phone)
                return int(from_userid), int(to_userid), from_phone, to_phone

        # 这个不是由tw过来的
        elif "/phone/sendsms/" in line:
            userid = get_userid_by_re(cursor, line)
            to_phone = get_tophone_by_re(line)
            to_userid = get_userid_by_phone(cursor, line, to_phone)
            return int(userid), int(to_userid), get_phone_by_userid(cursor, line, userid), to_phone

        else:
            userid = get_userid_by_re(cursor, line)
            if userid > 0:
                return userid, None, None, None

            print(f"ERROR: no match >>>>>")
            print(f"ERROR: no match >>>>>")
            print(f"ERROR: no match >>>>> {line} ")
            return ERROR_CANNOT_GET_USERID, None, None, None
    except Exception:
        print(line)
        traceback.print_exc()
        return ERROR_CANNOT_GET_USERID, None, None, None


def handle_for_new(userid_url_cnt_map: dict):
    print("handle_for_new...")
    new_unify_cnt = 0
    new_access_token_cnt = 0
    new_get_user_profile_cnt = 0
    new_vip_cnt = 0
    new_number_search_cnt = 0
    new_number_lock_cnt = 0
    new_order_cnt = 0
    new_check_user_cnt = 0
    new_send_sms_cnt = 0
    new_send_sms_incoming_cnt = 0
    new_call_cnt = 0
    new_call_incoming_cnt = 0
    new_call_status_cb_cnt = 0

    new_unify_user_cnt = 0
    new_access_token_user_cnt = 0
    new_get_user_profile_user_cnt = 0
    new_vip_user_cnt = 0
    new_number_search_user_cnt = 0
    new_number_lock_user_cnt = 0
    new_order_user_cnt = 0
    new_check_user_user_cnt = 0
    new_send_sms_user_cnt = 0
    new_send_sms_incoming_user_cnt = 0
    new_call_user_cnt = 0
    new_call_incoming_user_cnt = 0
    new_call_status_cb_user_cnt = 0

    otherto_user_cnt_call = 0
    otherto_cnt_call = 0

    otherto_user_cnt_sms = 0
    otherto_cnt_sms = 0

    otherfrom_user_cnt_call = 0
    otherfrom_cnt_call = 0

    otherfrom_user_cnt_sms = 0
    otherfrom_cnt_sms = 0

    otherto_call_list = []
    otherto_sms_list = []
    otherfrom_call_list = []
    otherfrom_sms_list = []

    # 只处理新用户
    for k, v in userid_url_cnt_map.items():
        if "otherto" in k:
            if "call" in k:
                otherto_call_list.append(k.split("_")[-1])
                otherto_user_cnt_call += 1
                otherto_cnt_call += v
            elif "sms" in k:
                otherto_sms_list.append(k.split("_")[-1])
                otherto_user_cnt_sms += 1
                otherto_cnt_sms += v
        if "otherfrom" in k:
            if "call" in k:
                otherfrom_call_list.append(k.split("_")[-1])
                otherfrom_user_cnt_call += 1
                otherfrom_cnt_call += v
            elif "sms" in k:
                otherfrom_sms_list.append(k.split("_")[-1])
                otherfrom_user_cnt_sms += 1
                otherfrom_cnt_sms += v
        elif "new" in k:
            if '/user/unifyLogin/' in k:
                new_unify_cnt += v
                new_unify_user_cnt += 1
            elif '/phone/access_token' in k:
                new_access_token_cnt += v
                new_access_token_user_cnt += 1
            elif '/user/getProfile/' in k:
                new_get_user_profile_cnt += v
                new_get_user_profile_user_cnt += 1
            elif '/config/vip/' in k:
                new_vip_cnt += v
                new_vip_user_cnt += 1
            elif '/number/search/' in k:
                new_number_search_cnt += v
                new_number_search_user_cnt += 1
            elif '/number/lock/' in k:
                new_number_lock_cnt += v
                new_number_lock_user_cnt += 1
            elif '/order/cert/' in k:
                new_order_cnt += v
                new_order_user_cnt += 1
            elif '/user/checkUser/' in k:
                new_check_user_cnt += v
                new_check_user_user_cnt += 1
            elif '/phone/sendsms/' in k:
                new_send_sms_cnt += v
                new_send_sms_user_cnt += 1
            elif '/phone/incoming/sms/' in k:
                new_send_sms_incoming_cnt += v
                new_send_sms_incoming_user_cnt += 1
            elif '/phone/call/status/' in k:
                new_call_status_cb_cnt += v
                new_call_status_cb_user_cnt += 1
            elif '/phone/call/' in k:
                new_call_cnt += v
                new_call_user_cnt += 1
            elif '/phone/incoming/call/' in k:
                new_call_incoming_cnt += v
                new_call_incoming_user_cnt += 1

    print(f"去重新用户数 : 登录：{new_unify_user_cnt}")
    print(f"去重新用户数 : 获取accessToken：{new_access_token_user_cnt}")
    print(f"去重新用户数 : 获取用户信息：{new_get_user_profile_user_cnt}")
    print(f"去重新用户数 : 获取电话号码列表：{new_number_search_user_cnt}")
    print(f"去重新用户数 : 锁定号码：{new_number_lock_user_cnt}")
    print(f"去重新用户数 : 获取vip列表：{new_vip_user_cnt}")
    print(f"去重新用户数 : 下单：{new_order_user_cnt}")
    print(f"去重新用户数 : 检查用户合法性：{new_check_user_user_cnt}")
    print(f"去重新用户数 : 发送短信：{new_send_sms_user_cnt}")
    print(f"去重新用户数 : 接收短信：{new_send_sms_incoming_user_cnt}")
    print(f"去重新用户数 : 打电话 : {new_call_user_cnt}")
    print(f"去重新用户数 : 接电话 : {new_call_incoming_user_cnt}")
    print(f"去重新用户数 : 电话回调 : {new_call_status_cb_user_cnt}")

    print(f"新用户访问数 : 登录：{new_unify_cnt}")
    print(f"新用户访问数 : 获取accessToken：{new_access_token_cnt}")
    print(f"新用户访问数 : 获取用户信息：{new_get_user_profile_cnt}")
    print(f"新用户访问数 : 获取电话号码列表：{new_number_search_cnt}")
    print(f"新用户访问数 : 锁定号码：：{new_number_lock_cnt}")
    print(f"新用户访问数 : 获取vip列表{new_vip_cnt}")
    print(f"新用户访问数 : 下单：{new_order_cnt}")
    print(f"新用户访问数 : 检查用户合法性：{new_check_user_cnt}")
    print(f"新用户访问数 : 发送短信：{new_send_sms_cnt}")
    print(f"新用户访问数 : 接收短信：{new_send_sms_incoming_cnt}")
    print(f"新用户访问数 : 打电话 : {new_call_cnt}")
    print(f"新用户访问数 : 接电话 : {new_call_incoming_cnt}")
    print(f"新用户访问数 : 电话回调 : {new_call_status_cb_cnt}")

    print(f"今日电话来向第三方号码个数 : {otherfrom_user_cnt_call}")
    print(f"今日电话来向第三方号码接口数 : {otherfrom_cnt_call}")
    print(f"今日电话去向第三方号码个数 : {otherto_user_cnt_call}")
    print(f"今日电话去向第三方号码接口数 : {otherto_cnt_call}")

    print(f"今日短信来向第三方号码个数 : {otherfrom_user_cnt_sms}")
    print(f"今日短信来向第三方号码接口数 : {otherfrom_cnt_sms}")
    print(f"今日短信去向第三方号码个数 : {otherto_user_cnt_sms}")
    print(f"今日短信去向第三方号码接口数 : {otherto_cnt_sms}")

    print(f"今日电话来向第三方号码 : {otherfrom_call_list}")
    print(f"今日电话去向第三方号码 : {otherto_call_list}")
    print(f"今日短信来向第三方号码 : {otherfrom_sms_list}")
    print(f"今日短信去向第三方号码 : {otherto_sms_list}")

    send_list2 = [
        f"去重新用户数 : 登录：{new_unify_user_cnt}",
        f"去重新用户数 : 获取accessToken：{new_access_token_user_cnt}",
        f"去重新用户数 : 获取用户信息：{new_get_user_profile_user_cnt}",
        f"去重新用户数 : 获取电话号码列表：{new_number_search_user_cnt}",
        f"去重新用户数 : 锁定号码：{new_number_lock_user_cnt}",
        f"去重新用户数 : 获取vip列表：{new_vip_user_cnt}",
        f"去重新用户数 : 下单：{new_order_user_cnt}",
        f"去重新用户数 : 检查用户合法性：{new_check_user_user_cnt}",
        f"去重新用户数 : 发送短信：{new_send_sms_user_cnt}",
        f"去重新用户数 : 接收短信：{new_send_sms_incoming_user_cnt}",
        f"去重新用户数 : 打电话 : {new_call_user_cnt}",
        f"去重新用户数 : 接电话 : {new_call_incoming_user_cnt}",
        f"去重新用户数 : 电话回调 : {new_call_status_cb_user_cnt}",

        f"新用户访问数 : 登录：{new_unify_cnt}",
        f"新用户访问数 : 获取accessToken：{new_access_token_cnt}",
        f"新用户访问数 : 获取用户信息：{new_get_user_profile_cnt}",
        f"新用户访问数 : 获取电话号码列表：{new_number_search_cnt}",
        f"新用户访问数 : 锁定号码：：{new_number_lock_cnt}",
        f"新用户访问数 : 获取vip列表{new_vip_cnt}",
        f"新用户访问数 : 下单：{new_order_cnt}",
        f"新用户访问数 : 检查用户合法性：{new_check_user_cnt}",
        f"新用户访问数 : 发送短信：{new_send_sms_cnt}",
        f"新用户访问数 : 接收短信：{new_send_sms_incoming_cnt}",
        f"新用户访问数 : 打电话 : {new_call_cnt}",
        f"新用户访问数 : 接电话 : {new_call_incoming_cnt}",
        f"新用户访问数 : 电话回调 : {new_call_status_cb_cnt}",

        f"今日电话来向第三方号码个数 : {otherfrom_user_cnt_call}",
        f"今日电话来向第三方号码接口数 : {otherfrom_cnt_call}",
        f"今日电话去向第三方号码个数 : {otherto_user_cnt_call}",
        f"今日电话去向第三方号码接口数 : {otherto_cnt_call}",

        f"今日短信来向第三方号码个数 : {otherfrom_user_cnt_sms}",
        f"今日短信来向第三方号码接口数 : {otherfrom_cnt_sms}",
        f"今日短信去向第三方号码个数 : {otherto_user_cnt_sms}",
        f"今日短信去向第三方号码接口数 : {otherto_cnt_sms}",

        f"今日电话来向第三方号码 : {otherfrom_call_list}",
        f"今日电话去向第三方号码 : {otherto_call_list}",
        f"今日短信来向第三方号码 : {otherfrom_sms_list}",
        f"今日短信去向第三方号码 : {otherto_sms_list}",
    ]

    send_list.extend(send_list2)
    send_mail(send_list)


def work(date_str: str):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    lines = get_today_access_logs(date_str)
    print(f"lines size: {len(lines)}")
    send_list.append(f"lines size: {len(lines)}")

    # 不指定日期就默认过滤今天的内容
    if date_str:
        dt1 = datetime.datetime.strptime(date_str, "%Y%m%d")
        _date_str = dt1.strftime("%d/%b/%Y")
    else:
        _date_str = handel_dt.strftime("%d/%b/%Y")
    send_list.append(f"过滤日期：{_date_str}")
    print(f"过滤日期：{_date_str}")
    lines = [v for v in lines if _date_str in v]
    print(f"after date filter, lines size: {len(lines)}")
    send_list.append(f"after date filter, lines size: {len(lines)}")

    userid_url_cnt_map = defaultdict(int)
    double_userid_api = ["/phone/sendsms/", "/phone/incoming/sms/", "/phone/incoming/call/", "/phone/call/status/",
                         "/phone/call/", "/phone/sms/status/"]

    valid_urls = ["/phone/", "/user/", "/number/", "/config/", "/point/", "/order/"]
    not_200_map = defaultdict(int)

    for line in lines:
        a = line.split()
        url = a[6].split("?")[0]
        status = a[8]

        # 必须在合法的url中
        for i in valid_urls:
            if i in line:
                break
        else:
            continue

        # 如果不是200，则记录下
        if status != '"200"':
            not_200_map[url] += 1
            continue

        if url in double_userid_api:
            from_userid, to_userid, from_phone, to_phone = get_userid(cursor, line)
            if not from_phone:
                continue  # raise Exception(f"from_phone not ok, line: {line}")
            if not to_phone:
                continue  # raise Exception(f"to_phone not ok, line: {line}")

            if url in ["/phone/incoming/sms/", "/phone/incoming/call/"]:
                if not from_userid or from_userid < 0:
                    userid_url = f"otherfrom_{url}_{from_phone}"
                    userid_url_cnt_map[userid_url] += 1

                if to_userid is not None and to_userid < 0:
                    print(f"url:{url}, to_userid < 0, line: {line}")
                    continue
                if not to_userid:
                    print(f"url:{url}, to_userid is None, line: {line}")
                    continue

                is_new = is_new_user(cursor, to_userid, date_str)
                if is_new == 0:
                    userid_url = f"old_{url}_{to_userid}"
                    userid_url_cnt_map[userid_url] += 1
                elif is_new == 1:
                    userid_url = f"new_{url}_{to_userid}"
                    userid_url_cnt_map[userid_url] += 1
                else:
                    userid_url = f"otherto_{url}_{to_phone}"
                    userid_url_cnt_map[userid_url] += 1
            elif url in ["/phone/call/"]:
                if not to_userid or to_userid < 0:
                    userid_url = f"otherto_{url}_{to_phone}"
                    userid_url_cnt_map[userid_url] += 1

                if from_userid is not None and from_userid < 0:
                    print(f"url:{url}, from_userid < 0, line: {line}")
                    continue
                if not from_userid:
                    print(f"url:{url}, from_userid is None, line: {line}")
                    continue

                is_new = is_new_user(cursor, from_userid, date_str)
                if is_new == 0:
                    userid_url = f"old_{url}_{from_userid}"
                    userid_url_cnt_map[userid_url] += 1
                elif is_new == 1:
                    userid_url = f"new_{url}_{from_userid}"
                    userid_url_cnt_map[userid_url] += 1
                else:
                    userid_url = f"otherfrom_{url}_{from_phone}"
                    userid_url_cnt_map[userid_url] += 1
            elif url in ["/phone/sendsms/"]:
                if not to_userid or to_userid < 0:
                    userid_url = f"otherto_{url}_{to_phone}"
                    userid_url_cnt_map[userid_url] += 1

                if from_userid is not None and from_userid < 0:
                    print(f"url:{url}, from_userid < 0, line: {line}")
                    continue
                if not from_userid:
                    print(f"url:{url}, from_userid is None, line: {line}")
                    continue

                is_new = is_new_user(cursor, from_userid, date_str)
                if is_new == 0:
                    userid_url = f"old_{url}_{from_userid}"
                    userid_url_cnt_map[userid_url] += 1
                elif is_new == 1:
                    userid_url = f"new_{url}_{from_userid}"
                    userid_url_cnt_map[userid_url] += 1
                else:
                    userid_url = f"otherfrom_{url}_{from_phone}"
                    userid_url_cnt_map[userid_url] += 1

            elif url in ["/phone/call/status/", "/phone/sms/status/"]:
                is_new = is_new_user(cursor, from_userid, date_str)
                if is_new == 0:
                    userid_url = f"old_{url}_{from_userid}"
                    userid_url_cnt_map[userid_url] += 1
                elif is_new == 1:
                    userid_url = f"new_{url}_{from_userid}"
                    userid_url_cnt_map[userid_url] += 1
                else:
                    userid_url = f"otherfrom_{url}_{from_phone}"
                    userid_url_cnt_map[userid_url] += 1

                is_new = is_new_user(cursor, to_userid, date_str)
                if is_new == 0:
                    userid_url = f"old_{url}_{to_userid}"
                    userid_url_cnt_map[userid_url] += 1
                elif is_new == 1:
                    userid_url = f"new_{url}_{to_userid}"
                    userid_url_cnt_map[userid_url] += 1
                else:
                    userid_url = f"otherto_{url}_{to_phone}"
                    userid_url_cnt_map[userid_url] += 1
        else:
            userid, _, _, _ = get_userid(cursor, line)
            if userid is not None and userid < 0:
                print(f"simple url: userid < 0, line: {line}")
            if userid == ERROR_UUID_NOT_IN_DB:
                print(f"ERROR_CANNOT_GET_USERID: {line}")
            elif userid == ERROR_PHONE_NOT_IN_DB:
                print(f"ERROR_PHONE_NOT_IN_DB: {line}")
            elif userid == ERROR_PHONE_IN_DB_BUT_NOT_IN_USE:
                print(f"ERROR_PHONE_IN_DB_BUT_NOT_IN_USE: {line}")
            elif userid == ERROR_CANNOT_GET_USERID:
                print(f"ERROR_CANNOT_GET_USERID: {line}")
            else:
                is_new = is_new_user(cursor, userid, date_str)
                if is_new == 0:
                    userid_url = f"old_{url}_{userid}"
                    userid_url_cnt_map[userid_url] += 1
                elif is_new == 1:
                    userid_url = f"new_{url}_{userid}"
                    userid_url_cnt_map[userid_url] += 1
                else:
                    raise Exception(f"userid:{userid} not in DB")

    for k, v in not_200_map.items():
        send_list.append(f"请求不是200状态的url: {k}, 次数：{v}")

    handle_for_new(userid_url_cnt_map)

    cursor.close()
    db.close()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        print(f"send mail ing... msg size: {len(send_list)}")

        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}用户漏斗 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>用户漏斗：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
        <table border="1">
         <tr>
            <th>用户漏斗</th>
        </tr>
        """
        for send_item in send_list:
            html += f"""
            <tr>
                <td>{send_item}</td>
            </tr>
            """
        html += '</table></body></html>'

        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"漏斗 {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        if len(sys.argv) == 1:
            work('')
        else:
            date_str = sys.argv[1]
            work(date_str)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"漏斗 finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
