# encoding=utf-8
import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 目标特征：注册 2 天内，并且接电话次数 - 打电话次数 > 10，差距 10 倍以上
    sql = """SELECT incoming.user_id, incoming.incoming_count as in_cnt, outgoing.outgoing.outgoing_count as out_cnt  
        FROM (  
            SELECT user_id, COUNT(*) AS incoming_count  
            FROM `Call_callrecord`  
            WHERE  direction = 'INCOMING'  
            GROUP BY user_id  
        ) AS incoming  
        JOIN (  
            SELECT user_id, COUNT(*) AS outgoing_count  
            FROM `Call_callrecord`  
            WHERE  direction = 'OUTGOING'  
            GROUP BY user_id  
        ) AS outgoing ON incoming.user_id = outgoing.user_id  
        JOIN Order_order ON incoming.user_id = Order_order.user_id  
        WHERE incoming.incoming_count - outgoing.outgoing_count >= 10  
        AND Order_order.created_at >= DATE_SUB(CURDATE(), INTERVAL 3 DAY);
    """
    cursor.execute(sql)
    res = cursor.fetchall()
    bad_users = []
    for i in res:
        # 太多电话打进来，很可能是坏人
        in_cnt = i['in_cnt']
        out_cnt = i['out_cnt']
        if in_cnt > out_cnt and in_cnt / out_cnt > 30:

            # 添加到黑名单
            user_id = i['user_id']
            sql1 = f"select * from User_blackuser where user_id={user_id}"
            cursor.execute(sql1)
            res1 = cursor.fetchall()
            if len(res1) == 0:
                print(i)
                bad_users.append(i)

                sql2 = f"select * from User_user where id={user_id}"
                print(sql2)
                cursor.execute(sql2)
                res2 = cursor.fetchall()
                if len(res2) > 0:
                    uuid = res2[0]['uuid']
                    ip = res2[0]['ip']
                    reason = f'incoming call too much than outgoing call, {in_cnt}>{out_cnt}'
                    sql3 = f"insert into User_blackuser(user_id, uuid, ip, reason, ban_type, created_at) values ({user_id}, '{uuid}', '{ip}', '{reason}', 0, now())"
                    print(sql3)
                    cursor.execute(sql3)
            else:
                print(user_id, "already black user, skip...")

        elif out_cnt > in_cnt and out_cnt / in_cnt > 10:
            print(i)
            bad_users.append(i)

    db.commit()
    cursor.close()
    db.close()

    if bad_users:
        send_mail(bad_users)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(bad_users: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}电话坏人 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>电话坏人：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html = f'<p>特征：订单创建3天内，打电话和接电话相差 10 以上，并且相差倍数 > 10</p>'
        html += """
         <table border="1">
          <tr>
             <th>怀疑是电话坏人</th>
             <th>接电话</th>
             <th>打电话</th>
         </tr>
         """
        for send_item in bad_users:
            html += f"""
             <tr>
                 <td>{send_item['user_id']}</td>
                 <td>{send_item['in_cnt']}</td>
                 <td>{send_item['out_cnt']}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"check bad call users, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"check bad call users, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
