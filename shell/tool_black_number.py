# coding=utf-8
import sys

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(number: str):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    sql = f"select * from Number_numberinventory where number='{number}'"
    print(sql)
    cursor.execute(sql)
    numbers = cursor.fetchall()
    print(numbers)

    sql = f"select * from Number_numberused where number_id={numbers[0]['id']} and status='USING'"
    print(sql)
    cursor.execute(sql)
    users = cursor.fetchall()
    print(users)

    sql = f"update Number_numberinventory set status='RELEASED' where number='{number}' and id={numbers[0]['id']}"
    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()
    print(f"res: {res}")

    sql = f"update Number_numberused set status='EXPIRE' where number_id={numbers[0]['id']} and status='USING'"
    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()
    print(f"res: {res}")

    sql = f"INSERT INTO `secphone`.`User_blackuser`(`user_id`, `created_at`, `updated_at`) VALUES ({users[0]['user_id']}, now(), now());"
    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()
    print(f"res: {res}")

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    number = str(sys.argv[1])
    uuid = str(sys.argv[2])
    work(number, uuid)
