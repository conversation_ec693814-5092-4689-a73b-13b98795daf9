import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
from pymysql import cursors
from telnyx.http_client import requests


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_sip(page_number: int, page_size: int):
    url = "https://api.telnyx.com/v2/credential_connections"

    query = {
        "page[number]": f"{page_number}",
        "page[size]": f"{page_size}",
        "filter[outbound.outbound_voice_profile_id]": "2201593832611513488",
        "sort": "connection_name"
    }

    headers = {"Authorization": "Bearer KEY0193D036537A4B8864C8C5751E010685_OmTXhITKI4JA0jBUzKRhMh"}

    response = requests.get(url, headers=headers, params=query)

    data = response.json()
    return data


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    invalid_user_ids = []
    invalid_user_details = []

    page_size = 100
    for i in range(1000):
        data = get_sip(i + 1, page_size)

        if len(data['data']) == 0:
            break

        for index2, d in enumerate(data['data']):
            if 'txtnow' not in d['user_name']:
                continue

            print(i * page_size + index2, d['id'], d['user_name'], d['registration_status'])

            user_id = int(d['user_name'].replace('txtnow', ''))

            sip = d["id"]
            sip_username = d["user_name"]
            sip_password = d["password"]

            sql2 = f"select telnyx_sip_connection_id, telnyx_sip_username, telnyx_sip_password from User_user where id={user_id}"
            cursor.execute(sql2)
            result = cursor.fetchone()
            if not result:
                print(f"########## user_id not exists??? {sql2}")
                raise Exception(f"user_id not exsits: {user_id}")

            if (result["telnyx_sip_connection_id"] != sip or result["telnyx_sip_username"] != sip_username
                    or result["telnyx_sip_password"] != sip_password):
                print("invalid sql", sql2)
                invalid_user_ids.append(user_id)
                d.update({
                    "db_telnyx_sip_connection_id": result["telnyx_sip_connection_id"],
                    "db_telnyx_sip_username": result["telnyx_sip_username"],
                    "db_telnyx_sip_password": result["telnyx_sip_password"],
                })
                invalid_user_details.append(d)

    db.commit()
    cursor.close()
    db.close()

    print("#" * 30)
    for i in invalid_user_ids:
        print(i)
    for i in invalid_user_details:
        print(i)
    print("size:", len(invalid_user_ids))

    if len(invalid_user_ids):
        send_mail(invalid_user_details)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(invalid_user_details: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}telnyx对比DB和平台SIP', 'utf-8').encode()

        txt = f"<br>len: {len(invalid_user_details)}"
        for i in invalid_user_details:
            txt += f"<br>   {i}"

        msg.attach(MIMEText(txt, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print(f"telnyx compare sip sip start time: {datetime.datetime.now(datetime.timezone.utc)}")

    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"telnyx compare sip finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
