from twilio.rest import Client

client_map = {
    1: Client("**********************************", "d703a14afd6342ce8699d3ed8607bfc2"),
    2: Client("**********************************", "7901ab6886b148460b16edc337d1343a"),
}
client = client_map[1]

number_sid_map = {}


def get_from_msg_pool(sid: str):
    phone_numbers = client.messaging \
        .v1 \
        .services(sid) \
        .phone_numbers \
        .list(limit=2000)

    for record in phone_numbers:
        number_sid_map[record.phone_number] = record.sid

    return [v.phone_number for v in phone_numbers]


if __name__ == '__main__':
    # pool = 9,2,1
    message_service_sid_list = ["MG6792341559ef97fc759ac314e633240b",
                                "MG1364926db36d3fb434dc1d9af5bb661c",
                                "MGdd082ba25c1602029b1b0a94da712f83"]

    print("pool 9...")
    pool_9_numbers = get_from_msg_pool("MG6792341559ef97fc759ac314e633240b")
    print(len(pool_9_numbers))

    print("pool 2...")
    pool_2_numbers = get_from_msg_pool("MG1364926db36d3fb434dc1d9af5bb661c")
    print(len(pool_2_numbers))

    print("pool 1...")
    pool_1_numbers = get_from_msg_pool("MGdd082ba25c1602029b1b0a94da712f83")
    print(len(pool_1_numbers))

    data = """
    +19312776607
+12267999801
+12058838670
+17864813736
+19188183771
+15108228768
+15012981134
+16614517955
+12019480512
+16158027510
+14014967880
+12513131847
+19543729680
+17603837185
+18646894031
+17653054267
+17053006719
+14045745237
+17145819705
+18189627373
+17853909080
+18647636049
+12108997720
+15624797947
+19094432223
+13372219013
+17867512446
+16193913506
+12166773392
+16057022301
+12192075522
+19033453832
+19107206914
+12109727728
+13606639940
+16282004299
+13612043518
+13476097844
+13018809176
+13474078870
+12708184218
+16414501021
+13148768171
+19376321780
+18307831880
+19206718031
+17739120715
"""
    not_send_cnt_pool_9 = 0
    not_send_cnt_pool_2 = 0
    not_send_cnt_pool_1 = 0
    for d in data.split("\n"):
        d = d.strip()
        if not d:
            continue
        if d in pool_9_numbers:
            not_send_cnt_pool_9 += 1
        elif d in pool_2_numbers:
            not_send_cnt_pool_2 += 1
        elif d in pool_1_numbers:
            not_send_cnt_pool_1 += 1

    print("not_send_cnt_pool_9:", not_send_cnt_pool_9)
    print("not_send_cnt_pool_2:", not_send_cnt_pool_2)
    print("not_send_cnt_pool_1:", not_send_cnt_pool_1)
