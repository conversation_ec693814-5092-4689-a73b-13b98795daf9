# encoding=utf-8
import datetime
import json
import smtplib
import sys
import time
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import jwt
import pymysql
import requests

"""
教程：https://cloud.tencent.com/developer/article/1939304

https://developer.apple.com/documentation/appstoreserverapi/revocationdate
https://developer.apple.com/documentation/appstoreserverapi/revocationreason
"""

import redis

# 创建 Redis 连接
my_redis = redis.Redis(host='localhost', port=6379, db=0)


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(flag: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 全量
    if flag == 0:
        now = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=-360)
        now_str = f"{now.year}-{now.month}-{now.day} 00:00:00"
        sql = f"select id, user_id, original_transaction_id from Order_order where is_refund=0 and created_at>='{now_str}';"
    else:
        # 最近一个月
        now = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=-30)
        now_str = f"{now.year}-{now.month}-{now.day} 00:00:00"
        sql = f"select id, user_id, original_transaction_id from Order_order where  is_refund=0 and created_at>='{now_str}';"

    print(sql)
    cursor.execute(sql)
    order_list = cursor.fetchall()
    size = len(order_list)
    print(f"{sql}, handle size: {size}")

    send_items = []

    for index, o in enumerate(order_list):
        order_id = o['id']
        user_id = o['user_id']
        original_transaction_id = o['original_transaction_id']
        redis_done_key = f"order_done_{order_id}_{user_id}"
        expire_seconds = 3600 * 8

        try:
            if index % 100 == 0:
                print(f"######## {index}/{size} user:{user_id}, original_transaction_id:{original_transaction_id}")

            if user_id in [1000002, 1000018]:
                my_redis.set(f'order_done_{order_id}', '1', expire_seconds)
                continue

            if not original_transaction_id:
                print(f"######## {index}/{size} user:{user_id}, original_transaction_id is null")

            if my_redis.get(redis_done_key):
                continue

            refund_rsp_list, err_msg = check_refund_order(user_id, jwt_token, original_transaction_id)
            if err_msg:
                print("### err_msg:", err_msg, "tmp pass!!!")
                continue
            if len(refund_rsp_list) == 0:
                my_redis.set(redis_done_key, '1', expire_seconds)
                continue

            send_items.extend(refund_rsp_list)
            try:
                print("#" * 100)
                sql = f"update Order_order set is_refund=1,valid=0 where id={order_id} and user_id={user_id}"
                print(sql)
                cursor.execute(sql)
                sql2 = f"insert into Order_ordersendvip(user_id, send_days, created_at, updated_at) values ({user_id}, -1000, now(), now()) ON DUPLICATE KEY UPDATE send_days = send_days -100, updated_at = NOW();"
                print(sql2)
                cursor.execute(sql2)

                for item in refund_rsp_list:
                    print("#" * 50)
                    revocation_date = datetime.datetime.fromtimestamp(item['revocationDate'] / 1000)
                    refund_type = item['type']
                    refund_product_id = item['productId']
                    refund_price = item['price']
                    refund_currency = item['currency']

                    web_order_line_item_id = item.get('webOrderLineItemId', '')
                    original_transaction_id = item['originalTransactionId']
                    transaction_id = item['transactionId']
                    original_purchase_date = datetime.datetime.fromtimestamp(item['originalPurchaseDate'] / 1000)
                    purchase_date = datetime.datetime.fromtimestamp(item['purchaseDate'] / 1000)
                    expires_date = datetime.datetime.fromtimestamp(
                        item['expiresDate'] / 1000) if 'expiresDate' in item else '1970-01-01'
                    revocation_reason = item['revocationReason']
                    transaction_reason = item['transactionReason']

                    sql3 = (
                        f"insert into Order_orderrefund(user_id, original_transaction_id, transaction_id,  "
                        f"web_order_line_item_id, revocation_date, revocation_reason,  refund_type, refund_product_id, "
                        f"refund_price, refund_currency, original_purchase_date, purchase_date, expires_date, transaction_reason, created_at, updated_at) "
                        f"values ({user_id}, '{original_transaction_id}', '{transaction_id}', "
                        f"'{web_order_line_item_id}', '{revocation_date}', '{revocation_reason}', "
                        f"'{refund_type}', '{refund_product_id}', {refund_price}, '{refund_currency}', "
                        f"'{original_purchase_date}', '{purchase_date}', '{expires_date}', '{transaction_reason}', now(), now())")
                    print(sql3)
                    cursor.execute(sql3)

                db.commit()
                print("数据插入成功，事务已提交", order_id)
                my_redis.set(redis_done_key, '1', expire_seconds)
            except Exception as e:
                db.rollback()
                print("数据插入失败，事务已回滚", order_id)
                print(f"Error: {e}")
        except Exception as e:
            traceback.print_exc()
            refund_failed_out.write(f"{user_id}\t{original_transaction_id}\n")

    db.commit()
    cursor.close()
    db.close()

    if len(send_items) > 0:
        send_mail(send_items=send_items)


def get_jwt_token_for_in_app():
    # 读取密钥文件证书内容
    f = open("/root/www/VirtualSIM_BackEnd/shell/order/AuthKey_8J425X2QDA.p8")
    key_data = f.read()
    f.close()

    # JWT Header
    header = {
        "alg": "ES256",
        "kid": "8J425X2QDA",
        "typ": "JWT"
    }

    # JWT Payload
    payload = {
        "iss": "e46a3bfd-1a50-4a96-9e65-2fbd8a742301",
        "aud": "appstoreconnect-v1",
        "iat": int(time.time()),
        "exp": int(time.time()) + 60 * 60,  # 60 minutes timestamp
        "nonce": "6edffe66-b482-11eb-8529-0242ac130003",
        "bid": "com.zehoutech.secondphone"
    }

    # JWT token
    token = jwt.encode(headers=header, payload=payload, key=key_data, algorithm="ES256")

    print("JWT Token:", token)
    if isinstance(token, bytes):
        return token.decode('utf-8')
    else:
        return token


def check_refund_order(user_id: int, jwt_token: str, original_transaction_id: str) -> (list, str):
    try:
        url = f"https://api.storekit.itunes.apple.com/inApps/v1/refund/lookup/{original_transaction_id}"
        headers = {
            'Authorization': f'Bearer {jwt_token}'
        }

        response = requests.get(url, headers=headers)
        rsp = json.loads(response.text)
        refund_list = []

        if 'signedTransactions' not in rsp:
            print("##### signedTransactions not in rsp", user_id, original_transaction_id, response.text)
            return [], 'err'

        if len(rsp['signedTransactions']) > 0:
            for i in rsp['signedTransactions']:
                refund_rsp = jwt.decode(i, options={"verify_signature": False})
                print(str(user_id), '\t', refund_rsp)
                refund_list.append(refund_rsp)
                refund_total_out.write(str(user_id) + '\t' + json.dumps(refund_rsp, ensure_ascii=True) + '\n')
        return refund_list, ''
    except Exception:
        traceback.print_exc()
        print("##### check_refund_order failed", user_id, original_transaction_id)
        return [], 'err'


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_items: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}点数退款量:{len(send_items)}', 'utf-8').encode()

        html = f'<html><body><h2>点数退款量：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
        <table border="1">
         <tr>
            <th>ERROR日志</th>
        </tr>
        """
        for send_item in send_items:
            html += f"""
            <tr>
                <td>{send_item}</td>
            </tr>
            """
        html += '</table></body></html>'

        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(datetime.datetime.now(datetime.timezone.utc))
    print("*" * 20)
    try:
        if len(sys.argv) == 1:
            flag = 0
        else:
            flag = int(sys.argv[1])
        jwt_token = get_jwt_token_for_in_app()
        time_suffix = (datetime.datetime.now()).strftime("%Y%m%d_%H%M")
        with  open(f'refund_total_{time_suffix}.txt', 'w') as refund_total_out, \
                open(f'refund_failed_{time_suffix}.txt', 'w') as refund_failed_out:
            work(flag)

    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"refund points error log finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
