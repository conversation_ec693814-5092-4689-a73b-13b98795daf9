# coding=utf-8
import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
import pytz


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(res: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(
            f'{env_config["EMAIL_PREFIX"]}OrderException.怎么会有订单过期了还在使用号码的情况呢 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>OrderException.怎么会有订单过期了还在使用号码的情况呢: {datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
        <table border="1">
         <tr>
            <th>号码情况</th>
        </tr>
        """
        for t in res:
            html += f"""
            <tr><td>{t}</td></tr>
            """

        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def work(send_limit_cnt: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    cursor.execute("""
    select t1.id as order_id, t1.expire_at, t2.user_id as user_id, t2.number,  t2.number_id, 
                   t1.created_at as order_created_at, t2.created_at as number_created_at  
                   from Order_order t1 left join Number_numberused t2 on t1.user_id =t2.user_id  
                   where t1.`order_status`<>'OPEN' AND t2.status='USING';
    """)
    res = cursor.fetchall()
    send_res = []
    if len(res) > 0:
        for obj in res:
            user_id = obj['user_id']
            number_id = obj['number_id']

            if GetDiffDays(GetNow(), obj['expire_at']) >= 3:
                sql1 = f"update Number_numberinventory set status='EXPIRE' where id={number_id} and status='USING';"
                sql2 = f"update Number_numberused set status='EXPIRE' where user_id={user_id} " \
                       f"and status='USING' and number_id={number_id};"

                print(f"update over day number: {sql1}")
                cursor.execute(sql1)
                print(f"update over day number: {sql2}")
                cursor.execute(sql2)
            else:
                send_res.append(obj)

        # 确实有要发送的情况
        if len(send_res) > send_limit_cnt:
            send_mail(send_res)

    db.commit()
    cursor.close()
    db.close()


def GetUTCDateTime(dt) -> datetime.datetime:
    if isinstance(dt, datetime.datetime):
        if dt.tzinfo != pytz.UTC:
            return dt.replace(tzinfo=pytz.UTC)
        return dt
    elif isinstance(dt, int):
        if len(str(dt)) == 10:
            return datetime.datetime.fromtimestamp(int(dt), tz=pytz.UTC)
        elif len(str(dt)) == 13:
            return datetime.datetime.fromtimestamp(int(dt) / 1000, tz=pytz.UTC)

    print(f"[Common.util.GetUTCDateTime] sth is wrong for: {dt}")
    return None


def GetDiffDays(dt1: datetime.datetime, dt2: datetime.datetime) -> int:
    dt1 = GetUTCDateTime(dt1)
    dt2 = GetUTCDateTime(dt2)
    return (dt1 - dt2).days


def GetDiffDaysWithNow(dt1: datetime.datetime) -> int:
    return GetDiffDays(dt1, GetNow())


def GetNow() -> datetime.datetime:
    dt = datetime.datetime.now(datetime.timezone.utc).replace(tzinfo=pytz.UTC)
    return dt


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    try:
        print(f"check closed order with using phone: {datetime.datetime.now(datetime.timezone.utc)}")
        send_limit_cnt = int(sys.argv[1])
        work(send_limit_cnt)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"check closed order with using phone finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
