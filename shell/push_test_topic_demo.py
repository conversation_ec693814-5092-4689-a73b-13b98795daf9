# coding=utf-8
import datetime
import json
import os
import smtplib
import sys
import time
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import firebase_admin
import pymysql
import requests
from firebase_admin import initialize_app, credentials
from firebase_admin.messaging import Message


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def test_topic_push():
    """
    测试topic推送
    - 获取指定用户的push_id
    - 订阅测试topic
    - 发送测试消息
    """
    env_config = get_env_config()

    # 初始化Firebase
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    FCM_CERT_FILEPATH = BASE_DIR + env_config["FCM_CERT_FILEPATH"]
    FCM_APP_NAME = env_config["FCM_TITLE"]

    with open(FCM_CERT_FILEPATH) as f:
        credential = credentials.Certificate(json.load(f))
    firebase_app = initialize_app(credential=credential, name=FCM_APP_NAME)

    # 连接数据库
    db = pymysql.connect(
        host=env_config["DB_HOST_KEY"],
        port=env_config["DB_PORT_KEY"],
        user=env_config["DB_USER_KEY"],
        passwd=env_config["DB_PASSWORD_KEY"],
        db=env_config["DB_DATABASE_NAME_KEY"],
        charset='utf8'
    )
    cursor = db.cursor(pymysql.cursors.DictCursor)

    try:
        # 获取指定用户的push_id
        sql = "SELECT push_id FROM User_user WHERE id=1214162"
        cursor.execute(sql)
        result = cursor.fetchone()

        if not result or not result['push_id']:
            print("未找到有效的push_id")
            return

        push_id = result['push_id']
        topic_name = 'test_topic_20250228'

        print(f"获取到push_id: {push_id}")

        # 1. 订阅topic
        try:
            response = firebase_admin.messaging.subscribe_to_topic(
                tokens=[push_id],
                topic=topic_name,
                app=firebase_app
            )
            print(f"Topic订阅结果 - 成功: {1 - response.failure_count}, 失败: {response.failure_count}")

            if response.failure_count > 0:
                print("订阅失败，退出测试")
                return

            # 等待一下确保订阅生效
            time.sleep(1)

            # 2. 发送测试消息
            message = firebase_admin.messaging.Message(
                notification=firebase_admin.messaging.Notification(
                    title='Test Notification',
                    body=f'This is a test message sent at {datetime.datetime.now()}'
                ),
                topic=topic_name
            )

            message_id = firebase_admin.messaging.send(message, app=firebase_app)
            print(f"消息发送成功，message_id: {message_id}")

            # 3. 等待几秒后取消订阅
            time.sleep(3)

            # 4. 取消订阅
            response = firebase_admin.messaging.unsubscribe_from_topic(
                tokens=[push_id],
                topic=topic_name,
                app=firebase_app
            )
            print(f"取消订阅结果 - 成功: {1 - response.failure_count}, 失败: {response.failure_count}")

        except Exception as e:
            print(f"操作失败: {str(e)}")
            traceback.print_exc()

    finally:
        cursor.close()
        db.close()


if __name__ == '__main__':
    test_topic_push()
