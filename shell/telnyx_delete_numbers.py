import requests

if __name__ == '__main__':

    number_lists = """+15858211090
+15597298033
+13158305577
+18633049948
+19704990033
+15205211867
+12296260259
+15752059129
+15155061650
+19803691346
+12363013620
+12367601155
+12177161685
+17653580874
+13309671370
+12293358032
+15107381270
+18179892528
+14782099586
+14792400192
+19402873956
+12064881330
+12029774245
+17327037145
+15023471175
+15617604449
+15648889827
+16615433826
+14174042227
+12402480054
+12098807108
+15618728625
+15108264441
+19102908621
+14248558004
+14063561973
+18454425352
+14092322608
+14708210269
+17076980113
+17072043676
+19706939616
+15512166110
+13198888187
+16062358061
+12675998743
+16172718021
+12284604179
+13463581874
+16182664003
+14704028859
+12136725622
+12135679746
+19548721253
+14028009214
+14027797125
+19713189878
+15599109852
+16062664190
+15413611138
+14053384277
+14696990846
+18676751343
+13306215088
+19153182274
+16095661405
+12702605051
+12192481747
+14587573077
+14092041418
+12093924888
+12606601373
+19187239290
+19027083155
+12172360924
+15304438826
+19283921230
+13204570127
+14706469023
+13526320056
+17076618107
+15309274484
+12269175785
+12267970376
+17257550349
+12548310107
+14809448924
+19316661300
+17089319207
+14243970711
+16062221053
+18595352070
+18047198860
+12084273961
+18573860734
+17578430049
+17073685109
+13159232557
+16062459049
+18452868725
+12172250040
+18043953134
+17078674920
+15162000961
+19807590094
+19105184160
+14064159029
+16059540657
+19866421015
+12406520707
+19288521447
+15676006051
+13367158576
+14132489183
+16036331048
+14437469259
+16282342037
+15633560002
+12137212105
+18303338322
+13526776677
+15163476124
+13088009311
+18164280165
+15803251308
+14175678320
+18052497690
+18127694768
+18052497569
+18058676971
+19808920398
+12674918201
+16124930097
+12604009638
+15029731094
+17373402198
+12099192216
+18122609567
+16505860109
+19404407174
+12705500196
+12268295797
+14127170743
+15624889177
+15013001351
+19803034172
+13349476728
+14029358491
+19713503503
+19713503479
+19713503441
+15066162973
+18305293374
+15677081039
+17406470897
+15067999509
+17752052268
+12702035812
+18026318240
+17095090563
+18704772384
+18645509041
+12629560733
+17073142604
+13206869158
+14406290123
+18302252799
+13862301872
+12899072686
+14707225278
+12722380115
+17404814147
+12678271930
+19403048281
+19183959276
+15065600937
+14793517021
+17693909943
+15069903048
+16187101105
+13255230073
+12409186873
+17163855361
+14195547226
+18284788125
+19708070257
+19844897699
+15806602358
+19844897804
+19147450234
+12675780863
+14095479109
+18452618265
+14238104485
+15065164144
+18045684077
+18157830047
+14055618435
+19844897819
+18459202244
+18452858356
+19074165974
+16813324525
+17095090634
+19187198106
+18599798382
+16313100352
+13367388523
+18045830026
+14344841317
+12722460070
+19108310142
+12709211208
+19076008414
+15854070976
+13346431194
+15065600942
+18723133175
+18312767627
+17759661221
+19108301518
+12542448193
+17542261420
+13346054659
+17273870151
+16812230900
+12094201937
+16183481522
+14403745705
+18023658175
+15067970639
+16693451010
+15489014472
+18634740525
+19122962555
+19074164712
+18438298513
+17473897721
+18482758092
+12058809101
+12067509340
+19043656780
+15802042981
+12405691570
+18583395896
+19014600889
+15312780371
+15706978050
+13302368878
+12058319629
+15734924173
+18453051298
+19152936314
+15065269975
+14092750104
+15818932001
+14092750111
+18023658043
+18436309175
+19103878203
+17622480189
+13158305144
+14053091378
+16019670828
+13808880757
+12623602311
+12623602315
+12702497609
+14053091319
+12183958522
+13083781187
+18153761103
+13097800077
+16205139240
+14706880101
+14348299492
+16816820144
+19127320322
+16203444249
+17542251780
+15738470100
+13657476274
+18597852894
+14064456044
+14064456065
+16505360557
+18582835631
+17603881881
+15626050124
+17165840118
+14707459773
+16054050609
+14842189367
+15126437068
+15127646455
+12402077949
+19025365730
+19283890564
+15857146894
+15853788596
+19032241804
+17822381069
+13126380059
+15302037534
+19143484591
+16076781143
+13369741204
+17822381091
+19143407437
+17089677174
+18633018231
+12534520130
+14062986714
+12815490457
+19103871455
+17085239745
+18154523280
+18156538693
+15012988115
+18452762320
+14238104504
+19792271370
+16827198776
+12134911586
+15078889240
+18055579106
+19253616148
+19127378496
+14709460631
+18635008974
+12097334262
+16898889401
+12706936839
+18127370182
+15312151815
+12093798078
+19025369964
+17179139437
+15739308236
+19402269398
+17752185704
+17698888733
+12078577501
+12183328627
+19096989658
+14132389582
+12364842762
+14632510390
+14708210479
+12532949139
+15306521452
+19106360113
+12087108239
+14842968974
+18305293951
+12544566028
+19257097999
+19514163950
+19514171242
+19514171325
+13213951807
+12363142785
+19734885316
+16602346036
+19177798838
+12086837127
+19408520164
+12813269703
+14053371095
+12702497562
+15025765262
+14052961828
+17206989429
+17785042685
+17655976755
+14243885174
+19863331172
+19807450927
+14846168909
+14023950576
+14257383313
+12542638052
+19077594234
+17785042693
+19187239245
+12253802631
+15648889485
+17026206322
+13862497365
+18029491197
+14407371194
+18782286911
+17033978249
+18282768643
+17079795845
+17127720184
+17206401422
+17169195048
+13657475724
+15752328727
+13233288608
+17012910275
+18148201146
+13657475727
+16209460203
+13657475702
+16292721013
+13022740152
+18046091712
+17743820476
+17409108985
+19124160058
+15878646246
+15409236853
+19563961901
+15677420932
+17204871875
+12813269312
+19789719880
+15878646639
+17312841184
+12342261957
+15153850882
+15878646097
+14107848877
+17577908447
+19842066084
+18312984212
+14792410196
+17314586983
+14436469280
+12768355366
+15045349761
+15802463587
+14075988747
+12189220378
+12055098312
+14302180112
+12816990363
+17402098413
+13464180127
+18027878287
+17067031916
+15702037747
+12055750194
+15635261728
+12725001385
+19025173003
+19297440104
+18108806996
+12099701401
+14172726317
+17193986319
+13642220345
+12202260164
+16808081174
+16802561069
+19103569844
+15024344265
+12543209118
+12704453498
+14314825532
+18658569143
+14783780007
+13392120290
+13414440072
+18583998453
+16183666351
+12342332790
+14328420070
+13204261807
+14704313396
+19253188898
+15164071861
+16098746271
+16674218345
+19104595535
+16062578173
+14245381956
+12534782241
+13043188198
+17252086940
+17375298162
+14019551017
+12097636571
+17604007112
+17609936180
+18123151112
+18632658663
+18287194841
+13313341174
+18302090726
+17252919313
+12082130601
+13022915058
+15185130222
+14127298896
+15716002592
+17043680483
+16812230676
+16052054796
+14702878311
+18146150350
+19786538776
+15713710251
+19106360425
+12093142071
+17578008043
+17128906704
+16062664362
+19513931891
+13048261092
+13615851055
+13082039790
+19713189271
+19707131864
+12098059866
+12562544488
+12562074761
+19373558797
+12723339780
+13014449516
+12726669339
+19782420235
+14302640141
+12702538753
+17345197784
+18252023125
+12187938073
+12084156927
+17325062905
+13303008663
+12396880248
+15672161064
+18082727392
+17206400120
+14322800407
+18082727579
+16508226325
+15513417157
+12792351588
+12792351120
+18434028054
+12272035230
+17152551862
+15053988270
+19166599842
+12185422579
+18252023439
+12185422624
+19732219565
+12797860304
+12172031652
+17743520780
+17472880387
+12252068916
+13177428650
+18254046043
+15312068949
+16896891445
+19282518307
+14706880516
+16156231456
+14172287301
+12702031764
+13527580464
+18027558109
+15512094520
+16067288017
+19786840140
+12396670312
+15152950321
+17722624563
+14094261342
+12134515901
+14434393351
+15404188009
+14843710589
+16592280446
+17745160104
+15597522289
+17077306133
+17073718874
+17077306148
+15598388639
+15596011840
+15597949821
+15596468170
+13368923994
+19842496927
+14706340015
+15076909188
+19102121254
+14172008439
+18023081096
+19549835999
+19087408798
+19863330240
+15406691790
+19047850230
+14199620385
+12192158241
+15718773492
+19372844981
+14172012276
+16182721018
+14133341150
+19013291329
+16107028764
+12242939508
+15075750137
+12405691651
+19452287519
+18059956153
+15619600871
+15597852826
+15622492338
+14387975433
+14436095787
+13253450687
+17083922211
+17122015577
+12672189390
+12094201970
+18073005958
+13603716737
+18079077905
+12087108220
+19094887358
+18085090110
+19084659958
+18085090189
+15759450066
+16602051373
+17162728689
+19737888771
+19062700159
+18102645798
+18102648301
+13183303595
+15392821295
+15392821366
+12184201471
+16062830786
    """

    numbers_need_to_delete = []

    for line in number_lists.split("\n"):
        line = line.strip()
        if not line:
            continue

        print(line)
        numbers_need_to_delete.append(line)

    print(numbers_need_to_delete)

    # 配置 API URL 和 Token
    url = "https://api.telnyx.com/v2/phone_numbers/jobs/delete_phone_numbers"

    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer KEY0193D036537A4B8864C8C5751E010685_OmTXhITKI4JA0jBUzKRhMh"
    }

    # 请求数据
    data = {
        "phone_numbers": numbers_need_to_delete
    }

    # 发送 POST 请求
    response = requests.post(url, headers=headers, json=data)

    # 处理响应
    if response.status_code == 200:
        print("Request successful:", response.json())
    else:
        print(f"Failed with status code {response.status_code}: {response.text}")
