# coding=utf-8
import csv
import datetime
import json
import os
import random
import smtplib
import sys
import time
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr
from hashlib import md5

import pymysql
import pymysql.cursors
import requests
import xlwt


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

# For list of language codes, please refer to `https://api.fanyi.baidu.com/doc/21`
from_lang = 'en'
to_lang = 'zh'

endpoint = 'http://api.fanyi.baidu.com'
path = '/api/trans/vip/translate'
url = endpoint + path

# 文件缓存路径
CACHE_FILE = "/root/translation_cache.json"
CACHE_EXPIRATION_DAYS = 300
CACHE_EXPIRATION_SECONDS = CACHE_EXPIRATION_DAYS * 24 * 60 * 60

""" json, cache
{
    "key1": {"value": "some_data", "timestamp": 1697718400},
    "key2": {"value": "other_data", "timestamp": 1697718500},
}
"""


# 加载缓存
def load_cache():
    if not os.path.exists(CACHE_FILE):
        return {}

    with open(CACHE_FILE, "r", encoding="utf-8") as file:
        try:
            cache = json.load(file)  # 加载缓存
        except json.JSONDecodeError:
            print("Invalid cache file format.")
            return {}

    # 当前时间戳
    current_time = time.time()

    # 过滤过期的缓存
    return {
        k: v
        for k, v in cache.items()
        if isinstance(v, dict) and "timestamp" in v and current_time - v["timestamp"] <= CACHE_EXPIRATION_SECONDS
    }


# 保存缓存
def save_cache(cache):
    with open(CACHE_FILE, "w", encoding="utf-8") as f:
        json.dump(cache, f, ensure_ascii=False, indent=4)


# Generate salt and sign
def make_md5(s, encoding='utf-8'):
    return md5(s.encode(encoding)).hexdigest()


def tran(query: str, session: requests.Session, cache: dict):
    """
    返回值：
    {
        "from": "zh",
        "to": "en",
        "trans_result": [
            {
                "src": "刘德华",
                "dst": "Lau Andy"
            }
        ]
    }
    """
    try:
        if not query:
            print(f"[baidu_tran_util] query is invalid")
            return query

        # 如果缓存命中，直接返回结果
        if query in cache:
            print(f"Cache hit for query: {query}")
            return cache[query]["value"]

        if session is None:
            session = requests.Session()  # 如果没有提供 session，则创建一个新的
        try:
            query = query.replace("\n", " ").replace("\r", " ")

            appid = '20230330001620603'
            appkey = 'mylvxuPuX0ThXe4Nl0Mg'

            salt = random.randint(32768, 65536)
            sign = make_md5(appid + query + str(salt) + appkey)

            # Build request
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            payload = {'appid': appid, 'q': query, 'from': from_lang, 'to': to_lang, 'salt': salt, 'sign': sign}

            # 使用 session 发送请求
            response = session.post(url, params=payload, headers=headers, timeout=10)
            result = response.json()

            if 'trans_result' in result and len(result['trans_result']) > 0:
                return result['trans_result'][0]['dst']
            return ""
        except Exception:
            traceback.print_exc()
            return ""
        finally:
            if session is not None and not hasattr(session, '_tran_session_external'):  # 避免在外部创建的 session 被意外关闭
                # 这里我们不关闭 session，因为它是在外部创建的
                pass
    except Exception:
        traceback.print_exc()
        return query


def batch_tran(queries: list, session: requests.Session) -> dict:
    """
    批量翻译函数
    """
    try:
        queries = [v.replace('\n', ' ') for v in queries]

        appid = '20230330001620603'
        appkey = 'mylvxuPuX0ThXe4Nl0Mg'
        salt = random.randint(32768, 65536)
        sign = make_md5(appid + '\n'.join(queries) + str(salt) + appkey)

        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        payload = {
            'appid': appid,
            'q': "\n".join(queries),
            'from': from_lang,
            'to': to_lang,
            'salt': salt,
            'sign': sign
        }

        response = session.post(url, params=payload, headers=headers, timeout=10)
        result = response.json()
        if "error_code" in result:
            print("invalid req", result)
            return {}

        dic = {}
        if 'trans_result' in result:
            for r in result["trans_result"]:
                key = r['src']
                value = r['dst']
                dic[key] = value
        else:
            print("invalid trans_result in result")
            print(response)

        return dic
    except Exception:
        traceback.print_exc()
        return {}


def work():
    # 加载缓存
    cache = load_cache()

    # 获取短信
    sms = get_sms_list()

    # session
    session = requests.Session()

    # 获取所有待翻译的文本
    texts_to_translate = []

    # 按批次处理翻译
    batch_size = 50  # 百度 API 限制一次最多翻译 50 个
    translated_res = {}
    for row in sms:
        q = row["content"]
        q = q.replace('\n', ' ')
        v = cache.get(q)
        if v and v['value']:
            translated_res[q] = v['value']
        else:
            texts_to_translate.append(q)

    print("total need to handle size:", len(sms))
    print("total need to tran size:", len(texts_to_translate))
    print("total already tran size:", len(translated_res))

    for i in range(0, len(texts_to_translate), batch_size):
        print("tran batch:", i, "batch size:", batch_size, "...")
        batch = texts_to_translate[i:i + batch_size]
        translated_batch_dic = batch_tran(batch, session)
        translated_res.update(translated_batch_dic)

        for k, v in translated_batch_dic.items():
            cache[k] = {"value": v, "timestamp": time.time()}

        save_cache(cache)

    # 翻译并生成 CSV 文件
    yesterday = (datetime.datetime.today() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d")
    csv_file = f"sms_report_{env_config['EMAIL_PREFIX']}_{yesterday.replace('-', '')}.csv"
    excel_file = f"sms_report_{env_config['EMAIL_PREFIX']}_{yesterday.replace('-', '')}.xls"

    try:
        with open(csv_file, "w", newline='', encoding="utf-8") as file:
            writer = csv.writer(file)
            writer.writerow(
                ["Index", "UserId", "From", "To", "Content (EN)", "Content (CN)", "Filtered Content"])  # CSV 头部

            for index, row in enumerate(sms):
                if index % 100 == 0:
                    print(f"{index}/{len(sms)}...")
                writer.writerow([index, row["user_id"], row["from_number"], row["to_number"],
                                 row["content"], translated_res.get(row["content"].replace('\n', ' '), ""),
                                 row["filtered_content"]])

            print("tran done!")
    finally:
        # 保存缓存
        save_cache(cache)
        print("save cache done")

        # 关闭连接池
        session.close()

    # 通过 email 发送 CSV 文件
    convert_csv_to_xls_with_wrap(csv_file, excel_file)
    print("tran to xls done")

    send_mail_with_csv(excel_file)
    print("send email done")

    # 删除 CSV 文件
    os.remove(csv_file)
    os.remove(excel_file)


def get_sms_list():
    # 从数据库加载数据
    db = pymysql.connect(
        host=env_config["DB_HOST_KEY"],
        port=env_config["DB_PORT_KEY"],
        user=env_config["DB_USER_KEY"],
        passwd=env_config["DB_PASSWORD_KEY"],
        db=env_config["DB_DATABASE_NAME_KEY"],
        charset='utf8'
    )
    cursor = db.cursor(pymysql.cursors.DictCursor)
    today = datetime.datetime.today().strftime("%Y-%m-%d")
    yesterday = (datetime.datetime.today() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d")
    sql = f"""
        SELECT user_id, from_number, to_number, content, filtered_content 
        FROM Call_smsrecord 
        WHERE created_at >= '{yesterday}' 
          AND created_at < '{today}' 
          AND is_it = 0 
          AND direction = 'SEND' 
          AND status <> 'send_fake' 
          AND images = '';
    """
    print(sql)
    cursor.execute(sql)
    sms = cursor.fetchall()
    print(f"sms size: {len(sms)}")
    db.commit()
    cursor.close()
    db.close()  # 立即关闭数据库连接
    return sms


def convert_csv_to_xls_with_wrap(csv_file, xls_file):
    # 创建工作簿和表
    workbook = xlwt.Workbook()
    sheet = workbook.add_sheet("Sheet1")

    # 打开 CSV 文件并读取数据
    with open(csv_file, "r", encoding="utf-8") as file:
        reader = csv.reader(file)
        data = list(reader)

    # 定义单元格样式
    style = xlwt.XFStyle()

    # 设置对齐方式
    alignment = xlwt.Alignment()
    alignment.wrap = 1  # 设置内容自动换行
    style.alignment = alignment

    # 设置边框
    borders = xlwt.Borders()
    borders.left = xlwt.Borders.THIN
    borders.right = xlwt.Borders.THIN
    borders.top = xlwt.Borders.THIN
    borders.bottom = xlwt.Borders.THIN
    style.borders = borders

    # 写入数据并设置样式
    for row_num, row in enumerate(data):
        for col_num, cell in enumerate(row):
            sheet.write(row_num, col_num, cell, style)

    # 自动调整列宽，但不过度放大
    for col_num in range(len(data[0])):
        max_width = min(max(len(str(row[col_num])) for row in data), 50)  # 限制最多 50 个字符
        sheet.col(col_num).width = 256 * (max_width + 2)  # 宽度调整

    # 保存 XLS 文件
    workbook.save(xls_file)


def send_mail_with_csv(csv_file_path):
    try:
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>', ]

        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr[0])
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}今天的短信求过目', 'utf-8').encode()

        html = f'<html><body><h2>今天的发送短信情况：{datetime.datetime.now(datetime.timezone.utc)}</h2></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        if csv_file_path:
            # 附加 CSV 文件
            try:
                with open(csv_file_path, "rb") as f:
                    attachment = MIMEText(f.read(), "base64", "utf-8")
                    attachment["Content-Type"] = "application/octet-stream"
                    attachment["Content-Disposition"] = f'attachment; filename="{csv_file_path}"'
                    msg.attach(attachment)
            except Exception:
                traceback.print_exc()
        else:
            print("failed: csv_file_path is nil")

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"{env_config['MAIL_PREFIX']}{__file__}邮件异常")


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"check sms, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"{env_config['MAIL_PREFIX']}{__file__}脚本异常")
        send_mail_with_csv("")
    finally:
        print(f"check sms, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
