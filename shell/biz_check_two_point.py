# coding=utf-8
import datetime
import smtplib
import sys
import traceback
from collections import defaultdict
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def is_user_vip(cursor, user_id: int) -> bool:
    cursor.execute(
        f"select * from User_user where DATEDIFF(expired_at, created_at) >= 10 and id={user_id};")
    res = cursor.fetchall()
    if len(res) > 0:
        return True
    return False


def get_user_callrecord(cursor, user_id: int) -> list:
    cursor.execute(
        f"select * from Call_callrecord where user_id={user_id};")
    return cursor.fetchall()


def get_user_smsrecord(cursor, user_id: int) -> list:
    cursor.execute(
        f"select * from Call_smsrecord where user_id={user_id};")
    return cursor.fetchall()


def get_user_pointrecords(cursor, user_id: int) -> list:
    cursor.execute(
        f"select * from Point_pointrecord where user_id={user_id};")
    return cursor.fetchall()


def work(send_limit_cnt: int, point_limit_cnt: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    cursor.execute("select * from Point_point where point != predict_point;")
    user_point_list = cursor.fetchall()
    print(f"user_point_list size: {len(user_point_list)}")

    cursor.execute(
        """
        select user_id, sum(point) as sum_point from 
        ( select * from Point_pointrecord 
            where user_id in (select user_id from Point_point where point != predict_point) and event != 'CHARGE'
        ) t1 
        group by user_id;
        """
    )
    user_record_list = cursor.fetchall()
    print(f"user_record_list size: {len(user_record_list)}")

    user_record_map = defaultdict(int)
    for user_record in user_record_list:
        user_record_map[user_record['user_id']] = user_record['sum_point']

    send_list = []
    diff_point_list = []
    for user_point in user_point_list:
        print(f"check user:{user_point}")
        user_id = user_point['user_id']

        # 白名单忽略
        if user_id in (1, 2, 6, 36, 37, 41, 397, 784):
            continue

        begin_point = 20
        if is_user_vip(cursor, user_id):
            begin_point = 50

        # 如果有充值，则要带上这个充值
        point_records = get_user_pointrecords(cursor, user_id)
        consumer_points = [v['point'] for v in point_records if v['event'] != 'CHARGE']
        charge_points = [v['point'] for v in point_records if v['event'] == 'CHARGE']
        if len(charge_points) > 0:
            charge_cnt = sum(charge_points)
            begin_point += charge_cnt

        calls = get_user_callrecord(cursor, user_id)
        completed_calls = [v for v in calls if v['status'] == 'completed']
        sms = get_user_smsrecord(cursor, user_id)

        calls_point = sum([v['point'] for v in calls if v['point']])
        sms_point = sum([v['point'] for v in sms if v['point']])

        # 差距的点数
        diff_point_list.append(abs(user_point['point'] - user_point['predict_point']))

        msg = f"用户: {user_id}, 起始点数: {begin_point}, 当前点数: {user_point['point']}, " \
              f"当前预测点数: {user_point['predict_point']}, 打电话消耗点数: {calls_point}, 发短信消耗点数: {sms_point}, " \
              f"总共消费点数: {user_record_map[user_id]}, 总共消费了多少笔:{len(consumer_points)}, " \
              f"打了几个电话: {len(calls)}, 打了几个completed的电话: {len(completed_calls)}, 发了几个短信: {len(sms)}"
        print(msg)
        send_list.append(msg)

    db.commit()
    cursor.close()
    db.close()

    # 看情况发送邮件
    if send_list:
        for send_item in send_list:
            print(send_item)

        # 如果很多人都对不上点数，则马上报警
        if len(send_list) >= send_limit_cnt:
            print(f"很多人:{send_limit_cnt} 都对不上点数，马上报警!")
            send_mail(send_list)
            return

        # 如果有人diff点数超过阈值，则马上报警
        if max(diff_point_list) >= point_limit_cnt:
            print(f"有人diff点数超过 {point_limit_cnt}，马上报警!")
            send_mail(send_list)
            return

        print("暂时达不到报警阈值！")


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}两个点数对不上异常报警 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>两个点数对不上异常报警：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
         <table border="1">
          <tr>
             <th>两个点数对不上</th>
         </tr>
         """
        for index, send_item in enumerate(send_list):
            html += f"""
             <tr>
                 <td>{index}: {send_item}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"check two point, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        # 2022年4月20日01:11:08 放弃predict_point
        # send_limit_cnt = int(sys.argv[1])
        # point_limit_cnt = int(sys.argv[2])
        # work(send_limit_cnt=send_limit_cnt, point_limit_cnt=point_limit_cnt)
        pass
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"check two point, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
