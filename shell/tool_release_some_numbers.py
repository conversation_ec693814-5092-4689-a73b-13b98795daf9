import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def set_status_need_to_release_numbers(cursor, ):
    sql = f"select * from Number_numberinventory where status='EXPIRE';"
    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()

    need_release_cnt = 0
    need_release_number_ids = []
    for i in res:
        number_id = i['id']
        number = i['number']
        print(number_id, number)
        sql2 = f"select * from Number_numberused where number_id={number_id}"
        cursor.execute(sql2)
        res2 = cursor.fetchall()

        for j in res2:
            if j['status'] != 'EXPIRE':
                print("######### not EXPIRE")
                raise Exception("numberused status not EXPIRE")

        if len(res2) > 7:
            print("######## > 7")
            need_release_cnt += 1
            need_release_number_ids.append(number_id)

    print("total:", len(res))
    print("need_release_cnt:", need_release_cnt)

    for index, i in enumerate(need_release_number_ids[:20]):
        print(index, "release:", i)
        sql3 = f"update Number_numberinventory set status='RELEASED', release_at=now() where id={i} "
        cursor.execute(sql3)


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    set_status_need_to_release_numbers(cursor)

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    work()
