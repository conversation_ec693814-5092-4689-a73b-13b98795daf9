# encoding=utf-8

import datetime
import json
import queue
import smtplib
import sys
import threading
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
import requests

send_list = []
todo_queue = queue.Queue()

s = requests.Session()


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def do_batch_users(worker_name: str):
    try:
        index = 0
        while not todo_queue.empty():
            user_db = todo_queue.get()
            index += 1
            userid = user_db['id']
            if index % 100 == 0:
                print(f"{worker_name}: check index: {index}, user:{userid} ")
            r = s.get(f"http://127.0.0.1:8000/order/check/?userid={userid}&token=fatpo-hello")
            content = r.content.decode('utf-8')

            ret = json.loads(content)
            if ret['err_code'] == -1:
                print(ret)
                continue

            if ret['data']['refresh'] == 0:
                continue

            send_list.append(ret['data'])
    except Exception:
        traceback.print_exc()
        send_mail([])


def work(worker_cnt):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 白名单用户
    white_user_list = [1000018, 1000002, 1013552, 1086033, 1086032, 1086155]
    white_user_list_str = ','.join([str(v) for v in white_user_list])

    # 只检查订阅的用户
    cmd = "select t1.* from User_user t1 left join Order_order t2 on t1.id=t2.user_id " \
          f"where t1.expired_at is not null and t2.valid=1 and t2.order_status='OPEN' and t2.expiration_intent not in (6) and t1.id not in ({white_user_list_str}); "
    print(cmd)
    cursor.execute(cmd)
    todo_list = cursor.fetchall()
    todo_size = len(todo_list)
    print(f"handle size: {todo_size}")

    db.commit()
    cursor.close()
    db.close()

    print("start produce ... ")
    threads = []
    for i in todo_list:
        todo_queue.put(i)

    print("start consume ... ")
    for i in range(worker_cnt):
        worker_name = "worker" + str(i)
        t = threading.Thread(target=do_batch_users, args=(worker_name,))
        threads.append(t)
        t.start()

    for i in threads:
        i.join()

    if len(send_list) > 0:
        open_2_open_cnt = 0
        open_2_closed_cnt_normal = 0
        open_2_closed_cnt_refund = 0
        closed_2_open_cnt = 0
        closed_2_closed_cnt = 0
        for i in send_list:
            if i['before_order_status'] == 'CLOSED' and i['order_status'] == 'OPEN':
                closed_2_open_cnt += 1
            if i['before_order_status'] == 'OPEN' and i['order_status'] == 'OPEN':
                open_2_open_cnt += 1
            elif i['before_order_status'] == 'CLOSED' and i['order_status'] == 'CLOSED':
                closed_2_closed_cnt += 1
            elif i['before_order_status'] == 'OPEN' and i['order_status'] == 'CLOSED':
                if i['before_expired_at'] == i['expired_at']:
                    open_2_closed_cnt_normal += 1
                else:
                    open_2_closed_cnt_refund += 1

        send_list.append(f"订单变化：{len(send_list)}个")
        send_list.append(f"之前关闭，现在打开：{closed_2_open_cnt}个")
        send_list.append(f"之前打开，现在打开：{open_2_open_cnt}个")
        send_list.append(f"之前打开，现在正常关闭：{open_2_closed_cnt_normal}个")
        send_list.append(f"之前打开，现在退款关闭：{open_2_closed_cnt_refund}个")
        send_list.append(f"之前关闭，现在关闭：{closed_2_closed_cnt}个")

    if len(send_list) > 30:
        send_mail(send_list)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}OrderException.用户退款警告 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>OrderException.用户退款警告：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
         <table border="1">
          <tr>
             <th>订单变化用户</th>
         </tr>
         """
        for send_item in send_list:
            html += f"""
             <tr>
                 <td>{send_item}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"refund user, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        worker_cnt = int(sys.argv[1])
        work(worker_cnt)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"refund user finished, time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
