import datetime
import smtplib
import sys
import traceback
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
import requests
from elasticsearch import Elasticsearch, helpers


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


class SyncSmsToEs:

    # data: datetime, use for date range and index name
    def __init__(self, date=None):
        self.es_url = ['http://*********:9200']
        self.es = Elasticsearch(self.es_url)
        self.date = date if date is not None else datetime.datetime.today()
        self.db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"],
                                  user=env_config["DB_USER_KEY"],
                                  passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"],
                                  charset='utf8')
        self.cursor = self.db.cursor()
        self.today_sms_sql = '''
            select sid, user_id, direction, from_number, to_number, content, created_at, latest_ts
            from Call_smsrecord
            where created_at > '%s' and created_at < '%s'
            '''

    def bulk_insert(self):
        today_str = self.date.strftime('%Y-%m-%d')
        tmr_str = (self.date + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        today_sms_sql = self.today_sms_sql % (today_str, tmr_str)
        print(today_sms_sql)
        self.cursor.execute(today_sms_sql)

        data = ({
            "_index": today_str,
            "_type": "doc",
            "_source": {
                "sid": row[0],
                "user_id": row[1],
                "direction": row[2],
                "from_number": row[3],
                "to_number": row[4],
                "content": row[5],
                "created_at": row[6],
                "latest_ts": row[7],
                "length": len(row[5].split(" ")),
            }
        } for row in self.cursor.fetchall())

        self.delete_index(today_str)

        helpers.bulk(self.es, data)

    def delete_30d_age(self):

        day = (datetime.datetime.today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d')
        self.delete_index(day)

    def delete_index(self, index_name: str):

        try:
            r = requests.delete(self.es_url[0] + '/%s' % index_name)
        except Exception as e:
            print(str(e))
            pass


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(err_msg: str):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>', '<EMAIL>', ]

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}同步es失败 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>同步es失败：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += f"{err_msg}"
        html += '</body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"SyncSmsToEs, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        sync = SyncSmsToEs(datetime.datetime.today() - datetime.timedelta(days=1))
        sync.delete_30d_age()
        sync.bulk_insert()
    except Exception as ex:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail(traceback.format_exc())
    finally:
        print(f"SyncSmsToEs, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
