# coding=utf-8
import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(alert_cnt: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 号码池 using
    cursor.execute("select * from Number_numberinventory where status = 'USING';")
    number_inventory_using_list = cursor.fetchall()
    print(f"number_inventory_list USING size: {len(number_inventory_using_list)}")

    # 号码池 expired
    cursor.execute("select * from Number_numberinventory where status = 'EXPIRE';")
    number_inventory_expire_list = cursor.fetchall()
    print(f"number_inventory_list EXPIRE size: {len(number_inventory_expire_list)}")

    # 用户using号码
    cursor.execute("select * from Number_numberused where status = 'USING';")
    number_used_using_list = cursor.fetchall()
    print(f"number_used_list USING size: {len(number_used_using_list)}")

    # 虽然using但已经expired的号码
    cursor.execute(
        "select * from Number_numberused where status = 'USING' and expired_at <  DATE_SUB(CURDATE(), INTERVAL 1 DAY);")
    number_used_using_but_expired_list = cursor.fetchall()
    print(f"number_used_using_but_expired_list size: {len(number_used_using_but_expired_list)}")

    number_id_pool_used = [v['id'] for v in number_inventory_using_list]
    number_id_pool_expire = [v['id'] for v in number_inventory_expire_list]
    number_id_user_used = [v['number_id'] for v in number_used_using_list]
    number_used_using_but_expired = [v['number_id'] for v in number_used_using_but_expired_list]

    # 号码池显示using但映射关系查不到
    ans1 = [v for v in number_id_pool_used if v not in number_id_user_used]

    # 映射关系显示using但号码池查不到
    ans2 = [v for v in number_id_user_used if v not in number_id_pool_used]

    print(f"in inventory status = USING but not in number_used: {ans1}")
    print(f"in number_used status = USING but not in inventory: {ans2}")
    print(f"number_used_using_but_expired: {number_used_using_but_expired}")

    cursor.execute(
        "select count(*) as cnt from Number_numberinventory where status NOT in ('RELEASED', 'TW_HAND_DEL_DONE');")
    ans3 = cursor.fetchone()
    number_inventory_total_len = ans3['cnt']

    print(f"number_inventory_list TOTAL size: {number_inventory_total_len}")

    db.commit()
    cursor.close()
    db.close()

    # 看情况发送邮件
    if (number_inventory_total_len >= alert_cnt or len(ans1) > 0 or len(ans2) > 0
            or len(number_used_using_but_expired) > 0):
        send_mail(number_id_pool_used, number_id_pool_expire, number_id_user_used, ans1, ans2,
                  number_inventory_total_len, number_used_using_but_expired)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(number_id_pool_used: list, number_id_pool_expire: list, number_id_user_used: list, ans1: list, ans2: list,
              number_inventory_total_len: int, number_used_using_but_expired: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}统计号码使用情况 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>统计号码使用情况：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        if ans1:
            html += "<p>在号码池子中显示USING状态，但实际上没人用的号码：" + str(ans1) + "</p>"
        if ans2:
            html += "<p>在用户号码映射中显示USING状态，但实际上池子中不是USING的号码：" + str(ans2) + "</p>"
        if number_used_using_but_expired:
            html += "<p>在用户号码映射中显示USING状态，但实际上已经过期的号码：" + \
                    str(number_used_using_but_expired) + "</p>"

        if alert_cnt <= number_inventory_total_len:
            html += f"<p>号码总的使用超过了告警阈值{alert_cnt}个，共{number_inventory_total_len}个！</p>"
        else:
            html += f"<p>号码总的使用暂未超过了告警阈值{alert_cnt}个，只有{number_inventory_total_len}个！</p>"

        html += f"<p>其中池子中在使用的，有{len(number_id_pool_used)}个！</p>"
        html += f"<p>其中池子中在待命的，有{len(number_id_pool_expire)}个！</p>"
        html += f"<p>用户使用，有：{len(number_id_user_used)}个！</p>"

        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"check number status, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        alert_cnt = int(sys.argv[1])
        work(alert_cnt)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail([], [], [], [], [], -1)
    finally:
        print(f"check number status, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
