# encoding=utf-8
import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 检查CLOSED的订单
    sql = "select t1.id from (select id from User_user where uuid in (select uuid from User_user  group by uuid having count(uuid) > 1))t1 left join Order_order t2 on t1.id=t2.user_id where t2.expire_at>='2023-12-11'"
    cursor.execute(sql)
    res = cursor.fetchall()
    all_cnt = len(res)

    activated_cnt = 0
    non_activated_cnt = 0

    for i in res:
        user_id = i['id']
        sql2 = f"select count(*) as cnt from Call_smsrecord where created_at >= '2023-11-01' and user_id = {user_id}"
        sql3 = f"select count(*) as cnt from Call_callrecord where created_at >= '2023-11-01' and user_id = {user_id}"

        cursor.execute(sql2)
        res2 = cursor.fetchall()

        cursor.execute(sql3)
        res3 = cursor.fetchall()

        if res2[0]['cnt'] > 0 or res3[0]['cnt'] > 0:
            activated_cnt += 1
        else:
            non_activated_cnt += 1

    print("all_cnt:", all_cnt)
    print("activated_cnt:", activated_cnt)
    print("non_activated_cnt:", non_activated_cnt)

    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    work()
