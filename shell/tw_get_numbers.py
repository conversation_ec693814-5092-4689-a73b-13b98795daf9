from twilio.rest import Client

client_map = {
    1: Client("**********************************", "d703a14afd6342ce8699d3ed8607bfc2"),
    2: Client("**********************************", "7901ab6886b148460b16edc337d1343a"),
}


def get_tw_numbers(tw_version) -> list:
    tw_numbers = []

    for t in client_map[tw_version].incoming_phone_numbers.list(limit=10):
        tw_numbers.append(t.phone_number)
    print("size:", len(tw_numbers))
    return tw_numbers


if __name__ == '__main__':
    print("tw version 1...")
    tw_numbers_1 = get_tw_numbers(1)
    print(len(tw_numbers_1))
    #
    # print("tw version 2...")
    # tw_numbers_2 = get_tw_numbers(2)
    # print(len(tw_numbers_2))
