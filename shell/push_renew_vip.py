# coding=utf-8
import datetime
import json
import os
import random
import smtplib
import sys
import time
import traceback
from email.header import Header
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import firebase_admin
import firebase_admin.messaging
import pymysql
from firebase_admin import _messaging_utils
from firebase_admin import initialize_app, credentials
from firebase_admin._messaging_utils import APNSPayload, Aps


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

FCM_APP_NAME = env_config["FCM_TITLE"]
FCM_RENEW_VIP_TITLE = env_config["FCM_TITLE"]
FCM_RENEW_VIP_CONTENT = "Your subscription is about to expire and renewal fees may apply. Please manage your subscription promptly."

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
FCM_CERT_FILEPATH = BASE_DIR + env_config["FCM_CERT_FILEPATH"]
print(FCM_CERT_FILEPATH)
with open(FCM_CERT_FILEPATH) as f:
    credential = credentials.Certificate(json.load(f))

FIREBASE_APP = initialize_app(credential=credential, name=FCM_APP_NAME)


def fcm_push(push_ids):
    if not push_ids:
        return

    # 去重
    push_ids = list(set(push_ids))

    # 将 push_ids 分成多个子列表，每个子列表最多包含 100 个推送目标
    push_ids = [v for v in push_ids if v and len(v) > 0]
    push_id_chunks = [push_ids[i:i + 100] for i in range(0, len(push_ids), 100)]

    # 构造多个 Message 对象，每个 Message 对象包含一个子列表中的推送目标
    fcm_app = firebase_admin.get_app(FCM_APP_NAME)
    for index, push_id_chunk in enumerate(push_id_chunks):
        print(f"fcm push batch: {index}, size:{len(push_id_chunk)}")
        m = firebase_admin.messaging.MulticastMessage(
            notification=firebase_admin.messaging.Notification(
                title=FCM_RENEW_VIP_TITLE,
                body=FCM_RENEW_VIP_CONTENT,
                image=None,
            ),
            apns=_messaging_utils.APNSConfig(payload=APNSPayload(aps=Aps(badge=0))),
            tokens=push_id_chunk,
        )
        response = firebase_admin.messaging.send_multicast(m, app=fcm_app)
        print(response)


def get_days_future_date_str(days_future: int) -> str:
    import datetime

    # 获取当前日期
    current_date = datetime.datetime.now().date()

    days_future = current_date + datetime.timedelta(days=days_future)

    # 将日期转换为字符串格式
    days_future_str = days_future.strftime('%Y-%m-%d')

    print(days_future_str)  # 输出：2022-10-01
    return days_future_str


def work(days: int):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    start_date = get_days_future_date_str(days)
    end_date = get_days_future_date_str(days + 1)

    sql = f"select t1.user_id, t2.push_id, t2.created_at, t1.created_at, t1.expire_at " \
          f"from Order_order t1 left join User_user t2 on t1.user_id=t2.id " \
          f"where (t2.push_id is not null and t2.push_id <> '' and  t1.valid=1 " \
          f"and t1.expire_at >= '{start_date}' and t1.expire_at < '{end_date}') or t2.id in (1013552, 1000002, 1000018)"

    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()
    print(f"size: {len(res)}")

    # add customer service sms
    for r in res:
        user_id = r['user_id']
        number_sql = f"select * from Number_numberused where user_id={user_id} order by id desc limit 1"
        cursor.execute(number_sql)
        number_obj = cursor.fetchone()
        if not number_obj:
            to_number = f"+999{user_id}"
        else:
            to_number = number_obj['number']

        from_number = '+1000009999'
        content_list = [FCM_RENEW_VIP_CONTENT,
                        "You can manage your subscription by following the link below:",
                        "https://phone2.zehougroup.xyz/how-to-check-subscription.html"]
        price = 0
        point = 0
        latest_ts = time.time() * 1000

        for index, content in enumerate(content_list):
            sid = f"{user_id}_{time.time() * 1000}_{random.randint(10000, 99999)}"
            _latest_ts = latest_ts + index
            insert_sql = "INSERT INTO Call_smsrecord (sid, user_id, latest_ts, ip, direction, from_number, to_number, " \
                         "status, err_code, price, point, content, filtered_content, images, " \
                         "created_at, updated_at) " \
                         f"VALUES ('{sid}', {user_id}, {_latest_ts}, '', 'RECIEVE', '{from_number}', '{to_number}', " \
                         f"'received', null, {price}, {point}, '{content}',null, null, now(), now());"
            cursor.execute(insert_sql)

    # push
    push_ids = [v['push_id'] for v in res]
    print("push size:", len(push_ids))
    fcm_push(push_ids)

    db.commit()
    cursor.close()
    db.close()

    if len(res) > 0:
        send_mail(res)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>', '<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}推送RENEW订阅警告', 'utf-8').encode()

        html = f'<html><body><h2>推送RENEW订阅警告：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += f"""
         <table border="1">
          <tr>
             <th>index</th>
             <th>订阅者</th>
             <th>订单创建日期</th>
             <th>订单过期日期</th>
         </tr>
         """
        for index, send_item in enumerate(send_list):
            html += f"""
             <tr>
                 <td>{index}</td>
                 <td>{send_item["user_id"]}</td>
                 <td>{send_item["created_at"]}</td>
                 <td>{send_item["expire_at"]}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"renew vip, start time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        if len(sys.argv) == 1:
            days = 1
        else:
            days = int(sys.argv[1])
        print(f"days: {days}")
        work(days)
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail([])
    finally:
        print(f"renew vip, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
