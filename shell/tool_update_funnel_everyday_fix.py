# coding=utf-8
import datetime
import os
import sys
from collections import defaultdict

import environ
import pymysql

access_log = '/var/log/nginx/access.log'


def get_env_config():
    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def save_log_to_db(cnx, cursor):
    # 全部.gz 文件解压
    # find /var/log/nginx -name "*.gz" -exec gzip -d {} \;

    # 删除表
    drop_table_sql = "drop table if exists my_tmp_log"
    print("drop_table_sql:", drop_table_sql)
    cursor.execute(drop_table_sql)
    cnx.commit()

    # 创建表
    create_table_sql = """
        CREATE TABLE IF NOT EXISTS `my_tmp_log` (
          `ds` int DEFAULT NULL,
          `ds_str` varchar(64) DEFAULT NULL,
          `uuid` varchar(64) DEFAULT NULL,
          `url` varchar(64) DEFAULT NULL,
          KEY `idx_uuid` (`uuid`),
          KEY `idx_ds` (`ds`),
          KEY `idx_ds_str` (`ds_str`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
    """
    print("create_table_sql:", create_table_sql)

    # 执行创建表的 SQL 语句
    cursor.execute(create_table_sql)
    cnx.commit()

    log_directory = "/var/log/nginx"

    # 只要这几个
    valid_urls = ["/user/profileByDevice/", "/number/search/", "/number/lock/", "/config/vip/", "/order/cert/"]

    # 遍历日志目录下的文件
    for filename in os.listdir(log_directory):
        print("=================\nfilename:", filename)
        if filename.startswith("access"):
            file_path = os.path.join(log_directory, filename)
            with open(file_path, "r") as file:
                cnt = 0
                for line in file:
                    cnt += 1
                    if cnt % 10000 == 0:
                        print(cnt)

                    a = line.split()
                    # print(a)
                    status = a[9]
                    if status != "\"200\"":
                        continue

                    url = a[7].split("?")[0]
                    # 必须在合法的url中
                    for i in valid_urls:
                        if i in line:
                            break
                    else:
                        continue

                    uuid = a[-5].strip('"').replace("uuid:", '')

                    ds_str = a[3].strip("[").strip("]")
                    format_str = "%d/%b/%Y:%H:%M:%S"
                    dt = datetime.datetime.strptime(ds_str, format_str)
                    ds = int(dt.timestamp())

                    # 构建插入语句
                    insert_query = "INSERT INTO my_tmp_log (ds, ds_str, uuid, url) VALUES (%s, %s, %s, %s)"
                    values = (ds, dt.strftime("%Y-%m-%d %H:%M:%S"), uuid, url)

                    # 执行插入语句
                    cursor.execute(insert_query, values)


def get_funnel_data_by_date(cnx, cursor, date_str: str):
    dt = datetime.datetime.strptime(date_str, "%Y%m%d")

    # 设置时间为 00:00:00
    start_time = dt.replace(hour=0, minute=0, second=0)

    # 设置时间为 23:59:59
    end_time = dt.replace(hour=23, minute=59, second=59)

    # 计算开始时间的时间戳
    start_timestamp = int(start_time.timestamp())

    # 计算结束时间的时间戳
    end_timestamp = int(end_time.timestamp())

    print("start_timestamp:", start_timestamp)
    print("end_timestamp:", end_timestamp)

    new_device_ids = get_new_users(cursor, date_str)
    # print(new_device_ids)

    userid_url_cnt_map = defaultdict(int)

    sql = f"select * from my_tmp_log where ds >= {start_timestamp} and ds < {end_timestamp}"
    print(sql)
    cursor.execute(sql)
    db_data = cursor.fetchall()
    print("size:", len(db_data))
    for d in db_data:
        url = d['url']
        device_id = d['uuid']
        if device_id in new_device_ids:
            userid_url = f"new_{url}_{device_id}"
            userid_url_cnt_map[userid_url] = 1
        else:
            userid_url = f"old_{url}_{device_id}"
            userid_url_cnt_map[userid_url] = 1

    data = handle_for_new(userid_url_cnt_map)

    sql = f"INSERT INTO Report_funnel (`day`, login_cnt, search_cnt, lock_cnt, vip_cnt, order_cnt) " \
          f"VALUES({date_str}, {data['login_cnt']}, {data['search_cnt']}, " \
          f"{data['lock_cnt']}, {data['vip_cnt']}, {data['order_cnt']} ) " \
          f"ON DUPLICATE KEY UPDATE login_cnt={data['login_cnt']}, " \
          f"search_cnt={data['search_cnt']}, lock_cnt={data['lock_cnt']}, " \
          f"vip_cnt={data['vip_cnt']}, order_cnt={data['order_cnt']}"

    print(f"更新漏斗数据, sql={sql}")
    cursor.execute(sql)
    cnx.commit()

    cursor.close()
    cnx.close()


def get_new_users(cursor, date_str: str) -> list:
    dt = datetime.datetime.strptime(date_str, "%Y%m%d")

    end_dt = dt + datetime.timedelta(days=1)
    sql = f"""select uuid from User_user where created_at>='{dt.strftime("%Y-%m-%d")}' and created_at < '{end_dt.strftime("%Y-%m-%d")}'"""
    print(sql)
    cursor.execute(sql)
    res = cursor.fetchall()
    user_ids = []
    for i in res:
        user_ids.append(i['uuid'])
    return user_ids


def handle_for_new(deviceid_url_cnt_map: dict) -> dict:
    print("handle_for_new...")

    new_get_user_profile_user_cnt = 0
    new_vip_user_cnt = 0
    new_number_search_user_cnt = 0
    new_number_lock_user_cnt = 0
    new_order_user_cnt = 0

    # 只处理新用户
    for k, v in deviceid_url_cnt_map.items():
        if "new" in k:
            if '/user/profileByDevice/' in k:
                new_get_user_profile_user_cnt += 1
            elif '/config/vip/' in k:
                new_vip_user_cnt += 1
            elif '/number/search/' in k:
                new_number_search_user_cnt += 1
            elif '/number/lock/' in k:
                new_number_lock_user_cnt += 1
            elif '/order/cert/' in k:
                new_order_user_cnt += 1

    print(f"去重新用户数 : 获取用户信息：{new_get_user_profile_user_cnt}")
    print(f"去重新用户数 : 获取电话号码列表：{new_number_search_user_cnt}")
    print(f"去重新用户数 : 锁定号码：{new_number_lock_user_cnt}")
    print(f"去重新用户数 : 获取vip列表：{new_vip_user_cnt}")
    print(f"去重新用户数 : 下单：{new_order_user_cnt}")

    return {
        "login_cnt": new_get_user_profile_user_cnt,
        "search_cnt": new_number_search_user_cnt,
        "lock_cnt": new_number_lock_user_cnt,
        "vip_cnt": new_vip_user_cnt,
        "order_cnt": new_order_user_cnt,
    }


def work(need_save_db: bool, date_str: str):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    # 不指定日期就默认过滤今天的内容
    if not date_str:
        handel_dt = datetime.datetime.today() + datetime.timedelta(days=-1)
        date_str = handel_dt.strftime("%Y%m%d")

    print("date_str:", date_str)

    if need_save_db:
        # 如果你还没保存数据，先打开这个注释
        save_log_to_db(db, cursor)

    get_funnel_data_by_date(db, cursor, date_str)


if __name__ == '__main__':
    if len(sys.argv) == 1:
        yesterday = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=-1)
        date_str = yesterday.strftime("%Y%m%d")
        work(False, date_str)
    elif len(sys.argv) == 2:
        date_str = sys.argv[1]
        work(False, date_str)
    elif len(sys.argv) == 3:
        date_str = sys.argv[1]
        need_save_db = sys.argv[2]
        work(True, date_str)
