# coding=utf-8
import telnyx


def work():
    import requests

    url = "https://api.telnyx.com/v2/credential_connections"

    app_map = {"txtnow": "2201593832611513488",
               "v3phone": ""}

    query = {
        "page[number]": "1",
        "page[size]": "1",
        "filter[outbound.outbound_voice_profile_id]": app_map['txtnow'],
        "sort": "connection_name"
    }

    telnyx.api_key = "**********************************************************"
    api_key = telnyx.api_key
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    response = requests.get(url, headers=headers, params=query)

    data = response.json()
    print(data)
    total_size = data['meta']['total_results']
    total_pages = data['meta']['total_pages']
    print("meta:", data['meta'])
    print("total_size:", total_size)
    print("total_pages:", total_pages)

    batch_size = 50
    batch_cnt = int(total_size / 50) + 1
    registration_status_map = {}
    for i in range(batch_cnt):
        print("################")
        print(f"batch_cnt:{batch_cnt}, batch_size:{batch_size}")
        query = {
            "page[number]": str(i + 1),
            "page[size]": str(batch_size),
            "filter[outbound.outbound_voice_profile_id]": app_map['txtnow'],
            "sort": "connection_name"
        }
        response = requests.get(url, headers=headers, params=query)
        data = response.json()
        for i in data['data']:
            print(i)
            if i['registration_status'] not in registration_status_map:
                registration_status_map[i['registration_status']] = 1
            else:
                registration_status_map[i['registration_status']] += 1

    for k, v in registration_status_map.items():
        print(k, v)


def check_for_one_sip_conneciton():
    import requests
    TELNYX_API_KEY = "**********************************************************"
    url = f"https://api.telnyx.com/v2/credential_connections/2270970685247456336"
    headers = {"Authorization": f"Bearer {TELNYX_API_KEY}"}
    response = requests.get(url, headers=headers)
    data = response.json()
    print(data)


if __name__ == '__main__':
    work()
    check_for_one_sip_conneciton()
