# encoding=utf-8

import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def Levenshtein_Distance(str1: str, str2: str) -> int:
    """
    计算字符串 str1 和 str2 的编辑距离
    :param str1
    :param str2
    :return:
    """
    matrix = [[i + j for j in range(len(str2) + 1)] for i in range(len(str1) + 1)]

    for i in range(1, len(str1) + 1):
        for j in range(1, len(str2) + 1):
            if str1[i - 1] == str2[j - 1]:
                d = 0
            else:
                d = 1

            matrix[i][j] = min(matrix[i - 1][j] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j - 1] + d)

    ans = matrix[len(str1)][len(str2)]
    return ans


def edit_distance(sms_list):
    distance_ans = []
    sum_length = 0
    for i in range(len(sms_list)):
        sum_length += len(sms_list[i]['content'])
        for j in range(i + 1, len(sms_list)):
            d = Levenshtein_Distance(sms_list[i]['content'], sms_list[j]['content'])
            distance_ans.append(d)

    # 去掉最高分和最低分
    distance_ans.sort()
    distance_ans = distance_ans[1:-1]

    print(
        f"sum(distance_ans) : {sum(distance_ans)}, len(distance_ans): {len(distance_ans)}, sum_length * 0.1 = {sum_length / len(sms_list) * 0.1}")
    if sum(distance_ans) / len(distance_ans) <= sum_length / len(sms_list) * 0.1:
        return True
    else:
        return False


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    now = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=-1)
    sql = f"select * from User_user t1 where t1.created_at >= '{now.strftime('%Y-%m-%d %H:%M:%S')}'"
    cursor.execute(sql)
    user_list = cursor.fetchall()
    print(f"{sql}, handle size: {len(user_list)}")

    send_list = []
    for u in user_list:
        user_id = u['id']
        sql = f"select content, to_number from Call_smsrecord where user_id={user_id} and direction='SEND'"
        cursor.execute(sql)
        sms_list = cursor.fetchall()
        sms_list = [v for v in sms_list if len(v['content'].split()) >= 15]
        if len(sms_list) >= 5:
            print(sql)
            print(f"user: {user_id} handle size: {len(sms_list)}")
            contact_cnt = len(set([v['to_number'] for v in sms_list]))
            sms_cnt = len(sms_list)

            if contact_cnt / sms_cnt >= 0.6 and edit_distance(sms_list):
                sql = f"select * from User_contactcontrol where user_id={user_id}"
                cursor.execute(sql)
                contact_list = cursor.fetchall()
                if len(contact_list) > 0:
                    continue
                else:
                    sql = f"insert into User_contactcontrol(user_id, status, created_at) values ({user_id}, 2, now())"
                    cursor.execute(sql)

                    print(f"user: {user_id} 是复读机")
                    send_list.append(f"user: {user_id} 是复读机")
                    for i in sms_list:
                        send_list.append(i)

    if len(send_list) > 0:
        send_mail(send_list)

    db.commit()
    cursor.close()
    db.close()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}复读机', 'utf-8').encode()

        html = f'<html><body><h2>复读机报警：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
             <table border="1">
              <tr>
                 <th>复读机报警</th>
             </tr>
             """
        for index, send_item in enumerate(send_list):
            html += f"""
                 <tr>
                     <td>{index}: {send_item}</td>
                 </tr>
                 """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"check fishing sms, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"check fishing sms, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
