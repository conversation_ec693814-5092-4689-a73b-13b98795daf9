# coding=utf-8
import traceback

import pymysql
from pymysql.cursors import DictCursor


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_target_user_ids(conn):
    """
    第一步：从Order_ordersendvip表查询目标user_id
    查询条件：send_days < -1500，按id倒序，取前10条
    """
    sql = """
        SELECT user_id 
        FROM Order_ordersendvip 
        WHERE send_days < -1500 
        ORDER BY id DESC 
    """
    user_ids = []
    try:
        with conn.cursor(DictCursor) as cursor:  # DictCursor：返回字典格式结果（便于取值）
            cursor.execute(sql)
            results = cursor.fetchall()  # 获取所有查询结果
            user_ids = [row["user_id"] for row in results]  # 提取user_id列表

        if not user_ids:
            print("未查询到符合条件的user_id（Order_ordersendvip表）")
        else:
            print(f"成功获取{len(user_ids)}个目标user_id：{user_ids}")
    except pymysql.Error as e:
        print(f"查询Order_ordersendvip表失败：{e.args[0]} - {e.args[1]}")
        traceback.print_exc()
        raise
    return user_ids


def get_bad_images_by_user(conn, user_id):
    """
    第二步：根据user_id查询Call_smsrecord表中的“坏图片”
    查询条件：is_image=1、is_fake_send=1、direction='SEND'、指定user_id
    返回：符合条件的图片数据列表（含id、images、image_md5、created_at）
    """
    sql = f"""
        SELECT id, images, image_md5, created_at 
        FROM Call_smsrecord 
        WHERE is_image = 1 
          AND is_fake_send = 1 
          AND direction = 'SEND' 
          AND user_id = {user_id}  
    """
    try:
        with conn.cursor(DictCursor) as cursor:
            cursor.execute(sql)  # 第二个参数为元组，传递user_id（必须带逗号，避免被当作字符串）
            bad_images = cursor.fetchall()

        print(f"user_id={user_id}：查询到{len(bad_images)}条坏图片记录")
        return bad_images
    except pymysql.Error as e:
        print(f"查询Call_smsrecord表（user_id={user_id}）失败：{e.args[0]} - {e.args[1]}")
        traceback.print_exc()
        raise


def insert_into_badimage_table(conn, bad_image):
    """
    第三步：将“坏图片”数据插入Call_badimage表
    处理逻辑：若image_md5已存在（唯一键冲突），则忽略插入（避免重复数据）
    """
    # 提取需要插入的字段（注意字段映射：Call_smsrecord的images → Call_badimage的image_url）
    insert_data = {
        "user_id": bad_image["user_id"],  # 来自外层循环的user_id
        "image_md5": bad_image["image_md5"],  # 图片MD5（Call_smsrecord的image_md5）
        "image_url": bad_image["images"],  # 图片URL（Call_smsrecord的images字段）
        "created_at": bad_image["created_at"]  # 图片创建时间（Call_smsrecord的created_at）
    }

    # INSERT IGNORE：若唯一键（uniq_image_md5）冲突，则跳过插入（避免报错）
    sql = """
        INSERT IGNORE INTO Call_badimage 
        (user_id, image_md5, image_url, created_at, updated_at) 
        VALUES (%(user_id)s, %(image_md5)s, %(image_url)s, %(created_at)s, NOW())
    """
    try:
        with conn.cursor() as cursor:
            affected_rows = cursor.execute(sql, insert_data)  # 传递字典格式参数，更直观
            # 记录插入结果：affected_rows=1表示插入成功，0表示已存在（被忽略）
            if affected_rows == 1:
                print(f"插入成功：user_id={insert_data['user_id']}, image_md5={insert_data['image_md5']}")
            else:
                print(f"忽略重复：user_id={insert_data['user_id']}, image_md5={insert_data['image_md5']}（已存在）")
    except pymysql.Error as e:
        print(f"插入Call_badimage表失败（data={insert_data}）：{e.args[0]} - {e.args[1]}")
        traceback.print_exc()
        raise


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')

    # 2. 第一步：获取目标user_id列表
    user_ids = get_target_user_ids(db)
    if not user_ids:
        return  # 无目标user_id，直接退出

    # 3. 第二步+第三步：遍历user_id，查坏图片并插入新表
    total_inserted = 0  # 统计总插入成功数
    total_ignored = 0  # 统计总忽略重复数

    for user_id in user_ids:
        # 3.1 查当前user_id的坏图片
        bad_images = get_bad_images_by_user(db, user_id)
        if not bad_images:
            continue  # 无坏图片，跳过后续插入

        # 3.2 为每条坏图片添加user_id（便于插入时使用），并执行插入
        for img in bad_images:
            img["user_id"] = user_id  # 补充user_id字段（Call_smsrecord查询结果不含user_id，需手动添加）
            # 执行插入（捕获单条插入异常，避免一条失败导致整个循环终止）
            try:
                insert_into_badimage_table(db, img)
                total_inserted += 1
            except Exception:
                total_ignored += 1  # 此处忽略单条插入失败，继续处理下一条（可根据需求改为终止）

    # 4. 提交事务（所有插入操作完成后统一提交，确保数据一致性）
    db.commit()
    print(f"===== 同步完成 ===== 总插入成功：{total_inserted}条，总忽略重复：{total_ignored}条")


if __name__ == '__main__':
    work()
