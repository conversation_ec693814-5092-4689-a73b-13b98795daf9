import time
import traceback

import requests
import telnyx

TELNYX_API_KEY = "**********************************************************"


def set_phone_number_tags_and_billing_group(phone_number_id: str, tags: list, billing_group_id: str):
    try:
        headers = {
            'Authorization': f'Bearer {TELNYX_API_KEY}',
            'Content-Type': 'application/json'
        }
        data = {
            "tags": tags,
            "billing_group_id": billing_group_id,
        }
        response = requests.patch(f"https://api.telnyx.com/v2/phone_numbers/{phone_number_id}", json=data,
                                  headers=headers)
        print(
            f"[TelnyxUtil.set_phone_number_tags] number_id: {phone_number_id}, tags:{tags} success: {response.content}")
        return response.json()

    except Exception:
        traceback.print_exc()
        print(f"[TelnyxUtil.set_phone_number_tags] number_id: {phone_number_id}, tags:{tags} failed")


if __name__ == '__main__':
    # 设置Telnyx API认证信息
    telnyx.api_key = "**********************************************************"
    api_key = telnyx.api_key
    v2_numbers_set = set()
    v3_numbers_set = set()

    v2_billing_group = "72c8856f-0a46-4c18-8236-5e2f070087ef"
    v3_billing_group = "a55bfe73-15fb-4658-af07-856864d4002f"

    for i in range(100):
        print("####### batch #####", i)
        numbers = telnyx.PhoneNumber.list(page={"number": i + 1, "size": 100})
        if len(numbers) == 0 or not numbers or ("data" in numbers and len(numbers['data']) == 0):
            break
        for index, n in enumerate(numbers):
            # print(index, n)
            if "txtnow" in n["messaging_profile_name"]:
                v2_numbers_set.add(n['phone_number'])
                set_phone_number_tags_and_billing_group(n['id'], ["v2number"], v2_billing_group)
                continue
            if "VSIM" == n["messaging_profile_name"]:
                v3_numbers_set.add(n['phone_number'])
                set_phone_number_tags_and_billing_group(n['id'], ["v3number"], v3_billing_group)

        time.sleep(1)

    print("number v2 set size:", len(v2_numbers_set))
    print("number v3 set size:", len(v3_numbers_set))
