# coding=utf-8
import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
from pymysql import cursors


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def work(date_str: str):
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    start_day = datetime.datetime.strptime(date_str, "%Y%m%d")
    end_day = start_day + datetime.timedelta(days=1)
    sql = f"select * from User_user where created_at >= '{start_day.strftime('%Y-%m-%d')}' and created_at < '{end_day.strftime('%Y-%m-%d')}'"
    print(sql)
    cursor.execute(sql)
    users = cursor.fetchall()
    print(f"{start_day} - {end_day}, users size: {len(users)}")

    today_cnt = len(users)
    invalid_cnt = 0
    empty_cnt = 0
    invalid_list = []

    # users = [{"id": 1225669}]
    for u in users:
        user_id = u['id']
        sql1 = f"select sum(point) as cnt from Call_smsrecord where user_id={user_id} and is_it=0"
        print(sql1)

        sql2 = f"select sum(point) as cnt from Call_callrecord where user_id={user_id}"
        print(sql2)

        sql3 = f"select sum(point) as cnt from Point_pointrecord where user_id={user_id} and point < 0"
        print(sql3)

        cursor.execute(sql1)
        sms_record = cursor.fetchone()
        sms_cnt = 0 if (not sms_record or not sms_record['cnt']) else sms_record['cnt']

        cursor.execute(sql2)
        call_record = cursor.fetchone()
        call_cnt = 0 if (not call_record or not call_record['cnt']) else call_record['cnt']

        cursor.execute(sql3)
        point_record = cursor.fetchone()
        point_cnt = 0 if (not point_record or not point_record['cnt']) else point_record['cnt']

        if sms_cnt == call_cnt == point_cnt:
            empty_cnt += 1
            continue

        if sms_cnt + call_cnt != abs(point_cnt):
            invalid_cnt += 1
            invalid_list.append(f"user:{user_id}, sms_cnt:{sms_cnt}, call_cnt:{call_cnt}, point:{point_cnt}")

    print("today_cnt:", today_cnt)
    print("empty_cnt:", empty_cnt)
    print("invalid_cnt:", invalid_cnt)
    for i in invalid_list:
        print(i)

    db.commit()
    cursor.close()
    db.close()


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail():
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}统计点数结算异常 ！！！', 'utf-8').encode()

        html = f'<html><body><h2>统计点数结算异常情况：{datetime.datetime.now(datetime.timezone.utc)}</h2>'

        msg.attach(MIMEText(html, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"check points ok, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        if len(sys.argv) == 1:
            yesterday = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=-1)
            date_str = yesterday.strftime("%Y%m%d")
            work(date_str)
        elif len(sys.argv) == 2:
            date_str = sys.argv[1]
            work(date_str)
        else:
            raise Exception("invalid param when check points ok")

    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}脚本异常")
        send_mail()
    finally:
        print(f"check points ok, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
