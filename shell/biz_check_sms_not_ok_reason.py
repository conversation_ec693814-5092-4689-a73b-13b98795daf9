# coding=utf-8
import datetime
import json
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
import pymysql.cursors
import redis

gap_days = 1


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    return env_config


env_config = get_env_config()


def get_sms_list():
    # 从数据库加载数据
    db = pymysql.connect(
        host=env_config["DB_HOST_KEY"],
        port=env_config["DB_PORT_KEY"],
        user=env_config["DB_USER_KEY"],
        passwd=env_config["DB_PASSWORD_KEY"],
        db=env_config["DB_DATABASE_NAME_KEY"],
        charset='utf8'
    )

    # 当前日期时间
    yesterday = datetime.datetime.today() - datetime.timedelta(days=gap_days)
    dt = yesterday.strftime("%Y-%m-%d 00:00:00")
    cursor = db.cursor(pymysql.cursors.DictCursor)
    sql = f"""
           select id, content, first_check_reason,created_at,user_id from Call_smsrecord where created_at >= '{dt}' 
           and status='send_fake' and images ='';
    """

    sql2 = f"""
      select id, content, first_check_reason,created_at,user_id from Call_smsrecord where created_at >= '{dt}' 
      and status='delivered' and first_check_reason like '%fatpo_resend' and images ='';
    """
    cursor.execute(sql)
    sms = cursor.fetchall()
    print(f"sms size: {len(sms)}")

    cursor.execute(sql2)
    sms2 = cursor.fetchall()
    print(f"sms2 size: {len(sms2)}")

    total = []
    uniq_dic = {}
    order_dic = {}

    for s in sms:
        user_id = s['user_id']
        if user_id == 1214162:
            continue
        if user_id not in order_dic:
            sql3 = f"select expire_at from Order_order where user_id={user_id}"
            cursor.execute(sql3)
            order = cursor.fetchone()
            order_dic[user_id] = order["expire_at"]
            expire_at = order["expire_at"]
        else:
            expire_at = order_dic[user_id]

        s['content'] = s['content'].replace("\n", " ") + "___" + str(s['user_id']) + "___" + str(expire_at)
        if s['id'] not in uniq_dic:
            total.append(s)
            uniq_dic[s['id']] = 1

    for s in sms2:
        if s['id'] not in uniq_dic:
            user_id = s['user_id']
            if user_id == 1214162:
                continue
            if user_id not in order_dic:
                sql3 = f"select expire_at from Order_order where user_id={user_id}"
                cursor.execute(sql3)
                order = cursor.fetchone()
                order_dic[user_id] = order["expire_at"]
                expire_at = order["expire_at"]
            else:
                expire_at = order_dic[user_id]

            s['content'] = s['content'].replace("\n", " ") + "___" + str(s['user_id']) + "___" + str(expire_at)

            total.append(s)
            uniq_dic[s['id']] = 1

    db.commit()
    cursor.close()
    db.close()  # 立即关闭数据库连接

    return total


# 创建 Redis 连接
my_redis = redis.Redis(host='*********', port=6379, db=0)


def work():
    # 获取短信
    sms = get_sms_list()

    dic = {}
    text_dic = {}
    for s in sms:
        reasons = [s["first_check_reason"]]
        for r in reasons:
            if r not in dic:
                dic[r] = 1
                text_dic[r] = [s['content']]
            else:
                dic[r] += 1
                text_dic[r].append(s['content'])

    sorted_items = sorted(dic.items(), key=lambda x: x[1])

    redis_cache_rsp = []

    for index, s in enumerate(sorted_items):
        reason = s[0]
        if reason in ("fatpo_resend", "wangyi"):
            continue

        dic = {
            "cate": reason,
            "size": len(text_dic[reason]),
            "details": []
        }
        for index2, i in enumerate(text_dic[reason]):
            detail = i.replace("\t", " ").replace("\n", " ").strip()
            dic["details"].append(f"{index2}___{detail}")

        redis_cache_rsp.append(dic)

    yesterday = datetime.datetime.today() - datetime.timedelta(days=gap_days)
    day = yesterday.strftime("%Y%m%d")
    key = f"check_bad_sms_{day}"
    print(key)
    my_redis.set(key, json.dumps(redis_cache_rsp), 30 * 86400)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail():
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}生成坏短信失败', 'utf-8').encode()

        html = f'<html><body><h2>生成坏短信：{datetime.datetime.now(datetime.timezone.utc)}</h2></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()


if __name__ == '__main__':
    print("*" * 20)
    print(f"check bad sms, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        work()
    except Exception:
        traceback.print_exc()
        send_mail()
    finally:
        print(f"check bad sms, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
