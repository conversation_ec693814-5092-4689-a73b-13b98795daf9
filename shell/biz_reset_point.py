# coding=utf-8
import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def is_user_call_finised_cost(cursor, user_id: int) -> (bool, str):
    # 首先，所有的completed状态的号码都有price啦
    cursor.execute(
        f"select * from Call_callrecord where user_id={user_id} and status <> 'failed' and price is null")
    res = cursor.fetchall()
    if len(res) > 0:
        return False, ''

    # 其次，在 Point_pointrecord 中都能一一对应
    cursor.execute(
        f"select * from Call_callrecord where user_id={user_id} and status <> 'failed'")
    calls = cursor.fetchall()
    for call in calls:
        cursor.execute(
            f"select * from Point_pointrecord where user_id={call['user_id']} and record_id={call['id']} and event='CALL' and point <= 0")
        res = cursor.fetchall()
        if len(res) != 1:
            err_msg = f"error: call record: {call} not in Point_pointrecord !!! "
            print(err_msg)
            return False, err_msg
    return True, ''


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    cursor.execute("select * from Point_point where point != predict_point;")
    user_point_list = cursor.fetchall()
    print(f"user_point_list size: {len(user_point_list)}")

    send_list = []
    for user_point in user_point_list:
        print(f"check user: {user_point}")
        user_id = user_point['user_id']
        is_ok, err_msg = is_user_call_finised_cost(cursor, user_id)
        if is_ok:
            msg = f"update finised cost call record: {user_point}."
            cmd = f"update Point_point set predict_point=point, updated_at=now() where user_id={user_id} ;"
            print(msg + "\r\n" + cmd)
            cursor.execute(cmd)
            send_list.append(msg + "\r\n" + cmd)
        elif err_msg:
            send_list.append(err_msg)

    db.commit()
    cursor.close()
    db.close()

    # 看情况发送邮件
    # if send_list:
    #     send_mail(send_list)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(send_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}callRecord结算完毕后重置predictPoint ！！！',
                                'utf-8').encode()

        html = f'<html><body><h2>callRecord结算完毕后重置predictPoint：{datetime.datetime.now(datetime.timezone.utc)}</h2>'
        html += """
         <table border="1">
          <tr>
             <th>callRecord结算完毕后重置predictPoint</th>
         </tr>
         """
        for send_item in send_list:
            html += f"""
             <tr>
                 <td>{send_item}</td>
             </tr>
             """
        html += '</table></body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print("*" * 20)
    print(f"reset predict point, time: {datetime.datetime.now(datetime.timezone.utc)}")
    print("*" * 20)
    try:
        # 2022年4月20日01:10:10 打算放弃predict_point
        # work()
        pass
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail([])
    finally:
        print(f"reset predict point, finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
