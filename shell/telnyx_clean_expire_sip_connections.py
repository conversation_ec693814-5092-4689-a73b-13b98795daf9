import datetime
import smtplib
import sys
import traceback
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

import pymysql
import requests


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "FCM_TITLE": env("FCM_TITLE"),
        "FCM_CERT_FILEPATH": env("FCM_CERT_FILEPATH"),
        "FCM_FATPO_PUSHID": env("FCM_FATPO_PUSHID"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()


def get_sip(page_number: int, page_size: int):
    url = "https://api.telnyx.com/v2/credential_connections"

    query = {
        "page[number]": f"{page_number}",
        "page[size]": f"{page_size}",
        "filter[outbound.outbound_voice_profile_id]": "2201593832611513488",
        "sort": "connection_name"
    }

    headers = {"Authorization": "Bearer KEY0193D036537A4B8864C8C5751E010685_OmTXhITKI4JA0jBUzKRhMh"}

    response = requests.get(url, headers=headers, params=query)

    data = response.json()
    return data


def delete_a_sip(sid: str):
    url = "https://api.telnyx.com/v2/credential_connections/" + sid

    headers = {"Authorization": "Bearer KEY0193D036537A4B8864C8C5751E010685_OmTXhITKI4JA0jBUzKRhMh"}

    response = requests.delete(url, headers=headers)
    data = response.json()
    # print(data)
    return data


def work():
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)

    total_need_to_clean_cnt = 0
    total_no_need_to_clean_cnt = 0
    failed_list = []
    page_size = 100
    for i in range(1000):
        if i % 10 == 0:
            print(f"======================\n======================\nbatch: {i}/{1000}")

        data = get_sip(i + 1, page_size)

        if len(data['data']) == 0:
            break

        need_to_clean_cnt = 0
        no_need_to_clean_cnt = 0

        for index2, d in enumerate(data['data']):
            if 'txtnow' not in d['user_name']:
                continue
            if (i * page_size + index2) % 100 == 0:
                print(f"====================== \ncnt: {i * page_size + index2}/batch_{i}")

            print(i * page_size + index2, d['id'], d['user_name'], d['registration_status'])

            user_id = int(d['user_name'].replace('txtnow', ''))

            sql2 = f"select order_status, expire_at from Order_order where user_id={user_id}"
            cursor.execute(sql2)
            result = cursor.fetchone()
            if not result:
                print(f"########## user_id not exists??? {sql2}")
                failed_list.append(f"########## user_id not exists??? {sql2}")
                continue
            if result['order_status'] == "CLOSED" and (datetime.datetime.now(datetime.timezone.utc) - result['expire_at']).days > 15:
                # print("need to clean", result)
                total_need_to_clean_cnt += 1
                need_to_clean_cnt += 1

                # clean telnyx
                delete_rsp = ""
                try:
                    delete_rsp = delete_a_sip(d['id'])

                    # clean DB
                    sql3 = f"update User_user set telnyx_sip_connection_id=null,telnyx_sip_username=null,telnyx_sip_password=null where id={user_id}"
                    print(sql3)
                    cursor.execute(sql3)
                except Exception:
                    failed_list.append(f"delete sip failed: {d['id']}, delete_rsp：{delete_rsp}")
            else:
                # print("not need to clean", result)
                total_no_need_to_clean_cnt += 1
                no_need_to_clean_cnt += 1

        print("need vs no-need:",
              f"{need_to_clean_cnt}/{no_need_to_clean_cnt} vs {total_need_to_clean_cnt}/{total_no_need_to_clean_cnt}")
        # 每批更新一次
        db.commit()

    cursor.close()
    db.close()

    if total_need_to_clean_cnt == 0 or total_no_need_to_clean_cnt == 0 or failed_list:
        send_mail(total_need_to_clean_cnt, total_no_need_to_clean_cnt, failed_list)


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_mail(total_need_to_clean_cnt: int, total_no_need_to_clean_cnt: int, _failed_list: list):
    try:
        # 邮箱定义
        smtp_server = 'smtp.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'geodowcfakrsbhhi'
        to_addr = ['<EMAIL>']

        # 邮件对象
        msg = MIMEMultipart()
        msg['From'] = _format_addr('发件人 <%s>' % from_addr)
        msg['To'] = _format_addr('收件人 <%s>' % to_addr)
        msg['Subject'] = Header(f'{env_config["EMAIL_PREFIX"]}telnyx淘汰过期SIP', 'utf-8').encode()

        txt = f"<br>total_need_to_clean_cnt: {total_need_to_clean_cnt}"
        txt += f"<br>total_no_need_to_clean_cnt: {total_no_need_to_clean_cnt} <br>"
        for i in _failed_list:
            txt += f"<br>   {i}"

        msg.attach(MIMEText(txt, 'html', 'utf-8'))
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr, msg.as_string())
        server.quit()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


if __name__ == '__main__':
    print(f"telnyx clean expire sip start time: {datetime.datetime.now(datetime.timezone.utc)}")

    try:
        work()
    except Exception:
        traceback.print_exc()
        send_slack_message(f"TXTNOW{__file__}邮件异常")
        send_mail(-1, -1, [])
    finally:
        print(f"telnyx clean expire sip finished time: {datetime.datetime.now(datetime.timezone.utc)}")
        sys.exit(0)
