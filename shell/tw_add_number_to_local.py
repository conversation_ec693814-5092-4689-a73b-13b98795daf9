import sys
import traceback

import pymysql
from twilio.rest import Client


def get_env_config():
    import environ

    # 指定.env 文件的路径
    env_file_path = "/root/www/VirtualSIM_BackEnd/SecPhone/.env"

    # 从.env 文件中加载环境变量
    env = environ.Env()
    environ.Env.read_env(env_file=env_file_path)

    # 使用环境变量
    env_config = {
        "DB_HOST_KEY": env("DB_HOST_KEY"),
        "DB_USER_KEY": env("DB_USER_KEY"),
        "DB_PASSWORD_KEY": env("DB_PASSWORD_KEY"),
        "DB_DATABASE_NAME_KEY": env("DB_DATABASE_NAME_KEY"),
        "DB_PORT_KEY": int(env("DB_PORT_KEY")),
        "EMAIL_PREFIX": env("EMAIL_PREFIX"),
        "TWILIO_ACCOUNT_SID": env("TWILIO_ACCOUNT_SID"),
        "TWILIO_ACCOUNT_TOKEN": env("TWILIO_ACCOUNT_TOKEN"),
    }
    print(env_config)
    return env_config


env_config = get_env_config()

client_map = {
    1: Client("**********************************", "d703a14afd6342ce8699d3ed8607bfc2"),
    2: Client("**********************************", "7901ab6886b148460b16edc337d1343a"),
}


def update_tw(tw_version, sid) -> list:
    print("update tw... tw_version:", tw_version, "sid: ", sid)
    incoming_number = client_map[tw_version].incoming_phone_numbers.get(sid).fetch()
    print(incoming_number)
    print("phone_number:", incoming_number.phone_number)
    print("sid:", incoming_number.sid)
    print("sms_url:", incoming_number.sms_url)
    print("voice_url:", incoming_number.voice_url)
    print("status_callback:", incoming_number.status_callback)
    incoming_number.update(sms_url="https://tw.zehougroup.xyz/phone/incoming/sms/",
                           voice_url="https://tw.zehougroup.xyz/phone/incoming/call/",
                           status_callback="https://tw.zehougroup.xyz/phone/call/status/")

    incoming_number = client_map[tw_version].incoming_phone_numbers.get(sid).fetch()
    print("after update...")
    print(incoming_number)
    print("phone_number:", incoming_number.phone_number)
    print("sid:", incoming_number.sid)
    print("sms_url:", incoming_number.sms_url)
    print("voice_url:", incoming_number.voice_url)
    print("status_callback:", incoming_number.status_callback)


def insert_to_db(number: str, sid: str):
    print("insert_to_db number:", number, "sid: ", sid)
    db = pymysql.connect(host=env_config["DB_HOST_KEY"], port=env_config["DB_PORT_KEY"], user=env_config["DB_USER_KEY"],
                         passwd=env_config["DB_PASSWORD_KEY"], db=env_config["DB_DATABASE_NAME_KEY"], charset='utf8')
    # 使用cursor方法创建一个游标
    cursor = db.cursor(pymysql.cursors.DictCursor)
    region = number[2:5]
    friendly_name = f"({region}){number[5:8]}-{number[8:]}"

    capabilities = """{"fax": false, "voice": true, "sms": true, "mms": true}"""
    sql = f"""
    INSERT INTO `Number_numberinventory` (`number`,`friendly_name`,`sid`,`created_at`,`updated_at`,`release_at`,`capabilities`,`status`,`platform`,`tw_version`) 
    VALUES 
    ('{number}','{friendly_name}','{sid}',now(),now(),NULL,'{capabilities}','EXPIRE',0,1);
    """
    print(sql)
    cursor.execute(sql)
    db.commit()
    cursor.close()
    db.close()


if __name__ == '__main__':
    try:
        tw_version = int(sys.argv[1])
        phone_number = sys.argv[2]
        phone_number_sid = sys.argv[3]

        update_tw(tw_version, phone_number_sid)
        insert_to_db(phone_number, phone_number_sid)
    except Exception:
        traceback.print_exc()
        print("python3 1.py {tw_version} {number} {sid}")
        print("python3 1.py 1 +17736924742 PNc737aad2316d311bf0c306ae42c24f67")
