* {
	margin: 0;
	padding: 0;
}
html, body {
	height: 100%;
}
#app {
	height: 100%;
	font-size: 1rem;
}
#list {
	background-color: rgb(245, 249, 250);
	height: 100%;
	width: 100%;
	overflow: auto;
	position: relative;
}
#list > div {
	cursor: pointer;
	position: relative;
	overflow: hidden;
	display: flex;
}
#list .size {
	position: sticky;
	top: 0;
	height: 1.875rem;
	font-size: 0.75rem;
	text-align: center;
	color: #666;
	background-color: #fff;
	line-height: 1.875rem;
}
#list .head {
	float: left;
	width: 2.5rem;
	height: 2.5rem;
	text-align: center;
	line-height: 2.5rem;
	border-radius: 50%;
	background-color: rgb(93, 114, 167);
	color: #fff;
	margin: 0.625rem;
}
#list .tips {
	background-color: red;
	font-size: 0.75rem;
	color: #fff;
	border-radius: 50%;
	width: 1.25rem;
	height: 1.25rem;
	position: absolute;
	top: 1.875rem;
	right: 0.9375rem;
	text-align: center;
	line-height: 1.25rem;
}
#list .content {
	overflow: hidden;
	flex: 1;
	height: 3.25rem;
	padding: 0.625rem 0;
}
#list .content .number {
	color: #333;
	float: left;
}
#list .content .time {
	color: #999;
	float: right;
	padding-right: 0.625rem;
}
#list .content .messgae {
	width: 100%;
	color: #bbb;
	font-size: 0.875rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-right: 2.5rem;
	box-sizing: border-box;
}

#detail {
	height: 100%;
	background-color: #fff;
	position: relative;
}
#detail .head {
	height: 2.5rem;
	background-color: #fff;
	position: fixed;
	top: 0;
	right: 0;
	left: 0;
}
#detail .head .back {
	float: left;
	line-height: 2.5rem;
	font-size: 1.125rem;
	color: #4169e1;
	cursor: pointer;
	position: relative;
	padding-left: 1.875rem;
	padding-right: 0.75rem;
}
#detail .head .back::before,
#detail .head .back::after {
	content: '';
	width: 0;
	height: 0;
	border-top: 0.625rem solid transparent;
	border-bottom: 0.625rem solid transparent;
	border-right: 0.625rem solid #4169e1;
	position: absolute;
	left: 0.75rem;
	top: 0.625rem;
}
#detail .head .back::after {
	border-right-color: #fff;
	left: 0.9375rem;
}
#detail .head .number {
	font-size: 1.125rem;
	font-weight: 700;
	line-height: 2.5rem;
}
#detail .content {
	position: fixed;
	top:  2.5rem;
	left: 0;
	right: 0;
	width: 100%;
	bottom:  8.5rem;
	/* padding-top: 2.5rem; */
	/* padding-bottom: 2.5rem; */
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	overflow: auto;
}
#detail .content .word {
	width: 70%;
	background-color: rgb(230, 231, 235);
	margin: 0.9375rem;
	padding: 0.625rem;
	font-size: 1rem;
	white-space: pre-line;
	word-break: break-all;
}
#detail .content .word.send {
	background-color: rgb(204, 228, 252);
	align-self: flex-end;
	white-space: pre-line;
}
#detail .content .word pre {
	white-space: pre-line;
}
#detail .operate {
	height: 8.5rem;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	right: 0;
	left: 0;
	background-color: rgb(227, 227, 227);
}
#detail .operate .operateBtns {
	padding: 0.5rem;
}
#detail .operate .operateBtn {
	height: 2rem;
}
#detail .operate .inputWrapper {
	display: flex;
	align-items: center;
}
#detail .operate .input {
	height: 4.5rem;
	margin: 0.5rem;
	flex-grow: 1;
	border: 0;
	border-radius: 0.3125rem;
	background-color: rgb(235, 235, 235);
}
#detail .operate .send {
	background-color: rgb(52, 117, 253);
	border: 0;
	border-radius: 50%;
	height: 2.1875rem;
	width: 2.1875rem;
	margin: 0.625rem;
}

#detail .operate .send img {
	width: 80%;
	height: 80%;
}

/* 添加一些自定义样式以使按钮更大和更吸引人 */
#refreshButton {
	display: block; /* 使按钮占满整行 */
	width: 150px; /* 设置按钮宽度 */
	height: 30px; /* 设置按钮高度 */
	background-color: #007bff; /* 设置背景颜色 */
	color: #fff; /* 设置文本颜色 */
	font-size: 18px; /* 设置文本大小 */
	border: none; /* 移除边框 */
	border-radius: 10px; /* 添加圆角 */
	cursor: pointer; /* 添加鼠标指针效果 */
	margin: 20px auto; /* 设置按钮的外边距以使其水平居中 */
}

/* 当鼠标悬停在按钮上时改变颜色 */
#refreshButton:hover {
	background-color: #0056b3; /* 设置悬停状态的背景颜色 */
}

/* 为按钮添加样式 */
.wordButton {
	background-color: #007bff; /* 设置按钮背景颜色 */
	color: #fff; /* 设置按钮文本颜色 */
	border: none; /* 移除边框 */
	border-radius: 5px; /* 圆角边框 */
	padding: 8px 12px; /* 设置内边距 */
	margin: 0 5px; /* 设置按钮间距 */
	cursor: pointer; /* 鼠标指针样式 */
}

/* 布局按钮 */
.wordButtons {
	text-align: center; /* 文本居中 */
	margin-top: 30px; /* 设置按钮与输入框的距离 */
	background-color: #fff;
	position: absolute;
	bottom: 6.125rem;
	right: 0;
	left: 0;
	opacity:0.65;
	display:flex;
	overflow-x:scroll;
}

#replyPopup {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 70%;
	background-color: #fff;
	border-top: 2px solid #333;
}

#replyPopup .closeBtn {
	position: absolute;
	width: 2.5rem;
	height: 2.5rem;
	font-size: 1.5rem;
	top: 0.5rem;
	right: 0.5rem;
	display: flex;
	align-items: center;
	justify-content: center;
}

#replyPopup .title {
	text-align: center;
	height: 3rem;
	line-height: 3rem;
	font-size: 1rem;
}

#replyPopup .main {
	position: absolute;
	top: 3rem;
	left: 0;
	right: 0;
	width: 100%;
	bottom: 0;
	overflow-y: scroll;
}