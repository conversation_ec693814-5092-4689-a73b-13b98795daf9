<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Support Scripts</title>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/4.0.0/github-markdown.min.css">
    <style>
        /* Add custom styles for your Markdown content here */
        .markdown-body {
            padding: 20px;
        }

        .copy-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
        }
    </style>
</head>
<body>
<div class="markdown-body" id="markdown-content">
    <!-- Markdown content will be loaded here -->
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/2.0.0/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>
<script>
    const markdownFilePath = 'huashu.md'; // Replace with the actual path to your Markdown file

    fetch(markdownFilePath)
        .then(response => response.text())
        .then(markdownText => {
            // Automatically generate unique IDs for code blocks
            let index = 1;
            const htmlText = marked(markdownText.replace(/<pre><code/g, match => `<pre><code id="code-block-${index++}"`));
            document.getElementById('markdown-content').innerHTML = htmlText;

            addCopyButtons();
        })
        .catch(error => console.error('Error fetching Markdown:', error));

    function addCopyButtons() {
        const textBlocks = document.querySelectorAll('.markdown-body pre code');
        textBlocks.forEach((block, index) => {
            block.setAttribute("id", `code-block-${index + 1}`)
            const copyButton = document.createElement('button');
            copyButton.textContent = 'Copy';
            copyButton.className = 'copy-button';
            copyButton.dataset.clipboardTarget = `#code-block-${index + 1}`;
            block.parentNode.appendChild(copyButton);
        });

        const copyButtons = document.querySelectorAll('.copy-button');
        const clipboard = new ClipboardJS(copyButtons);

        clipboard.on('success', function (e) {
            e.clearSelection();
            e.trigger.textContent = 'Copied!';
        });

        clipboard.on('error', function (e) {
            e.trigger.textContent = 'Press Ctrl+C to copy';
        });
    }
</script>
</body>
</html>
