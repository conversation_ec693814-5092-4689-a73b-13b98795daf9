<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Document</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="description" content="Description">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
    <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/vue.css">
    <link rel="stylesheet" href="//unpkg.com/gitalk/dist/gitalk.css">

    <link rel="shortcut icon" href="hema.ico" type="image/x-icon">
    <link rel="icon" href="hema.ico" type="image/x-icon">
</head>


<body>
<div id="app"></div>


<script>
    window.$docsify = {
        name: '发财暴富客服话术',
        loadSidebar: true,
        sidebarDisplayLevel: 1, // set sidebar display level
        autoHeader: true, // 自动给文章加一个title，名字就是文件名
        homepage: 'huashu.md',
        showLevel: false,
        toc: {
            tocMaxLevel: 6,
            target: 'h1, h2, h3, h4, h5, h6'
        },
        copyCode: {
            buttonText: 'Copy to clipboard',
            errorText: 'Error',
            successText: 'Copied'
        },
        scrollToTop: {
            auto: true,
            text: 'Top',
            right: 15,
            bottom: 15,
            offset: 300
        },
        search: {
            paths: 'auto',
            placeholder: '搜索',
            noData: '找不到结果',
            depth: 3,
        },
        count: {
            countable: true,
            fontsize: '0.9em',
            color: 'rgb(90,90,90)',
            language: 'chinese'
        }
    }

</script>

<!-- 侧边栏折叠 -->
<script src="//cdn.jsdelivr.net/npm/docsify-sidebar-collapse/dist/docsify-sidebar-collapse.min.js"></script>

<!-- 代码复制 -->
<script src="//unpkg.com/docsify-copy-code@2.1.1/dist/docsify-copy-code.min.js"></script>

<!-- 回到顶部 -->
<script src="//unpkg.com/docsify-scroll-to-top/dist/docsify-scroll-to-top.min.js"></script>

<!-- 图片缩放 -->
<script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/zoom-image.min.js"></script>

<!-- 搜索 -->
<script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/search.min.js"></script>

<!-- 字数统计 -->
<script src="//unpkg.com/docsify-count/dist/countable.js"></script>

<!-- Docsify v4 -->
<script src="//cdn.jsdelivr.net/npm/docsify@4"></script>
<script src="https://unpkg.com/docsify-plugin-toc@1.2.1/dist/docsify-plugin-toc.min.js"></script>

<!--<script src="docsify-toc-min.js"></script>-->
<script src="md5.min.js"></script>

</body>
</html>
