# encoding=utf-8
import json
import traceback
from typing import Union

import requests
import telnyx

TELNYX_API_KEY = "**********************************************************"
TELNYX_OUTBOUND_VOICE_PROFILE = "2201593832611513488"
TELNYX_PUBLIC_KEY = "84W+FlInraGv7d1K2SvcMMJhPwfOIwpnFIgcU9njNHQ="
TELNYX_CONNECTION_ID = "2201611210929996890"
TELNYX_MESSAGING_PROFILE_ID = "4001891c-3745-4477-bf69-90749dd81d0d"
TELNYX_VOICE_API_APPLICATION_ID = "2151324095860442440"
TELNYX_TEXML_APPLICATION_ID = "2201610292201260090"
TELNYX_IOS_PUSH_CREDENTIAL_ID = "6d0e3aac-234b-42af-b025-5d5e1cf38d73"
TELNYX_BILLING_GROUP_ID = "72c8856f-0a46-4c18-8236-5e2f070087ef"
STATIC_URL_BASE2 = 'http://phone2.zehougroup.xyz'


class DemoTelnyxUtil:
    @staticmethod
    def create_credential_id(user_id: int, sip_connection_id: str) -> (str, str, str):
        url = "https://api.telnyx.com/v2/telephony_credentials"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {TELNYX_API_KEY}"
        }
        data = {
            "connection_id": sip_connection_id
        }

        response = requests.post(url, headers=headers, json=data)
        rsp = json.loads(response.content)
        if "data" not in rsp:
            print(f"[TelnyxUtil.create_credential_id] user_id:{user_id}, failed: {response.text}")
            return "", "", ""

        credential_id = rsp["data"]["id"]
        sip_username = rsp["data"]["sip_username"]
        sip_password = rsp["data"]["sip_password"]
        print(f"[TelnyxUtil.create_credential_id] user_id:{user_id}, credential_id:{credential_id}, rsp: {rsp}")
        return credential_id, sip_username, sip_password

    @staticmethod
    def create_access_token(user_id: int, credential_id) -> str:
        url = f"https://api.telnyx.com/v2/telephony_credentials/{credential_id}/token"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {TELNYX_API_KEY}"
        }
        data = {}

        print(f"TelnyxUtil.create_access_token {credential_id}")
        response = requests.post(url, headers=headers, json=data)
        token = response.content.decode('utf-8')
        if "error" in token:
            print(f"[TelnyxUtil.create_access_token] user_id:{user_id}, {credential_id}, token: {token}")
            return "error"
        print(f"[TelnyxUtil.create_access_token] user_id:{user_id}, {credential_id}, token: {token}")
        return token

    @staticmethod
    def create_a_sip_connection(user_id: int, username: str, password: str) -> Union[str, None]:
        url = 'https://api.telnyx.com/v2/credential_connections'

        headers = {
            'Authorization': f'Bearer {TELNYX_API_KEY}',
            'Content-Type': 'application/json'
        }
        data = {
            "active": True,
            "anchorsite_override": "Latency",
            "connection_name": username,
            "default_on_hold_comfort_noise_enabled": True,
            "dtmf_type": "RFC 2833",
            "encode_contact_header_enabled": True,
            "encrypted_media": None,
            "inbound": {
                "ani_number_format": "+E.164-national",
                "channel_limit": 10,
                "codecs": ["G722", "G711U", "G711A", "G729", "OPUS", "H.264", "VP8", "AMR-WB"],
                "default_routing_method": "sequential",
                "dnis_number_format": "national",
                # string Default: "e164", Enum: "+e164" "e164" "national" "sip_username"
                "generate_ringback_tone": False,
                "instant_ringback_enabled": True,
                "isup_headers_enabled": True,
                "prack_enabled": True,
                "privacy_zone_enabled": True,
                "sip_compact_headers_enabled": True,
                "timeout_1xx_secs": 60,
                "timeout_2xx_secs": 60,
                "third_party_control_enabled": True,
            },
            "ios_push_credential_id": TELNYX_IOS_PUSH_CREDENTIAL_ID,
            "onnet_t38_passthrough_enabled": True,
            "outbound": {
                "ani_override": "",
                "ani_override_type": "always",
                "call_parking_enabled": False,
                "channel_limit": 10,
                "generate_ringback_tone": False,
                "instant_ringback_enabled": True,
                "localization": "US",
                "outbound_voice_profile_id": f"{TELNYX_OUTBOUND_VOICE_PROFILE}",
                "t38_reinvite_source": "telnyx"
            },
            "password": password,
            "rtcp_settings": {
                "capture_enabled": True,
                "port": "rtp+1",
                "report_frequency_seconds": 10
            },
            "sip_uri_calling_preference": "unrestricted",  # internal, disabled, unrestricted
            "user_name": username,
            "webhook_api_version": "2",
            "webhook_event_failover_url": "https://phone2.zehougroup.xyz/zhphone/phone/call/sipconnection/webhook/failed/v2/",
            "webhook_event_url": "https://phone2.zehougroup.xyz/zhphone/phone/call/sipconnection/webhook/v2/",
            "webhook_timeout_secs": 25
        }

        response = requests.post(url, json=data, headers=headers)
        print(f"[TelnyxUtil.create_a_sip_connection] user_id: {user_id}, rsp:{response.text}")
        rsp = json.loads(response.content)
        if 'data' not in rsp:
            if "has already been taken" in response.text:
                DemoTelnyxUtil.get_number_sip_connection(f"txtnow{user_id}")

            print(f"[TelnyxUtil.create_a_sip_connection] user_id: {user_id}, failed rsp:{response.text}")
            return None
        sip_connection_id = rsp['data']['id']
        return sip_connection_id

    @staticmethod
    def send_sms(from_: str, to_: str, text: str) -> str:
        print(f"[TelnyxUtil.send_sms] from:{from_}, to:{to_}, text:{text}")

        rsp = telnyx.Message.create(
            to=to_,
            from_=from_,
            text=text,
            webhook_url=STATIC_URL_BASE2 + "/zhphone/phone/outbound/sms/webhook/",
            webhook_failover_url=STATIC_URL_BASE2 + "/zhphone/phone/sms/outbound/failed/",
        )
        print(f"[TelnyxUtil.send_sms] from:{from_}, to:{to_}, text:{text}, success")
        message_id = rsp["id"]
        return message_id

    @staticmethod
    def send_mms(from_: str, to_: str, image_url: str) -> str:
        print(f"[TelnyxUtil.send_mms] from:{from_}, to:{to_}, image_url:{image_url}")
        rsp = telnyx.Message.create(
            to=to_,
            from_=from_,
            media_urls=[image_url],
            webhook_url=STATIC_URL_BASE2 + "/zhphone/phone/outbound/sms/webhook/",
            webhook_failover_url=STATIC_URL_BASE2 + "/zhphone/phone/sms/outbound/failed/",
        )
        print(f"[TelnyxUtil.send_mms] from:{from_}, to:{to_}, image_url:{image_url}, rsp: {rsp}")
        message_id = rsp["id"]
        return message_id

    @staticmethod
    def get_number_id(number: str) -> str:
        telnyx_resp = telnyx.NumberOrder.list(filter={"phone_number": number})
        if "data" not in telnyx_resp:
            print(f"[TelnyxUtil.get_number_id] get number:{number} id failed, telnyx rsp:{telnyx_resp}")
            return ""

        if "phone_numbers" not in telnyx_resp["data"] or len(telnyx_resp["data"]["phone_numbers"]) == 0:
            print(f"[TelnyxUtil.get_number_id] get number:{number} id failed, telnyx rsp:{telnyx_resp}")
            return ""

        number_sid = telnyx_resp["data"]["phone_numbers"][0]["id"]
        number_2 = telnyx_resp["data"]["phone_numbers"][0]["phone_number"]
        print(f"[TelnyxUtil.get_number_id] get number:{number}, sid:{number_sid}, number from telnyx:{number_2}")

        return number_sid

    @staticmethod
    def buy_number(number: str) -> str:
        print(f"[TelnyxUtil.buy_number] number:{number}")
        rsp = telnyx.NumberOrder.create(
            phone_numbers=[{"phone_number": number}],
            messaging_profile_id=TELNYX_MESSAGING_PROFILE_ID,
            connection_id=TELNYX_TEXML_APPLICATION_ID,
            billing_group_id=TELNYX_BILLING_GROUP_ID,
        )
        print(f"[TelnyxUtil.buy_number] number:{number}, rsp: {rsp}")
        number_sid = rsp["phone_numbers"][0]["id"]
        return number_sid

    @staticmethod
    def search_number(country: str, area_code: str):
        try:
            if not country:
                country = "US"

            telnyx.api_key = TELNYX_API_KEY

            if not area_code:
                phone_number_list = telnyx.AvailablePhoneNumber.list(
                    filter={
                        'country_code': country,  # Change to the country code you're interested in
                        'limit': 20,  # Change this to get more numbers,
                        "best_effort": True,
                    }
                )
                print(
                    f"[TelnyxUtil.search_number] country:{country}, no area_code, size: {len(phone_number_list)}")
            elif area_code and len(area_code) >= 3:
                phone_number_list = telnyx.AvailablePhoneNumber.list(
                    filter={"country_code": country,
                            "national_destination_code": area_code[0:3],
                            "limit": 20,
                            "best_effort": True,
                            }
                )
                print(f"[TelnyxUtil.search_number] country:{country}, area_code:{area_code}, "
                      f"size: {len(phone_number_list)}")
            else:
                phone_number_list = telnyx.AvailablePhoneNumber.list(
                    filter={'country_code': country,
                            'limit': 20,
                            "best_effort": True,
                            }
                )
                print(f"[TelnyxUtil.search_number] country:{country}, invalid area_code:{area_code}, "
                      f"search without area_code, size: {len(phone_number_list)}")
            if "data" not in phone_number_list:
                print(f"[TelnyxUtil.search_number] country:{country}, area:{area_code} has no result")
                return []

            res = []
            for index, phone in enumerate(phone_number_list["data"]):
                print(index, phone)
                number = phone["phone_number"]
                region = number[2:5]
                friendly_name = f"({region}){number[5:8]}-{number[8:]}"
                j = {'country': country,
                     'friendly_name': friendly_name,
                     'region': region,
                     'phone_number': number}
                res.append(j)
            print(f"[TelnyxUtil.search_number] country:{country}, area:{area_code} result: {len(res)}")
            if len(res) == 0:
                print(f"[TelnyxUtil.search_number] country:{country}, area:{area_code} result is 0")
            return res
        except Exception:
            print(f"[TelnyxUtil.search_number] country:{country}, area:{area_code} failed")
            return []

    @staticmethod
    def change_to_p2p(number: str):
        """
        https://developers.telnyx.com/docs/v2/messaging/messages/resources/traffic/traffic_type/#python

        :param number:
        :return:
        """
        res = telnyx.MessagingPhoneNumber.retrieve(number)
        print(f"[TelnyxUtil.change_to_p2p] number:{number}, before: {res}")
        res.messaging_product = "P2P"
        res.save()

        res = telnyx.MessagingPhoneNumber.retrieve(number)
        print(f"[TelnyxUtil.change_to_p2p] number:{number}, after: {res}")
        return res

    @staticmethod
    def get_all_numbers_from_telnyx():
        # 设置Telnyx API认证信息
        api_key = telnyx.api_key
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # 发送请求获取号码列表
        response = requests.get("https://api.telnyx.com/v2/phone_numbers", headers=headers)
        phone_numbers = response.json()["data"]

        numbers = []
        # 遍历号码列表并打印每个号码
        for number in phone_numbers:
            print(number)
            print(f"[TelnyxUtil.get_all_numbers_from_telnyx] {number['phone_number']}")
            numbers.append(number['phone_number'])

        return numbers

    @staticmethod
    def get_sms_cost_by_id(sms_id: str):
        sms = telnyx.Message.retrieve(sms_id)
        print(sms)
        print("cost:", sms.get("cost", {}).get("cost"))
        return sms.get("cost", {}).get("cost")

    @staticmethod
    def get_sms_info_by_id(sms_id: str):
        sms = telnyx.Message.retrieve(sms_id)
        print(f"[TelnyxUtil.get_sms_info_by_id] sid:{sms_id}, info:{sms}")
        return sms

    @staticmethod
    def get_call_cost_by_id(call_id: str):
        try:
            response = telnyx.Call.retrieve(call_id)
            print(response)
        except telnyx.error.TelnyxError as e:
            print("Failed to retrieve call cost:", str(e))
            return None

    @staticmethod
    def list_calls():
        api_key = telnyx.api_key
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        connection_id = TELNYX_CONNECTION_ID
        url = "https://api.telnyx.com/v2/connections/" + connection_id + "/active_calls"

        query = {
            "page[limit]": "20"
        }

        response = requests.get(url, headers=headers, params=query)

        data = response.json()
        print(data)

    @staticmethod
    def list_credential_connection():
        connections = telnyx.CredentialConnection.list()
        for c in connections:
            print(c)

    @staticmethod
    def create_sip_user_connection(username):
        connection = telnyx.CredentialConnection.create(
            connection_name="SIPUser_" + username,
            user_name=username,
            password="123qweQWE",
            outbound_voice_profile_id=TELNYX_OUTBOUND_VOICE_PROFILE,
            inbound_voice_profile_id="YOUR_INBOUND_VOICE_PROFILE_ID",
            push_credential_id=TELNYX_IOS_PUSH_CREDENTIAL_ID
        )
        connection.update({
            "outbound": {
                "outbound_voice_profile_id": TELNYX_OUTBOUND_VOICE_PROFILE
            }
        })
        return connection

    @staticmethod
    def list_telephony_credential():
        credential_list = telnyx.TelephonyCredential.list()
        for c in credential_list:
            print(c)

    @staticmethod
    def delete_a_number(number: str) -> bool:
        # https://developers.telnyx.com/openapi/spec/tag/Number-Configurations-API/#tag/Number-Configurations-API/operation/deletePhoneNumber
        try:
            print(f"[TelnyxUtil.delete_a_number] {number} ...")
            url = "https://api.telnyx.com/v2/phone_numbers"
            query = {
                "filter[phone_number]": number,
            }

            headers = {"Authorization": f"Bearer {TELNYX_API_KEY}"}
            response = requests.get(url, headers=headers, params=query)
            data = response.json()
            print(f"[TelnyxUtil.delete_a_number] {number}, rsp:{data}")
            if len(data['data']) == 0:
                print(f"[TelnyxUtil.delete_a_number] {number} not in Telnyx...")
                print(f"[TelnyxUtil.delete_a_number] {number} not in Telnyx...")
                return False

            for i in data['data']:
                delete_url = "https://api.telnyx.com/v2/phone_numbers/" + i['id']
                print(f"[TelnyxUtil.delete_a_number] {number}, url:{delete_url}")
                response = requests.delete(delete_url, headers=headers)
                delete_data = response.json()
                print(f"[TelnyxUtil.delete_a_number] {number}, delete_data:{delete_data}")
                if delete_data['data']['status'] == 'deleted' and delete_data['data']['phone_number'] == number:
                    print(f"[TelnyxUtil.delete_a_number] {number}, delete success!")
                    return True

            print(f"[TelnyxUtil.delete_a_number] {number}, delete failed!")
            return False
        except Exception:
            print(f"[TelnyxUtil.delete_a_number] {number} failed")
            traceback.print_exc()
            return False

    @staticmethod
    def get_number_sip_connection(number: str) -> str:
        # https://developers.telnyx.com/openapi/spec/tag/Number-Configurations-API/#tag/Number-Configurations-API/operation/deletePhoneNumber
        try:
            print(f"[TelnyxUtil.get_number_sip_connection] {number}...")
            url = "https://api.telnyx.com/v2/phone_numbers"
            query = {
                "filter[phone_number]": number,
            }

            headers = {"Authorization": f"Bearer {TELNYX_API_KEY}"}
            response = requests.get(url, headers=headers, params=query)
            data = response.json()
            print(f"[TelnyxUtil.get_number_sip_connection] {number}, rsp:{data}")
            if len(data['data']) == 0:
                print(f"[TelnyxUtil.get_number_sip_connection] {number} not in Telnyx...")
                return ""

            for i in data['data']:
                phone_number = telnyx.PhoneNumber.retrieve(i['id'])
                return phone_number.connection_id

            print(f"[TelnyxUtil.get_number_sip_connection] {number}, get sip_connection failed!")
            return ""
        except Exception:
            print(f"[TelnyxUtil.get_number_sip_connection] {number} failed")
            traceback.print_exc()
            return ""

    @staticmethod
    def update_number_sip_connection(number: str, sip_connection_name: str, sip_connection_id: str) -> bool:
        # https://developers.telnyx.com/openapi/spec/tag/Number-Configurations-API/#tag/Number-Configurations-API/operation/deletePhoneNumber
        try:
            print(f"[TelnyxUtil.update_number_sip_connection] {number}, "
                  f"sip_connection_name:{sip_connection_name}, sip_connection_id:{sip_connection_id}")
            url = "https://api.telnyx.com/v2/phone_numbers"
            query = {
                "filter[phone_number]": number,
            }

            headers = {"Authorization": f"Bearer {TELNYX_API_KEY}"}
            response = requests.get(url, headers=headers, params=query)
            data = response.json()
            print(f"[TelnyxUtil.update_number_sip_connection] {number}, rsp:{data}")
            if len(data['data']) == 0:
                print(f"[TelnyxUtil.update_number_sip_connection] {number} not in Telnyx...")
                return False

            for i in data['data']:
                phone_number = telnyx.PhoneNumber.retrieve(i['id'])
                phone_number.connection_id = sip_connection_id
                phone_number.connection_name = sip_connection_name
                phone_number.save()
                return True

            print(f"[TelnyxUtil.update_number_sip_connection] {number}, update failed!")
            return False
        except Exception:
            print(f"[TelnyxUtil.update_number_sip_connection] {number} failed")
            traceback.print_exc()
            return False

    @staticmethod
    def is_sip_connection_registered(sip_connection_id: str) -> bool:
        try:
            url = f"https://api.telnyx.com/v2/credential_connections/{sip_connection_id}"
            headers = {"Authorization": f"Bearer {TELNYX_API_KEY}"}
            response = requests.get(url, headers=headers)
            print(f"[TelnyxUtil.is_sip_connection_not_registered] rsp:{response.content}")
            data = response.json()
            if 'data' not in data:
                return False

            return data['data']['registration_status'] == "Registered"
        except Exception:
            print(f"[TelnyxUtil.is_sip_connection_not_registered] sip_connection_id:{sip_connection_id}")
            traceback.print_exc()
            return False

    @staticmethod
    def get_sip_connection_by_sip_username(sip_username: str):
        url = "https://api.telnyx.com/v2/credential_connections"

        query = {
            "page[number]": "1",
            "page[size]": "20",
            "filter[connection_name][contains]": sip_username,
            "sort": "connection_name"
        }

        headers = {"Authorization": f"Bearer {TELNYX_API_KEY}"}

        response = requests.get(url, headers=headers, params=query)

        data = response.json()
        print(data)
        if len(data['data']) == 0:
            return None, None, None

        sip_connection_id = data['data'][0]["id"]
        sip_password = data['data'][0]["password"]
        sip_username = data['data'][0]["user_name"]
        return sip_connection_id, sip_username, sip_password

    @staticmethod
    # 给号码设置标签
    def set_phone_number_tags(phone_number_id: str, tags: list):
        headers = {
            'Authorization': f'Bearer {TELNYX_API_KEY}',
            'Content-Type': 'application/json'
        }
        data = {
            "tags": tags
        }
        response = requests.patch(f"https://api.telnyx.com/v2/phone_numbers/{phone_number_id}", json=data,
                                  headers=headers)
        return response.json()


if __name__ == '__main__':
    # show number details
    test_number = "+15802601115"
    telnyx.api_key = "**********************************************************"

    # 设置标签
    DemoTelnyxUtil.set_phone_number_tags("2545200798464214939", ["v2number"])

    # # 购买号码
    # DemoTelnyxUtil.buy_number("+16063801207")

    # 获取短信 info
    # TelnyxUtil.get_sms_info_by_id("403188f2-1574-47d7-82fe-a43dbbd897e7")

    # 获取电话 info
    # TelnyxUtil.get_call_cost_by_id('v3:c6xrCgwSLQYkKxflvtcUoSgIQzgn7e3yOmUrKBHt3dqzd84683wwNQ')

    # list all calls
    # TelnyxUtil.list_calls()

    # 创建 sip_user
    # TelnyxUtil.create_sip_user_connection("fatpo2")
    # TelnyxUtil.list_credential_connection()

    # 列表 list_telephony_credential
    # TelnyxUtil.list_telephony_credential()

    # list_resp = telnyx.NumberOrder.list()
    #
    # print("Success: %r" % (list_resp))
    #
    # list_resp = telnyx.NumberOrder.list(filter={"phone_number": test_number})
    #
    # print("Success: %r" % (list_resp))
    #
    # # return nothing
    # r = telnyx.PortingPhoneNumber.list(filter={"phone_number": test_number})
    # print(r)
    #
    # # return nothing
    # r = telnyx.PortingPhoneNumber.list(page={
    #     "number": 1,
    # })
    # print(r)

    # # The grammar of  API Document PYTHON demo is not even correct
    # r = telnyx.PortingPhoneNumber.list(page: { number: 1, size: 20})
    # print(r)

    # resp = telnyx.AvailablePhoneNumber.list(filter={"country_code": "US", "limit": 10})
    # print(resp)

    # resp = telnyx.AvailablePhoneNumber.list(
    #     filter={"country_code": "US", "limit": 10, })
    # print(resp)

    # resp = telnyx.NumberOrder.create(
    #     phone_numbers=[{"phone_number": "+16062270590"}]
    # )
    # print(resp)

    # 搜索号码
    # res = telnyx.AvailablePhoneNumber.list(
    #     filter={"country_code": "US",
    #             "limit": 20,
    #             "features": ["sms", "mms", "voice"],
    #             }
    # )
    # print(res)

    # 查看号码属性
    # res = telnyx.MessagingPhoneNumber.retrieve("+15075096284")
    # print(res)
    #
    # res = TelnyxUtil.change_to_p2p("+15075096284")
    # print(res)

    # 获取全部号码
    # numbers = TelnyxUtil.get_all_numbers_from_telnyx()
    # print(numbers)
    # print(len(numbers))

    #
    # print("start Connection...")
    # r = telnyx.Connection.list(page={"number": 1, "size": 20})
    # print(r)
    #
    # print("start Comment...")
    # r = telnyx.CredentialConnection.retrieve('2159407315478906123')
    # print(r)
    # print("start...")

    # 根据号码查看号码 info
    # r = telnyx.NumberOrder.list(filter={"phone_numbers.phone_number": ["+14162739607"]})
    # print(r)

    # print("start....")
    # TelnyxUtil.init_phone_number("+16062210527")
    # TelnyxUtil.init_phone_number("+16062210523")
    # TelnyxUtil.init_phone_number("+16062210522")
    # print("end.")

    # 搜索号码
    # DemoTelnyxUtil.search_number("US", "315")

    # 删除号码
    # TelnyxUtil.delete_a_number("+19162348030")

    # 更新号码
    # TelnyxUtil.update_number_sip_connection("+16475764958", "v3phone5630", "2270477499127301417")

    # 获取 sip-connection
    # url = "https://api.telnyx.com/v2/credential_connections/2263999993666864867"
    # headers = {"Authorization": f"Bearer {TELNYX_API_KEY}"}
    # response = requests.get(url, headers=headers)
    # data = response.json()
    # print(data)

    # 查一下这个 sip
    # print(DemoTelnyxUtil.get_sip_connection_by_sip_username("txtnow1115907 "))
    #
    # DemoTelnyxUtil.is_sip_connection_registered("2293863143060801416")
    # DemoTelnyxUtil.is_sip_connection_registered("2283544840228570853")

    # 获取sms cost
    # DemoTelnyxUtil.get_sms_cost_by_id("8a847c1a-7ab6-4fd7-8e29-f51a1d08fe27")
    # DemoTelnyxUtil.get_call_cost_by_id("v3:-1UF-4uovpJnm-nFhtbI0zLt82H4xcoDYyivfh5E1P_xGlWcjjvluA")
