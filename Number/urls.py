from django.conf.urls import url

from Number import numberviews

urlpatterns = [
    # txtnow - telnyx
    url(r'^zhphone/number/search/$', numberviews.SearchNumber.as_view()),
    url(r'^zhphone/number/search/self/$', numberviews.SearchNumberForSelf.as_view()),
    url(r'^zhphone/number/lock/$', numberviews.LockNumber.as_view()),

    # 后台工具
    url(r'^zhphone/number/update_status/$', numberviews.UpdateNumberStatus.as_view()),
    url(r'^zhphone/number/release_backend_tool/$', numberviews.ReleaseNumberBackendTool.as_view()),
]
