from django.contrib import admin

from Number.models import NumberUsed, NumberInventory, NumberAdminLock, NumberLocked


@admin.register(NumberUsed)
class NumberUsedAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'number_id', 'number', 'type',
                    'expired_at', 'status', 'created_at', 'updated_at',)
    search_fields = ('user_id', 'number_id', 'number', 'created_at',)
    list_filter = ('created_at',)
    readonly_fields = ('user_id', 'number_id', 'number', 'created_at', 'updated_at',)
    exclude = ('tw_version',)  # 排除 tw_version 字段
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(NumberInventory)
class NumberInventoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'number', 'sid', 'status', 'created_at', 'updated_at', 'release_at',)
    search_fields = ('id', 'number', 'created_at',)
    list_filter = ('created_at',)
    exclude = ('tw_version',)  # 排除 tw_version 字段
    readonly_fields = ('number', 'sid', 'friendly_name', 'created_at', 'updated_at', 'tw_version', 'platform',)
    list_per_page = 50  # 设置每页显示 50 条记录

    def get_queryset(self, request):
        return NumberInventory.objects.all()


@admin.register(NumberLocked)
class NumberLockedAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'number', 'created_at', 'updated_at',)
    search_fields = ('user_id', 'number',)
    list_filter = ('created_at',)
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(NumberAdminLock)
class NumberAdminLockAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'number', 'expired_at', 'created_at', 'updated_at',)
    search_fields = ('user_id', 'number',)
    list_filter = ('created_at',)
    list_per_page = 50  # 设置每页显示 50 条记录
