import threading
from threading import Timer

from django.core.cache import cache
from django.utils.decorators import method_decorator

from Common.err import ErrInfo
from Common.ratelimit.decorators import ratelimit
from Common.rediskey import <PERSON>isKey
from Common.timeutil import TimeUtil
from Common.views import SecP<PERSON>View
from MyTelnyx.my_telnyx import TelnyxUtil
from Number.models import NumberLocked, NumberInventory, NumberUsed
from Number.tools.number_buy_tool import NumberBuyTool
from Number.tools.number_refresh_status_tool import NumberRefreshStatusTool
from Number.tools.number_release_tool import NumberReleaseTool
from Number.tools.number_search_tool import NumberSearchTool
from Number.tools.number_tool import NumberTool
from Order.models import Order
from Order.tools.tool_order import OrderTool
from SecPhone import settings
from SecPhone.settings import logger
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool


class SearchNumber(SecPhoneView):

    @SecPhoneView.VerifySign
    @SecPhoneView.VerifyToken
    def post(self, request):
        # {'country': 'US', 'page_size': 20, 'area_code': '409', 'page_number': 4}
        post_data, err_code, err_msg = self.ReadPostJson(request)
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        header = self.GetHeaderInRequest(request)
        userid = header['userid']
        logger.info(f"[SearchNumber] user_id={userid}, param: {post_data}")

        page_number = post_data.get("page_number", 1)
        _area_code = post_data.get('area_code', '')
        country = post_data.get('country', 'US').upper()

        # 确保 area_code 干净
        area_code = NumberTool.get_clean_area_code(area_code=_area_code)
        if area_code and len(area_code) < 3:
            logger.info(
                f"[SearchNumber] user_id={userid}, country:{country}, area_code:{area_code}, simple search in db")
            data = NumberSearchTool.search_in_db(userid, page_number, country, area_code)
            return self.ReturnSuccess(data)

        # 放弃 toll-free
        if area_code in settings.TOLL_FREE_AREA_CODES:
            logger.warning(
                f"[SearchNumber] user_id={userid}, country:{country}, area_code:{area_code}, not support toll free")
            return self.ReturnError(ErrInfo.NUMBER_SEARCH_ERROR, "Toll-free numbers are currently not supported")

        # 没有P2P牌照， 放弃加拿大
        if country == "CA":
            logger.info(
                f"[SearchNumber] user_id={userid}, country:{country}, area_code:{area_code}, not support canada number")
            return self.ReturnError(ErrInfo.NUMBER_SEARCH_ERROR, "Canada numbers are currently not supported")
        if area_code in settings.canada_area_codes:
            logger.info(
                f"[SearchNumber] user_id={userid}, country:{country}, area_code:{area_code}, not support canada number")
            return self.ReturnError(ErrInfo.NUMBER_SEARCH_ERROR, "Canada numbers are currently not supported")

        # check inventory size
        inventory_size = NumberSearchTool.get_inventory_size()
        logger.info(f"[SearchNumber] user_id={userid}, inventory_size: {inventory_size}")

        # 判断用户是否允许无视号码池数量，直接搜索 telnyx
        key = RedisKey.GenIsAllowSearchVendorDirectly(userid)
        is_allow_search_vendor = cache.get(key)
        if is_allow_search_vendor:
            logger.warning(f"[SearchNumber] user_id={userid}, inventory_size: {inventory_size}, allow_search_vendor")
            data = NumberSearchTool.search_with_telnyx(userid, page_number, country, area_code)
            if len(data["list"]) == 0:
                data = NumberSearchTool.search_in_db(userid, 1, country, area_code="")
            return self.ReturnSuccess(data)

        # 检查是否全局禁用第三方搜索
        close_vendor_key = RedisKey.GenCloseSearchVendor()
        is_close_vendor_search = cache.get(close_vendor_key)
        if is_close_vendor_search and inventory_size > 30:
            logger.warning(f"[SearchNumber] user_id={userid}, inventory_size: {inventory_size}, close_vendor_search")
            data = NumberSearchTool.search_in_db(userid, page_number, country, area_code)
            if len(data["list"]) == 0:
                data = NumberSearchTool.search_in_db(userid, 1, country, area_code="")
            return self.ReturnSuccess(data)

        if inventory_size <= 50:
            data = NumberSearchTool.search_with_telnyx(userid, page_number, country, area_code)
            if len(data["list"]) == 0:
                data = NumberSearchTool.search_in_db(userid, 1, country, area_code="")
            return self.ReturnSuccess(data)
        else:
            data = NumberSearchTool.search_in_db(userid, page_number, country, area_code)
            if len(data["list"]) == 0:
                data = NumberSearchTool.search_in_db(userid, 1, country, area_code="")
            return self.ReturnSuccess(data)


class SearchNumberForSelf(SecPhoneView):

    def post(self, request):
        post_data, err_code, err_msg = self.ReadPostJson(request)
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        token = post_data.get("token")
        if token != 'fatpo-jiejie':
            logger.error(f"[SearchNumberForSelf] invalid token:{token}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

        area_code = post_data.get('area_code', '')
        country = post_data.get('country', 'US').upper()

        res_telnyx = TelnyxUtil.search_number(country, area_code)
        return self.ReturnSuccess({"list": res_telnyx, "db_size": NumberSearchTool.get_inventory_size()})


class LockNumber(SecPhoneView):

    @staticmethod
    def send_push_notification(userid: int, number: str):
        try:
            order = OrderTool.get_user_order(userid)
            if order:
                system_content, db_content, push_content = SmsNoticeTool.lock_number_success(number)
            else:
                system_content, db_content, push_content = SmsNoticeTool.lock_number_half_success(number)

            SmsItSupportTool.add_support_sms_v2(userid, settings.SMS_DIRECTION_RECEIVE,
                                                settings.APP_IT_SUPPORT_SHOW_PHONE,
                                                NumberTool.get_mock_number_by_userid(userid), db_content=db_content,
                                                push_content=push_content)
        except Exception:
            logger.error(f"[LockNumber.send_push_notification] user:{userid} {number} failed", exc_info=True)
            return

    @SecPhoneView.VerifySign
    @SecPhoneView.VerifyToken
    def post(self, request):
        post_data, err_code, err_msg = self.ReadPostJson(request, ('number',))
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        header = self.GetHeaderInRequest(request)
        userid = header['userid']
        if NumberTool.GetNumberByUserId(userid) is not None:
            logger.info(f'[lock number] user {userid} already has a number')
            return self.ReturnError(ErrInfo.USER_ALREADY_HAS_PHONE_NUMBER)

        number = post_data['number']
        if NumberTool.is_number_being_used(number) is True:
            logger.info(f'[lock number] number ({number}) already be used')
            return self.ReturnError(ErrInfo.NUMBER_ALREADY_BEING_USED)

        # 存入数据库，以备后续观察
        lock_num = NumberLocked(user_id=userid, number=number)
        lock_num.save()

        lock_number_key = RedisKey.GenLockNumberKey(number)
        lock_number_user_key = RedisKey.GenLockNumberUserKey(userid)
        lock_number_userid = cache.get(lock_number_key)
        logger.info(f"[lock number] lock_number_key is {lock_number_key},  lock_number_userid is {lock_number_userid}")

        # 没人锁过这个号码或者锁号码的人就是自己
        if lock_number_userid is None or lock_number_userid == userid:
            if OrderTool.is_user_vip_expire(userid) == ErrInfo.SUCCESS:
                logger.info(f"[lock number] user {userid} is vip, just buy {number}...")
                is_success, err_msg = NumberBuyTool.buy_number(userid, number)
                if is_success:
                    # 顺便更新下订单表的number_id
                    number_used = NumberTool.GetNumberObjByUserId(userid)
                    if number_used:
                        logger.info(f"[lock number] user {userid} get number {number_used.number_id}, update order")
                        Order.objects.filter(user_id=userid).update(number_id=number_used.number_id)
                    else:
                        logger.error(f"[lock number] user {userid} get no number to update order's number_id")
                    return self.ReturnSuccess(data={"number": number})
                else:
                    logger.error(f"[lock number] user {userid} is vip, but buy number {number} error, {err_msg}.")
                    return self.ReturnError(err_msg)
            else:
                # 没人锁过这个号码或者锁号码的人就是自己，但不是VIP，暂时锁定3分钟
                cache.set(lock_number_key, userid, settings.LOCK_NUMBER_EXPIRE_SECONDS)
                cache.set(lock_number_user_key, number, settings.LOCK_NUMBER_EXPIRE_SECONDS)

                # 延迟推送提醒
                t = Timer(4 * 60, self.send_push_notification, (userid, number,))
                t.start()

                logger.info(f"[lock number] user {userid} is not a vip, lock number {number} success")
                return self.ReturnSuccess(data={"number": number})

        # 这是别人锁定了号码，返回错误
        logger.info(
            f"[lock number] user {userid} lock number {number} failed, already lock by {lock_number_userid}!")
        return self.ReturnError(ErrInfo.NUMBER_LOCK_BY_OTHER_USER)


class UpdateNumberStatus(SecPhoneView):

    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, ("token",))
        if err_code != 0:
            logger.info(f"[UpdateNumberStatus] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(err_code, err_msg)
        if data['token'] != 'fatpomiehahage!':
            logger.error(f"[UpdateNumberStatus] tokan failed")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

        logger.info(f"[UpdateNumberStatus] request: {request.GET}")
        t = threading.Thread(target=NumberRefreshStatusTool.do_update_number_status, args=())
        t.start()

        return self.ReturnSuccess()


def custom_sort(item) -> tuple:
    # 先按照使用次数排序（used_cnt 越大越靠前）
    # 如果 used_cnt 相同，则按照创建时间排序（created_at 越早越靠前）
    return -item["used_cnt"], item["created_at"]


class ReleaseNumberBackendTool(SecPhoneView):
    @staticmethod
    def worker(limit: int):
        try:
            all_expire_numbers = NumberInventory.objects.filter(status='EXPIRE')
            total = len(all_expire_numbers)
            logger.error(f"[ReleaseNumberBackendTool] START RELEASE, check release inventory number, cnt:{total}")
            do_release_cnt = 0
            release_failed_cnt = 0

            if total < settings.SKIP_RELEASE_WHEN_EXPIRE_NUMBER_CNT:
                logger.error(f"[ReleaseNumberBackendTool] number size: {total} < "
                             f"{settings.SKIP_RELEASE_WHEN_EXPIRE_NUMBER_CNT}, no need to release")
                return

            need_to_release_numbers = []
            for index, number in enumerate(all_expire_numbers):
                logger.info(f"[ReleaseNumberBackendTool] {index}/{total}, number:{number.number} checking...")
                if do_release_cnt > limit:
                    logger.info(f"[ReleaseNumberBackendTool] do_release_cnt:{do_release_cnt} > {limit} break")
                    break

                if NumberReleaseTool.is_need_release(number.id, number.number, number.created_at) is False:
                    logger.warning(
                        f"[ReleaseNumberBackendTool] {index}/{total}, number:{number.number} not need release!")
                    continue

                # check again in case user buy in this loop
                if NumberInventory.objects.filter(status='EXPIRE', id=number.id).count() == 0:
                    logger.error(f"[ReleaseNumberBackendTool] {index}/{total}, number:{number.number} not expired pass")
                    continue
                if NumberUsed.objects.filter(number=number.number, status='USING').exists():
                    logger.error(f"[ReleaseNumberBackendTool] {index}/{total}, number:{number.number} is used, pass!")
                    continue

                need_to_release_numbers.append({
                    "number": number.number,
                    "number_id": number.id,
                    "used_cnt": NumberTool.get_number_cnt_by_number_id(number.id),
                    "created_at": number.created_at,
                    "number_obj": number,
                })

            need_to_release_cnt = len(need_to_release_numbers)
            no_need_to_release_cnt = len(all_expire_numbers) - need_to_release_cnt

            # 使用 sorted 函数进行排序
            sorted_numbers = sorted(need_to_release_numbers, key=custom_sort)
            for index, n in enumerate(sorted_numbers):
                number_obj = n["number_obj"]
                number_id = n["number_id"]
                number = n["number"]
                used_cnt = n["used_cnt"]
                created_at = n["created_at"]
                logger.info(
                    f'[ReleaseNumberBackendTool] {index}/{need_to_release_cnt}, number:{number}, id:{number_id}, '
                    f'used_cnt:{used_cnt}, created_at:{created_at}  release doing...')

                # 达到limit阈值放弃
                if do_release_cnt >= limit:
                    logger.warning(
                        f'[ReleaseNumberBackendTool] {index}/{need_to_release_cnt}, number:{number}, id:{number_id}, '
                        f'do_release_cnt:{do_release_cnt} >= limit:{limit}, break')
                    break

                # 至少要保证号码池有100个号码
                if no_need_to_release_cnt + (
                        need_to_release_cnt - do_release_cnt) < settings.STOP_RELEASE_WHEN_EXPIRE_NUMBER_CNT_AT:
                    logger.info(
                        f'[ReleaseNumberBackendTool] {index}/{need_to_release_cnt}, number:{number}, id:{number_id}, '
                        f'need_to_release_cnt:{need_to_release_cnt}, release_cnt:{do_release_cnt}, '
                        f'no_need_to_release_cnt:{no_need_to_release_cnt} ',
                        f'total - release_cnt < {settings.STOP_RELEASE_WHEN_EXPIRE_NUMBER_CNT_AT}, break')
                    break

                if TelnyxUtil.delete_a_number(number):
                    number_obj.status = 'RELEASED'
                    number_obj.release_at = TimeUtil.GetNow()
                    number_obj.save()
                    do_release_cnt += 1
                    logger.info(
                        f'[ReleaseNumberBackendTool] {index}/{need_to_release_cnt}, number:{number}, id:{number_id} release success')
                else:
                    logger.error(
                        f'[ReleaseNumberBackendTool] {index}/{need_to_release_cnt}, telnyx release failed, number:{number}, id:{number_id}, need to check it!')
                    number_obj.status = 'RELEASED_FAILED'
                    number_obj.release_at = TimeUtil.GetNow()
                    number_obj.save()
                    release_failed_cnt += 1

            logger.info(
                f'[ReleaseNumberBackendTool] released_cnt: {do_release_cnt}, release_failed_cnt:{release_failed_cnt}')
            res = NumberTool.get_all_status_number_cnt()
            res["current_release_cnt"] = do_release_cnt
            res["current_release_failed_cnt"] = release_failed_cnt
            logger.error(f'[ReleaseNumberBackendTool] FINISHED! number all status: {res}')
        except Exception:
            logger.error(f"[ReleaseNumberBackendTool] failed", exc_info=True)

    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, ("limit", "token",))
        if err_code != 0:
            logger.info(f"[ReleaseNumberBackendTool] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(err_code, err_msg)
        if data['token'] != 'fatpomiehahage':
            logger.info(f"[ReleaseNumberBackendTool] param error, path: {request.path}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)
        limit = int(data['limit'])
        if limit > 300 or limit <= 0:
            logger.error(f"[ReleaseNumberBackendTool] param error invalid limit: {limit}, path: {request.path}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)

        logger.info(f"[ReleaseNumberBackendTool] request: {request.GET}")
        t = threading.Thread(target=ReleaseNumberBackendTool.worker, args=(limit,))
        t.start()

        return self.ReturnSuccess()


class SearchNumberV4(SecPhoneView):

    @SecPhoneView.VerifySignV4
    @method_decorator(ratelimit(key='post:area_code', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='post:area_code', rate='1/60s', block=True))
    @method_decorator(ratelimit(key='post:page_number', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='post:page_number', rate='1/60s', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/1h', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/60s', block=True))
    def post(self, request):
        try:
            # {'country': 'US', 'page_size': 20, 'area_code': '409', 'page_number': 4}
            post_data, err_code, err_msg = self.ReadPostJson(request)
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            page_number = post_data.get("page_number", 1)
            _area_code = post_data.get('area_code', '')
            country = post_data.get('country', 'US').upper()
            logger.error(f"[SearchNumberV4] page_number:{page_number}, _area_code:{_area_code}, country:{country}")
            data = NumberSearchTool.search_with_telnyx(0, page_number, country, _area_code)
            return self.ReturnSuccess(data)
        except Exception:
            logger.error("[SearchNumberV4] failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class BuyNumberV4(SecPhoneView):

    @SecPhoneView.VerifySignV4
    @method_decorator(ratelimit(key='post:number', rate='10/1h', block=True))
    @method_decorator(ratelimit(key='post:number', rate='1/60s', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/1h', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/60s', block=True))
    def post(self, request):
        try:
            post_data, err_code, err_msg = self.ReadPostJson(request, check_list=("number",))
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            number = post_data["number"]
            logger.error(f"[BuyNumberV4] number:{number}")
            data = TelnyxUtil.buy_number_v4(number)
            return self.ReturnSuccess(data)
        except Exception:
            logger.error("[BuyNumberV4] failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class ListNumberV4(SecPhoneView):

    @SecPhoneView.VerifySignV4
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/1h', block=True))
    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='1/60s', block=True))
    def post(self, request):
        try:
            post_data, err_code, err_msg = self.ReadPostJson(request, check_list=("message_profile_id",))
            if err_code != 0:
                return self.ReturnError(err_code, err_msg)

            message_profile_id = post_data["message_profile_id"]
            logger.error(f"[ListNumberV4] message_profile_id:{message_profile_id}")
            data = TelnyxUtil.list_number_by_message_profile_id(message_profile_id)
            return self.ReturnSuccess(data)
        except Exception:
            logger.error("[ListNumberV4] failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)
