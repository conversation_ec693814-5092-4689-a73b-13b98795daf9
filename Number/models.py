from django.db import models


class NumberInventory(models.Model):
    number = models.Char<PERSON><PERSON>('number', max_length=50)
    friendly_name = models.CharField('friendly_name', max_length=50)
    sid = models.Char<PERSON>ield('sid', max_length=100, default='')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    release_at = models.DateTimeField(blank=True)  # release to twilio
    capabilities = models.CharField('capabilities', max_length=200)
    tw_version = models.IntegerField('tw_version', default=0)
    status = models.CharField('status', max_length=20, default='')  # USING, EXPIRE, AVAILABLE, RELEASED
    platform = models.IntegerField('platform')  # 0-tw, 1-telnyx

    objects = models.Manager()


class NumberUsed(models.Model):
    user_id = models.IntegerField('user_id')
    number_id = models.IntegerField('number_id')
    number = models.Char<PERSON><PERSON>('number', max_length=50)
    type = models.Char<PERSON>ield('type', max_length=20)  # rent, trial
    expired_at = models.DateField()  # user expired date

    status = models.Char<PERSON>ield('status', max_length=20, default='')  # USING, EXPIRE, ABANDONED
    tw_version = models.IntegerField('tw_version', default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    platform = models.IntegerField('platform')  # 0-tw, 1-telnyx

    objects = models.Manager()


class NumberLocked(models.Model):
    user_id = models.IntegerField('user_id')
    number = models.CharField('number', max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class NumberAdminLock(models.Model):
    user_id = models.IntegerField('user_id')
    number = models.CharField('number', max_length=20)
    expired_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted = models.IntegerField(default=0)

    objects = models.Manager()
