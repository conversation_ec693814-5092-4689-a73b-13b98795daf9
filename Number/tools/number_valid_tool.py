from Call.models import ForbiddenContact
from Common.err import ErrInfo
from Number.tools.number_tool import NumberTool
from Order.tools.tool_order import OrderTool
from Point.pointcommon import <PERSON><PERSON><PERSON><PERSON>
from SecPhone import settings
from SecPhone.settings import logger
from User.tools.user_tool import UserTool

total_area_code_list = [str(number) for number in range(200, 1000)]
not_us_canada_area_code_list = ['242', '246', '264', '268', '284', '340', '345', '441', '473', '649', '658', '664',
                                '670', '671', '684', '721', '758', '767', '784', '787', '809', '829', '849', '868',
                                '869', '876', '939']
us_canada_area_code_list = [v for v in total_area_code_list if v not in not_us_canada_area_code_list]


class NumberValidTool:
    @staticmethod
    def is_number_area_code_ok(phone_number: str):
        if len(phone_number) < 10:
            logger.warning(f"[NumberValidTool.is_number_area_code_ok] phone:{phone_number} is not valid")
            return False

        if phone_number == settings.APP_IT_SUPPORT_SHOW_PHONE or phone_number == settings.APP_IT_SUPPORT_PHONE:
            return True

        area_code = phone_number[2:5] if phone_number[0] == '+' else phone_number[1:4]
        if area_code in us_canada_area_code_list:
            return True

        logger.warning(
            f"[NumberValidTool.is_number_area_code_ok] phone:{phone_number}, areacode:{area_code} is not valid")
        return False

    @staticmethod
    def is_to_number_ok(to_number: str) -> ErrInfo:
        """
        判断对方账户的合法性
        :param to_number: 对方号码
        :return:  是否合法
        """
        if not to_number or to_number == '+':
            logger.warning(f"[NumberValidTool.is_to_number_ok] number: {to_number} is invalid, not ok.")
            return ErrInfo.CONTACTED_ACCOUNT_UNAVAILABLE_NO_SUPPORT_NUMBER

        if len(to_number) > 12:
            logger.warning(f"[NumberValidTool.is_to_number_ok] number: {to_number} len is not 11, not ok.")
            return ErrInfo.CONTACTED_ACCOUNT_NUMBER_NOT_VALID

        tmp_number = to_number.replace('+', '')
        if tmp_number[0:3] == "999":
            logger.info(f"[NumberValidTool.is_to_number_ok] number: {to_number} start with 999, ok.")
            return ErrInfo.SUCCESS

        if tmp_number[0] != '1':
            logger.warning(f"[NumberValidTool.is_to_number_ok] number: {to_number} is not +1, not ok.")
            return ErrInfo.ONLY_NUMBERS_IN_THE_US_AND_CANADA_ARE_SUPPORTED
        if not NumberValidTool.is_number_area_code_ok(to_number):
            logger.warning(f"[NumberValidTool.is_to_number_ok] number: {to_number} is not US or Canada, not ok.")
            return ErrInfo.ONLY_NUMBERS_IN_THE_US_AND_CANADA_ARE_SUPPORTED
        if not tmp_number.isdigit():
            logger.error(f"[NumberValidTool.is_to_number_ok] number: {to_number} is not all digit, not ok.")
            return ErrInfo.CONTACTED_ACCOUNT_NUMBER_NOT_VALID
        if len(tmp_number) != 11:
            logger.warning(f"[NumberValidTool.is_to_number_ok] number: {to_number} len is not 11, not ok.")
            return ErrInfo.CONTACTED_ACCOUNT_NUMBER_NOT_VALID

        # 如果号码是禁止骚扰列表，则放弃
        if ForbiddenContact.objects.filter(number=to_number, direction="OUTGOING").exists():
            logger.warning(f"[NumberValidTool.is_to_number_ok] number: {to_number} is forbidden to contact")
            return ErrInfo.NUMBER_DONT_WANT_TO_BEEN_CONTACTED

        # 不是我们的app不做后续检查
        if not NumberTool.is_number_in_inventory(to_number):
            logger.info(f"[NumberValidTool.is_to_number_ok] number: {to_number} not our app's number, pass...")
            return ErrInfo.SUCCESS

        # 这个手机号必须被人使用
        to_userid = NumberTool.GetUserIdByNumber(to_number)
        if not to_userid:
            logger.warning(
                f"[NumberValidTool.is_to_number_ok] number: {to_number}, not in numberused or expired, not ok")
            return ErrInfo.CONTACTED_ACCOUNT_UNAVAILABLE_NO_VIP

        # 判断用户是否存在
        user = UserTool.get_user_by_id(to_userid)
        if not user:
            logger.warning(f"[NumberValidTool.is_to_number_ok] to user is not exist: {to_userid}, token error.")
            return ErrInfo.CONTACTED_ACCOUNT_UNAVAILABLE

        # 再次判断下使用者的订单
        err_code = OrderTool.is_user_vip_expire(to_userid)
        if err_code != ErrInfo.SUCCESS:
            return ErrInfo.CONTACTED_ACCOUNT_UNAVAILABLE_NO_VIP

        # 判断下使用者的点数是否正常
        if not PointCommon.CheckPointLeftV2(to_userid):
            logger.warning(f"[NumberValidTool.is_to_number_ok] to_user:{to_userid}, to number: {to_number} 's point "
                           f"not enough!")
            return ErrInfo.CONTACTED_ACCOUNT_UNAVAILABLE_NO_POINT

        logger.info(f"[NumberValidTool.is_to_number_ok] to_userid: {to_userid}, to number: {to_number} ok")
        return ErrInfo.SUCCESS

    @staticmethod
    def is_self_number_ok(user_id: int) -> ErrInfo:
        """
        判断自己账户的合法性
        :param user_id: 用户id
        :return:  是否合法
        """

        # check 用户
        user = UserTool.get_user_by_id(user_id)
        if not user:
            logger.error(f"[NumberValidTool.is_self_number_ok] user is not exist: {user_id} error")
            return ErrInfo.AUTH_TOKEN_INVALID

        # check 订单
        err_code = OrderTool.is_user_vip_expire(user_id)
        if err_code != ErrInfo.SUCCESS:
            logger.warning(f"[NumberValidTool.is_self_number_ok] user order invalid: {user_id}, error.")
            return ErrInfo.JUMP_VIP_VIEW

        # check 点数
        if not PointCommon.CheckPointLeftV2(user_id):
            logger.info(f"[NumberValidTool.is_self_number_ok] user_id: {user_id} 's point not enough!")
            return ErrInfo.JUMP_CHARGE_POINT_VIEW

        # check 号码
        lock_number = NumberTool.get_lock_number(user_id)
        number = NumberTool.GetNumberByUserId(user_id)

        # 只锁定号码，还没真正购买
        if not number and lock_number:
            logger.warning(f"[NumberValidTool.is_self_number_ok] user: {user_id} just lock phone without vip, "
                           f"to vip view!")
            return ErrInfo.JUMP_VIP_VIEW

        # 还没碰号码，去搜索
        if not number and not lock_number:
            logger.warning(f"[NumberValidTool.is_self_number_ok] user: {user_id} has no phone, go to number view!")
            return ErrInfo.JUMP_BUY_NUMBER_VIEW

        if not NumberTool.is_number_in_inventory(number):
            logger.error(f"[NumberValidTool.is_self_number_ok] number: {number} not our app's number, not ok.")
            return ErrInfo.JUMP_BUY_NUMBER_VIEW

        logger.info(f"[NumberValidTool.is_self_number_ok] user: {user_id} ok")
        return ErrInfo.SUCCESS
