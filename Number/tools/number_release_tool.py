import datetime
from datetime import timedelta

from dateutil.relativedelta import relativedelta

from Common.timeutil import TimeUtil
from Number.tools.number_lock_tool import NumberLockTool
from Number.tools.number_tool import NumberTool
from Order.models import Order
from SecPhone import settings
from SecPhone.settings import logger


class NumberReleaseTool:

    @staticmethod
    def _is_need_release_by_create_at(number_id, created_at):
        created_at = TimeUtil.GetUTCDateTime(created_at)
        today = TimeUtil.GetNow()
        delta = settings.NUMBER_CHECK_RELEASE_BY_CREATED_DELTA_DAYS
        logger.info(f'[NumberReleaseTool._is_need_release_by_create_at] number_id:{number_id}, created_at:{created_at}')
        for i in range(1, 120):
            same_day_next_month = created_at + relativedelta(months=i)
            if today > same_day_next_month:
                continue

            if same_day_next_month - today > timedelta(days=delta):
                logger.info(f'[NumberReleaseTool._is_need_release_by_create_at] number_id:{number_id}, '
                            f'{same_day_next_month} - {today} >  {timedelta(days=delta)}, not release.')
                return False

            if same_day_next_month - today < timedelta(days=delta):
                logger.warning(f'[NumberReleaseTool._is_need_release_by_create_at] number_id:{number_id}, '
                               f'{same_day_next_month} - {today} <  {timedelta(days=delta)}, do release.')
                return True

        return False

    @staticmethod
    def _is_need_release_by_duration(number_id):
        last_order = Order.objects.filter(number_id=number_id, order_status='CLOSED').order_by('-created_at')
        if len(last_order) == 0:
            logger.info(f'[NumberReleaseTool._is_need_release_by_duration] number_id: {number_id} '
                        f'has no CLOSED order, not release.')
            return False

        last_order = last_order[0]
        gap = last_order.expire_at - last_order.created_at
        if gap > timedelta(days=settings.NUMBER_CHECK_RELEASE_BY_DURATION_GAP_DAYS):
            logger.warning(f'[NumberReleaseTool._is_need_release_by_duration] number_id: {number_id} '
                           f'has CLOSED order > {settings.NUMBER_CHECK_RELEASE_BY_DURATION_GAP_DAYS} day, '
                           f'do release, order expired-created, time gap:{gap}.')
            return True

        logger.info(f'[NumberReleaseTool._is_need_release_by_duration] number_id: {number_id} '
                    f'has CLOSED order <= {settings.NUMBER_CHECK_RELEASE_BY_DURATION_GAP_DAYS} day, '
                    f'not release, order expired-created, time gap:{gap}')
        return False

    @staticmethod
    def _is_need_release_by_used_times(number_id) -> bool:
        number_used_cnt = NumberTool.get_number_cnt_by_number_id(number_id)
        logger.info(f'[NumberReleaseTool._is_need_release_by_used_times] number_id: {number_id}, '
                    f'number_used_cnt:{number_used_cnt}')

        if number_used_cnt > settings.NUMBER_CHECK_RELEASE_BY_USED_TIMES:
            logger.warning(f'[NumberReleaseTool._is_need_release_by_used_times] number_id: {number_id}, '
                           f'number_used_cnt:{number_used_cnt} > {settings.NUMBER_CHECK_RELEASE_BY_USED_TIMES}, '
                           f'need to release')
            return True

        return False

    @staticmethod
    def is_need_release(number_id: int, number: str, created_at: datetime.datetime):
        lock_number = NumberLockTool.get_lock_by_number(number)
        if lock_number:
            logger.warning(f"[NumberReleaseTool.is_need_release] number:{number}, created_at:{created_at}, "
                           f"lock expired:{lock_number.expired_at}, is admin lock, no need release")
            return False

        check_created_at = NumberReleaseTool._is_need_release_by_create_at(number_id, created_at)
        if check_created_at is True:
            return True

        # 用太多次数了
        check_used_times = NumberReleaseTool._is_need_release_by_used_times(number_id)
        if check_used_times is True:
            return True

        # long term used number should be released once expired
        check_duration = NumberReleaseTool._is_need_release_by_duration(number_id)
        if check_duration is True:
            return True

        return False
