from Common.timeutil import TimeUtil
from Number.models import NumberAdminLock


class NumberLockTool:
    @staticmethod
    def get_all_lock_numbers() -> list:
        db_lock_numbers = NumberAdminLock.objects.filter(deleted=0, expired_at__gt=TimeUtil.GetNow()).all()
        return db_lock_numbers

    @staticmethod
    def get_lock_by_number(number: str) -> NumberAdminLock:
        lock_number = NumberAdminLock.objects.filter(number=number, expired_at__gt=TimeUtil.GetNow(), deleted=0).first()
        return lock_number

    @staticmethod
    def add_lock_number(user_id: int, number: str, expired_at: str):
        lock_number = NumberAdminLock(user_id=user_id,
                                      number=number,
                                      expired_at=TimeUtil.str_to_ds(expired_at))
        lock_number.save()

    @staticmethod
    def remove_lock_number(user_id: int):
        NumberAdminLock.objects.filter(user_id=user_id).delete()

    @staticmethod
    def get_lock_number_by_userid(user_id: int) -> NumberAdminLock:
        lock_number = NumberAdminLock.objects.filter(user_id=user_id,
                                                     expired_at__gt=TimeUtil.GetNow(),
                                                     deleted=0).first()
        return lock_number
