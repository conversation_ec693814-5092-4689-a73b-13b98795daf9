from datetime import datetime
from typing import Union

from django.core.cache import cache
from django.db.models import Count

from Common.rediskey import RedisKey
from Common.timeutil import TimeUtil
from Number.models import NumberInventory, NumberUsed
from SecPhone import settings
from SecPhone.settings import logger


class NumberTool:
    @staticmethod
    def mv_tw_to_telnyx_for_user(user_id: int, old_number: str):
        # 释放之前的号码
        t = NumberUsed.objects.filter(user_id=user_id, status='USING').all()
        for i in t:
            i.status = 'EXPIRE'
            i.save()

        # 绑定回老号码
        NumberUsed.objects.filter(user_id=user_id, number=old_number).update(status='USING',
                                                                             platform=settings.VENDOR_TELNYX,
                                                                             tw_version=0)
        NumberInventory.objects.filter(number=old_number).update(status='USING',
                                                                 platform=settings.VENDOR_TELNYX,
                                                                 tw_version=0)

    @staticmethod
    def get_number_cnt(number: str) -> int:
        return NumberUsed.objects.filter(number=number).count()

    @staticmethod
    def get_number_cnt_by_user(user_id: int) -> int:
        return NumberUsed.objects.filter(user_id=user_id).count()

    @staticmethod
    def get_number_cnt_by_number_id(number_id: int) -> int:
        return NumberUsed.objects.filter(number_id=number_id).count()

    @staticmethod
    def get_clean_number(number: str) -> str:
        new_number = "+"
        for v in number:
            if v in '0123456789':
                new_number += v
        return new_number

    @staticmethod
    def get_clean_area_code(area_code: str) -> str:
        new_area_code = ""
        for v in area_code:
            if v in '0123456789':
                new_area_code += v
        if len(new_area_code) > 3:
            return new_area_code[0:3]
        return new_area_code

    @staticmethod
    def get_mock_number_by_userid(user_id: int) -> str:
        """
        如果用户没有号码，我们给他根据 userid mock一个
        :param user_id:
        :return:
        """
        s = str(user_id)
        for i in range(8 - len(s)):
            s = '0' + s
        return settings.APP_IT_SUPPORT_NO_NUMBER_PREFIX + s

    @staticmethod
    def get_userid_from_mock_number(number: str) -> int:
        return int(number.replace("+999", ""))

    @staticmethod
    def is_number_being_used(number):
        """
       看看号码有没有被用
        :param number:
        :return:
        """
        if NumberInventory.objects.filter(number=number, status='USING').exists():
            return True

        if NumberUsed.objects.filter(number=number, status='USING').exists():
            return True

        return False

    @staticmethod
    def get_number(number: str) -> Union[NumberInventory, None]:
        number = NumberInventory.objects.filter(number=number, status__in=('USING', 'EXPIRE')).first()
        return number

    @staticmethod
    def get_using_number(number: str) -> Union[NumberInventory, None]:
        number = NumberInventory.objects.filter(number=number, status='USING').first()
        return number

    @staticmethod
    def get_number_without_released(number: str) -> Union[NumberInventory, None]:
        number = NumberInventory.objects.filter(number=number, status__in=('USING', 'EXPIRE')).first()
        return number

    @staticmethod
    def unbind_user_phone_number(user_id: int) -> (bool, str):
        my_number_used = NumberUsed.objects.filter(user_id=user_id, status='USING').first()
        if not my_number_used:
            logger.warning(f"[NumberTool.unbind_user_phone_number] user:{user_id} no used number hit")
            return False, f"user:{user_id} no used number hit"

        my_number_used.status = 'EXPIRE'
        my_number_used.save()

        number_inventory = NumberInventory.objects.filter(id=my_number_used.number_id, status='USING').first()
        if not number_inventory:
            logger.warning(f"[NumberTool.unbind_user_phone_number] user:{user_id} no number inventory hit")
            return False, f"user:{user_id} no number inventory hit"

        number_inventory.status = 'EXPIRE'
        number_inventory.save()

        return True, "success"

    @staticmethod
    def get_number_by_userid(user_id: int) -> str:
        my_number_used = NumberUsed.objects.filter(user_id=user_id, status='USING').first()
        if my_number_used:
            return my_number_used.number
        return NumberTool.get_mock_number_by_userid(user_id)

    @staticmethod
    def get_numbers_by_userid(user_id: int) -> list:
        return NumberUsed.objects.filter(user_id=user_id).all()

    @staticmethod
    def get_platform_by_user(user_id: int) -> int:
        my_number_used = NumberUsed.objects.filter(user_id=user_id, status='USING').first()
        if my_number_used:
            return my_number_used.platform
        return -1

    @staticmethod
    def clean_tw_number(user_id: int):
        my_number_used = NumberUsed.objects.filter(user_id=user_id, status='USING',
                                                   platform=settings.VENDOR_TWILIO).first()
        if my_number_used:
            my_number_used.status = 'EXPIRE'
            my_number_used.save()
        else:
            logger.error(f"[NumberTool.clean_tw_number] user:{user_id} has no tw number, clean failed")

    @staticmethod
    def get_lock_number(user_id: int):
        """
        获取用户可能的锁定号码，一般3分钟就过期
        :param user_id:  用户ID
        :return: 电话号码
        """
        try:
            lock_number_user_key = RedisKey.GenLockNumberUserKey(user_id)
            lock_number = cache.get(lock_number_user_key)
            return lock_number
        except Exception:
            logger.error(f"[NumberTool.get_lock_number] failed: {user_id}", exc_info=True)
            return None

    @staticmethod
    def is_usable_number_in_inventory(number):
        """
        池子可用的号码
        :param number:  校验的号码
        :return:  号码str或者None
        """
        number = NumberInventory.objects.filter(number=number, status='EXPIRE')
        if number.exists():
            return number[0]

        return None

    @staticmethod
    def is_number_in_inventory(number):
        """
        只要在池子就好，不管用不用到
        :param number:  校验的号码
        :return:  号码str或者None
        """
        number = NumberInventory.objects.filter(number__contains=number, status__in=('USING', 'EXPIRE'))
        if number.exists():
            return number[0]

        return None

    @staticmethod
    def GetNumberByUserId(user_id):
        """
        号码0表示：完成内购，但是还没选号，这种不能算有号码
        :param user_id:
        :return:
        """
        res = NumberUsed.objects.filter(user_id=user_id, expired_at__gte=datetime.today(), status='USING')
        if res.count() > 1:
            logger.error(f"[GetNumberByUserId] user: {user_id} has more than 1 phone!")
        if res.exists():
            return res[0].number

        return None

    @staticmethod
    def GetNumberObjByUserId(user_id):
        """
        号码0表示：完成内购，但是还没选号，这种不能算有号码
        :param user_id:
        :return:
        """
        res = NumberUsed.objects.filter(user_id=user_id, expired_at__gte=datetime.today(), status='USING')
        if res.exists():
            return res[0]

        return None

    @staticmethod
    def GetNumberIdByUserIdWithoutFilterDatetime(user_id):
        res = NumberUsed.objects.filter(user_id=user_id).order_by('-id')
        if res.exists():
            return res[0].number_id

        return None

    @staticmethod
    def get_using_numbers_by_userid(user_id) -> list:
        res = NumberUsed.objects.filter(user_id=user_id, status='USING').order_by('-id')
        if res.count() > 1:
            logger.error(f"[NumberTool.get_using_numbers_by_userid] user:{user_id} phone size > 1, please check it!")
        return res

    @staticmethod
    def GetUserIdByNumber(number):
        res = NumberUsed.objects.filter(number__contains=number, expired_at__gte=datetime.today(), status='USING')
        if res.exists():
            return res[0].user_id

        return None

    @staticmethod
    def GetUserIdByNumberV2(number):
        res = NumberUsed.objects.filter(number=number, expired_at__gte=datetime.today(), status='USING')
        if res.exists():
            return res[0].user_id

        return None

    @staticmethod
    def get_user_id_by_number_even_expire(number: str) -> Union[int, None]:
        res = NumberUsed.objects.filter(number=number, expired_at__gte=datetime.today(), status='USING')
        if res.exists():
            return res[0].user_id

        res = NumberUsed.objects.filter(number=number).order_by('-id')
        if res.exists():
            return res[0].user_id

        return None

    @staticmethod
    def get_user_id_by_number(number: str) -> Union[int, None]:
        res = NumberUsed.objects.filter(number=number, expired_at__gte=datetime.today(), status='USING')
        if res.exists():
            return res[0].user_id
        return None

    @staticmethod
    def get_before_user_id_list_by_number(number: str) -> list:
        res = NumberUsed.objects.filter(number=number, status='EXPIRE').all()
        user_id_list = []
        for r in res:
            user_id_list.append(r.user_id)
        return user_id_list

    @staticmethod
    def get_number_obj_by_number_if_using(number: str) -> Union[NumberUsed, None]:
        res = NumberUsed.objects.filter(number=number, expired_at__gte=datetime.today(), status='USING')
        if res.exists():
            return res[0]
        return None

    @staticmethod
    def hide_number(number: str) -> bool:

        number_inventory = NumberInventory.objects.filter(number=number).first()
        if not number_inventory:
            logger.error(f"[NumberTool.hide_number] number:{number} no number inventory hit")
            return False

        number_inventory.status = 'HIDE'
        number_inventory.save()

        return True

    @staticmethod
    def delete_number(number: str) -> bool:

        number_inventory_list = NumberInventory.objects.filter(number=number, status='EXPIRE').all()
        if len(number_inventory_list) == 0:
            logger.error(f"[NumberTool.delete_number] number:{number} no number inventory hit")
            return False

        for number_inventory in number_inventory_list:
            number_inventory.status = 'RELEASED'
            number_inventory.release_at = TimeUtil.GetNow()
            number_inventory.save()

        return True

    @staticmethod
    def get_all_status_number_cnt() -> dict:
        queryset = NumberInventory.objects.all()  # 获取全部数据
        grouped_queryset = queryset.values('status').annotate(cnt=Count('status'))

        dic = {}
        for group in grouped_queryset:
            dic[group['status']] = group['cnt']
        return dic
