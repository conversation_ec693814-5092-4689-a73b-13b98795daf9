import datetime
import json

from django.db import transaction

import MyTelnyx.my_telnyx
from Common.err import ErrInfo
from Common.timeutil import TimeUtil
from Common.util import Util
from Number.models import NumberInventory, NumberUsed
from Number.tools.number_tool import NumberTool
from Order.tools.tool_order import OrderTool
from SecPhone import settings
from SecPhone.settings import logger


class NumberBuyTool:

    @staticmethod
    def buy_number(user_id: int, number: str) -> (bool, ErrInfo):
        try:
            if user_id is None or user_id == 0 or number is None or number == '':
                logger.error(f"[NumberBuyTool.buy_number] user: {user_id}, number: {number}, param is invalid")
                return False, ErrInfo.JUMP_BUY_NUMBER_VIEW

            if OrderTool.is_user_vip_expire(user_id) != ErrInfo.SUCCESS:
                return False, ErrInfo.JUMP_VIP_VIEW

            if NumberTool.is_number_being_used(number):
                logger.error(f"[NumberBuyTool.buy_number] user: {user_id}, number: {number}, is already being used!")
                return False, ErrInfo.JUMP_BUY_NUMBER_VIEW

            # 获取expire
            expired_at = OrderTool.get_user_expire(user_id)
            if not expired_at:
                logger.warning(f"[NumberBuyTool.buy_number] user: {user_id}, has no order, jump to vip view!")
                return False, ErrInfo.JUMP_VIP_VIEW

            inventory_number = NumberTool.is_usable_number_in_inventory(number)
            if inventory_number is None:
                logger.info(f"[NumberBuyTool.buy_number] user: {user_id}, number: {number},  buy from tw!")
                try:
                    number_sid = MyTelnyx.my_telnyx.TelnyxUtil.buy_number(number)
                    logger.info(f"[NumberBuyTool.buy_number] user: {user_id}, number: {number}, success, "
                                f"number sid:{number_sid}")
                    # 打上标签
                    MyTelnyx.my_telnyx.TelnyxUtil.set_phone_number_tags(number_sid,
                                                                        [MyTelnyx.my_telnyx.TELNYX_NUMBER_TAGS])

                    with transaction.atomic():
                        num = NumberInventory(
                            number=number,
                            sid=number_sid,
                            friendly_name=Util.GetFriendlyNameByPhoneNumber(number),
                            capabilities=json.dumps(
                                [{"name": "sms"}, {"name": "mms"}, {"name": "voice"}, {"name": "fax"},
                                 {"name": "emergency"}]),
                            status='USING',
                            platform=settings.VENDOR_TELNYX,
                            tw_version=0,
                        )
                        num.save()

                        duration = TimeUtil.GetDiffDaysWithNow(expired_at)
                        rent_type = 'rent' if duration >= 30 else 'trail'
                        user_number = NumberUsed(
                            user_id=user_id,
                            number_id=num.id,
                            number=num.number,
                            type=rent_type,
                            expired_at=expired_at,
                            status='USING',
                            platform=settings.VENDOR_TELNYX,
                            tw_version=0,
                        )
                        user_number.save()
                except Exception as e:
                    if "Did you first search" in str(e):
                        logger.warning(f"[NumberBuyTool.buy_number] user: {user_id}, number: {number}, buy failed")
                    else:
                        logger.error(f"[NumberBuyTool.buy_number] user: {user_id}, number: {number}, buy failed",
                                     exc_info=True)
                    return False, ErrInfo.NUMBER_SERVER_ERROR
            else:
                try:
                    NumberBuyTool.__buy_from_inventory(user_id, number, inventory_number, expired_at)
                except Exception:
                    logger.error(
                        f"[NumberBuyTool.buy_number] user:{user_id}, number {number} buy from inventory failed",
                        exc_info=True)
                    return False, ErrInfo.NUMBER_SERVER_ERROR
            return True, None

        except Exception:
            logger.error(f"[NumberBuyTool.buy_number] user: {user_id}, number: {number}, error: ", exc_info=True)
            return False, ErrInfo.NUMBER_BUY_SERVER_ERROR

    @staticmethod
    def __buy_from_inventory(user_id: int, number: str, number_db_obj: NumberUsed, expired_at: datetime):
        """
        buy from local inventory
        :param user_id:  用户ID
        :param number:  手机号
        :param number_db_obj: 手机号在本地池子，数据库的obj
        :param expired_at: 过期时间
        :return:
        """

        if OrderTool.is_user_vip_expire(user_id) != ErrInfo.SUCCESS:
            raise Exception(f"[NumberBuyTool.__buy_from_inventory] user:{user_id} not vip, can't buy number "
                            f"from inventory")

        with transaction.atomic():
            status = 'USING'
            logger.info(f"[NumberBuyTool.__buy_from_inventory] update inventory status: {status}, number: {number}")

            # 先更新本地池子
            number_in_inventory = NumberInventory.objects.filter(number=number, status='EXPIRE').first()
            if not number_in_inventory:
                raise Exception(f"[NumberBuyTool.__buy_from_inventory] user:{user_id} number:{number} "
                                f"is not expire status, can't buy number from inventory")

            number_in_inventory.status = status
            number_in_inventory.save()

            # 在更新用户号码表
            number_used = NumberUsed.objects.filter(user_id=user_id, number=number)
            if number_used.count() == 0:
                # 如果之前没有这个映射，则插入
                logger.info(
                    f"[NumberBuyTool.__buy_from_inventory] insert user used, userid: {user_id}, number: {number}")
                duration = TimeUtil.GetDiffDaysWithNow(expired_at)
                rent_type = 'rent' if duration >= 30 else 'trail'
                user_number = NumberUsed(
                    user_id=user_id,
                    number_id=number_db_obj.id,
                    number=number_db_obj.number,
                    type=rent_type,
                    expired_at=expired_at,
                    status=status,
                    tw_version=0,
                    platform=number_in_inventory.platform
                )
                user_number.save()
            else:
                # 如果之前有这个映射，则更新
                logger.info(f"[NumberBuyTool.__buy_from_inventory] update history user used, userid: {user_id}, "
                            f"number: {number}")
                number_used = number_used[0]
                number_used.expired_at = expired_at
                number_used.status = status
                number_used.tw_version = number_in_inventory.tw_version
                number_used.save()
