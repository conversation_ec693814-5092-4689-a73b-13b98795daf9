from datetime import datetime

from Number.models import NumberInventory, NumberUsed
from Number.tools.number_release_tool import NumberReleaseTool
from SecPhone import settings
from SecPhone.settings import logger


class NumberRefreshStatusTool:

    @staticmethod
    def do_update_number_status():
        # 遍历号码池子里面的所有号码 - 把USING改为EXPIRE,如果号码没人用
        check_inventory_expire_numbers = NumberInventory.objects.filter(status__in=('USING', 'EXPIRE'))
        total_size = len(check_inventory_expire_numbers)
        logger.info(f"[UpdateNumberStatus] step 1: check expire inventory number, size = {total_size}")

        # 获取当前日期
        current_date = datetime.utcnow().date()
        for index, inventory_number in enumerate(check_inventory_expire_numbers):
            try:
                is_number_use = NumberUsed.objects.filter(number_id=inventory_number.id, status='USING',
                                                          expired_at__gte=current_date).count()
                status = 'USING' if is_number_use > 0 else 'EXPIRE'
                inventory_number.status = status
                inventory_number.save()
                logger.info(f'[UpdateNumberStatus] check using or expire, {index}/{total_size} '
                            f'number_id:{inventory_number.id}, status={status}')

                # 号码池同步状态
                if status == 'EXPIRE':
                    # 看一下有没有漏网之鱼，干掉
                    number_used_list = NumberUsed.objects.filter(number_id=inventory_number.id, status='USING').all()
                    for nu in number_used_list:
                        logger.error(f'[UpdateNumberStatus] check using or expire, {index}/{total_size} '
                                     f'number_id:{inventory_number.id}, number:{inventory_number.number},'
                                     f'user_id:{nu.user_id} status={nu.status} set to EXPIRE')
                        nu.status = 'EXPIRE'
                        nu.save()

            except Exception:
                logger.error(
                    f"[OrderException.UpdateNumberStatus] number_id = {inventory_number.id}, update inventory error",
                    exc_info=True)

        # 遍历号码池子里面的所有号码 - 对于每一个EXPIRE号码，都要看看是否要还给供应商
        check_inventory_release_numbers = NumberInventory.objects.filter(status='EXPIRE')
        expire_size = len(check_inventory_release_numbers)
        logger.info(f"[UpdateNumberStatus] step 2: check release inventory number, size = {expire_size}")
        need_release_cnt = 0
        for index, number in enumerate(check_inventory_release_numbers):
            try:
                logger.info(f'[UpdateNumberStatus] check release, {index}/{expire_size} number_id = {number.id}...')
                if NumberReleaseTool.is_need_release(number.id, number.number, number.created_at) is False:
                    continue

                # check again in case user buy in this loop
                if NumberInventory.objects.filter(status='EXPIRE', id=number.id).count() == 0:
                    continue

                need_release_cnt += 1
            except Exception:
                logger.error(f'[OrderException.UpdateNumberStatus] release error: ', exc_info=True)
                continue

        if need_release_cnt > settings.NUMBER_NEED_RELEASE_ALERT_CNT:
            logger.error(f"[UpdateNumberStatus] finished check orders, need_release_cnt:{need_release_cnt} > "
                         f"{settings.NUMBER_NEED_RELEASE_ALERT_CNT}, expire_size:{expire_size}, ")
        else:
            logger.warning(f"[UpdateNumberStatus] finished check orders, need_release_cnt:{need_release_cnt} <= "
                           f"{settings.NUMBER_NEED_RELEASE_ALERT_CNT}, expire_size:{expire_size}, ")
