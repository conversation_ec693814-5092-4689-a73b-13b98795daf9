import random
import re

from django.core.cache import cache

from Common.rediskey import <PERSON><PERSON><PERSON><PERSON>
from MyTelnyx.my_telnyx import TelnyxUtil
from Number.models import NumberInventory
from Number.tools.number_lock_tool import Number<PERSON>ockTool
from SecPhone import settings
from SecPhone.settings import logger


class NumberSearchTool(object):

    @staticmethod
    def get_from_inventory(country: str, area_code: str) -> list:
        if area_code:
            numbers = NumberInventory.objects \
                .filter(status='EXPIRE') \
                .filter(number__contains=area_code) \
                .filter(platform=settings.VENDOR_TELNYX) \
                .order_by('-id')
            logger.info(
                f"[SearchNumber] get_from_inventory, area_code: {area_code}, numbers: {len(numbers)}")
        else:
            numbers = NumberInventory.objects \
                .filter(status='EXPIRE') \
                .filter(platform=settings.VENDOR_TELNYX) \
                .order_by('-id')
            logger.info(
                f"[SearchNumber] get_from_inventory, area_code: {area_code}, numbers: {len(numbers)}")
        return NumberSearchTool._wrap_rsp(numbers, country)

    @staticmethod
    def get_from_inventory_fallback(country: str) -> list:
        numbers = NumberInventory.objects \
            .filter(status='EXPIRE') \
            .filter(platform=settings.VENDOR_TELNYX) \
            .order_by('-id')
        return NumberSearchTool._wrap_rsp(numbers, country)

    @staticmethod
    def get_inventory_size() -> int:
        return NumberInventory.objects.filter(status='EXPIRE', platform=settings.VENDOR_TELNYX).count()

    @staticmethod
    def _wrap_rsp(db_numbers, country: str) -> list:

        res = []
        for number in db_numbers:
            area_code = number.number[2:5]
            if area_code in settings.TOLL_FREE_AREA_CODES:
                logger.info(f"[SearchNumber._wrap_rsp] give up toll-free numbers: {area_code}: {number.number}")
                continue
            if area_code in settings.canada_area_codes:
                logger.info(f"[SearchNumber._wrap_rsp] give up canada numbers: {area_code}: {number.number}")
                continue

            j = {'country': country,
                 'friendly_name': number.friendly_name,
                 'phone_number': number.number,
                 'capabilities': ''
                 }

            p_region = re.compile(r'\((\d+?)\).*')
            m = p_region.match(number.friendly_name)
            if m:
                j['region'] = m.groups()[0]
            res.append(j)

        # 本地池子要打乱
        random.shuffle(res)
        return res

    @staticmethod
    def remove_lock_numbers(user_id: int, numbers: list):
        """
        number_item : {
                "country": "US",
                "friendly_name": "(*************",
                "phone_number": "+13156661816",
                "capabilities": {
                    "voice": true,
                    "sms": true,
                    "mms": true
                },
                "region": "315"
            }
        :param user_id:
        :param numbers:
        :return:
        """
        if not numbers:
            return []

        logger.info(f"[SearchNumber.remove_lock_numbers] user:{user_id}, before remove lock, size：{len(numbers)}")
        lock_number_prefix = RedisKey.GenAllLockNumberPrefix()
        cache_lock_numbers = cache.keys(lock_number_prefix + "*")

        db_lock_numbers = NumberLockTool.get_all_lock_numbers()
        db_lock_numbers_str_list = []
        db_lock_numbers_map = {}
        for db_lock in db_lock_numbers:
            db_lock_numbers_map[db_lock.number] = db_lock.user_id
            db_lock_numbers_str_list.append(db_lock.number)

        after_filter_lock_numbers = []
        for n in numbers:
            number_ = n['phone_number']
            if lock_number_prefix + number_ in cache_lock_numbers:
                continue
            if number_ in db_lock_numbers_str_list:
                if user_id != db_lock_numbers_map[number_]:
                    continue
                else:
                    logger.info(
                        f"[SearchNumber.remove_lock_numbers] user:{user_id}, get the db lock number：{number_}")

            after_filter_lock_numbers.append(n)

        logger.info(f"[SearchNumber.remove_lock_numbers] user:{user_id}, cache lock numbers:{cache_lock_numbers}, "
                    f"db lock number: {db_lock_numbers_str_list}, after remove lock, "
                    f"left size：{len(after_filter_lock_numbers)}")
        return after_filter_lock_numbers

    @staticmethod
    def search_in_db(userid: int, page_number: int, country: str, area_code: str) -> dict:
        res_local = NumberSearchTool.get_from_inventory(country, area_code=area_code)
        res_local = NumberSearchTool.remove_lock_numbers(userid, res_local)

        # cut page size
        start = (page_number - 1) * 20
        end = page_number * 20
        res = res_local[start: end]

        if len(res) >= 20:
            logger.info(f"[SearchNumber] user={userid}, country:{country}, area:{area_code}, page_number:{page_number},"
                        f"local size: {len(res_local)}, final size:{len(res)}")
            return {"list": res, "page": page_number}
        else:
            res_fallback = NumberSearchTool.get_from_inventory_fallback(country)
            res_fallback = NumberSearchTool.remove_lock_numbers(userid, res_fallback)
            res = res_local + res_fallback

            # cut page size
            start = (page_number - 1) * 20
            end = page_number * 20
            res = res[start: end]

            logger.info(f"[SearchNumber] user={userid}, country:{country}, area:{area_code}, page_number:{page_number},"
                        f"local size:{len(res_local)}, fallback size:{len(res_fallback)}, final size: {len(res)}")
            return {"list": res, "page": page_number}

    @staticmethod
    def search_with_telnyx(userid: int, page_number: int, country: str, area_code: str) -> dict:
        res_local = NumberSearchTool.get_from_inventory(country, area_code=area_code)
        res_local = NumberSearchTool.remove_lock_numbers(userid, res_local)

        # cut page size
        start = (page_number - 1) * 20
        end = page_number * 20
        res = res_local[start: end]

        if len(res) >= 20:
            logger.info(
                f"[SearchNumber.search_with_telnyx] user_id={userid}, country:{country}, area_code:{area_code}, "
                f"page_number:{page_number}, local size: {len(res_local)}, final size: {len(res)}")
            return {"list": res, "page": page_number}
        else:
            # 最多 20 个
            if not area_code:
                res_telnyx = TelnyxUtil.search_number(country, "")
            else:
                res_telnyx = TelnyxUtil.search_number(country, area_code)

            res = res + res_telnyx
            logger.info(
                f"[SearchNumber.search_with_telnyx] user_id={userid}, country: {country}, area_code:{area_code}, "
                f"page_number:{page_number}, local size: {len(res_local)}, telnyx size:{len(res_telnyx)}, "
                f"final size: {len(res)}")
            return {"list": res, "page": page_number}
