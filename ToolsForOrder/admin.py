from django.contrib import admin
from django.http import JsonResponse
from django.template.response import TemplateResponse
from django.urls import path

from Number.tools.number_tool import NumberTool
from Order.tools.tool_apple_order import OrderAppleTool
from Order.tools.tool_order import OrderTool
from Order.tools.tool_refresh_order import RefreshOrderTool
from SecPhone.settings import logger
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from ToolsForOrder.models import UpdateSubscriptionTool, GetUserSubscriptionByUserIdTool, \
    GetUserSubscriptionByCertTool, RecoverNewDeviceSubscriptionTool
from User.tools.user_profile_tool import UserProfileTool
from User.tools.user_tool import UserTool


@admin.register(UpdateSubscriptionTool)
class UpdateSubscriptionToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/update_subscription.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('update_subscription/', self.admin_site.admin_view(self.update_subscription),
                 name='update_subscription'),
        ]
        return custom_urls + urls

    @staticmethod
    def update_subscription(request):
        if request.method == 'POST':
            userid = int(request.POST.get('userid'))

            logger.warning(f"[UpdateSubscriptionToolAdmin] user_id:{userid}")
            ret = RefreshOrderTool.refresh_user_vip(userid)
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/update_subscription.html', {})


@admin.register(RecoverNewDeviceSubscriptionTool)
class RecoverNewDeviceSubscriptionToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/recover_new_device_subscription.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('recover_new_device_subscription/', self.admin_site.admin_view(self.recover_new_device_subscription),
                 name='recover_new_device_subscription'),
        ]
        return custom_urls + urls

    @staticmethod
    def recover_new_device_subscription(request):
        if request.method == 'POST':
            user_id = int(request.POST.get('userid'))
            logger.warning(f"[RecoverNewDeviceSubscriptionTool] user_id:{user_id}")

            done, err_msg = RefreshOrderTool.recover_order_subscription(user_id)
            if not done:
                ret = {"user_id": user_id,
                       "err_msg_zh": "恢复失败，请截图找技术部门查看",
                       "err_msg": err_msg}
                return JsonResponse(ret)

            to_number = NumberTool.get_mock_number_by_userid(user_id)
            content = SmsNoticeTool.recover_subscription()
            SmsItSupportTool.add_support_sms_both_from_it(user_id, to_number, content)

            ret = {"user_id": user_id,
                   "user_info": UserProfileTool.get_user_profile(user_id, app_version=0)}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/recover_new_device_subscription.html', {})


@admin.register(GetUserSubscriptionByUserIdTool)
class GetUserSubscriptionByUserIdToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/get_user_subscription_by_userid.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('get_user_subscription_by_userid/', self.admin_site.admin_view(self.get_user_subscription_by_userid),
                 name='get_user_subscription_by_userid'),
        ]
        return custom_urls + urls

    @staticmethod
    def get_user_subscription_by_userid(request):
        if request.method == 'POST':
            data = request.POST

            user_id = int(data['userid'].strip())
            if not UserTool.get_user_by_id(user_id):
                ret = {"err": f'user: {user_id} not exists'}
                return JsonResponse(ret)

            order = OrderTool.get_user_order_without_condition(user_id)
            if not order:
                ret = {"err": f"user:{user_id} order is not exist"}
                return JsonResponse(ret)

            res, is_sandbox = OrderAppleTool.get_cert_rsp_from_apple(order.certificate)
            ret = {"apple_res": res, "is_sandbox": is_sandbox}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/get_user_subscription_by_userid.html', {})


@admin.register(GetUserSubscriptionByCertTool)
class GetUserSubscriptionByCertToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/get_user_subscription_by_cert.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('get_user_subscription_by_cert/', self.admin_site.admin_view(self.get_user_subscription_by_cert),
                 name='get_user_subscription_by_cert'),
        ]
        return custom_urls + urls

    @staticmethod
    def get_user_subscription_by_cert(request):
        if request.method == 'POST':
            data = request.POST
            cert = data['cert']
            res, is_sandbox = OrderAppleTool.get_cert_rsp_from_apple(cert)
            ret = {"apple_res": res, "is_sandbox": is_sandbox}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/get_user_subscription_by_cert.html', {})
