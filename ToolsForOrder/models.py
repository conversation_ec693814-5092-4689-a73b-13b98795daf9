from django.db import models


class Tool(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    objects = models.Manager()


class UpdateSubscriptionTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Subscription - Update User Subscription'
        verbose_name_plural = 'Subscription - Update User Subscription'


class RecoverNewDeviceSubscriptionTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Subscription - Recover New Device Subscription'
        verbose_name_plural = 'Subscription - Recover New Device Subscription'


class GetUserSubscriptionByUserIdTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Subscription - Get User Subscription by UserId'
        verbose_name_plural = 'Subscription - Get User Subscription by UserId'


class GetUserSubscriptionByCertTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Subscription - Get User Subscription by Certificate'
        verbose_name_plural = 'Subscription - Get User Subscription by Certificate'
