from django.db import models


class Tool(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    objects = models.Manager()


class DeleteNumberTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Number - Delete a number'
        verbose_name_plural = 'Number - Delete numbers'


class AdminLockNumberTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Number - Admin lock a number'
        verbose_name_plural = 'Number - Admin lock numbers'


class GetNumberInfoTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Number - Get Number Info'
        verbose_name_plural = 'Number - Get Number Info'


class UnbindUserNumberTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'A - Number - Unbind User Number'
        verbose_name_plural = 'A - Number - Unbind User Number'


class AllowUserSearchVendorDirectlyTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Number - Allow User Search Vendor Directly'
        verbose_name_plural = 'Number - Allow User Search Vendor Directly'


class SearchTelnyxNumbersTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Number - Search Telnyx Numbers'
        verbose_name_plural = 'Number - Search Telnyx Numbers'


class TempCloseVendorNumbersTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Number - Temp Dont Search Telnyx Numbers'
        verbose_name_plural = 'Number - Temp Dont Search Telnyx Numbers'


class GetUserInfoTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Get User Info'
        verbose_name_plural = 'Get User Info'
