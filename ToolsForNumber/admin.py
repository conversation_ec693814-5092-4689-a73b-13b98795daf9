from django.contrib import admin
from django.core.cache import cache
from django.http import JsonResponse
from django.template.response import TemplateResponse
from django.urls import path

from Common.rediskey import RedisKey
from Common.timeutil import TimeUtil
from Common.util import Util
from MyTelnyx.my_telnyx import TelnyxUtil
from Number.tools.number_lock_tool import Number<PERSON>ockTool
from Number.tools.number_search_tool import NumberSearchTool
from Number.tools.number_tool import NumberTool
from Order.tools.tool_order import OrderTool
from SecPhone import settings
from SecPhone.settings import logger
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from ToolsForNumber.models import DeleteNumberTool, AdminLockNumberTool, \
    GetNumberInfoTool, UnbindUserNumberTool, AllowUserSearchVendorDirectlyTool, \
    SearchTelnyxNumbersTool, TempCloseVendorNumbersTool, GetUserInfoTool
from User.tools.user_context_tool import User<PERSON>ontextTool
from User.tools.user_tool import UserTool


@admin.register(DeleteNumberTool)
class DeleteNumberToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/delete_number.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('delete_number/', self.admin_site.admin_view(self.delete_number), name='delete_number'),
        ]
        return custom_urls + urls

    @staticmethod
    def delete_number(request):
        if request.method == 'POST':
            number = request.POST.get('number')
            number = Util.FormatNumberV2(number)
            logger.error(f"[DeleteNumberToolAdmin] number:{number}")

            number_obj = NumberTool.get_number(number)
            if not number_obj:
                ret = {"error": "number not exists"}
                return JsonResponse(ret)

            user_id = NumberTool.get_user_id_by_number(number)
            if user_id:
                number_obj = NumberTool.GetNumberObjByUserId(user_id)
                ret = {"error": f"number is using by {user_id} till {number_obj.expired_at}"}
                return JsonResponse(ret)

            if number_obj.status != 'EXPIRE':
                ret = {"error": "number is being used"}
                return JsonResponse(ret)

            if TelnyxUtil.delete_a_number(number):
                if NumberTool.delete_number(number):
                    ret = {"number": number, "message": "delete number done"}
                    return JsonResponse(ret)
                else:
                    ret = {"number": number, "message": "delete number failed"}
                    return JsonResponse(ret)
            else:
                logger.error(f'[DeleteNumberToolAdmin] telnyx release api error, number:{number}')
                ret = {"number": number, "message": "vendor delete number failed"}
                return JsonResponse(ret)

        return TemplateResponse(request, 'admin/delete_number.html', {})


@admin.register(AdminLockNumberTool)
class AdminLockNumberToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/admin_lock_number.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('admin_lock_number/', self.admin_site.admin_view(self.admin_lock_number), name='admin_lock_number'),
        ]
        return custom_urls + urls

    @staticmethod
    def admin_lock_number(request):
        if request.method == 'POST':
            user_id = int(request.POST.get('userid').strip())
            number = request.POST.get('number').strip()
            number = Util.FormatNumberV2(number)
            expired_at = str(request.POST.get("expired_at")).strip()
            logger.error(f"[AdminLockNumberToolAdmin] user_id:{user_id}, number:{number}, expired_at:{expired_at}")

            if not UserTool.get_user_by_id(user_id):
                ret = {"error": f"user: {user_id} not in db"}
                return JsonResponse(ret)

            if not NumberTool.is_number_in_inventory(number):
                ret = {"error": f"number: {number} not in db"}
                return JsonResponse(ret)

            admin_lock_obj = NumberLockTool.get_lock_by_number(number)
            if admin_lock_obj:
                ret = {"error": f"number: +{number} already locking by {admin_lock_obj.user_id} "
                                f"till {admin_lock_obj.expired_at}"}
                return JsonResponse(ret)

            admin_lock_obj = NumberLockTool.get_lock_number_by_userid(user_id)
            if admin_lock_obj:
                ret = {"error": f"user_id: {user_id} already lock number: +{admin_lock_obj.number} "
                                f"till {admin_lock_obj.expired_at}"}
                return JsonResponse(ret)

            NumberLockTool.add_lock_number(user_id, number, expired_at)

            ret = {"number": number, "user_id": user_id, "expired_at": expired_at, "message": "admin lock done"}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/admin_lock_number.html', {})


@admin.register(GetNumberInfoTool)
class GetNumberInfoToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/get_number_info.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('get_number_info/', self.admin_site.admin_view(self.get_number_info), name='get_number_info'),
        ]
        return custom_urls + urls

    @staticmethod
    def get_number_info(request):
        if request.method == 'POST':
            data = request.POST

            number = data["number"]
            number = Util.FormatNumberV2(number)

            number_inventory = NumberTool.get_number_without_released(number)
            if not number_inventory:
                ret = {"err": f'{number} 号码不存在DB'}
                return JsonResponse(ret)

            ret = {}
            number_obj = NumberTool.get_number_obj_by_number_if_using(number)
            if number_obj:
                ret['归属情况'] = f"被{number_obj.user_id}使用直到{number_obj.expired_at}"
            else:
                ret['归属情况'] = f'{number} 无归属'

            platform_str_map = {0: "tw", 1: "tyx", }
            ret["number"] = number_inventory.number
            ret["sid"] = number_inventory.sid
            ret["platform"] = platform_str_map.get(number_inventory.platform, "unknown")
            ret["friendly_name"] = number_inventory.friendly_name
            ret["created_at"] = TimeUtil.GetBeijingTimeStr(number_inventory.created_at)
            ret["status"] = number_inventory.status

            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/get_number_info.html', {})


@admin.register(UnbindUserNumberTool)
class UnbindUserNumberToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/unbind_user_number.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('unbind_user_number/', self.admin_site.admin_view(self.unbind_user_number), name='unbind_user_number'),
        ]
        return custom_urls + urls

    @staticmethod
    def unbind_user_number(request):
        if request.method == 'POST':
            data = request.POST

            user_id = int(data['userid'].strip())
            force_unbind = int(data['force_unbind'].strip())
            if not UserTool.get_user_by_id(user_id):
                ret = {"err": f'user: {user_id} not exists'}
                return JsonResponse(ret)

            if not NumberTool.GetNumberByUserId(user_id):
                ret = {"err": f'user: {user_id} has no number'}
                return JsonResponse(ret)

            user_number_cnt = NumberTool.get_number_cnt_by_user(user_id)
            if force_unbind == 1:
                pass
            else:
                if user_number_cnt >= 7:
                    logger.error(f"[FatpoUnbindUserPhoneNumber] user unbind too much times: {user_id}")
                    SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                     settings.APP_IT_SUPPORT_SHOW_PHONE, f'+999{user_id}',
                                                     "Number unbinding failed. Please contact customer service to share "
                                                     "the situation.")
                    ret = {"err": "用户已经绑定超过7个号码了，别再绑定了吧"}
                    return JsonResponse(ret)

            res, err_msg = NumberTool.unbind_user_phone_number(user_id)

            # 推送
            system_content, db_content, push_content = SmsNoticeTool.go_and_pick_number()
            SmsItSupportTool.add_support_sms_v2(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                settings.APP_IT_SUPPORT_SHOW_PHONE, f'+999{user_id}',
                                                db_content=db_content,
                                                push_content=push_content)

            # 放开挑选号码权限
            cache.set(RedisKey.GenIsAllowSearchVendorDirectly(user_id), 1, 3600)

            ret = {"user": user_id,
                   "res": res,
                   "err_msg": err_msg,
                   "user_number_cnt": user_number_cnt,
                   }
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/unbind_user_number.html', {})


@admin.register(AllowUserSearchVendorDirectlyTool)
class AllowUserSearchVendorDirectlyToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/allow_user_search_vendor_number.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('allow_user_search_vendor_number/', self.admin_site.admin_view(self.allow_user_search_vendor_number),
                 name='allow_user_search_vendor_number'),
        ]
        return custom_urls + urls

    @staticmethod
    def allow_user_search_vendor_number(request):
        if request.method == 'POST':
            data = request.POST

            user_id = int(data['userid'].strip())
            if not UserTool.get_user_by_id(user_id):
                ret = {"err": f'user: {user_id} not exists'}
                return JsonResponse(ret)

            number_obj = NumberTool.GetNumberByUserId(user_id)
            if number_obj:
                ret = {"err": f'user: {user_id} already has number: {number_obj}'}
                return JsonResponse(ret)

            # 缓存允许
            cache.set(RedisKey.GenIsAllowSearchVendorDirectly(user_id), 1, 3600)
            ret = {"user_id": user_id, "seconds": 3600}

            # 推送
            system_content, db_content, push_content = SmsNoticeTool.go_and_pick_number()
            SmsItSupportTool.add_support_sms_v2(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                settings.APP_IT_SUPPORT_SHOW_PHONE, f'+999{user_id}',
                                                db_content=db_content,
                                                push_content=push_content)
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/allow_user_search_vendor_number.html', {})


@admin.register(SearchTelnyxNumbersTool)
class SearchTelnyxNumbersToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/search_telnyx_numbers.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('search_telnyx_numbers/', self.admin_site.admin_view(self.search_telnyx_numbers),
                 name='search_telnyx_numbers'),
        ]
        return custom_urls + urls

    @staticmethod
    def search_telnyx_numbers(request):
        if request.method == 'POST':
            data = request.POST
            area_code = data['area_code']
            country = data.get('country', 'US').upper()

            res_telnyx = TelnyxUtil.search_number(country, area_code)
            ret = {"list": res_telnyx, "db_size": NumberSearchTool.get_inventory_size()}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/search_telnyx_numbers.html', {})


@admin.register(TempCloseVendorNumbersTool)
class TempCloseVendorNumbersToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/tmp_close_vendor_numbers.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('tmp_close_vendor_numbers/', self.admin_site.admin_view(self.tmp_close_vendor_numbers),
                 name='tmp_close_vendor_numbers'),
        ]
        return custom_urls + urls

    @staticmethod
    def tmp_close_vendor_numbers(request):
        if request.method == 'POST':
            data = request.POST

            close_seconds = int(data["close_seconds"])
            logger.error(f"[TempCloseVendorNumbersToolAdmin] close_seconds:{close_seconds}")

            if close_seconds > 86400:
                ret = {"err": f"seconds too big: {close_seconds}, need < 86400"}
                return JsonResponse(ret)

            db_size = NumberSearchTool.get_inventory_size()
            if db_size < 50:
                ret = {"err": f"db expire size:{db_size} < 50, can not close vendor numbers"}
                return JsonResponse(ret)

            cache.set(RedisKey.GenCloseSearchVendor(), 1, close_seconds)
            ret = {"close_seconds": close_seconds, "db_size": db_size}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/tmp_close_vendor_numbers.html', {})


@admin.register(GetUserInfoTool)
class GetUserInfoToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/get_user_info.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('get_user_info/', self.admin_site.admin_view(self.get_user_info), name='get_user_info'),
        ]
        return custom_urls + urls

    @staticmethod
    def get_user_info(request):
        if request.method == 'POST':
            data = request.POST

            if ("userid" in data and data["userid"]) or ("userid2" in data and data["userid2"]):
                source = "GetFromUserId"
                user_id = 0
                if "userid" in data:
                    user_id = int(request.POST.get('userid').strip())
                if "userid2" in data:
                    user_id = int(request.POST.get('userid2').strip())
                user = UserTool.get_user_by_id(user_id)
                if not user:
                    if UserTool.get_user_by_id_with_deleted(user_id):
                        ret = {
                            "err_message": "user is deleted before",
                            "user_info": UserContextTool.get_user_context_dict(email="", content="", user_id=user_id)
                        }
                        return JsonResponse(ret)
                    else:
                        ret = {"err_message": f"user:{user_id} not exists"}
                        return JsonResponse(ret)
            elif "number" in data and data["number"]:
                source = "GetFromNumber"
                number = NumberTool.get_clean_number(data["number"].strip())
                user_id = NumberTool.get_user_id_by_number_even_expire(number)
                if not user_id:
                    logger.warning(f"[FatpoGetUserInfo] user not exists, number:{number}")
                    ret = {"err_message": f"user not exists, number: {number}"}
                    return JsonResponse(ret)
            elif "uuid" in data and data["uuid"]:
                source = "GetFromDeviceId"
                user_ids = UserTool.get_userid_list_by_uuid(data["uuid"].strip())
                user_ctx_list = [UserContextTool.get_user_context_dict(email="", content="", user_id=v)
                                 for v in user_ids]
                ret = {"source": source, "user_lists": user_ctx_list}
                return JsonResponse(ret)
            elif "email" in data and data["email"]:
                source = "GetFromEmail"
                email = data['email']
                user = UserTool.get_user_by_email(email)
                if not user:
                    logger.warning(f"[FatpoGetUserInfo] user not exists, user:{email}")

                    user = UserTool.get_user_by_email_with_deleted(email)
                    if user:
                        ret = {
                            "err_message": "user is deleted before",
                            "user_info": UserContextTool.get_user_context_dict(email="", content="", user_id=user.id)
                        }
                        return JsonResponse(ret)
                    else:
                        ret = {"err_message": f"user not exists, email: {email}"}
                        return JsonResponse(ret)
                else:
                    user_id = user.id
            elif "original_transaction_id" in data and data["original_transaction_id"]:
                source = "GetFromOriginalTransactionId"
                original_transaction_id = data["original_transaction_id"]
                orders = OrderTool.get_orders_by_original_transaction_id(original_transaction_id)
                if not orders:
                    ret = {"err_message": f"user not exists, original_transaction_id: {original_transaction_id}"}
                    return JsonResponse(ret)
                user_ids = [v.user_id for v in orders]
                user_ctx_list = [UserContextTool.get_user_context_dict(email="", content="", user_id=v)
                                 for v in user_ids]
                ret = {"source": source, "user_lists": user_ctx_list}
                return JsonResponse(ret)
            else:
                ret = {"err_message": "invalid param without user_id or number"}
                return JsonResponse(ret)

            user_context = UserContextTool.get_user_context_dict(email="", content="", user_id=user_id)
            ret = {"source": source, "user": user_context}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/get_user_info.html', {})
