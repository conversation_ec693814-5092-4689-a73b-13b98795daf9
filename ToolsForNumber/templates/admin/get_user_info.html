{# templates/admin/get_user_info.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
    <div class="container mt-4">
        <h1>Get User Info By UserId/DeviceId/Number/Email/OriginalTransactionId</h1>
        <form id="custom-form" method="post" action="{% url 'admin:get_user_info' %}">
            {% csrf_token %}
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="userid">User Id:</label>
                    <input type="text" id="userid" name="userid">
                </div>
                <br>
                <div class="form-group col-md-6">
                    <label for="number">Number:</label>
                    <input type="text" id="number" name="number">
                </div>
                <br>
                <div class="form-group col-md-6">
                    <label for="uuid">DeviceId:</label>
                    <input type="text" id="uuid" name="uuid">
                </div>
                <br>
                <div class="form-group col-md-6">
                    <label for="original_transaction_id">Original Transaction Id:</label>
                    <input type="text" id="original_transaction_id" name="original_transaction_id">
                </div>
                <br>
                <div class="form-group col-md-6">
                    <label for="email">Email:</label>
                    <input type="text" id="email" name="email">
                </div>
                <br>

            </div>
            <button type="submit">Submit</button>
        </form>
        <div class="form-group mt-3">
            <label for="response-textbox">Response</label>
            <textarea id="response-textbox" class="form-control" readonly rows="50" cols="80"></textarea>
        </div>
    </div>


    <script>
        document.getElementById('custom-form').onsubmit = function (event) {
            event.preventDefault();
            var formData = new FormData(this);
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                }
            }).then(response => response.json()).then(data => {
                var responseTextbox = document.getElementById('response-textbox');
                responseTextbox.value = JSON.stringify(data, null, 2);
            });
        };
    </script>
{% endblock %}
