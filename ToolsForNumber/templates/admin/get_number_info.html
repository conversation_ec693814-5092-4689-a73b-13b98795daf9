{# templates/admin/get_number_info.html #}
{% extends "admin/base_site.html" %}
{% load static %}

{% block content %}
    <div class="container mt-4">
        <h1>Get Number Info</h1>
        <form id="custom-form" method="post" action="{% url 'admin:get_number_info' %}">
            {% csrf_token %}
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="number">Number:</label>
                    <input type="text" id="number" name="number">
                </div>
            </div>
            <button type="submit">Submit</button>
        </form>
        <div class="form-group mt-3">
            <label for="response-textbox">Response</label>
            <textarea id="response-textbox" class="form-control" readonly rows="50" cols="80"></textarea>
        </div>
    </div>


    <script>
        document.getElementById('custom-form').onsubmit = function (event) {
            event.preventDefault();
            var formData = new FormData(this);
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                }
            }).then(response => response.json()).then(data => {
                var responseTextbox = document.getElementById('response-textbox');
                responseTextbox.value = JSON.stringify(data, null, 2);
            });
        };
    </script>
{% endblock %}
