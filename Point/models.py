from django.db import models

from Point.data import PointManager


class Point(models.Model):
    user_id = models.IntegerField('user_id')
    point = models.IntegerField('point', default=0)
    free_point = models.IntegerField('free_point', default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = PointManager()


class PointRecord(models.Model):
    user_id = models.IntegerField('user_id')
    point = models.IntegerField('point')
    event = models.CharField('event', max_length=200)  # charge, make call, send sms
    record_id = models.IntegerField(default=0, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    objects = PointManager()
