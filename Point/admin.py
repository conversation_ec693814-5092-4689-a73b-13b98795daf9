from django.contrib import admin

from Point.models import Point, PointRecord


@admin.register(Point)
class PointAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'point', 'free_point', 'created_at', 'updated_at',)
    search_fields = ('user_id',)
    list_filter = ('created_at',)
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(PointRecord)
class PointRecordAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'point', 'event', 'created_at',)
    search_fields = ('user_id',)
    list_filter = ('event',)
    list_per_page = 50  # 设置每页显示 50 条记录
