"""
@author: jon
"""

from django.db import transaction
from django.db.models import F

from Point.models import Point, PointRecord
from SecPhone.settings import logger


class PointCommon:

    @staticmethod
    def Add(user_id, point, event, record_id=0):

        logger.info(f"[PointCommon.Add] user: {user_id}, point: {point}, event:{event}, record: {record_id}")

        event = event.upper()
        if event in ('CHARGE', 'REFUND') and point <= 0:
            logger.error(f"[PointCommon.Add] charge point should > 0, user:{user_id}, point: {point}, "
                         f"event:{event}, record_id: {record_id}")
            return False, 'charge point should > 0'

        if event not in ('FATPO-CHARGE', 'CHARGE', 'REFUND') and point > 0:
            logger.error(f"[PointCommon.Add] consume point should < 0, user:{user_id}, point: {point}, "
                         f"event:{event}, record_id: {record_id}")
            return False, 'consume point should < 0'

        try:
            with transaction.atomic():
                pr = PointRecord(user_id=user_id, point=point, event=event, record_id=record_id)
                pr.save()

                p = Point.objects.get_or_create(user_id=user_id)[0]

                if event in ('CHARGE', 'FATPO-CHARGE'):
                    p.point = F('point') + point
                else:
                    need_point = abs(point)

                    # 免费的点数能cover住，直接扣
                    if p.free_point >= need_point:
                        p.free_point = F('free_point') + point
                    else:
                        # 免费点数cover不住
                        # 但不是完全cover不住，能扣多少是多少
                        if p.free_point >= 0:
                            gap_point = need_point - p.free_point
                            p.free_point = 0
                            p.point = F('point') - gap_point
                        else:
                            logger.warning(f"[PointCommon.Add] {user_id} {event}, why free point = {p.free_point} < 0")
                            p.point = F('point') + point

                p.save()
                return True, ''

        except Exception as e:
            logger.error(f"user: {user_id}, point:{point}, event:{event} add point error: ", str(e))
            return False, str(e)

    @staticmethod
    def GetPointLeft(user_id) -> int:

        p = Point.objects.filter(user_id=user_id)
        if len(p) == 0:
            return 0
        else:
            return p[0].point + p[0].free_point

    @staticmethod
    def CheckPointLeftV2(user_id: int) -> bool:

        p = Point.objects.filter(user_id=user_id)
        if len(p) == 0:
            return False
        else:
            if p[0].point + p[0].free_point >= 2:
                return True
            else:
                logger.warning(f"[CheckPointLeftV2] user: {user_id}, point not enough, point: {p[0].point},"
                               f" free_point: {p[0].free_point}")
                return False
