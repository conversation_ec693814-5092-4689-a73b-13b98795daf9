from django.conf.urls import url

from Order import orderviews

urlpatterns = [
    url(r'^order/cert/$', orderviews.PostCertificate.as_view()),
    url(r'^order/cert/consume/$', orderviews.PostConsumeCertificate.as_view()),
    url(r'^order/update/$', orderviews.UpdateOrderStatus.as_view()),
    url(r'^order/update/test/$', orderviews.UpdateOrderStatusTest.as_view()),
    url(r'^order/check/$', orderviews.CheckOrderStatus.as_view()),

    # new app
    url(r'^zhphone/order/cert/$', orderviews.PostCertificate.as_view()),
    url(r'^zhphone/order/cert/consume/$', orderviews.PostConsumeCertificate.as_view()),
    url(r'^zhphone/order/appleVerifyByUserId/$', orderviews.AppleVerifyByUserId.as_view()),
    url(r'^zhphone/order/appleVerifyByCert/$', orderviews.AppleVerifyByCert.as_view()),
    url(r'^zhphone/order/appleNotification/$', orderviews.AppleNotification.as_view()),
]
