import datetime
import json
import threading
import time
from datetime import timedelta

from django.conf import settings
from django.core.cache import cache

from Common.err import ErrInfo
from Common.rediskey import <PERSON><PERSON><PERSON>ey
from Common.timeutil import TimeUtil
from Common.util import Util
from Common.views import <PERSON>cP<PERSON><PERSON>iew
from MyTelnyx.my_telnyx import TelnyxUtil
from Number.models import NumberUsed
from Number.tools.number_buy_tool import NumberBuyTool
from Number.tools.number_tool import NumberTool
from Order.apple_notify_jws.jws_verify import AppleIapTools
from Order.models import Order, OrderConsume, OrderRefund
from Order.tools.tool_apple_order import OrderAppleTool
from Order.tools.tool_duplicate_order import OrderDuplicateTool
from Order.tools.tool_invalid_order import OrderInvalidTool
from Order.tools.tool_order import OrderTool
from Order.tools.tool_refresh_order import RefreshOrderTool
from Point.pointcommon import PointCommon
from SecPhone.settings import logger
from User.tools.user_tool import UserTool


class PostConsumeCertificate(SecPhoneView):

    def handle_inapp_order(self, user_id: str, number_id: str, appid: int, item: dict, cert: str) -> bool:
        transaction_id = item['transaction_id']
        order = OrderConsume.objects.filter(user_id=user_id, transaction_id=transaction_id)
        if order.count() == 0:
            order = OrderConsume(
                user_id=user_id,
                number_id=number_id,
                transaction_id=transaction_id,
                appid=appid,
                order_status='DONE',
                valid=1,
                certificate=cert
            )
            order.save()
            logger.info(f"[handle_inapp_order] user:{user_id} consumer order not exists, begin handle...")

            # 充值！
            self.do_product_charge(user_id, item['product_id'], appid)
            return True
        else:
            for o in order:
                logger.warning(f"[handle_inapp_order] user: {user_id}，already has order, give up handle, order：{o.id}")
            return False

    def do_product_charge(self, user_id, product_id, appid: int):
        point = int(settings.CHARGE_POINT_PRODUCT_CONFIG[appid][product_id])
        point = point if point > 0 else 0 - point
        res, err_msg = PointCommon.Add(user_id, point, 'CHARGE')
        if res is False:
            logger.error(
                f"[消耗型订单校验] 订单校验成功，但充值失败！user_id={user_id}, appid:{appid}, product={product_id}, "
                f"point={point}")
            return self.ReturnError(ErrInfo.CHARGE_ERROR, err_msg)
        else:
            logger.error(f"贵人加点 {user_id}, appid:{appid}, {point}")

    @SecPhoneView.VerifyToken
    @SecPhoneView.VerifySign
    def post(self, request):
        post_data, err_code, err_msg = self.ReadPostJson(request, check_list=['cert', ])
        logger.info(f"[消耗型订单校验] 请求body: {post_data}")
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        try:
            header = self.GetHeaderInRequest(request)
            user_id = header['userid']
            appid = int(header['appid'])
            logger.info(f'[消耗型订单校验] user_id={user_id}, appid:{appid}')

            number_id = NumberTool.GetNumberIdByUserIdWithoutFilterDatetime(user_id)
            if number_id is None:
                number_id = 0

            # 从苹果拿订单校验
            cert = post_data['cert'].replace('\n', '').replace('\r', '')
            receipt = OrderAppleTool.get_cert_rsp(cert)
            if not receipt:
                logger.error(f"[消耗型订单校验] 订单异常!!! user_id={user_id}, appid: {appid}")
                return self.ReturnError(ErrInfo.CONSUME_ORDER_CERT_INVALID)

            # 遍历产品ID
            in_app_items = receipt.get('in_app', [])
            for item in in_app_items:
                if item['product_id'] in settings.CHARGE_POINT_PRODUCT_CONFIG[appid]:
                    logger.info(f"[消耗型订单校验] user_id={user_id}, appid:{appid}, 处理产品：{item['product_id']} ...")
                    self.handle_inapp_order(user_id, number_id, appid, item, cert)
                else:
                    logger.info(f"[消耗型订单校验] user_id={user_id}, appid:{appid}, 处理产品：{item['product_id']} "
                                f"不在产品列表中，放弃 ...")

            logger.info(f"[消耗型订单校验] user_id={user_id}, appid: {appid} 处理成功！")
            return self.ReturnSuccess()
        except Exception:
            logger.error(f"[消耗型订单校验] 服务器错误！ {post_data} ", exc_info=True)
            return self.ReturnError(ErrInfo.CONSUME_ORDER_SERVER_ERROR)


class PostCertificate(SecPhoneView):

    @staticmethod
    def _save_order_to_db(user_id: int, cert_md5: str, cert: str):
        order = Order.objects.filter(user_id=user_id)
        if order.count() == 0:
            number_used = NumberTool.GetNumberObjByUserId(user_id)
            number_id = number_used.number_id if number_used else 0

            order = Order(
                user_id=user_id,
                number_id=number_id,
                certificate=cert,
                cert_md5=cert_md5,
                appid=1,
                order_status='OPEN',
                valid=1
            )
            order.save()
        else:
            order = order[0]
            order.certificate = cert
            order.cert_md5 = cert_md5
            order.appid = 1
            order.order_status = 'OPEN'
            order.valid = 1
            order.save()
        return order

    @staticmethod
    def _buy_a_number_when_locked(user_id: int) -> ErrInfo:
        # 看看用户是否有锁定号码，如果有的话，直接买入
        lock_number_user_key = RedisKey.GenLockNumberUserKey(user_id)
        lock_number = cache.get(lock_number_user_key)
        if not lock_number:
            logger.info(f"[PostCertificate._buy_a_number_when_locked] user {user_id} has no lock number, "
                        f"give up buy number.")
            return ErrInfo.SUCCESS

        # 再次确认下，该用户没有手机号
        if NumberUsed.objects.filter(user_id=user_id, status='USING').count() >= 1:
            logger.warning(f"[PostCertificate._buy_a_number_when_locked] user {user_id} has valid phone, "
                           f"give up buy number.")
            return ErrInfo.USER_ALREADY_HAS_PHONE_NUMBER

        # 看看用户是否有锁定号码，如果有的话，直接买入
        logger.info(f"[PostCertificate._buy_a_number_when_locked] user_id={user_id}, lock_number={lock_number}")
        is_success, err_msg = NumberBuyTool.buy_number(user_id, lock_number)
        if is_success:
            # 拿到号码
            number_used = NumberTool.GetNumberObjByUserId(user_id)
            if number_used is not None:
                logger.info(f"[PostCertificate._buy_a_number_when_locked] user_id={user_id}, update order's "
                            f"number_id={number_used.number_id}")
                Order.objects.filter(user_id=user_id).update(number_id=number_used.number_id)
                return ErrInfo.SUCCESS
            else:
                logger.error(f"[PostCertificate._buy_a_number_when_locked] user_id={user_id}, "
                             f"buy error without number!")
                return ErrInfo.JUMP_BUY_NUMBER_VIEW
        else:
            logger.info(f"[PostCertificate._buy_a_number_when_locked] user_id={user_id}, lock_number={lock_number}, "
                        f"buy number error:{err_msg}")
            return err_msg

    @SecPhoneView.VerifyToken
    @SecPhoneView.VerifySign
    def post(self, request):

        post_data, err_code, err_msg = self.ReadPostJson(request, check_list=['cert', ])
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        try:
            # 一个订单只能过来成功校验一次，如果cert落库后就不能继续校验了
            header = self.GetHeaderInRequest(request)
            user_id = header['userid']
            cert = post_data['cert'].replace('\n', '').replace('\r', '')
            cert_md5 = Util.MD5Sum(cert)
            logger.info(f'[PostCertificate] user_id={user_id}, cert_md5:{cert_md5}, cert:{cert}')

            user = UserTool.get_user_by_id(user_id)
            if not user:
                logger.error(f'[PostCertificate] user_id={user_id}, cert_md5:{cert_md5}, not exist.')
                return self.ReturnError(ErrInfo.USER_NOT_EXIST)

            # 去苹果校验订单
            cert_rsp = OrderInvalidTool.get_cert_rsp(user_id, cert)
            if not cert_rsp:
                logger.warning(f'[PostCertificate] user_id:{user_id}, cert_md5:{cert_md5}, rsp cert is invalid')
                return self.ReturnError(ErrInfo.CERT_HAS_NO_EXPIRE_ERROR)
            if cert_rsp.expire_at is None:
                logger.error(f"[PostCertificate] user:{user_id}, cert without expired_at!")
                return self.ReturnError(ErrInfo.CERT_HAS_NO_EXPIRE_ERROR)

            # 判断订单重复
            if OrderDuplicateTool.is_duplicate(user_id, cert_rsp.original_transaction_id, cert_rsp.is_sandbox):
                logger.warning(f"[PostCertificate] user:{user_id}, cert duplicate: {cert_rsp.original_transaction_id}! "
                               f"cert_rsp:{cert_rsp}")
                return self.ReturnError(ErrInfo.DUPLICATE_CERT_FOR_IOS)

            # 把订单存入数据库
            order = self._save_order_to_db(user_id, cert_md5, cert)

            # 更新订单
            order.renew_status = cert_rsp.renew_status
            order.expiration_intent = cert_rsp.expiration_intent
            order.expire_at = cert_rsp.expire_at
            order.valid = 1
            order.order_status = cert_rsp.order_status
            order.original_transaction_id = cert_rsp.original_transaction_id
            order.save()

            # 如果之前锁定了号码，直接给他落DB
            try:
                err_code = self._buy_a_number_when_locked(user_id)
                if err_code != ErrInfo.SUCCESS:
                    logger.info(f"[PostCertificate] user:{user_id}, buy a number when locked failed, "
                                f"error: {err_code}, skip")
                    # 删除之前锁定的号码，让用户再去买
                    lock_number_user_key = RedisKey.GenLockNumberUserKey(user_id)
                    cache.delete(lock_number_user_key)

            except Exception as e:
                logger.error(f"[PostCertificate] user:{user_id}, buy a number when locked, failed, "
                             f"param：{post_data}, error: {e}", exc_info=True)

            # 更新用户的expire_at
            RefreshOrderTool.update_user_all_expire(user_id, cert_rsp.expire_at)

            # 更新 sip-connection
            try:
                if not user.telnyx_sip_connection_id:
                    logger.info(f'[PostCertificate] user_id={user_id}, init a sip_connection...')
                    TelnyxUtil.refresh_sip_connection(user.id)
                else:
                    logger.info(f'[PostCertificate] user_id={user_id}, already has a sip_connection, give up init')
            except Exception:
                logger.error(f'[PostCertificate] user_id={user_id}, update a sip_connection failed', exc_info=True)

            ret = {"status": cert_rsp.renew_status, "expired_at": cert_rsp.expire_at}
            logger.info(f"[PostCertificate] user:{user_id}, success：{ret}")
            return self.ReturnSuccess(ret)
        except Exception as e:
            if "Duplicate entry" in str(e):
                logger.warning(f"[PostCertificate] failed, post_data: {post_data}", exc_info=True)
                return self.ReturnSuccess()

            logger.error(f"[PostCertificate] failed, post_data: {post_data}", exc_info=True)
            return self.ReturnError(ErrInfo.JUMP_VIP_VIEW)


class UpdateOrderStatus(SecPhoneView):

    @staticmethod
    def do_update_order_status(days_ago: int):
        try:
            expired_after = TimeUtil.GetNow()
            expired_before = TimeUtil.GetNow() - timedelta(days=days_ago)

            t1 = time.time()
            logger.info(f"[UpdateOrderStatus] days: {days_ago}, begin: {expired_before}, end: {expired_after}")

            # 遍历所有订单 根据创建 + 过期时间2个维度
            if days_ago < 3:
                # 高频
                orders1 = Order.objects.filter(created_at__lte=expired_after, created_at__gte=expired_before,
                                               valid=1, order_status='OPEN').all()
                orders2 = Order.objects.filter(expire_at__lte=expired_after, expire_at__gte=expired_before,
                                               valid=1, order_status='OPEN').all()
            else:
                # 低频
                orders1 = Order.objects.filter(created_at__lte=expired_after, created_at__gte=expired_before,
                                               valid=1).all()
                orders2 = Order.objects.filter(expire_at__lte=expired_after, expire_at__gte=expired_before,
                                               valid=1).all()

            # 合并2个订单
            orders1_ids = [v.id for v in orders1]
            orders = [v for v in orders1] + [v for v in orders2 if v.id not in orders1_ids]
            logger.info(f"[UpdateOrderStatus] step 1: check OPEN STATUS, created_at and expire_at "
                        f"between: {expired_before}, {expired_after} order size = {len(orders)}")

            total_order_size = len(orders)
            for index, order in enumerate(orders):
                userid = order.user_id
                logger.info(
                    f"[UpdateOrderStatus] index:{index}/{total_order_size}, user:{userid}, order_id: {order.id}")

                user = UserTool.get_user_by_id(userid)
                if not user:
                    logger.warning(
                        f'[OrderException.UpdateOrderStatus] index:{index}/{total_order_size}, user:{userid}, '
                        f'order_id: {order.id}, not exist, give up.')
                    continue

                cert_rsp = OrderInvalidTool.get_cert_rsp(order.user_id, order.certificate)
                if not cert_rsp or cert_rsp.expire_at is None:
                    logger.warning(f"[OrderException.UpdateOrderStatus] user_id:{userid} with order: {order.id}, "
                                   f"invalid order: {cert_rsp}")
                    continue

                order.renew_status = cert_rsp.renew_status
                order.expiration_intent = cert_rsp.expiration_intent
                order.expire_at = cert_rsp.expire_at
                order.order_status = cert_rsp.order_status
                order.original_transaction_id = cert_rsp.original_transaction_id
                order.save()

                # 更新用户的expire_at
                RefreshOrderTool.update_user_all_expire(order.user_id, order.expire_at)

            t2 = time.time()
            logger.info(f"[UpdateOrderStatus] finished check orders, cost: {int((t2 - t1) * 1000)} ms")
        except Exception:
            logger.error(f"[UpdateOrderStatus] check orders failed, days_ago:{days_ago}", exc_info=True)

    def get(self, request):
        data, _, _ = self.GetDataInGet(request)
        days_ago = int(data.get('days', 7))

        t = threading.Thread(target=UpdateOrderStatus.do_update_order_status, args=(days_ago,))
        t.start()

        return self.ReturnSuccess()


class UpdateOrderStatusTest(SecPhoneView):

    @staticmethod
    def do_update_order_status_test(days_ago: int, user_id: int):
        try:
            expired_after = TimeUtil.GetNow()
            expired_before = TimeUtil.GetNow() - timedelta(days=days_ago)

            t1 = time.time()
            logger.info(f"[UpdateOrderStatusTest] days: {days_ago}, begin: {expired_before}, end: {expired_after}")

            # 遍历所有订单 根据创建 + 过期时间2个维度
            if days_ago < 3:
                # 高频
                orders1 = Order.objects.filter(created_at__lte=expired_after, created_at__gte=expired_before,
                                               valid=1, order_status='OPEN', user_id=user_id).all()
                orders2 = Order.objects.filter(expire_at__lte=expired_after, expire_at__gte=expired_before,
                                               valid=1, order_status='OPEN', user_id=user_id).all()
            else:
                # 低频
                orders1 = Order.objects.filter(created_at__lte=expired_after, created_at__gte=expired_before,
                                               valid=1, user_id=user_id).all()
                orders2 = Order.objects.filter(expire_at__lte=expired_after, expire_at__gte=expired_before,
                                               valid=1, user_id=user_id).all()

            # 合并2个订单
            orders1_ids = [v.id for v in orders1]
            orders = [v for v in orders1] + [v for v in orders2 if v.id not in orders1_ids]
            logger.info(f"[UpdateOrderStatusTest] step 1: check OPEN STATUS, created_at and expire_at "
                        f"between: {expired_before}, {expired_after} order size = {len(orders)}")

            total_order_size = len(orders)
            for index, order in enumerate(orders):
                userid = order.user_id
                logger.info(
                    f"[UpdateOrderStatusTest] index:{index}/{total_order_size}, user:{userid}, order_id: {order.id}")

                user = UserTool.get_user_by_id(userid)
                if not user:
                    logger.warning(
                        f'[OrderException.UpdateOrderStatusTest] index:{index}/{total_order_size}, user:{userid}, '
                        f'order_id: {order.id}, not exist, give up.')
                    continue

                cert_rsp = OrderInvalidTool.get_cert_rsp(order.user_id, order.certificate)
                if not cert_rsp or cert_rsp.expire_at is None:
                    logger.warning(f"[OrderException.UpdateOrderStatusTest] user_id:{userid} with order: {order.id}, "
                                   f"invalid order: {cert_rsp}")
                    continue

                order.renew_status = cert_rsp.renew_status
                order.expiration_intent = cert_rsp.expiration_intent
                order.expire_at = cert_rsp.expire_at
                order.order_status = cert_rsp.order_status
                order.original_transaction_id = cert_rsp.original_transaction_id
                order.save()

                # 更新用户的expire_at
                RefreshOrderTool.update_user_all_expire(order.user_id, order.expire_at)

            t2 = time.time()
            logger.info(f"[UpdateOrderStatusTest] finished check orders, cost: {int((t2 - t1) * 1000)} ms")
        except Exception:
            logger.error(f"[UpdateOrderStatusTest] check orders failed, days_ago:{days_ago}", exc_info=True)

    def get(self, request):
        data, _, _ = self.GetDataInGet(request)
        days_ago = int(data.get('days', 7))
        user_id = int(data.get('user_id', 0))
        if user_id <= 0:
            return self.ReturnError(ErrInfo.JSON_REQUEST_PARA_MISS)

        t = threading.Thread(target=UpdateOrderStatusTest.do_update_order_status_test, args=(days_ago, user_id,))
        t.start()

        return self.ReturnSuccess()


class CheckOrderStatus(SecPhoneView):

    def get(self, request):
        data, err_code, err_msg = self.GetDataInGet(request, ("userid", "token",))
        if err_code != 0:
            logger.info(f"[CheckOrderStatus] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(err_code, err_msg)

        data, _, _ = self.GetDataInGet(request)
        userid = int(data.get('userid', 0))
        token = data.get('token')
        if token != 'fatpo-hello':
            logger.error(f"[CheckOrderStatus] userid: {userid}, token: {token} is invalid!")
            return self.ReturnError(-1, "token is invalid")

        ret = RefreshOrderTool.refresh_user_vip(userid)
        if "err" in ret:
            return self.ReturnError(-1, "order not exists!")
        return self.ReturnSuccess(data=ret)


class AppleVerifyByUserId(SecPhoneView):

    def post(self, request):
        post_data, err_code, err_msg = self.ReadPostJson(request, check_list=["user_id", "token", "appid", ])
        if err_code != 0:
            logger.info(f"[AppleVerifyByUserId] param error：{err_msg}, path: {request.path}")
            return self.ReturnError(err_code, err_msg)

        user_id = int(post_data.get('user_id', 0))
        token = post_data.get('token')
        if token != 'fatpo-hello-3':
            return self.ReturnError(-1, "token is invalid")

        order = OrderTool.get_user_order_without_condition(user_id)
        if not order:
            return self.ReturnError(-1, "order is not exist")
        res, is_sandbox = OrderAppleTool.get_cert_rsp_from_apple(order.certificate)
        return self.ReturnSuccess(data=res)


class AppleVerifyByCert(SecPhoneView):

    def post(self, request):
        post_data, err_code, err_msg = self.ReadPostJson(request, check_list=["cert", "token", "appid", ])
        if err_code != 0:
            return self.ReturnError(err_code, err_msg)

        token = post_data.get('token')
        if token != 'fatpo-hello-3':
            return self.ReturnError(-1, "token is invalid")

        res, is_sandbox = OrderAppleTool.get_cert_rsp_from_apple(post_data['cert'])
        return self.ReturnSuccess(data=res)


class AppleNotification(SecPhoneView):
    @staticmethod
    def decode_payload(signed_payload) -> dict:

        jws_data, err = AppleIapTools.verify_jws(signed_payload)
        if err:
            logger.error(f"[AppleNotification] verify failed: {signed_payload}, err: {err}")
            return {}

        rsp = {}
        data = jws_data.get("data", {})
        if "signedTransactionInfo" in data:
            transaction_info, err = AppleIapTools.verify_jws(jws_data.get("data").get("signedTransactionInfo"))
            if not err:
                rsp["transaction_info"] = transaction_info
        if "signedRenewalInfo" in data:
            renewal_info, err = AppleIapTools.verify_jws(jws_data.get("data").get("signedRenewalInfo"))
            if not err:
                rsp["renewal_info"] = renewal_info

        return rsp

    @staticmethod
    def handle_refund(transaction_info: dict) -> bool:
        try:
            if transaction_info['bundleId'] != settings.APPLE_BUNDLE_ID[1]:
                logger.error(
                    f"[[AppleNotification.handle_refund] bundleId is invalid!!! {transaction_info['bundleId']}")

            for k, v in transaction_info.items():
                if 'Date' in k:
                    date_value = datetime.datetime.fromtimestamp(v / 1000)
                    logger.info(f"[[AppleNotification.handle_refund] {k}={date_value}")
                else:
                    logger.info(f"[[AppleNotification.handle_refund] {k}={v}")

            oti = transaction_info['originalTransactionId']
            ti = transaction_info['transactionId']

            # refund users
            refund_users = []
            orders = OrderTool.get_orders_by_original_transaction_id(oti)
            orders_consume = OrderTool.get_consume_orders_by_original_transaction_id(oti)
            refund_users.extend([v.user_id for v in orders])
            refund_users.extend([v.user_id for v in orders_consume])
            logger.warning(f"[[AppleNotification.handle_refund] refund_users, {refund_users}")

            for u in refund_users:
                if OrderTool.get_user_refund_by_oti_ti(oti, ti, u):
                    user_left_vip_days = OrderTool.get_user_left_vip_days(u)
                    logger.warning(f"[[AppleNotification.handle_refund] refund record exists, {oti}:{ti}, user:{u}, "
                                   f"user_left_vip_days:{user_left_vip_days}")
                    OrderTool.add_user_vip_update_when_exists(user_id=u,
                                                              send_days=-(user_left_vip_days + 1),
                                                              update_days=-1)
                    vip_ret = RefreshOrderTool.refresh_user_vip(u)
                    logger.warning(
                        f"[[AppleNotification.handle_refund] refund record save, user:{u}, vip_ret:{vip_ret}")
                else:
                    web_order_line_item_id = transaction_info['webOrderLineItemId'] \
                        if 'webOrderLineItemId' in transaction_info else None

                    revocation_reason = str(transaction_info['revocationReason']) \
                        if 'revocationReason' in transaction_info else '-1'

                    transaction_reason = str(transaction_info['transactionReason']) \
                        if 'transactionReason' in transaction_info else ''

                    expires_date = TimeUtil.apple_order_ts_to_dt(transaction_info['expiresDate']) \
                        if 'expiresDate' in transaction_info else None

                    if 'price' in transaction_info:
                        refund_price = transaction_info['price']
                        refund_currency = transaction_info['currency']
                    else:
                        if 'quarterly' in transaction_info['productId']:
                            refund_price = 14990
                            refund_currency = 'USD'
                        elif 'monthly' in transaction_info['productId']:
                            refund_price = 5990
                            refund_currency = 'USD'
                        elif 'yearly' in transaction_info['productId']:
                            refund_price = 49990
                            refund_currency = 'USD'
                        else:
                            logger.error(f"[[AppleNotification.handle_refund] refund invalid price: {transaction_info}")
                            continue

                    order_refund = OrderRefund(original_transaction_id=oti,
                                               transaction_id=ti,
                                               user_id=u,
                                               web_order_line_item_id=web_order_line_item_id,
                                               revocation_date=TimeUtil.apple_order_ts_to_dt(
                                                   transaction_info['revocationDate']),
                                               revocation_reason=revocation_reason,
                                               transaction_reason=transaction_reason,
                                               refund_type=transaction_info['type'],
                                               refund_product_id=transaction_info['productId'],
                                               refund_price=refund_price,
                                               refund_currency=refund_currency,
                                               original_purchase_date=TimeUtil.apple_order_ts_to_dt(
                                                   transaction_info['originalPurchaseDate']),
                                               purchase_date=TimeUtil.apple_order_ts_to_dt(
                                                   transaction_info['purchaseDate']),
                                               expires_date=expires_date,
                                               )
                    order_refund.save()
                    logger.warning(f"[[AppleNotification.handle_refund] refund record save, {oti}:{ti}, user:{u}")

                    OrderTool.add_user_vip_update_when_exists(u, -1000, -1)
                    vip_ret = RefreshOrderTool.refresh_user_vip(u)
                    logger.warning(
                        f"[[AppleNotification.handle_refund] refund record save, user:{u}, vip_ret:{vip_ret}")
            return True
        except Exception:
            logger.error(f"[[AppleNotification.handle_refund] refund failed, {transaction_info}", exc_info=True)
            return False

    def post(self, request):
        try:
            json_str = request.body.decode('utf-8')
            json_obj = json.loads(json_str)
            logger.info(json_obj)
            logger.info(f"[AppleNotification.post] json_obj:  {json_obj}")

            decode_payload = self.decode_payload(json_obj['signedPayload'])
            logger.info(f"[AppleNotification.post] decode_payload:  {decode_payload}")

            if "revocationDate" in decode_payload["transaction_info"] \
                    or "revocationReason" in decode_payload["transaction_info"]:
                if not self.handle_refund(decode_payload["transaction_info"]):
                    logger.error(f"[AppleNotification.post] refund failed: {json_obj}")
                    return self.ReturnErrorToApple(ErrInfo.UNKNOWN_SERVER_ERROR)

            return self.ReturnSuccess()
        except Exception:
            logger.warning(f"[AppleNotification.post] apple notification failed", exc_info=True)
            return self.ReturnSuccess()
