from django.db import models


class Order(models.Model):
    RENEW_STATUS_CHOICES = (
        (1, 'auto-renew'),
        (0, 'not auto-renew'),
    )

    EXPIRATION_INTENT_CHOICES = (
        (0, 'bill success'),
        (1, 'user trial time to cancel'),
        (2, 'bill error'),
        (3, 'The customer did not agree to a recent price increase'),
        (4, 'The product was not available for purchase at the time of renewal'),
        (5, 'refund'),
        (6, 'other'),
    )

    VALID_CHOICES = (
        (0, 'invalid'),
        (1, 'valid'),
    )

    """
    expiration_intent Possible Values
    1 The customer voluntarily canceled their subscription.
    2 Billing error; for example, the customer's payment information was no longer valid.
    3 The customer did not agree to a recent price increase.
    4 The product was not available for purchase at the time of renewal.
    5 Unknown error.
    """
    user_id = models.IntegerField()
    number_id = models.IntegerField()
    renew_status = models.IntegerField(blank=True, choices=RENEW_STATUS_CHOICES)  # 1: auto-renew, 0: not auto-renew

    # 0: bill success, 1: user trial time to cancel, 2: bill error, 3: The customer did not agree to a recent price increase.4:The product was not available for purchase at the time of renewal. 5.refund. 6:other
    expiration_intent = models.IntegerField(blank=True, default=0, choices=EXPIRATION_INTENT_CHOICES)

    expire_at = models.DateTimeField(blank=True)
    original_transaction_id = models.CharField(default='', blank=True, max_length=128)
    cert_md5 = models.CharField(default='', blank=True, max_length=64)
    certificate = models.TextField()

    appid = models.IntegerField(default=0)

    valid = models.IntegerField(default=1, choices=VALID_CHOICES)  # 0: invalid, 1: valid
    order_status = models.CharField(default='OPEN', blank=True, max_length=20)  ## OPEN, CLOSED,

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class OrderConsume(models.Model):
    user_id = models.IntegerField()
    number_id = models.IntegerField()
    transaction_id = models.CharField(blank=False, max_length=128)
    appid = models.IntegerField(default=0)

    certificate = models.TextField()

    valid = models.IntegerField(default=1)  # 0: invalid, 1: valid
    order_status = models.CharField(default='DONE', blank=True, max_length=20)  ## ING, DONE,

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class OrderSendVip(models.Model):
    user_id = models.IntegerField()
    send_days = models.IntegerField(default=0)
    deleted = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class OrderDuplicate(models.Model):
    user_id = models.IntegerField()
    original_transaction_id = models.CharField(default='', blank=True, max_length=128)
    duplicate_user_id = models.IntegerField()
    duplicate_email = models.CharField(default='', blank=True, max_length=64)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class OrderRefund(models.Model):
    REVOCATION_REASON_CHOICES = (
        ('0', '意外购买'),
        ('1', 'APP有问题退款'),
    )

    original_transaction_id = models.CharField(default='', blank=True, max_length=64)
    transaction_id = models.CharField(default='', blank=True, max_length=64)
    web_order_line_item_id = models.CharField(default='', blank=True, max_length=64)
    user_id = models.IntegerField(blank=True)
    revocation_date = models.DateTimeField(blank=True)
    revocation_reason = models.CharField(default='', blank=True, max_length=32, choices=REVOCATION_REASON_CHOICES)
    transaction_reason = models.CharField(default='', blank=True, max_length=32)
    refund_type = models.CharField(default='', blank=True, max_length=32)
    refund_product_id = models.CharField(default='', blank=True, max_length=16)
    refund_price = models.IntegerField()
    refund_currency = models.CharField(default='', blank=True, max_length=16)
    original_purchase_date = models.DateTimeField(blank=True)
    purchase_date = models.DateTimeField(blank=True)
    expires_date = models.DateTimeField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class BlackOrderRecord(models.Model):
    user_id = models.IntegerField()
    device_id = models.CharField(blank=False, max_length=128)
    original_transaction_id = models.CharField(blank=False, max_length=128)
    cert_md5 = models.CharField(blank=False, max_length=128)
    ban_days = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()
