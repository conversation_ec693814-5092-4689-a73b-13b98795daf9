from datetime import datetime

from django.db import transaction

from Common.err import ErrInfo
from Common.timeutil import TimeUtil
from Common.util import Util
from Number.models import NumberUsed
from Number.tools.number_tool import NumberTool
from Order.models import OrderDuplicate, Order
from Order.tools.tool_invalid_order import OrderInvalidTool
from Order.tools.tool_order import OrderTool
from Point.models import PointRecord
from SecPhone.settings import logger
from User.tools.user_tool import UserTool


class RefreshOrderTool:
    @staticmethod
    def recover_order_subscription(user_id: int) -> (bool, str):

        od = OrderDuplicate.objects.filter(user_id=user_id).first()
        if not od:
            logger.warning(f"[RefreshOrderTool.recover_order_subscription] user:{user_id} has no duplicate user_id")
            return False, "can't find duplicate user_id to recover"

        if OrderTool.is_user_vip_expire(user_id) == ErrInfo.SUCCESS:
            logger.warning(f"[RefreshOrderTool.recover_order_subscription] user:{user_id} is already a vip")
            return False, "user is already a vip!"

        old_user_id = od.duplicate_user_id
        new_user_id = user_id

        new_order = Order.objects.filter(user_id=new_user_id).first()
        if new_order:
            logger.error(f"[RefreshOrderTool] recover failed")
            return False, f"user already has order, new_user_id:{new_user_id}, expired_at:{new_order.expire_at}, " \
                          f"status:{new_order.order_status}, valid:{new_order.valid}"

        Order.objects.filter(user_id=old_user_id).update(user_id=new_user_id)
        NumberUsed.objects.filter(user_id=old_user_id).update(user_id=new_user_id)
        PointRecord.objects.filter(user_id=old_user_id).update(user_id=new_user_id)

        RefreshOrderTool.refresh_user_vip(new_user_id)

        return True, "success"

    @staticmethod
    def refresh_user_vip(userid: int):
        order = OrderTool.get_user_order_without_condition(userid)
        if not order:
            od = OrderDuplicate.objects.filter(user_id=userid).first()
            if od:
                logger.warning(f"[CheckOrderStatus] userid: {userid}, order not exists!")
                return {"err": f"duplicate order",
                        "duplicate_user_id": od.duplicate_user_id,
                        "duplicate_email": od.duplicate_email,
                        "话术": f"Hello, may I ask if this account belongs to you? "
                                f"Your subscription belongs to this account：{Util.mask_email(od.duplicate_email)}"
                        }
            logger.warning(f"[CheckOrderStatus] userid: {userid}, order not exists!")
            return {"err": "invalid order, not exist"}

        cert_rsp = OrderInvalidTool.get_cert_rsp(order.user_id, order.certificate)
        if not cert_rsp:
            return {"err": "invalid order, no expired_at"}

        # 订单状态对不上、过期时间对不上、取消理由对不上则重新刷新
        ret = {
            "refresh": 0,
            "user_id": userid,
        }

        if order.expiration_intent != int(cert_rsp.expiration_intent) \
                or order.order_status != cert_rsp.order_status \
                or abs(TimeUtil.GetDiffMinutes(order.expire_at, cert_rsp.expire_at)) >= 10:
            ret["refresh"] = 1
            ret["before_original_transaction_id"] = order.original_transaction_id
            ret["before_renew_status"] = order.renew_status
            ret["before_expiration_intent"] = order.expiration_intent
            ret["before_expired_at"] = order.expire_at
            ret["before_order_status"] = order.order_status

            order.original_transaction_id = cert_rsp.original_transaction_id
            order.renew_status = cert_rsp.renew_status
            order.expiration_intent = cert_rsp.expiration_intent
            order.expire_at = cert_rsp.expire_at
            order.order_status = cert_rsp.order_status
            order.save()

        # 更新用户的expire_at
        RefreshOrderTool.update_user_all_expire(order.user_id, order.expire_at)

        ret.update({
            "original_transaction_id": cert_rsp.original_transaction_id,
            "renew_status": int(cert_rsp.renew_status),
            "expired_at": cert_rsp.expire_at,
            "order_status": cert_rsp.order_status,
            "expiration_intent": int(cert_rsp.expiration_intent),
            "is_order_valid": order.valid,
        })
        return ret

    @staticmethod
    def update_user_all_expire(user_id: int, expired_at: datetime):
        """
        更新所有生命周期的过期时间： order.order.expired_at + user.expired_at +  number_used.expired_at
        :param user_id: 用户ID
        :param expired_at: 订单实际的过期时间
        :return:
        """
        try:
            if not expired_at:
                logger.error(f"[RefreshOrderTool.update_user_all_expire] user:{user_id} expire: {expired_at} is empty")
                return

            with transaction.atomic():
                logger.info(f"[RefreshOrderTool.update_user_all_expire] user:{user_id} expire: {expired_at}...")
                user = UserTool.get_user_by_id_with_deleted(user_id)
                if user:
                    user.expired_at = expired_at
                    user.save()
                else:
                    logger.error(f"[RefreshOrderTool.update_user_all_expire] user:{user_id} not exists...")

                # 不排除一个用户可以映射多一个号码，但这种多号码状态要报警
                status = 'USING' if OrderTool.is_user_vip_expire(user_id) == ErrInfo.SUCCESS else 'EXPIRE'
                if user.deleted == 1:
                    status = 'EXPIRE'

                # 更新number_used状态
                number_used_list = NumberTool.get_using_numbers_by_userid(user_id)
                for number_used in number_used_list:
                    logger.info(f"[RefreshOrderTool.update_user_all_expire] update user {user_id} 's number_used "
                                f"expire: {expired_at}")
                    number_used.expired_at = expired_at
                    number_used.status = status
                    number_used.save()

                # 如果expire则顺手释放号码池
                if number_used_list and len(number_used_list) > 0 and status == 'EXPIRE':
                    number_inventory_obj = NumberTool.get_using_number(number_used_list[0].number)
                    if number_inventory_obj:
                        number_inventory_obj.status = 'EXPIRE'
                        number_inventory_obj.save()

        except Exception:
            logger.error(f"[RefreshOrderTool.update_user_all_expire] {user_id} {expired_at} failed", exc_info=True)
