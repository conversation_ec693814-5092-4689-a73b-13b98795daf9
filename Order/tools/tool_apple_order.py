import requests
from ratelimit import sleep_and_retry, limits

from SecPhone import settings
from SecPhone.settings import logger


class OrderAppleTool:
    @staticmethod
    def get_cert_rsp(cert: str) -> dict:
        try:
            appid = 1
            res, is_sandbox = OrderAppleTool.get_cert_rsp_from_apple(cert)
            logger.info(f"[OrderAppleTool] apple res：{res}, is_sandbox:{is_sandbox}")
            receipt = res['receipt']
            bundle_id = receipt['bundle_id']

            # 检查包名是否正确
            if bundle_id != settings.APPLE_BUNDLE_ID[int(appid)]:
                logger.error(f"[OrderAppleTool] apple invalid bundle_id：{bundle_id}")
                return {}

            return receipt

        except Exception:
            logger.error(f'[OrderAppleTool] get from apple order server failed', exc_info=True)
            return {}

    @staticmethod
    @sleep_and_retry
    @limits(calls=5, period=1)
    def get_cert_rsp_from_apple(cert) -> (dict, bool):
        # 先过一次生产校验
        appid = 1
        data = {"receipt-data": cert, "password": settings.APPLE_VERIFY_PASSWORD[int(appid)]}
        r = requests.post(settings.APPLE_VERIFY_URL, json=data)
        res = r.json()

        # 如果苹果返回21007，就要去沙箱校验
        apple_return_status = res['status']
        if apple_return_status == 21007:
            logger.info("[OrderAppleTool] apple prd env check failed，return 21007, change to sandbox env.")
            data = {"receipt-data": cert, "password": settings.APPLE_VERIFY_PASSWORD[int(appid)]}
            r = requests.post(settings.APPLE_VERIFY_URL_SANDBOX, json=data)
            res = r.json()
            return res, True
        return res, False
