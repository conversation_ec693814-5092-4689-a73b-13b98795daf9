from django.conf import settings
from django.core.cache import cache

from Common.rediskey import Redis<PERSON>ey
from Number.models import NumberUsed
from Number.tools.number_tool import NumberTool
from Order.models import Order, OrderDuplicate
from Point.models import PointRecord
from SecPhone.settings import logger
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from User.tools.user_tool import UserTool


class OrderDuplicateTool:

    @staticmethod
    def is_duplicate(user_id: int, original_transaction_id: str, is_sandbox: bool) -> bool:
        if user_id in settings.WHITE_USER_LIST:
            logger.info(f"[OrderDuplicateTool] user: {user_id} is white, pass !!!")
            return False

        if is_sandbox:
            logger.info(f"[OrderDuplicateTool] user: {user_id} is sandbox, pass!!!")
            return False

        orders = Order.objects.filter(original_transaction_id=original_transaction_id).all()
        if len(orders) == 0:
            return False

        exists_original_transaction_ids = [v.original_transaction_id for v in orders]
        exists_user_ids = [v.user_id for v in orders if v.user_id != user_id]
        for o in orders:
            if user_id == o.user_id:
                return False
        else:
            duplicate_userid = exists_user_ids[0]
            od = OrderDuplicate.objects.filter(user_id=user_id, original_transaction_id=original_transaction_id).first()
            if not od:
                logger.warning(f"[OrderDuplicateTool] user: {user_id} duplicate: {duplicate_userid} save")
                duplicate_user = UserTool.get_user_by_id_with_deleted(duplicate_userid)
                if duplicate_user:
                    od = OrderDuplicate(user_id=user_id,
                                        original_transaction_id=original_transaction_id,
                                        duplicate_user_id=duplicate_userid,
                                        duplicate_email=duplicate_user.email)
                    od.save()
                else:
                    logger.warning(f"[OrderDuplicateTool] user: {user_id} not exist?")
            else:
                logger.warning(f"[OrderDuplicateTool] user: {user_id} duplicate: {duplicate_userid} already saved")

            logger.warning(f"[OrderDuplicateTool] user: {user_id} original_transaction_id: {original_transaction_id}, "
                           f"exists_original_transaction_ids：{exists_original_transaction_ids}, duplicate!!!")

            # recover order when duplicate
            OrderDuplicateTool.recover_order_when_duplicate(user_id, duplicate_userid)
            return True

    @staticmethod
    def recover_order_when_duplicate(new_user_id: int, old_user_id: int):
        try:
            notice_key = RedisKey.notice_duplicate_order(new_user_id, old_user_id)
            if cache.get(notice_key):
                logger.info(
                    f"[OrderDuplicateTool] recover_order hit cache:{notice_key}, pass, new_user_id:{new_user_id} "
                    f"must be > old_user_id:{old_user_id}")
                return

            if new_user_id < old_user_id:
                logger.warning(f"[OrderDuplicateTool] recover_order failed, new_user_id:{new_user_id} "
                               f"must be > old_user_id:{old_user_id}")
                return

            # set cache，避免多次notice
            cache.set(notice_key, 1, 180)
            logger.info(f"[OrderDuplicateTool] recover_order set cache:{notice_key}, new_user_id:{new_user_id} "
                        f"must be > old_user_id:{old_user_id}")

            new_user = UserTool.get_user_by_id_with_deleted(new_user_id)
            old_user = UserTool.get_user_by_id_with_deleted(old_user_id)
            if new_user.uuid != old_user.uuid:
                logger.warning(
                    f"[OrderDuplicateTool] recover_order failed, new_user_id:{new_user_id} must be same device "
                    f"with old_user_id:{old_user_id}")

                to_number = NumberTool.get_mock_number_by_userid(new_user_id)
                content = SmsNoticeTool.get_bind_subscription_failed()
                SmsItSupportTool.add_support_sms_both_from_it(new_user_id, to_number, content)
                return

            if new_user.app_version < 300000:
                logger.warning(
                    f"[OrderDuplicateTool] recover_order failed, new_user:{new_user_id}:{new_user.app_version},"
                    f"old_user:{old_user_id}:{old_user.app_version}, must be app_version >= 300000")
                return

            if Order.objects.filter(user_id=new_user_id).exists():
                logger.warning(
                    f"[OrderDuplicateTool] recover_order failed, new_user:{new_user_id}:{new_user.app_version},"
                    f"old_user:{old_user_id}:{old_user.app_version}, new user already has a order")
                return

            if NumberUsed.objects.filter(user_id=old_user_id, status='USING').exists():
                logger.warning(
                    f"[OrderDuplicateTool] recover_order failed, new_user:{new_user_id}:{new_user.app_version},"
                    f"old_user:{old_user_id}:{old_user.app_version}, new user already has a number")
                return

            Order.objects.filter(user_id=old_user_id).update(user_id=new_user_id)
            NumberUsed.objects.filter(user_id=old_user_id).update(user_id=new_user_id)
            PointRecord.objects.filter(user_id=old_user_id).update(user_id=new_user_id)
            logger.warning(f"[OrderDuplicateTool] recover_order success, new_user_id:{new_user_id} must be same device "
                           f"with old_user_id:{old_user_id}")
        except Exception:
            logger.error(f"[OrderDuplicateTool] recover_order failed, new_user_id:{new_user_id} "
                         f"must be > old_user_id:{old_user_id}", exc_info=True)
