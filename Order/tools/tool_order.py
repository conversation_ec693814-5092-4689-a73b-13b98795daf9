import datetime
from typing import Union

from django.db.models import F

from Common.err import ErrInfo
from Common.timeutil import TimeUtil
from Order.models import Order, OrderConsume, OrderRefund, OrderSendVip, OrderDuplicate
from SecPhone import settings
from SecPhone.settings import logger


class OrderTool:

    @staticmethod
    def get_user_order(user_id: int) -> Union[Order, None]:
        order = Order.objects.filter(user_id=user_id, order_status='OPEN', valid=1).first()
        if order:
            return order
        else:
            return None

    @staticmethod
    def get_user_order_without_condition(user_id: int) -> Union[Order, None]:
        order = Order.objects.filter(user_id=user_id).first()
        if order:
            return order
        else:
            return None

    @staticmethod
    def get_orders_by_original_transaction_id(original_transaction_id: str) -> list:
        orders = Order.objects.filter(original_transaction_id=original_transaction_id).all()
        return orders

    @staticmethod
    def get_consume_orders_by_original_transaction_id(original_transaction_id: str) -> list:
        orders = OrderConsume.objects.filter(transaction_id=original_transaction_id).all()
        return orders

    @staticmethod
    def get_user_refund_by_oti_ti(original_transaction_id: str, transaction_id: str, user_id: int):
        return OrderRefund.objects.filter(original_transaction_id=original_transaction_id,
                                          transaction_id=transaction_id,
                                          user_id=user_id).first()

    @staticmethod
    def get_user_order_all_status(user_id: int) -> Union[Order, None]:
        order = Order.objects.filter(user_id=user_id, valid=1).first()
        if order:
            return order
        else:
            return None

    @staticmethod
    def get_user_expire(user_id: int) -> Union[datetime.datetime, None]:
        if user_id in settings.WHITE_USER_LIST:
            return TimeUtil.GetNow() + datetime.timedelta(days=3)

        order = OrderTool.get_user_order(user_id)
        if order:
            return order.expire_at
        else:
            return None

    @staticmethod
    def get_user_expire_all_status(user_id: int) -> Union[datetime.datetime, None]:
        if user_id in settings.WHITE_USER_LIST:
            return TimeUtil.GetNow() + datetime.timedelta(days=3)

        order = OrderTool.get_user_order_all_status(user_id)
        if order:
            return order.expire_at
        else:
            return None

    @staticmethod
    def get_order_by_cert(cert: str) -> Union[Order, None]:
        order = Order.objects.filter(certificate=cert).first()
        if order:
            return order
        else:
            return None

    @staticmethod
    def get_order_consume_by_cert(cert: str) -> Union[OrderConsume, None]:
        order = OrderConsume.objects.filter(certificate=cert).first()
        if order:
            return order
        else:
            return None

    @staticmethod
    def recover_user_order_valid(user_id: int):
        Order.objects.filter(user_id=user_id).update(valid=1)

    @staticmethod
    def is_user_vip_expire(userid: int) -> ErrInfo:
        if userid in settings.WHITE_USER_LIST:
            return ErrInfo.SUCCESS

        # 这里不允许closed订单出现，不能豁免
        order = OrderTool.get_user_order(userid)
        if not order:
            logger.info(f"[OrderTool.is_user_vip_expire] user: {userid} has no order, not vip.")
            return ErrInfo.JUMP_VIP_VIEW

        assert isinstance(order, Order)
        if not order.expire_at:
            logger.error(f"[OrderTool.is_user_vip_expire] user: {userid} has order but expire_at is null, not vip.")
            return ErrInfo.JUMP_VIP_VIEW

        if TimeUtil.GetDiffMinutesWithNow(order.expire_at) < 1:
            logger.info(f"[OrderTool.is_user_vip_expire] used: {userid}, "
                        f"order expire at {order.expire_at}, now is expired, is not vip.")
            return ErrInfo.JUMP_VIP_VIEW

        return ErrInfo.SUCCESS

    @staticmethod
    def is_user_trail_vip(user_id: int) -> bool:
        # 至少是一个合法订单
        if OrderTool.is_user_vip_expire(user_id) != ErrInfo.SUCCESS:
            return False

        # 再来判断是不是试用期
        return Order.objects.filter(user_id=user_id,
                                    expire_at__lte=F('created_at') + datetime.timedelta(days=4)).exists()

    @staticmethod
    def get_send_vip_days(user_id: int) -> int:
        send_vip = OrderSendVip.objects.filter(user_id=user_id, deleted=0).first()
        if send_vip:
            return send_vip.send_days
        return 0

    @staticmethod
    def add_user_vip(user_id: int, send_days: int):
        record = OrderSendVip.objects.filter(user_id=user_id).first()
        if not record:
            send_vip = OrderSendVip(user_id=user_id, send_days=send_days)
            send_vip.save()
        else:
            record.send_days += send_days
            record.save()

    @staticmethod
    def add_user_vip_update_when_exists(user_id: int, send_days: int, update_days: int):
        record = OrderSendVip.objects.filter(user_id=user_id).first()
        if not record:
            send_vip = OrderSendVip(user_id=user_id, send_days=send_days)
            send_vip.save()
        else:
            record.send_days += update_days
            record.save()

    @staticmethod
    def get_duplicate_info_list(user_id: int) -> list:
        records = OrderDuplicate.objects.filter(user_id=user_id).all()
        res = []
        for r in records:
            res.append({
                "original_transaction_id": r.original_transaction_id,
                "duplicate_user_id": r.duplicate_user_id,
                "duplicate_email": r.duplicate_email,
                "created_at": TimeUtil.GetBeijingTimeStr(r.created_at),
            })
        return res

    @staticmethod
    def get_user_vip_days(user_id: int) -> int:
        order = OrderTool.get_user_order(user_id)
        if not order:
            logger.warning(f"[OrderTool.get_user_vip_days] user_id:{user_id} order not exists or expired")
            return 0

        return TimeUtil.GetDiffDays(TimeUtil.GetNow(), order.created_at)

    @staticmethod
    def get_user_left_vip_days(user_id: int) -> int:
        order = OrderTool.get_user_order(user_id)
        if not order:
            logger.warning(f"[OrderTool.get_user_left_vip_days] user_id:{user_id} order not exists or expired")
            return 0

        left_vip_days = TimeUtil.GetDiffDays(order.expire_at, TimeUtil.GetNow())
        if left_vip_days < 0:
            logger.error(f"[OrderTool.get_user_left_vip_days] user_id:{user_id} left_vip_days:{left_vip_days} < 0")
            return 0

        return left_vip_days
