from django.contrib import admin
from django.utils.html import format_html

from Order.models import Order, OrderConsume, OrderSendVip, OrderDuplicate, OrderRefund


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'renew_status', 'expiration_intent', 'expire_at',
                    'original_transaction_id', 'valid', 'order_status', 'created_at', 'updated_at',)
    readonly_fields = ('user_id', 'original_transaction_id', 'cert_md5', 'certificate',)
    exclude = ('certificate', 'appid',)
    list_per_page = 50  # 设置每页显示 50 条记录

    class Media:
        js = ('admin/js/custom_order_record_admin.js',)  # 引入自定义的 JavaScript 代码
        css = {
            'all': ('admin/css/custom_order_record_admin.css',)  # 引入自定义的 CSS 文件
        }

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        user_id = request.GET.get('user_id', '').strip()
        original_transaction_id = request.GET.get('original_transaction_id', '').strip()
        cert_md5 = request.GET.get('cert_md5', '').strip()

        if user_id:
            user_id = int(user_id)
            queryset = queryset.filter(user_id=user_id)
        if original_transaction_id:
            queryset = queryset.filter(original_transaction_id=original_transaction_id)
        if cert_md5:
            queryset = queryset.filter(cert_md5=cert_md5)
        return queryset


@admin.register(OrderConsume)
class OrderConsumeAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'transaction_id', 'valid', 'order_status', 'created_at', 'updated_at',)
    search_fields = ('user_id', 'transaction_id',)
    exclude = ('certificate', 'appid',)
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(OrderSendVip)
class OrderSendVipAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'send_days', 'deleted', 'created_at', 'updated_at',)
    search_fields = ('user_id',)
    list_filter = ('created_at', 'send_days',)
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(OrderDuplicate)
class OrderDuplicateAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'user_id', 'original_transaction_id', 'duplicate_user_id', 'duplicate_email', 'created_at', 'updated_at',)
    search_fields = ('user_id', 'original_transaction_id', 'duplicate_user_id', 'duplicate_email',)
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(OrderRefund)
class OrderRefundAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'user_id', 'combined_id_info', 'refund_type',
        'revocation_date', 'revocation_reason',
        'transaction_reason', 'combined_refund_info', 'created_at', 'combined_dt_info',)
    search_fields = ('user_id', 'original_transaction_id', 'transaction_id',)
    list_filter = ('revocation_reason', 'purchase_date', 'expires_date', 'created_at',)
    list_per_page = 50  # 设置每页显示 50 条记录

    def combined_id_info(self, obj):
        return format_html(
            "original_transaction_id: {}<br><br>transaction_id: {}<br><br>web_order_line_item_id: {}",
            obj.original_transaction_id, obj.transaction_id, obj.web_order_line_item_id)

    combined_id_info.short_description = 'Order Id Info'

    def combined_dt_info(self, obj):
        return format_html(
            "original_purchase_date: {}<br><br>purchase_date: {}<br><br>expires_date: {}<br><br>updated_at: {}",
            obj.original_purchase_date, obj.purchase_date, obj.expires_date, obj.updated_at)

    combined_dt_info.short_description = 'Dt Info'

    def combined_refund_info(self, obj):
        return format_html(
            "refund_product_id: {}<br><br>refund_price: {}<br><br>refund_currency: {}",
            obj.refund_product_id, obj.refund_price, obj.refund_currency)

    combined_refund_info.short_description = 'Refund Info'
