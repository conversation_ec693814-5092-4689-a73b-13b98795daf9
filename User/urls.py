"""
@author: jon
"""
from django.conf.urls import url

from User import userviews

urlpatterns = [
    url(r'^user/unifyLogin/$', userviews.UserUnifyLogin.as_view()),
    url(r'^user/delete/$', userviews.Delete.as_view()),
    url(r'^user/getProfile/$', userviews.GetUserProfile.as_view()),
    url(r'^user/checkUser/$', userviews.CheckUserView.as_view()),
    url(r'^user/registerPushId/$', userviews.RegisterPushId.as_view()),
    url(r'^user/registerTwDevice/$', userviews.RegisterTwDevice.as_view()),

    # new app
    url(r'^zhphone/user/login/$', userviews.UserUnifyLogin.as_view()),
    url(r'^zhphone/user/delete/$', userviews.Delete.as_view()),
    url(r'^zhphone/user/profile/$', userviews.GetUserProfile.as_view()),
    url(r'^zhphone/user/profileByDevice/$', userviews.GetUserProfileWithDevice.as_view()),
    url(r'^zhphone/user/check/$', userviews.CheckUserView.as_view()),
    url(r'^zhphone/user/register/pushid/$', userviews.RegisterPushId.as_view()),
    url(r'^zhphone/user/register/tw/$', userviews.RegisterTwDevice.as_view()),
]
