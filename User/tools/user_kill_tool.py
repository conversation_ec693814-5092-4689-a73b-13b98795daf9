import datetime

from django.core.cache import cache

from Call.models import SMS<PERSON><PERSON>ord
from Common.rediskey import <PERSON><PERSON><PERSON><PERSON>
from Common.timeutil import TimeUtil
from MyTelnyx.my_telnyx import TelnyxUtil
from Number.models import NumberAdminLock
from Number.tools.number_lock_tool import <PERSON><PERSON><PERSON>Tool
from Number.tools.number_tool import NumberTool
from Order.models import BlackOrderRecord
from Order.tools.tool_order import OrderTool
from Order.tools.tool_refresh_order import Refresh<PERSON>rderTool
from SecPhone import settings
from SecPhone.settings import logger
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from Sms.tools.tool_sms import SmsTool
from User.tools.user_tool import UserTool


class UserKillTool:
    @staticmethod
    def tmp_kill_user(user_id: int):
        # 暂时封杀
        seconds = 3600
        cache.set(RedisKey.gen_tmp_kill_user(user_id), 1, seconds)
        logger.warning(f"[UserKillTool] kill user: {user_id} for {seconds} seconds")

    @staticmethod
    def tmp_mute_user(user_id: int, seconds: int):
        # 暂时封杀
        cache.set(RedisKey.gen_tmp_mute_user(user_id), 1, seconds)
        logger.warning(f"[UserKillTool] mute user: {user_id} for {seconds} seconds")

    @staticmethod
    def un_kill_tmp_user(user_id: int):
        # 暂时解除封杀
        cache.delete(RedisKey.gen_tmp_kill_user(user_id))
        logger.warning(f"[UserKillTool] un-kill user: {user_id}")

    @staticmethod
    def is_tmp_kill(user_id: int) -> bool:
        key = RedisKey.gen_tmp_kill_user(user_id)
        val = cache.get(key)
        if val:
            ttl = cache.ttl(key)
            logger.warning(f"[UserKillTool] is user killed: {user_id}, ttl:{ttl} seconds")
            return True
        return False

    @staticmethod
    def mute_user_temporarily(user_id: int, seconds: int):
        cache.set(RedisKey.gen_tmp_mute_user(user_id), 1, seconds)
        logger.warning(f"[UserKillTool] user mute: {user_id}, {seconds} seconds")

    @staticmethod
    def un_mute_user_temporarily(user_id: int):
        # 解除暂时禁言
        cache.delete(RedisKey.gen_tmp_mute_user(user_id))
        logger.warning(f"[UserKillTool] user un-mute: {user_id}")

    @staticmethod
    def is_tmp_mute(user_id: int) -> bool:
        key = RedisKey.gen_tmp_mute_user(user_id)
        val = cache.get(key)
        if val:
            ttl = cache.ttl(key)
            logger.warning(f"[UserKillTool] is user mute: {user_id}, ttl:{ttl} seconds")
            return True
        return False

    @staticmethod
    def kill_user(user_id: int, add_days: int):
        if user_id == 0:
            logger.error(f"[UserKillTool] userid is 0")
            return

        if add_days >= 0:
            logger.error(f"[UserKillTool] userid:{user_id}, add_days:{add_days} invalid")
            return

        # 号码暂时存着，不要被人使用
        number = NumberTool.get_number_by_userid(user_id)
        after_7_days_dt = TimeUtil.AddTimeDays(TimeUtil.GetNow(), 7)
        after_7_days_str = TimeUtil.GetBeijingDateStr(after_7_days_dt)
        lock_number = NumberAdminLock.objects.filter(user_id=user_id, number=number, deleted=0).first()
        if lock_number:
            lock_number.expired_at = after_7_days_dt
            lock_number.save()
        else:
            NumberLockTool.add_lock_number(user_id, number, after_7_days_str)

        # 加VIP
        OrderTool.add_user_vip(user_id, add_days)

        # 更新 expired
        vip_ret = RefreshOrderTool.refresh_user_vip(user_id)
        logger.error(f"[封号]{vip_ret}")

        # 顺便把待审核的短信干掉
        if add_days <= -1000:
            before_ts = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(hours=2)
            SMSRecord.objects.filter(user_id=user_id, status='send_fake', is_reviewed=0, is_fatpo_resend=0,
                                     created_at__gte=before_ts).update(is_reviewed=1)

        # 记录下
        try:
            user = UserTool.get_user_by_id(user_id)
            order = OrderTool.get_user_order_without_condition(user_id)
            black_order = BlackOrderRecord.objects.filter(user_id=user_id).first()
            if not black_order:
                black_order_record = BlackOrderRecord(user_id=user_id, device_id=user.uuid,
                                                      original_transaction_id=order.original_transaction_id,
                                                      ban_days=add_days, cert_md5=order.cert_md5)
                black_order_record.save()
        except Exception:
            logger.error(f"[UserKillTool] userid:{user_id}, add_days:{add_days} record failed", exc_info=True)

        # 把这个用户的坏图片加到 Call_badimage中
        try:
            bad_images = SmsTool.get_user_all_images(user_id)
            for image in bad_images:
                SmsTool.add_bad_image_to_call_bad_image(image.user_id, image.image_md5, image.images)
        except Exception:
            logger.error(f"[UserKillTool] add_bad_image_to_call_bad_image failed, user_id:{user_id}", exc_info=True)

        # 推送下
        if add_days == -1999:
            push_content = SmsNoticeTool.temporary_ban_notification_racial_discrimination()
        elif add_days == -1995:
            push_content = SmsNoticeTool.temporary_ban_notification_fraud()
        elif add_days == -1992:
            push_content = SmsNoticeTool.temporary_ban_notification_harassment()
        elif add_days == -1991:
            push_content = SmsNoticeTool.temporary_ban_notification_threaten()
        elif add_days == -1990:
            push_content = SmsNoticeTool.temporary_ban_notification_pornographic()
        else:
            push_content = SmsNoticeTool.temporary_ban_notification()

        to_number = NumberTool.get_number_by_userid(user_id)
        SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                         settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)

        # 删除SIP
        user = UserTool.get_user_by_id(user_id)
        if user and user.telnyx_sip_connection_id:
            TelnyxUtil.delete_a_sip(user.telnyx_sip_connection_id)
        else:
            logger.error(f"[UserKillTool] userid:{user_id} delete sip failed")

        return

    @staticmethod
    def un_kill_user(user_id: int):
        if user_id == 0:
            logger.error(f"[UserKillTool] userid is 0")
            return

        # 号码暂时不用存着了，马上给你恢复
        NumberLockTool.remove_lock_number(user_id)

        # 加VIP
        send_days = OrderTool.get_send_vip_days(user_id)
        if send_days < 0:
            OrderTool.add_user_vip(user_id, -send_days)

        # 更新 expired
        vip_ret = RefreshOrderTool.refresh_user_vip(user_id)
        logger.error(f"[恢复封号]{vip_ret}")

        # 黑名单删除
        BlackOrderRecord.objects.filter(user_id=user_id).delete()

        # 把这个用户的坏图挪出坏图片库
        SmsTool.remove_user_all_bad_images(user_id)

        # 把这个用户的可能坏短信也挪走
        SmsTool.remove_user_all_maybe_bad_images(user_id)

        # 推送下
        push_content = SmsNoticeTool.account_recovery_notification()
        to_number = NumberTool.get_number_by_userid(user_id)
        SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                         settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)

        return
