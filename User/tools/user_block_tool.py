from django.db.models import Q

from Common.err import ErrInfo
from SecPhone import settings
from SecPhone.settings import logger
from User.models import BlackUser


class UserBlockTool:
    @staticmethod
    def black_user(user_id: int, uuid: str, reason: str, ip: str):
        bu = BlackUser(user_id=user_id, uuid=uuid, reason=reason, ban_type=0, ip=ip, appid=1)
        bu.save()

    @staticmethod
    def is_user_black_ip_before(ip: str) -> bool:
        if not ip:
            logger.warning(f"[UserBlockTool.is_user_black_ip_before] ip is null")
            return False

        if BlackUser.objects.filter(ip=ip, ban_type=0, deleted=0).exists():
            return True
        return False

    @staticmethod
    def is_black_user(user_id: int, uuid: str) -> ErrInfo:
        if user_id in settings.WHITE_USER_LIST:
            return ErrInfo.SUCCESS

        # ban_type == 0 是人工标注的，一定是准的，然后不用看 ban_times，直接干掉
        if uuid:
            black_users = BlackUser.objects.filter(Q(user_id=user_id) | Q(uuid=uuid), deleted=0).all()
        else:
            black_users = BlackUser.objects.filter(user_id=user_id, deleted=0).all()

        # 防止分账户作恶
        total_ban_times = 0
        for b in black_users:
            if b.ban_type == 0:
                logger.warning(f"[UserBlockTool.is_black_user] ban by fatpo, {user_id}, {uuid} is black user.")
                return ErrInfo.SUSPECTED_VIOLATION
            if b.ban_times > 20:
                logger.warning(f"[UserBlockTool.is_black_user] ban_times > 10, {user_id}, {uuid} is black user.")
                return ErrInfo.SUSPECTED_VIOLATION

            total_ban_times += b.ban_times

        if total_ban_times > 20:
            logger.error(f"[UserBlockTool.is_black_user] total_ban_times > 10, {user_id}, {uuid} is black user.")
            return ErrInfo.SUSPECTED_VIOLATION

        return ErrInfo.SUCCESS

    @staticmethod
    def is_black_user_by_uuid(uuid: str) -> bool:
        black_user = BlackUser.objects.filter(uuid=uuid, ban_type=0, deleted=0).first()
        if black_user:
            return True
        return False
