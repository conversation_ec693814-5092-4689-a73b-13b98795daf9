from Common.err import ErrInfo
from Number.models import NumberUsed
from Number.tools.number_tool import NumberTool
from Order.tools.tool_order import OrderTool
from Point import pointcommon
from SecPhone import settings
from SecPhone.settings import logger
from User.tools.user_tool import UserTool


class UserProfileTool:

    @staticmethod
    def get_user_profile(user_id: int, app_version: int) -> dict:
        # get phone number's expire
        user = UserTool.get_user_by_id(user_id)
        if not user:
            logger.warning(f"[UserTool.get_user_profile] user is not exist: {user_id}, return empty.")
            return {}

        # get phone number or 3 min lock number
        user_phone = NumberUsed.objects.filter(user_id=user_id, status='USING').first()
        number = user_phone.number if user_phone is not None else NumberTool.get_lock_number(user_id)

        if OrderTool.is_user_vip_expire(user_id) == ErrInfo.SUCCESS:
            is_expired = False
        else:
            is_expired = True

        # get expire
        expire = OrderTool.get_user_expire_all_status(user_id)

        # get point
        point = pointcommon.PointCommon.GetPointLeft(user_id)

        # get email
        email = user.email
        if not email:
            if user.login_type == 2:
                email = "AppleUser%s" % (666666 + user.id)
            elif user.login_type == 3:
                email = "FaceBookUser%s" % (666666 + user.id)

        # wrap a user profile
        user_profile = {
            "userid": str(user.id),
            "loginId": user.login_id,
            "email": email,
            "token": user.token,
            "point": point,
            "number": number,
            "expire": expire.strftime("%Y-%m-%dT%H:%M:%S") if expire else "",
            "isExpired": is_expired,
            "loginType": user.login_type,
            "twRegisterUrl": "https://texttok-4477.twil.io",
            "twRegisterIdentity": UserTool.gen_identity(user_id, user.uuid),
            "customerServiceNumber": settings.APP_IT_SUPPORT_SHOW_PHONE,
            "isFlowToFo": False,  # 导流到fo
            "is_premium_first": False,  # 是否先显示订阅
            "is_single_premium": False,  # 是否显示单个订阅
        }
        # 新版本app才打开
        if app_version >= 3010300:
            user_profile["is_premium_first"] = True
            user_profile["is_single_premium"] = True

        return user_profile
