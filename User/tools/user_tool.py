from typing import Union

from django.core.cache import cache

from Common.rediskey import RedisKey
from Common.timeutil import TimeUtil
from SecPhone import settings
from SecPhone.settings import logger
from User.models import User, CheckUser, UserDelete


class UserTool:
    @staticmethod
    def get_uuid_total_cnt_and_deleted_cnt(uuid: str) -> (int, int):
        users = User.objects.filter(uuid=uuid).all()
        total_size = len(users)
        del_cnt = 0
        for u in users:
            if u.deleted == 1:
                del_cnt += 1

        return total_size, del_cnt

    @staticmethod
    def get_userid_by_uuid(uuid: str) -> int:
        user = User.objects.filter(uuid=uuid).first()
        if user:
            return user.id
        return 0

    @staticmethod
    def get_user_by_uuid(uuid: str) -> Union[User, None]:
        user = User.objects.filter(uuid=uuid, deleted=0).last()
        return user

    @staticmethod
    def get_user_by_email(email: str) -> Union[User, None]:
        user = User.objects.filter(email=email).first()
        if user:
            return user
        return None

    @staticmethod
    def get_userid_list_by_uuid(uuid: str) -> list:
        users = User.objects.filter(uuid=uuid).all()
        user_ids = []
        for i in users:
            user_ids.append(i.id)
        return user_ids

    @staticmethod
    def create_mock_number(user_id: int) -> str:
        """
        如果用户没有号码，我们给他根据 userid mock一个
        :param user_id:
        :return:
        """
        s = str(user_id)
        for i in range(8 - len(s)):
            s = '0' + s
        return settings.APP_IT_SUPPORT_NO_NUMBER_PREFIX + s

    @staticmethod
    def get_userid_from_mock_number(mock_phone: str) -> int:
        """
        从mock 的number抓出来 userid
        :param mock_phone:
        :return:
        """
        mock_phone = mock_phone.replace(settings.APP_IT_SUPPORT_NO_NUMBER_PREFIX, "")
        zero_index = 0
        for i in mock_phone:
            if i == '0':
                zero_index += 1
            else:
                break
        user_id_str = mock_phone[zero_index:]
        return int(user_id_str)

    @staticmethod
    def get_user_by_id(user_id: int) -> Union[None, User]:
        if not user_id or user_id == 0:
            logger.error(f"UserTool.get_user_by_id user_id is not valid: {user_id}")
            return None
        user = User.objects.filter(id=user_id, deleted=0).first()
        if not user:
            return None
        return user

    @staticmethod
    def get_user_by_id_with_deleted(user_id: int) -> Union[None, User]:
        if not user_id or user_id == 0:
            logger.error(f"UserTool.get_user_by_id_with_deleted user_id is not valid: {user_id}")
            return None
        user = User.objects.filter(id=user_id).first()
        if not user:
            return None
        return user

    @staticmethod
    def get_user_by_email_with_deleted(email: str) -> Union[None, User]:
        if not email:
            logger.error(f"UserTool.get_user_by_email_with_deleted user_id is not valid: {email}")
            return None

        user = User.objects.filter(email=email).first()
        if not user:
            return None
        return user

    @staticmethod
    def get_user_by_sip_username(sip_username: str) -> Union[None, User]:
        if not sip_username:
            logger.error(f"UserTool.get_user_by_sip_username sip_username is not valid: {sip_username}")
            return None
        user = User.objects.filter(telnyx_sip_username=sip_username, deleted=0).first()
        if not user:
            return None
        return user

    @staticmethod
    def get_user_by_loginid(login_id: str, appid: int):
        user = User.objects.filter(login_id=login_id, appid=appid, deleted=0).first()
        if not user:
            return None
        return user

    @staticmethod
    def get_uuid_by_userid(user_id):
        try:
            user = UserTool.get_user_by_id(user_id)
            if not user:
                logger.error(f"[get_uuid_by_userid] userid: {user_id} not exists, return None!")
                return None
            return user.uuid
        except Exception:
            logger.error(f"[get_uuid_by_userid] get user token failed, user:{user_id}", exc_info=True)
            return None

    @staticmethod
    def get_token_by_userid(user_id):
        if int(user_id) == 0:
            logger.info(f"[get_token_by_userid] userid: 0, return None!")
            return None

        token_key = RedisKey.GenTokenKey(user_id)
        token = cache.get(token_key)
        if token is not None:
            return token

        try:
            user = UserTool.get_user_by_id(user_id)
            if not user:
                logger.warning(f"[get_token_by_userid] userid: {user_id} not exists, return None!")
                return None
            logger.info(f"[get_token_by_userid] cache without token, reset: {token_key}, value: {user.token}，"
                        f"expire：{settings.TOKEN_EXPIRE_TIMEOUT} s")
            cache.set(token_key, user.token, settings.TOKEN_EXPIRE_TIMEOUT)
            return user.token
        except Exception:
            logger.error(f"[get_token_by_userid] get user: {user_id} token failed", exc_info=True)
            return None

    @staticmethod
    def gen_identity(user_id, uuid: str = None) -> str:
        # 如果没有传cm_uuid就拿一次DB
        try:
            if not uuid:
                # 先拿缓存
                uuid_key = RedisKey.GenUuidKey(user_id)
                uuid = cache.get(uuid_key)

                # 再拿DB
                if not uuid:
                    uuid = UserTool.get_uuid_by_userid(user_id)
                    logger.info(
                        f"[gen_identity] get user:{user_id} uuid from db, and set to cache: {uuid_key}, value: {uuid}")
                    cache.set(uuid_key, uuid, settings.UUID_EXPIRE_TIMEOUT)
        except Exception:
            logger.error(f"[gen_identity] failed for userid: {user_id}, uuid: {uuid}", exc_info=True)
        return 'identity_user_%s_%s' % (user_id, uuid)

    @staticmethod
    def update_app_version(user_id: int, app_version: int):
        if not app_version or app_version <= 0:
            logger.error(f"[UserTool.update_app_version] user_id:{user_id}, app_version is invalid: {app_version}")
            return

        User.objects.filter(id=user_id).update(app_version=app_version)

    @staticmethod
    def update_asaid(user_id: int, asaid: str):
        if not asaid:
            logger.error(f"[UserTool.update_asaid] user_id:{user_id}, asaid is invalid: {asaid}")
            return
        user = User.objects.filter(id=user_id).first()
        if user and user.asaid != asaid:
            user.asaid = asaid
            user.save()
            logger.warning(f"[UserTool.update_asaid] user_id:{user_id}, asaid update:{asaid}")
        else:
            logger.info(f"[UserTool.update_asaid] user_id:{user_id}, asaid:{asaid}, already be, "
                        f"no need to update")

    @staticmethod
    def update_app_source(user_id: int, app_source: str):
        if not app_source:
            logger.error(f"[UserTool.update_app_source] user_id:{user_id}, app_source is invalid: {app_source}")
            return

        user = User.objects.filter(id=user_id).first()
        if user and user.app_source != app_source:
            user.app_source = app_source
            user.save()
            logger.warning(f"[UserTool.update_app_source] user_id:{user_id}, app_source update:{app_source}")
        else:
            logger.info(f"[UserTool.update_app_source] user_id:{user_id}, app_source:{app_source}, already be, "
                        f"no need to update")

    @staticmethod
    def update_push_id(user_id: int, push_id: str):
        if not push_id or len(push_id) > 256 or len(push_id) < 30:
            logger.error(f"[UserTool.update_push_id] user_id:{user_id}, push_id is invalid: {push_id}")
            return

        User.objects.filter(id=user_id).update(push_id=push_id)

    @staticmethod
    def update_latest_sms_ts(user_id: int, latest_sms_ts: int):
        User.objects.filter(id=user_id).update(latest_sms_ts=latest_sms_ts)

    @staticmethod
    def delete_user(user_id: int):
        User.objects.filter(id=user_id).update(token='', deleted=1)

    @staticmethod
    def delete_user_device(user_id: int, device_id: str):
        User.objects.filter(id=user_id).update(uuid=device_id + "_deleted")

    @staticmethod
    def delete_check_user(user_id: int):
        CheckUser.objects.filter(user_id=user_id).delete()

    @staticmethod
    def get_check_user_list(user_id: int) -> list:
        return CheckUser.objects.filter(user_id=user_id).all()

    @staticmethod
    def recover_user(user_id: int):
        User.objects.filter(id=user_id).update(deleted=0)

    @staticmethod
    def record_user_delete(user_id: int, device_id: str):
        # 保存记录
        ud = UserDelete(user_id=user_id, device_id=device_id)
        ud.save()

    @staticmethod
    def get_user_delete_records(user_id: int) -> list:
        ans = UserDelete.objects.filter(user_id=user_id).all()
        return ans

    @staticmethod
    def recover_user_subscription(user_id: int):
        User.objects.filter(id=user_id).update(deleted=0)

    @staticmethod
    def get_user_register_days(user_id: int) -> int:
        user = UserTool.get_user_by_id(user_id)
        if not user:
            logger.error(f"[UserTool.get_user_register_days] user_id:{user_id} not exists")
            return 0
        return TimeUtil.GetDiffDays(TimeUtil.GetNow(), user.created_at)
