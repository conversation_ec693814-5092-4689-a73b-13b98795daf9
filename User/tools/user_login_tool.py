import time
from typing import Union

import geoip2.database
import geoip2.errors
from django.core.cache import cache
from django.db import transaction

from Common.rediskey import RedisKey
from Common.util import Util
from Point.models import Point, PointRecord
from SecPhone import settings
from SecPhone.settings import logger
from User.models import User, UserDevice, UserAccess

print("geoip2 file:", f'{settings.BASE_DIR}/GeoLite2-Country.mmdb')
geo_reader = geoip2.database.Reader(f'{settings.BASE_DIR}/GeoLite2-Country.mmdb')


def get_ip_tuple(ip) -> (str, str):
    ip_splits = ip.split(".")
    ip_segment1 = ".".join([str(v) for v in ip_splits[0:2]])
    ip_segment2 = ".".join([str(v) for v in ip_splits[0:3]])
    return ip_segment1, ip_segment2


def get_country_from_ip(ip_address):
    try:
        response = geo_reader.country(ip_address)
        country_name = response.country.name
        return country_name
    except geoip2.errors.AddressNotFoundError:
        return "Unknown"


class UserLoginTool:

    @staticmethod
    def update_ip(user_id: int, ip: str):
        try:
            if not ip:
                logger.error(f"[UserLoginTool.update_ip] user_id:{user_id}, ip is null")
                return

            # 很极端情况下，django传过来nginx的ip是： '**************, *************'
            if "," in ip:
                ip = ip.split(",")[0]

            ip_segment1, ip_segment2 = get_ip_tuple(ip)
            country = get_country_from_ip(ip)
            logger.info(f"[UserLoginTool.update_ip] user:{user_id}, ip:{ip}, ip_segment:{ip_segment1}, "
                        f"ip_segment2:{ip_segment2}, country:{country}")

            User.objects.filter(id=user_id).update(ip=ip, ip_segment1=ip_segment1, ip_segment2=ip_segment2,
                                                   country=country)
        except Exception:
            logger.error(f"[UserLoginTool.update_ip] user_id:{user_id}, ip:{ip} invalid", exc_info=True)

    @staticmethod
    def clear_user_login_token(user_id: int):
        User.objects.filter(id=user_id).update(token='')
        cache.set(RedisKey.GenTokenKey(user_id), "test")

    @staticmethod
    def is_user_has_too_much_device_by_email(email: str) -> bool:
        # 不要加上deleted = 0，有些人会删除账户再注册
        if User.objects.filter(email=email).count() > 3:
            return True
        return False

    @staticmethod
    def is_email_in_first_three_account(email: str, login_id: str) -> bool:
        accounts = User.objects.filter(email=email).order_by('id')[:3]
        for account in accounts:
            if account.login_id == login_id:
                return True
        return False

    @staticmethod
    def is_user_has_too_much_device_by_uuid(uuid: str, limit: int) -> bool:
        if uuid in settings.WHITE_DEVICE_LIST:
            logger.warning(f"[UserLoginTool.is_user_has_too_much_device_by_uuid] white user:{uuid} pass")
            return False

        # 不要加上deleted = 0，有些人会删除账户再注册，同一个登录方式，不能超过 n 个
        if User.objects.filter(uuid=uuid).count() >= limit:
            return True
        return False

    @staticmethod
    def is_uuid_in_first_n_account(uuid: str, login_id: str, limit: int) -> bool:
        accounts = User.objects.filter(uuid=uuid).order_by('id')[:limit]
        for account in accounts:
            if account.login_id == login_id:
                return True
        return False

    @staticmethod
    def is_user_has_too_much_device_by_ip(ip: str, login_type: int, limit: int) -> bool:
        if not ip:
            logger.warning(f"[UserLoginTool.is_user_has_too_much_device_by_ip] ip is null")
            return False

        # 不要加上deleted = 0，有些人会删除账户再注册
        if User.objects.filter(ip=ip, login_type=login_type).count() > limit:
            return True
        return False

    @staticmethod
    def is_ip_in_first_n_account(ip: str, login_type: int, login_id: str, limit: int) -> bool:
        accounts = User.objects.filter(ip=ip, login_type=login_type).order_by('id')[:limit]
        for account in accounts:
            if account.login_id == login_id:
                return True
        return False

    @staticmethod
    def is_deleted_before_by_loginid(login_id: str, login_type: int) -> bool:
        user = User.objects.filter(login_id=login_id, login_type=login_type).first()
        if user and user.deleted == 1:
            return True
        return False

    @staticmethod
    def is_register_before_by_uuid(login_id: str, uuid: str) -> Union[User, None]:
        if uuid in settings.WHITE_DEVICE_LIST:
            logger.warning(f"[UserLoginTool.is_register_before_by_uuid] white user:{uuid} pass")
            return None

        users = User.objects.filter(uuid=uuid).all()
        if len(users) == 0:
            return None

        for u in users:
            if u.login_id == login_id:
                return None
        else:
            return users[0]

    @staticmethod
    def handle_old_user(user_id: int, user_passwd: str, uuid: str, ip: str, app_source: str) -> None:
        # update db
        token = Util.MD5Sum(user_passwd + str(time.time()))

        # ip, ip_segment1, ip_segment2
        ip_segment1, ip_segment2 = get_ip_tuple(ip)

        User.objects.filter(id=user_id).update(token=token, uuid=uuid, ip=ip, ip_segment1=ip_segment1,
                                               ip_segment2=ip_segment2, app_source=app_source)

        # 更新app_source
        user = User.objects.filter(id=user_id).first()
        if user:
            # 我们只记录第一次的app_source
            if user.app_source == '' and app_source:
                user.app_source = app_source
                user.save()
        else:
            logger.error(f"[handle_old_user] user not exits:{user_id}")

        # update cache
        cache.set(RedisKey.GenTokenKey(user_id), token, settings.TOKEN_EXPIRE_TIMEOUT)
        cache.set(RedisKey.GenUuidKey(user_id), uuid, settings.UUID_EXPIRE_TIMEOUT)

    @staticmethod
    def handle_new_user(login_id: str, email: str, login_type: int, uuid: str, appid: int, ip: str,
                        app_source: str) -> None:
        logger.info(f"[UserLoginTool.handle_new_user] register new user, login_id:{login_id}, "
                    f"email: {email}, login_type:{login_type}, appid: {appid}, ip:{ip}, app_source：{app_source}")

        with transaction.atomic():
            # give a password
            password = email
            password_salt = Util.MD5Sum(password + settings.SALT_SECRET_KEY)
            token = Util.MD5Sum(password_salt + str(time.time()))

            # ip, ip_segment1, ip_segment2
            ip_segment1, ip_segment2 = get_ip_tuple(ip)

            # now we just have one appid: 0
            user = User(login_id=login_id, email=email, passwd=password_salt, token=token, appid=appid,
                        login_type=login_type, uuid=uuid, ip=ip, ip_segment1=ip_segment1,
                        ip_segment2=ip_segment2, deleted=0, app_source=app_source)
            user.save()

            # new user, give begin_point points
            begin_point = 50
            logger.info(
                f"[UserLoginTool.handle_new_user] register user, get begin point, email: {email}, point: {begin_point}")
            p = Point(user_id=user.id, point=begin_point)
            p.save()

            # update cache
            cache.set(RedisKey.GenTokenKey(user.id), token, settings.TOKEN_EXPIRE_TIMEOUT)
            cache.set(RedisKey.GenUuidKey(user.id), uuid, settings.UUID_EXPIRE_TIMEOUT)

            # 新用户只能每天发送10个用户
            cache.set(RedisKey.GenNewUserFlag(user.id), RedisKey.NEW_USER_FLAG_VALUE,
                      RedisKey.NEW_USER_FLAG_EXPIRE_SECONDS)

    @staticmethod
    def handle_new_user_by_device(device_id: str) -> int:
        logger.info(f"[UserLoginTool.handle_new_user_by_device] register new user, device_id:{device_id}")

        with transaction.atomic():
            # now we just have one appid: 0
            user = User(login_id='',
                        email='',
                        passwd='',
                        token='',
                        appid=1,
                        login_type=4,
                        uuid=device_id,
                        ip='',
                        ip_segment1='',
                        ip_segment2='',
                        deleted=0)
            user.save()
            logger.info(f"[UserLoginTool.handle_new_user_by_device] register user, user_id:{user.id} -> {device_id}")

            # bind device with a user
            ud = UserDevice(user_id=user.id, device_id=device_id)
            ud.save()

            # new user, give begin_point points
            begin_point = 100
            logger.info(f"[UserLoginTool.handle_new_user_by_device] register user, get begin point: {begin_point}")
            p = Point(user_id=user.id, point=begin_point)
            p.save()

            pr = PointRecord(user_id=user.id, point=begin_point, event='REGISTER', record_id=0)
            pr.save()

            # 新用户只能每天发送10个用户
            cache.set(RedisKey.GenNewUserFlag(user.id), RedisKey.NEW_USER_FLAG_VALUE,
                      RedisKey.NEW_USER_FLAG_EXPIRE_SECONDS)
            return user.id

    @staticmethod
    def get_userid_by_device(device_id: str) -> Union[None, UserDevice]:
        ud = UserDevice.objects.filter(device_id=device_id).first()
        return ud

    @staticmethod
    def is_device_already_bind(device_id: str) -> bool:
        if UserDevice.objects.filter(device_id=device_id).exists():
            return True
        return False

    @staticmethod
    def is_device_user_match(device_id: str, user_id: int) -> bool:
        if UserDevice.objects.filter(device_id=device_id, user_id=user_id).exists():
            return True
        return False

    @staticmethod
    def add_user_device_bind(user_id: int, device_id: str) -> bool:

        user = UserDevice.objects.filter(user_id=user_id).first()
        if user:
            logger.warning(f"[UserLoginTool.add_user_device_bind] user_id:{user_id} already bind: {user.device_id}")
            return False

        user = UserDevice.objects.filter(device_id=device_id).first()
        if user:
            logger.warning(f"[UserLoginTool.add_user_device_bind] device_id:{device_id} already bind: {user.id}")
            return False

        user = UserDevice(user_id=user_id, device_id=device_id)
        user.save()
        return True

    @staticmethod
    def is_device_can_login_with_account(device_id: str) -> bool:
        if UserAccess.objects.filter(device_id=device_id, deleted=0).exists():
            return True
        return False
