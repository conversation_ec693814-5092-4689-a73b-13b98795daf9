import json

from Call.models import <PERSON><PERSON><PERSON><PERSON>
from Call.tool_call import CallTool
from Common.err import ErrInfo
from Common.timeutil import TimeUtil
from Number.tools.number_tool import Number<PERSON>ool
from Order.tools.tool_order import OrderTool
from Point import pointcommon
from SecPhone.settings import logger
from Sms.tools.tool_invalid_sms import SmsInvalidTool
from Sms.tools.tool_sms import SmsTool
from User.models import CheckUser
from User.tools.user_block_tool import UserBlockTool
from User.tools.user_tool import UserTool


class UserContextTool:
    @staticmethod
    def get_user_context_str(email: str, content: str, user_id: int) -> str:
        user_ctx_dict = UserContextTool.get_user_context_dict(email, content, user_id)
        user_ctx_str = json.dumps(user_ctx_dict, indent=4, default=str, ensure_ascii=False)
        return user_ctx_str

    @staticmethod
    def get_user_context_dict(email: str, content: str, user_id: int) -> dict:
        try:
            # 联系人数量
            logger.info(f"[UserContextTool] user:{user_id}, email:{email}, content:{content}")
            user = UserTool.get_user_by_id_with_deleted(user_id)
            if not user:
                logger.error(f"[get_user_context] {user_id} not exists.")
                return {
                    "user_id": -1,
                    "err_msg": "user not exists"
                }

            order = OrderTool.get_user_order_without_condition(user_id)
            point = pointcommon.PointCommon.GetPointLeft(user_id)

            # 短信相关
            send_sms_count = SmsTool.get_total_send_cnt(user_id)
            receive_sms_count = SmsTool.get_total_receive_cnt(user_id)
            send_sms_person_count = SmsTool.get_all_to_numbers_cnt_when_outgoing(user_id)
            receive_sms_person_count = SmsTool.get_all_from_numbers_cnt_when_incoming(user_id)
            sms_send_failed_count = SmsTool.get_total_failed_cnt(user_id)
            invalid_sms_cnt = SmsInvalidTool.get_user_invalid_sms_cnt(user_id)
            invalid_mms_cnt = ImageCheck.objects.filter(user_id=user_id, deleted=0).count()

            # 电话相关
            incoming_call_count = CallTool.get_total_incoming_cnt(user_id)
            outgoing_call_count = CallTool.get_total_outgoing_cnt(user_id)
            incoming_call_person_count = CallTool.get_all_from_numbers_cnt_when_incoming(user_id)
            outgoing_call_person_count = CallTool.get_all_to_numbers_cnt_when_outgoing(user_id)

            black_user = UserBlockTool.is_black_user(user_id, user.uuid)
            number = NumberTool.GetNumberByUserId(user_id)
            uuid_total_cnt, uuid_del_cnt = UserTool.get_uuid_total_cnt_and_deleted_cnt(user.uuid)

            # 这个号码跟了多少人
            if number:
                number_be_owned_cnt = NumberTool.get_number_cnt(number)
            else:
                number_be_owned_cnt = 0

            if not email:
                logger.info(f"[UserContextTool] user:{user_id}, replace email:{user.email}")
                email = user.email

            # 订单相关
            vip_days = OrderTool.get_send_vip_days(user_id)  # 赠送VIP天数
            order_tran_id = order.original_transaction_id if order else '无'
            order_created_at = TimeUtil.GetBeijingTimeStr(order.created_at) if order else '无'
            order_expired_at = TimeUtil.GetBeijingTimeStr(order.expire_at) if order else '无'
            order_expired_at_original = TimeUtil.GetBeijingTimeStr(
                TimeUtil.AddTimeDays(order.expire_at, -vip_days)) if order else '无'
            order_status = order.order_status if order else '无'
            if OrderTool.is_user_vip_expire(user_id) != ErrInfo.SUCCESS:
                order_status += '[已过期]'

            # 登录类型
            user_status = "正常" if user.deleted == 0 else "已删除"
            login_type = ""
            if user.login_type == 1:
                login_type = 'Google'
            elif user.login_type == 2:
                login_type = 'Apple'
            elif user.login_type == 3:
                login_type = 'Facebook'
            elif user.login_type == 4:
                login_type = 'Device'

            user_context_dict = {
                "用户状态": f"{user_status}",
                "用户版本": f"{user.app_version}",
                "用户id": f"{user_id}",
                "用户创建时间": f"{TimeUtil.GetBeijingTimeStr(user.created_at)}",
                "号码": f"{number}",
                "可用点数": f"{point}",
                "拥有过几个号码": f"{NumberTool.get_number_cnt_by_user(user_id)}",
                "当前号码之前跟了多少人": f"{number_be_owned_cnt}",
                "设备ID": f"{user.uuid}",
                "设备总数_设备被删除数_app版本号": f"{uuid_total_cnt}_{uuid_del_cnt}",
                "订单状态": f"{order_status}",
                "订单创建": f"{order_created_at}",
                "订单过期": f"{order_expired_at}",
                "订单过期如果不加VIP": f"{order_expired_at_original}",
                "订单id": f"{order_tran_id}",
                "订单赠送天数": f"{vip_days}",
                "订单撞车情况": f"{json.dumps(OrderTool.get_duplicate_info_list(user_id))}",
                "登录邮箱": f"{email}",
                "登录类型": f"{login_type}",
                "登录token": f"{user.token}",
                "登录id": f"{user.login_id}",
                "发短信数in_out": f"{receive_sms_count}_{send_sms_count}",
                "发短信人数in_out": f"{receive_sms_person_count}_{send_sms_person_count}",
                "打电话数in_out": f"{incoming_call_count}_{outgoing_call_count}",
                "打电话人数in_out": f"{incoming_call_person_count}_{outgoing_call_person_count}",
                "失败短信_违规短信_违规彩信_是否在黑名单": f"{sms_send_failed_count}_{invalid_sms_cnt}_{invalid_mms_cnt}_{'在' if black_user else '不在'}",
            }

            # 附带删除的记录
            if user.deleted == 1:
                user_delete_records = UserTool.get_user_delete_records(user_id)
                user_delete_records_list = [{"device_id": v.device_id,
                                             "user_id": v.user_id,
                                             "created_at": TimeUtil.GetBeijingTimeStr(v.created_at)}
                                            for v in user_delete_records]
                user_context_dict["用户删除记录表"] = user_delete_records_list

            sms_req_cnt = []
            call_req_cnt = []
            for m in [10, 60, 1440]:
                sms_req_cnt.append(str(UserContextTool.get_user_frequently(user_id, "sms", m)))
                call_req_cnt.append(str(UserContextTool.get_user_frequently(user_id, "call", m)))
            user_context_dict[f'短信频率 10_60_1440 分钟'] = "_".join(sms_req_cnt)
            user_context_dict[f'电话频率 10_60_1440 分钟'] = "_".join(call_req_cnt)

            if content:
                user_context_dict["反馈内容"] = content

            return user_context_dict
        except Exception:
            logger.error(f"[UserContextTool] failed for {user_id}, content: {content}", exc_info=True)
            return {}

    @staticmethod
    def get_user_frequently(user_id, event, minutes):

        before_ts = TimeUtil.GetDateTimeBeforeMinutes(before_minutes=minutes)
        to_cnt = CheckUser.objects.filter(user_id=user_id, check_event=event, created_at__gt=before_ts) \
            .values("to_number").distinct().count()
        return to_cnt
