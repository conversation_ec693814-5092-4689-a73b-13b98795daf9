from django.db import models


class User(models.Model):
    login_id = models.Char<PERSON><PERSON>('login_id', max_length=256)
    email = models.Char<PERSON>ield('email', max_length=100)
    ip = models.CharField('ip', max_length=40, blank=True)
    country = models.Char<PERSON>ield('country', max_length=30, blank=True)
    ip_segment1 = models.Char<PERSON>ield('ip_segment1', max_length=20, blank=True)
    ip_segment2 = models.Char<PERSON>ield('ip_segment2', max_length=20, blank=True)
    passwd = models.Char<PERSON><PERSON>('passwd', max_length=50)
    name = models.Char<PERSON><PERSON>('user name', blank=True, max_length=50)
    token = models.Char<PERSON>ield('token', max_length=50, default='DEFAULT-TOKEN-JONHALL')
    uuid = models.Char<PERSON>ield('uuid', max_length=128, default='')
    push_id = models.Char<PERSON>ield('push_id', max_length=256, default='')
    expired_at = models.DateTimeField('expired_at', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    appid = models.IntegerField(default=0)
    app_version = models.IntegerField(default=0)
    login_type = models.IntegerField(default=0)  # case google = 1, case apple = 2, case facebook = 3, case device=4
    telnyx_sip_connection_id = models.CharField('telnyx_sip_connection_id', max_length=64, default=None)
    telnyx_sip_username = models.CharField('telnyx_sip_username', max_length=64, default=None)
    telnyx_sip_password = models.CharField('telnyx_sip_password', max_length=64, default=None)

    # 用户最新的拉取短信打点
    latest_sms_ts = models.IntegerField('latest_sms_ts', blank=True, default=0)

    # 是否删除
    deleted = models.IntegerField('deleted', blank=True, default=0)

    # 广告
    asaid = models.CharField(' asaid', blank=True, max_length=32)
    # 安装来源
    app_source = models.CharField(' app_source', blank=True, max_length=32)

    objects = models.Manager()


class UserDevice(models.Model):
    user_id = models.IntegerField('user_id', default=0)
    device_id = models.CharField('device_id', max_length=48, default='')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class UserAccess(models.Model):
    device_id = models.CharField('device_id', max_length=48, default='')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted = models.IntegerField('deleted', blank=True, default=0)

    class Meta:
        verbose_name = 'User Access (Decide whether to display the login view or not)'
        verbose_name_plural = 'User Accesses'

    objects = models.Manager()


class UserDelete(models.Model):
    user_id = models.IntegerField('user_id', default=0)
    device_id = models.CharField('device_id', max_length=48, default='')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class BlackUser(models.Model):
    user_id = models.IntegerField('user_id')
    uuid = models.CharField('uuid', max_length=64, default='')
    ip = models.CharField('ip', max_length=20, blank=True)
    appid = models.IntegerField('appid', default=0)
    ban_type = models.IntegerField('ban_type', default=0)  # 0-人工标注，1-脚本标注
    reason = models.CharField('reason', max_length=1024)  # 被ban的原因
    ban_times = models.IntegerField('ban_times', default=0)  # 次数，达到阈值就 ban 了
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted = models.IntegerField('deleted', default=0)

    objects = models.Manager()


class CheckUser(models.Model):
    user_id = models.IntegerField('user_id')
    to_number = models.CharField(max_length=15)
    check_event = models.CharField(max_length=10)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Check User (Each user action is stored to control the frequency of calls and SMS)'
        verbose_name_plural = 'Check User'

    objects = models.Manager()
