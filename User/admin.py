from django.contrib import admin
from django.utils.html import format_html

from User.models import User, User<PERSON><PERSON><PERSON>, Check<PERSON><PERSON>, <PERSON>User, UserAccess


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ('id', 'uuid', 'expired_at', 'combined_user_info', 'combined_telnyx_info',
                    'combined_created_updated_info',)
    search_fields = ('id', 'uuid', 'expired_at', 'created_at',)
    list_filter = ('expired_at', 'created_at',)
    readonly_fields = ('ip', 'country',)
    exclude = ('login_id', 'token', 'deleted', 'latest_sms_ts', 'ip_segment1', 'ip_segment2',)
    list_per_page = 50  # 设置每页显示 50 条记录

    def combined_user_info(self, obj):
        return format_html(
            "app_version: {}<br><br>login_type: {}<br><br>country: {}<br><br>ip: {}<br><br>email:{}",
            obj.app_version, obj.login_type, obj.country, obj.ip, obj.email)

    combined_user_info.short_description = 'User Info'

    def combined_telnyx_info(self, obj):
        return format_html(
            "telnyx_sip_connection_id:{}<br><br>telnyx_sip_username: {}<br><br>telnyx_sip_password: {}",
            obj.telnyx_sip_connection_id, obj.telnyx_sip_username, obj.telnyx_sip_password)

    combined_telnyx_info.short_description = 'Telnyx Info'

    def combined_created_updated_info(self, obj):
        return format_html(
            "created_at:{}<br><br>updated_at: {}",
            obj.created_at, obj.updated_at)

    combined_created_updated_info.short_description = 'DateTime Info'


@admin.register(UserDevice)
class UserDeviceAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'device_id', 'created_at', 'updated_at',)
    search_fields = ('id', 'user_id', 'device_id',)
    list_filter = ('created_at',)
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(CheckUser)
class CheckUserAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_id', 'to_number', 'check_event', 'created_at', 'updated_at',)
    search_fields = ('user_id', 'to_number',)
    list_filter = ('created_at', 'check_event',)
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(BlackUser)
class BlackUserAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'user_id', 'uuid', 'ip', 'ban_type', 'reason', 'ban_times', 'deleted', 'created_at', 'updated_at',)
    search_fields = ('user_id', 'uuid', 'ip')
    list_filter = ('ban_type', 'created_at', 'deleted',)
    list_per_page = 50  # 设置每页显示 50 条记录


@admin.register(UserAccess)
class UserAccessAdmin(admin.ModelAdmin):
    list_display = ('id', 'device_id', 'deleted', 'created_at', 'updated_at',)
    search_fields = ('id', 'device_id',)
    list_filter = ('created_at', 'deleted')
    list_per_page = 50  # 设置每页显示 50 条记录
