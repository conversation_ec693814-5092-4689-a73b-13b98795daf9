from django.core.cache import cache

from Common.err import ErrInfo
from Common.rediskey import RedisKey
from Common.timeutil import TimeUtil
from Common.util import Util
from Common.views import SecPhoneView
from Number.tools.number_tool import NumberTool
from Number.tools.number_valid_tool import <PERSON><PERSON><PERSON><PERSON>Tool
from Order.models import Order
from SecPhone import settings
from SecPhone.settings import logger
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from User.models import CheckUser
from User.tools.user_block_tool import UserBlockTool
from User.tools.user_kill_tool import UserKillTool
from User.tools.user_login_tool import UserLoginTool
from User.tools.user_profile_tool import UserProfileTool
from User.tools.user_tool import UserTool

ERR_MSG_DELETE_BEFORE = f"The account has been deleted as per your request. For any needs, please reach out to us " \
                        f"at <EMAIL>, ticketId: 213"
ERR_MSG_INVALID_USER = f"Something wrong with the server. Please contact customer support " \
                       f"for assistance: <EMAIL>, ticketId: 213"
ERR_MSG_BIND_BEFORE = f"Your device has already been bound to an account. " \
                      f"Please use that account to login this device. "


def gen_delete_before_err_msg(user_id):
    return ERR_MSG_DELETE_BEFORE + str(user_id) + '8'


def gen_invalid_user_err_msg(user_id):
    return ERR_MSG_INVALID_USER + str(user_id) + '8'


class UserUnifyLogin(SecPhoneView):
    def post(self, request):
        logger.info(f"[User.views.UserUnifyLogin] param: {request.body}")
        data = {}
        try:
            data, err_code, err_msg = self.ReadPostJson(request, ("login_id", "email", "type"))
            if err_code != 0:
                logger.error(f"[User.views.UserUnifyLogin] param error:  {request.body}")
                return self.ReturnError(err_code, err_msg)

            login_id = data['login_id']
            email = data['email']
            login_type = data['type']

            # 拿到设备id
            headers = self.GetHeaderInRequest(request)
            ip = request.META.get('HTTP_X_FORWARDED_FOR')
            logger.info(f"[User.views.UserUnifyLogin] headers: {headers}, ip:{ip}")
            uuid = headers['uuid']
            app_source = headers['source']

            if 'app_version' not in headers or headers['app_version'] < 200200:
                logger.warning(f"[User.views.UserUnifyLogin] headers: {headers}, need to update app!!!")
                return self.ReturnError(ErrInfo.NEED_TO_UPDATE_APP)

            # 删除过的用户不能再注册，防止无限绕过
            if UserLoginTool.is_deleted_before_by_loginid(login_id, login_type):
                user_id = UserTool.get_userid_by_uuid(uuid)
                logger.warning(f"[User.views.UserUnifyLogin] user:{user_id}, login_id:{login_id}, email:{email}, "
                               f"login_type:{login_type} is deleted before, return error")
                return self.ReturnError(ErrInfo.SIGNUP_ERROR_DELETE_BEFORE, gen_delete_before_err_msg(user_id))

            # 注册过的用户，不能再注册一次
            register_before_user = UserLoginTool.is_register_before_by_uuid(login_id, uuid)
            if register_before_user:
                if register_before_user.login_type == settings.LOGIN_TYPE_GOOGLE:
                    if register_before_user.email:
                        before_account = f"Try your GOOGLE account: {register_before_user.email}. " \
                                         f"TicketId: 213{register_before_user.id}8"
                    else:
                        before_account = f"Try your GOOGLE account. TicketId: 213{register_before_user.id}8"
                elif register_before_user.login_type == settings.LOGIN_TYPE_APPLE:
                    if register_before_user.email:
                        before_account = f"Try your APPLE account: {register_before_user.email}. " \
                                         f"TicketId: 213{register_before_user.id}8"
                    else:
                        before_account = f"Try your APPLE account. TicketId: 213{register_before_user.id}8"
                elif register_before_user.login_type == settings.LOGIN_TYPE_FACEBOOK:
                    if register_before_user.email:
                        before_account = f"Try your FACEBOOK account: {register_before_user.email}. " \
                                         f"TicketId: 213{register_before_user.id}8"
                    else:
                        before_account = f"Try your FACEBOOK account. TicketId: 213{register_before_user.id}8"
                else:
                    logger.error(f"[User.views.UserUnifyLogin] headers: {headers}, invalid login_type, uuid:{uuid}")
                    before_account = f"Try other account. TicketId: 213{register_before_user.id}8"

                support_msg = ". Please feel free to contact us: <EMAIL>"
                user_id = UserTool.get_userid_by_uuid(uuid)
                logger.warning(f"[User.views.UserUnifyLogin] user:{user_id}, login_id:{login_id}, email:{email}, "
                               f"uuid:{uuid}, login_type:{login_type} is registered before, "
                               f"return error: {ERR_MSG_BIND_BEFORE + before_account}")
                return self.ReturnError(ErrInfo.SIGNUP_ERROR_ALREADY_BIND_USER,
                                        ERR_MSG_BIND_BEFORE + before_account + support_msg)

            # 如果之前是设备黑名单，不让注册和登录
            if UserBlockTool.is_black_user_by_uuid(uuid):
                user_id = UserTool.get_userid_by_uuid(uuid)
                logger.warning(
                    f"[User.views.UserUnifyLogin] user, uuid:{uuid}, email:{email}, is bad user, return error")
                return self.ReturnError(ErrInfo.SIGNUP_ERROR_INVALID_ACCOUNT, gen_invalid_user_err_msg(user_id))

            # 如果之前是设备太多，可能只是暂时没发现，最多 n 台
            LOGIN_LIMIT = 1
            if UserLoginTool.is_user_has_too_much_device_by_uuid(uuid, limit=LOGIN_LIMIT):
                if not UserLoginTool.is_uuid_in_first_n_account(uuid, login_id, limit=LOGIN_LIMIT):
                    logger.warning(f"[User.views.UserUnifyLogin] uuid:{uuid}, email:{email}, ip:{ip}, login:{login_id} "
                                   f"uuid has too much device, return error")
                    user_id = UserTool.get_userid_by_uuid(uuid)
                    return self.ReturnError(ErrInfo.SIGNUP_ERROR_INVALID_ACCOUNT, gen_invalid_user_err_msg(user_id))
                else:
                    logger.info(f"[User.views.UserUnifyLogin] uuid:{uuid}, email:{email}, ip:{ip}, login:{login_id} "
                                f"uuid has too much device, but current loginid is first 2 account, check ok")

            if UserLoginTool.is_user_has_too_much_device_by_ip(ip, login_type, limit=LOGIN_LIMIT):
                if not UserLoginTool.is_ip_in_first_n_account(ip, login_type, login_id, limit=LOGIN_LIMIT):
                    logger.warning(f"[User.views.UserUnifyLogin] uuid:{uuid}, email:{email}, ip:{ip}, login:{login_id} "
                                   f"ip has too much device, return error")
                    user_id = UserTool.get_userid_by_uuid(uuid)
                    return self.ReturnError(ErrInfo.SIGNUP_ERROR_INVALID_ACCOUNT, gen_invalid_user_err_msg(user_id))
                else:
                    logger.info(f"[User.views.UserUnifyLogin] uuid:{uuid}, email:{email}, ip:{ip}, login:{login_id}"
                                f"ip has too much device, but current loginid is first 2 account, check ok")

            if UserBlockTool.is_user_black_ip_before(ip):
                logger.warning(f"[User.views.UserUnifyLogin] user, uuid:{uuid}, email:{email}, ip:{ip}, "
                               f"is bad user with ip before, return error")
                user_id = UserTool.get_userid_by_uuid(uuid)
                return self.ReturnError(ErrInfo.SIGNUP_ERROR_INVALID_ACCOUNT.err_code,
                                        gen_invalid_user_err_msg(user_id))

            if data.get("email", ""):
                if UserLoginTool.is_user_has_too_much_device_by_email(email):
                    if not UserLoginTool.is_email_in_first_three_account(email, login_id):
                        logger.warning(f"[User.views.UserUnifyLogin] user, uuid:{uuid}, email:{email}, "
                                       f"email has too much device, return error")
                        user_id = UserTool.get_userid_by_uuid(uuid)
                        return self.ReturnError(ErrInfo.SIGNUP_ERROR_INVALID_ACCOUNT, gen_invalid_user_err_msg(user_id))
                    else:
                        logger.info(f"[User.views.UserUnifyLogin] user, uuid:{uuid}, email:{email}, "
                                    f"email has too much device, but current loginid is first 2 account, check ok")

            appid = headers['appid']
            user = UserTool.get_user_by_loginid(login_id, appid)
            if user:
                if UserBlockTool.is_black_user(user.id, headers['uuid']) != ErrInfo.SUCCESS:
                    logger.warning(f"[User.views.UserUnifyLogin] user login failed, because black: {user.id}")
                    return self.ReturnError(ErrInfo.SIGNUP_SERVER_ERROR)

                user_id = user.id
                user_passwd = user.passwd
                UserLoginTool.handle_old_user(user_id, user_passwd, uuid, ip, app_source)
                user_profile = UserProfileTool.get_user_profile(user_id, app_version=headers["app_version"])
                if not user_profile:
                    logger.error(f"[User.views.UserUnifyLogin] user login as old user success, "
                                 f"but getUserProfile failed: {user_id}")
                    return self.ReturnError(ErrInfo.AUTH_TOKEN_INVALID)

                logger.info(f"[User.views.UserUnifyLogin] user login success: {user_profile}")
                return self.ReturnSuccess(user_profile)
            else:
                UserLoginTool.handle_new_user(login_id, email, login_type, uuid, appid, ip, app_source)
                user = UserTool.get_user_by_loginid(login_id, appid)
                user_profile = UserProfileTool.get_user_profile(user.id, app_version=headers["app_version"])
                if not user_profile:
                    logger.error(f"[User.views.UserUnifyLogin] user register as new user success, "
                                 f"but getUserProfile failed: {user.id}")
                    return self.ReturnError(ErrInfo.AUTH_TOKEN_INVALID)

                logger.info(f"[User.views.UserUnifyLogin] user register success: {user_profile}")
                return self.ReturnSuccess(user_profile)
        except Exception:
            logger.error(f"[User.views.UserUnifyLogin] unify login error, body: {data}", exc_info=True)
            return self.ReturnError(ErrInfo.SIGNUP_SERVER_ERROR)


class GetUserProfile(SecPhoneView):
    @SecPhoneView.VerifyToken
    @SecPhoneView.VerifySign
    def post(self, request):
        header = self.GetHeaderInRequest(request)
        user_id = header['userid']
        ip = request.META.get('HTTP_X_FORWARDED_FOR')
        user = UserTool.get_user_by_id(user_id)
        if not user:
            logger.error(f"[User.views.GetUserProfile] user: {user_id} not exist")
            return self.ReturnError(ErrInfo.AUTH_TOKEN_INVALID)

        # 更新ip
        UserLoginTool.update_ip(user_id, ip)

        # 更新 app version
        app_version = header['app_version']
        UserTool.update_app_version(user_id, app_version)

        user_profile = UserProfileTool.get_user_profile(user_id, app_version=app_version)
        if not user_profile:
            logger.warning(
                f"[User.views.GetUserProfile] user: {user_id}, app_version:{app_version} getUserProfile failed")
            return self.ReturnError(ErrInfo.AUTH_TOKEN_INVALID)

        # 如果用户是tw的号码，我们帮它抹掉，让他重新选择telnyx
        if app_version >= 200000 and 'number' in user_profile:
            number = user_profile['number']
            platform = NumberTool.get_platform_by_user(user_id)
            if platform == settings.VENDOR_TWILIO:
                NumberTool.clean_tw_number(user_id)
                user_profile['number'] = ''
                logger.warning(f"[User.views.GetUserProfile] app_version:{app_version}, user:{user_id} "
                               f"clean tw number:{number}, platform:{platform}")

        logger.info(f"[User.views.GetUserProfile] get profile success: {user_profile}")
        return self.ReturnSuccess(user_profile)


class GetUserProfileWithDevice(SecPhoneView):

    def warp_rsp(self, user_id: int, device_id: str, ip: str, app_version: int, asaid: str, app_source: str) -> dict:
        user = UserTool.get_user_by_id(user_id)
        if not user:
            logger.error(f"[GetUserProfileWithDevice] user:{user_id} is not exists!!!")
            return {}

        # 更新ip
        if user.ip != ip or not user.ip or not user.country:
            UserLoginTool.update_ip(user_id, ip)

        # 更新 app version
        if user.app_version != app_version:
            UserTool.update_app_version(user_id, app_version)

        # 更新广告
        if asaid and user.asaid != asaid:
            UserTool.update_asaid(user_id, asaid)

        # 更新来源
        if app_source and user.app_source != app_source:
            UserTool.update_app_source(user_id, app_source)

        user_profile = UserProfileTool.get_user_profile(user_id, app_version=app_version)
        if not user_profile:
            logger.error(f"[User.views.GetUserProfileWithDevice] user: {user_id} device_id:{device_id} failed get info")
            return self.ReturnError(ErrInfo.AUTH_TOKEN_INVALID)

        # 默认不用账户登录
        if UserLoginTool.is_device_can_login_with_account(device_id):
            user_profile['is_allow_account_login'] = True
            logger.warning(
                f"[User.views.GetUserProfileWithDevice] user: {user_id} device_id:{device_id}, login account, "
                f"get profile success: {user_profile}")
        else:
            user_profile['is_allow_account_login'] = False

        return user_profile

    @SecPhoneView.VerifySign
    def post(self, request):
        header = self.GetHeaderInRequest(request)
        device_id = header['uuid']
        ip = request.META.get('HTTP_X_FORWARDED_FOR')
        app_version = header['app_version']
        asaid = header['asaid']
        app_source = header['source']

        try:
            ud = UserLoginTool.get_userid_by_device(device_id)
            if ud:
                # 已经绑定好的用户
                user_id = ud.user_id
                logger.info(f"[User.views.GetUserProfileWithDevice] {device_id} already bing to {user_id}")
            else:
                # 未绑定设备的用户
                user = UserTool.get_user_by_uuid(device_id)
                if user:
                    # 老用户新版本
                    user_id = user.id
                    logger.info(f"[User.views.GetUserProfileWithDevice] {device_id} old user but not bind to {user_id}")
                    UserLoginTool.add_user_device_bind(user_id, device_id)
                    logger.info(f"[User.views.GetUserProfileWithDevice] {device_id} old user bind to {user_id} success")
                else:
                    # 纯新用户
                    logger.info(f"[User.views.GetUserProfileWithDevice] {device_id} new user creating..")
                    user_id = UserLoginTool.handle_new_user_by_device(device_id)
                    logger.info(f"[User.views.GetUserProfileWithDevice] {device_id} new user bind to {user_id}")

            user_profile = self.warp_rsp(user_id, device_id, ip, app_version, asaid, app_source)
            if not user_profile:
                logger.error(f"[User.views.GetUserProfileWithDevice] {user_id}, {device_id} failed get info")
                return self.ReturnError(ErrInfo.AUTH_TOKEN_INVALID)

            logger.info(f"[User.views.GetUserProfileWithDevice] {user_id}, {device_id}, profile: {user_profile}")
            return self.ReturnSuccess(user_profile)
        except Exception as e:
            if 'Duplicate entry' in str(e):
                logger.warning(f"[User.views.GetUserProfileWithDevice] {device_id} failed", exc_info=True)
            else:
                logger.error(f"[User.views.GetUserProfileWithDevice] {device_id} failed", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class CheckUserView(SecPhoneView):

    @staticmethod
    def check_frequently_ok_person_cnt(user_id: int, check_event: str, minutes: int, threshold: int) -> bool:
        try:
            before_ts = TimeUtil.GetDateTimeBeforeMinutes(before_minutes=minutes)
            to_cnt = CheckUser.objects.filter(user_id=user_id, check_event=check_event, created_at__gt=before_ts) \
                .values("to_number").distinct().count()
            logger.info(f"[CheckUser][check_frequently_ok_person_cnt] user:{user_id}, event:{check_event}, "
                        f"before_ts:{before_ts}, to_cnt:{to_cnt}")
            if to_cnt >= threshold:
                logger.warning(
                    f"[CheckUser][check_frequently_ok_person_cnt] user:{user_id}:{check_event}, {to_cnt} contacts "
                    f"in {minutes} minutes，reject!")

                # 推送一条劝说频率过载短信
                number = NumberTool.get_number_by_userid(user_id)
                content = SmsNoticeTool.request_too_fast()
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, number, content)

                return False

            return True
        except Exception:
            logger.error(f"[CheckUser][check_frequently_ok_person_cnt] user:{user_id}, event:{check_event} failed!",
                         exc_info=True)
            return True

    @staticmethod
    def check_frequently_ok_event_cnt(user_id: int, check_event: str, minutes: int, threshold: int) -> bool:
        try:
            before_ts = TimeUtil.GetDateTimeBeforeMinutes(before_minutes=minutes)
            to_cnt = CheckUser.objects.filter(user_id=user_id, check_event=check_event,
                                              created_at__gt=before_ts).count()
            logger.info(f"[CheckUser][check_frequently_ok_event_cnt] user:{user_id}, event:{check_event}, "
                        f"before_ts:{before_ts}, to_cnt:{to_cnt}")
            if to_cnt >= threshold:
                logger.warning(
                    f"[CheckUser][check_frequently_ok_event_cnt] user:{user_id}:{check_event}, {to_cnt} contacts "
                    f"in {minutes} minutes，reject!")

                # 推送一条劝说频率过载短信
                number = NumberTool.get_number_by_userid(user_id)
                content = SmsNoticeTool.request_too_fast()
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, number, content)

                return False

            return True
        except Exception:
            logger.error(f"[CheckUser][check_frequently_ok_event_cnt] user:{user_id}, event:{check_event} failed!",
                         exc_info=True)
            return True

    @SecPhoneView.VerifyToken
    @SecPhoneView.VerifySign
    def post(self, request):
        try:
            data, err_code, err_msg = self.ReadPostJson(request, ("to_number",))
            if err_code != 0:
                logger.error(f"[CheckUser] param error:  {request.body}")
                return self.ReturnError(err_code, err_msg)

            header = self.GetHeaderInRequest(request)
            user_id = header['userid']
            check_event = data.get("check_event", '').replace("'", "")

            to_phone = Util.FormatNumberV2(data['to_number'])
            logger.info(f"[CheckUser] request user:{user_id}, event:{check_event}, to_phone: {to_phone}, data:{data}")

            if to_phone == settings.APP_IT_SUPPORT_PHONE or to_phone == settings.APP_IT_SUPPORT_SHOW_PHONE:
                logger.info(f"[CheckUser] user:{user_id}, request data: {data}, header: {header}, sms to it support")
                return self.ReturnSuccess({})

            if user_id == settings.APP_IT_SUPPORT_USERID:
                logger.info(f"[CheckUser] user:{user_id}, data: {data}, header: {header}, it number skip checking")
                return self.ReturnSuccess({})

            logger.info(f"[CheckUser] check is_self_number_ok: {user_id}")
            err = NumberValidTool.is_self_number_ok(user_id)
            from_number = NumberTool.GetNumberByUserId(user_id)
            if err != ErrInfo.SUCCESS:
                logger.warning(f"[CheckUser] user: {user_id}:{from_number}->{to_phone}, "
                               f"event: {check_event} not ok, err:{ErrInfo.ErrMsg_EN.get(err, 'unknown')}!")
                return self.ReturnError(err)

            err = NumberValidTool.is_to_number_ok(to_phone)
            if err != ErrInfo.SUCCESS:
                logger.warning(f"[CheckUser] user:{user_id}:{from_number}, to phone is invalid: {to_phone}, "
                               f"header: {header}, err:{ErrInfo.ErrMsg_EN.get(err, 'unknown')}")

                # 短信推送
                number = NumberTool.get_number_by_userid(user_id)
                if err == ErrInfo.NUMBER_DONT_WANT_TO_BEEN_CONTACTED:
                    content = SmsNoticeTool.number_dont_want_been_contacted(to_phone)
                else:
                    content = SmsNoticeTool.follow_correct_number_format(to_phone)
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, number, content)

                if err == ErrInfo.CONTACTED_ACCOUNT_UNAVAILABLE_NO_SUPPORT_NUMBER:
                    return self.ReturnError(err, f"The number {to_phone} you contacted is not supported")
                elif err == ErrInfo.ONLY_NUMBERS_IN_THE_US_AND_CANADA_ARE_SUPPORTED:
                    return self.ReturnError(err, f"Only numbers in the US and Canada are supported!")
                elif err == ErrInfo.CONTACTED_ACCOUNT_NUMBER_NOT_VALID:
                    return self.ReturnError(err, f"The number {to_phone} you contacted is invalid")
                return self.ReturnError(err)

            if from_number == to_phone:
                logger.warning(f"[CheckUser] user: {user_id}:{from_number}->{to_phone}: {check_event} same number,"
                               f"invalid event")
                return self.ReturnError(ErrInfo.NUMBER_INVALID_SAME)

            # 比较极端的用户，10分钟发送了x个联系人
            if not self.check_frequently_ok_person_cnt(user_id, check_event, 10, 5):
                return self.ReturnError(ErrInfo.REQUESTS_TOO_FREQUENTLY)

            if not self.check_frequently_ok_person_cnt(user_id, check_event, 60, 6):
                return self.ReturnError(ErrInfo.REQUESTS_TOO_FREQUENTLY)

            if not self.check_frequently_ok_person_cnt(user_id, check_event, 1440, 10):
                return self.ReturnError(ErrInfo.REQUESTS_TOO_FREQUENTLY)

            # 如果是暂时封杀
            if UserKillTool.is_tmp_kill(user_id):
                ttl = cache.ttl(RedisKey.gen_tmp_kill_user(user_id))
                error_message = (
                    "Hello. Because your recipient has reported you for suspected violations. "
                    "You have been temporarily muted for {}. "
                    "If you have any questions, please contact our customer service center."
                ).format(Util.seconds_to_str(ttl))
                logger.warning(f"[CheckUser] user {user_id}:{from_number}->{to_phone}:{check_event} is tmp kill, "
                               f"error_message:{error_message}")
                return self.ReturnError(ErrInfo.TEMP_MUTE_24_HOUR, error_message)

            # 存一下
            c = CheckUser(user_id=user_id, to_number=to_phone, check_event=check_event)
            c.save()

            logger.info(f"[CheckUser] user: {user_id}:{from_number}, to: {to_phone}, event:{check_event} is ok.")
            return self.ReturnSuccess({})

        except Exception:
            logger.error(f"[CheckUser] check toNumber error, body: {request.body}", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class RegisterPushId(SecPhoneView):
    @SecPhoneView.VerifySign
    def post(self, request):
        try:
            data, err_code, err_msg = self.ReadPostJson(request, ("push_id",))
            if err_code != 0:
                logger.error(f"[User.views.RegisterPushId] param error:  {request.body}")
                return self.ReturnError(err_code, err_msg)

            header = self.GetHeaderInRequest(request)
            user_id = header['userid']
            push_id = data['push_id']

            UserTool.update_push_id(user_id, push_id)
            logger.info(f"[User.view.RegisterPushId] user: {user_id}, push_id: {push_id}, register success")
            return self.ReturnSuccess({})

        except Exception:
            logger.error(f"[User.views.RegisterPushId] check toNumber error, body: {request.body}", exc_info=True)
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR)


class RegisterTwDevice(SecPhoneView):
    @SecPhoneView.VerifyToken
    @SecPhoneView.VerifySign
    def post(self, request):
        logger.info(f"[RegisterTwDevice] request: {request.body}")
        return self.ReturnSuccess({})


class Delete(SecPhoneView):

    @SecPhoneView.VerifyToken
    @SecPhoneView.VerifySign
    def post(self, request):
        data, err_code, err_msg = self.ReadPostJson(request, check_list=())
        if err_code != 0:
            logger.info(f"[用户删除] 入参错误 {request.body}")
            return self.ReturnError(err_code, err_msg)

        headers = self.GetHeaderInRequest(request)
        userid = headers['userid']

        # 保存记录
        UserTool.record_user_delete(userid, headers['uuid'])

        # update token db
        UserTool.delete_user(userid)

        # update order
        Order.objects.filter(user_id=userid).update(valid=0, order_status='CLOSED')

        # update token cache
        cache.delete(RedisKey.GenTokenKey(userid))

        logger.info(f"[用户删除] {userid} 删除成功 {request.body}")
        return self.ReturnSuccess()
