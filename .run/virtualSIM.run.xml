9
<component name="ProjectRunConfigurationManager">
    <configuration default="false" name="virtualSIM" type="PythonConfigurationType" factoryName="Python"
                   singleton="false">
        <module name="VirtualSIM_BackEnd"/>
        <option name="INTERPRETER_OPTIONS" value=""/>
        <option name="PARENT_ENVS" value="true"/>
        <envs>
            <env name="PYTHONUNBUFFERED" value="1"/>
        </envs>
        <option name="SDK_HOME" value="C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe"/>
        <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$"/>
        <option name="IS_MODULE_SDK" value="false"/>
        <option name="ADD_CONTENT_ROOTS" value="true"/>
        <option name="ADD_SOURCE_ROOTS" value="true"/>
        <option name="SCRIPT_NAME" value="$PROJECT_DIR$/manage.py"/>
        <option name="PARAMETERS" value="runserver --noreload"/>
        <option name="SHOW_COMMAND_LINE" value="false"/>
        <option name="EMULATE_TERMINAL" value="false"/>
        <option name="MODULE_MODE" value="false"/>
        <option name="REDIRECT_INPUT" value="false"/>
        <option name="INPUT_FILE" value=""/>
        <method v="2"/>
    </configuration>
</component>