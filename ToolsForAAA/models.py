from django.db import models

from Call.models import SMSRecord


class Tool(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    objects = models.Manager()


class AddVipTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Add User Subscription Days'
        verbose_name_plural = 'Add User Subscription Days'


class UnbindUserNumberTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'A - Number - Unbind User Number'
        verbose_name_plural = 'A - Number - Unbind User Number'


class CustomerServiceTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'AI - Customer Service'
        verbose_name_plural = 'AI - Customer Service'


class SmsFakeRecordProxy(SMSRecord):
    class Meta:
        proxy = True
        verbose_name = 'A - Sms - Admin resent fake sms'
        verbose_name_plural = 'A - Sms - Admin resent fake sms'


class AdminLockNumberTool(Tool):
    class Meta:
        proxy = True
        verbose_name = 'Number - Admin lock a number'
        verbose_name_plural = 'Number - Admin lock numbers'
