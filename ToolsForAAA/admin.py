import datetime
import json
import traceback

import pytz
from django.contrib import admin
from django.core.cache import cache
from django.db.models import Count
from django.http import JsonResponse
from django.template.response import TemplateResponse
from django.urls import path
from django.utils.html import format_html
from django.utils.timezone import localtime, now

from Call.models import SMSR<PERSON>ord
from Common import timeutil, util
from Common.err import ErrInfo
from Common.rediskey import RedisKey
from Common.timeutil import TimeUtil
from Common.util import Util
from MyTelnyx.my_telnyx import TelnyxUtil
from Number.tools.number_lock_tool import NumberLockTool
from Number.tools.number_tool import NumberTool
from Order.tools.tool_order import OrderTool
from Order.tools.tool_refresh_order import RefreshOrderTool
from SecPhone import settings
from SecPhone.settings import logger
from Sms.filters.filter_english import SmsEnglishFilter
from Sms.filters.filter_simple import sms_filter_simple
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from Sms.tools.tool_sms import SmsTool
from ToolsForAAA.models import AddVipTool, AdminLockNumberTool, UnbindUserNumberTool, CustomerServiceTool, \
    SmsFakeRecordProxy
from User.tools.user_context_tool import UserContextTool
from User.tools.user_kill_tool import UserKillTool
from User.tools.user_profile_tool import UserProfileTool
from User.tools.user_tool import UserTool


@admin.register(AddVipTool)
class AddVipToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/add_vip_days.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('add_vip_days/', self.admin_site.admin_view(self.add_vip_days), name='add_vip_days'),
        ]
        return custom_urls + urls

    @staticmethod
    def add_vip_days(request):
        if request.method == 'POST':
            user_id = int(request.POST.get('userid').strip())
            add_days = int(request.POST.get('days').strip())
            if -1000 < add_days:
                logger.error(f"[AddVipToolAdmin] user:{user_id}, days:{add_days}")
            else:
                # 因为-1000会触发封号，下面封号已经有通知了，减少重复通知
                logger.warning(f"[AddVipToolAdmin] user:{user_id}, days:{add_days}")

            before_user_profile = UserProfileTool.get_user_profile(user_id, app_version=0)
            before_user_profile = {key: value for key, value in before_user_profile.items() if key == "expire"}

            # 特殊日期用来解除封禁
            if add_days == 3333:
                UserKillTool.un_kill_tmp_user(user_id)
                return JsonResponse({
                    "msg": "解除临时封杀",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3334:
                UserKillTool.un_mute_user_temporarily(user_id)
                return JsonResponse({
                    "msg": "解除临时禁言",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3335:
                cache.set(RedisKey.gen_check_event_allow(user_id), 1, 86400)
                return JsonResponse({
                    "msg": "临时允许打电话（一天有效期）",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3336:
                # 禁言
                UserKillTool.mute_user_temporarily(user_id, 3600)
                # 推送
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.say_you_are_reported_as_harasser("number")
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)
                return JsonResponse({
                    "msg": "临时禁言1小时",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3337:
                # 禁言
                UserKillTool.mute_user_temporarily(user_id, 3600 * 12)
                # 推送
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.say_you_are_reported_as_harasser("number")
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)
                return JsonResponse({
                    "msg": "临时禁言12小时",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3338:
                # 禁言
                UserKillTool.mute_user_temporarily(user_id, 3600 * 24)
                # 推送
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.say_you_are_reported_as_harasser("number")
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)
                return JsonResponse({
                    "msg": "临时禁言24小时",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3339:
                # 禁言
                UserKillTool.mute_user_temporarily(user_id, 3600 * 48)
                # 推送
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.say_you_are_reported_as_harasser("number")
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)
                return JsonResponse({
                    "msg": "临时禁言48小时",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })
            if add_days == 3340:
                # 禁言
                UserKillTool.mute_user_temporarily(user_id, 3600 * 72)
                # 推送
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.say_you_are_reported_as_harasser("number")
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)
                return JsonResponse({
                    "msg": "临时禁言72小时",
                    "user": user_id,
                    "before_expired_at": before_user_profile,
                    "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
                })

            if add_days > 2100:
                logger.error(f"[AddVipToolAdmin] add_days to much, error：{add_days}")
                ret = {"error": "add days too much > 2100 invalid"}
                return JsonResponse(ret)

            if not UserTool.get_user_by_id(user_id):
                ret = {"error": "user_id not exists"}
                return JsonResponse(ret)

            # 封号
            if add_days <= -1000:
                UserKillTool.kill_user(user_id, add_days)
            else:
                # 加VIP
                OrderTool.add_user_vip(user_id, add_days)

                # 更新 expired
                RefreshOrderTool.refresh_user_vip(user_id)

            # 推送，如果加很多days可能是后台自己补偿，而不想用户知道
            if 0 < add_days < 200:
                to_number = NumberTool.get_number_by_userid(user_id)
                push_content = SmsNoticeTool.add_vip(add_days)
                SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                 settings.APP_IT_SUPPORT_SHOW_PHONE, to_number, push_content)

            return JsonResponse({
                "user": user_id,
                "before_expired_at": before_user_profile,
                "vip_ret": RefreshOrderTool.refresh_user_vip(user_id),
            })

        return TemplateResponse(request, 'admin/add_vip_days.html', {})


@admin.register(AdminLockNumberTool)
class AdminLockNumberToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/admin_lock_number.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('admin_lock_number/', self.admin_site.admin_view(self.admin_lock_number), name='admin_lock_number'),
        ]
        return custom_urls + urls

    @staticmethod
    def admin_lock_number(request):
        if request.method == 'POST':
            user_id = int(request.POST.get('userid').strip())
            number = request.POST.get('number').strip()
            number = Util.FormatNumberV2(number)
            expired_at = str(request.POST.get("expired_at")).strip()
            logger.error(f"[AdminLockNumberToolAdmin] user_id:{user_id}, number:{number}, expired_at:{expired_at}")

            if not UserTool.get_user_by_id(user_id):
                ret = {"error": f"user: {user_id} not in db"}
                return JsonResponse(ret)

            if not NumberTool.is_number_in_inventory(number):
                ret = {"error": f"number: {number} not in db"}
                return JsonResponse(ret)

            admin_lock_obj = NumberLockTool.get_lock_by_number(number)
            if admin_lock_obj:
                ret = {"error": f"number: +{number} already locking by {admin_lock_obj.user_id} "
                                f"till {admin_lock_obj.expired_at}"}
                return JsonResponse(ret)

            admin_lock_obj = NumberLockTool.get_lock_number_by_userid(user_id)
            if admin_lock_obj:
                ret = {"error": f"user_id: {user_id} already lock number: +{admin_lock_obj.number} "
                                f"till {admin_lock_obj.expired_at}"}
                return JsonResponse(ret)

            NumberLockTool.add_lock_number(user_id, number, expired_at)

            ret = {"number": number, "user_id": user_id, "expired_at": expired_at, "message": "admin lock done"}
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/admin_lock_number.html', {})


@admin.register(UnbindUserNumberTool)
class UnbindUserNumberToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/unbind_user_number.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('unbind_user_number/', self.admin_site.admin_view(self.unbind_user_number), name='unbind_user_number'),
        ]
        return custom_urls + urls

    @staticmethod
    def unbind_user_number(request):
        if request.method == 'POST':
            data = request.POST

            user_id = int(data['userid'].strip())
            force_unbind = int(data['force_unbind'].strip())
            if not UserTool.get_user_by_id(user_id):
                ret = {"err": f'user: {user_id} not exists'}
                return JsonResponse(ret)

            if not NumberTool.GetNumberByUserId(user_id):
                ret = {"err": f'user: {user_id} has no number'}
                return JsonResponse(ret)

            user_number_cnt = NumberTool.get_number_cnt_by_user(user_id)
            if force_unbind == 1:
                pass
            else:
                if user_number_cnt >= 7:
                    logger.error(f"[FatpoUnbindUserPhoneNumber] user unbind too much times: {user_id}")
                    SmsItSupportTool.add_support_sms(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                     settings.APP_IT_SUPPORT_SHOW_PHONE, f'+999{user_id}',
                                                     "Number unbinding failed. Please contact customer service to share "
                                                     "the situation.")
                    ret = {"err": "用户已经绑定超过7个号码了，别再绑定了吧"}
                    return JsonResponse(ret)

            res, err_msg = NumberTool.unbind_user_phone_number(user_id)

            # 推送
            system_content, db_content, push_content = SmsNoticeTool.go_and_pick_number()
            SmsItSupportTool.add_support_sms_v2(user_id, settings.SMS_DIRECTION_RECEIVE,
                                                settings.APP_IT_SUPPORT_SHOW_PHONE, f'+999{user_id}',
                                                db_content=db_content,
                                                push_content=push_content)

            # 放开挑选号码权限
            cache.set(RedisKey.GenIsAllowSearchVendorDirectly(user_id), 1, 3600)

            ret = {"user": user_id,
                   "res": res,
                   "err_msg": err_msg,
                   "user_number_cnt": user_number_cnt,
                   }
            return JsonResponse(ret)

        return TemplateResponse(request, 'admin/unbind_user_number.html', {})


@admin.register(CustomerServiceTool)
class CustomerServiceToolAdmin(admin.ModelAdmin):
    change_list_template = "admin/customer_service.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('customer_service_detail/<str:number>/', self.admin_site.admin_view(self.customer_service_detail),
                 name='customer_service_detail'),
            path('customer_service_send_msg/', self.admin_site.admin_view(self.customer_service_send_msg),
                 name='customer_service_send_msg'),
        ]
        return custom_urls + urls

    def changelist_view(self, request, extra_context=None):
        # 这里加载你想要在初始化时加载的数据
        extra_context = CustomerServiceToolAdmin.get_conversations()

        reply_data_path = settings.BASE_DIR + '/Config/replylist.json'
        try:
            with open(reply_data_path) as f:
                reply_data = json.loads(f.read())
        except Exception:
            logger.error(f"[CustomerServiceToolAdmin] failed to load replyList: {reply_data_path}")
            reply_data = []

        code_dict = {}
        item_counter = 0

        def generate_menu(items):
            nonlocal item_counter
            html = "<ul>"
            for item in items:
                indent = item['level'] * 20
                if "children" in item:
                    html += (f'<li onclick="toggleMenu(\'{item["label"]}-menu\')" style="margin-left: {indent}px;">'
                             f'{item["label"]}')
                    html += f'<ul id="{item["label"]}-menu" class="nested-menu">'
                    html += generate_menu(item["children"])
                    html += '</ul></li>'
                elif "code" in item:
                    data_id = f'item-{item_counter}'
                    code_dict[data_id] = item["code"]
                    html += (f'<li onclick="fillInputNew(\'{data_id}\')" style="margin-left: {indent}px;" '
                             f'data-id="{data_id}">{item["label"]}</li>')
                    item_counter += 1
            html += "</ul>"
            return html

        quick_reply_html = generate_menu(reply_data)
        extra_context["quick_reply_html"] = quick_reply_html
        extra_context["code_json"] = json.dumps(code_dict)
        return super().changelist_view(request, extra_context=extra_context)

    def customer_service_detail(self, request, number):
        logger.info(f"[customer_service_detail] {request.META}")
        details = self.get_conversation_details(number)
        return JsonResponse({"details": details})

    def customer_service_send_msg(self, request):
        if request.method == 'POST':
            data = request.POST

            to_number = data["number"]
            content = data["content"].strip().strip("\n")

            if not content:
                return JsonResponse({"err_code": -1, "err_msg": "content is empty"})

            if "+999" in to_number:
                to_userid = NumberTool.get_userid_from_mock_number(to_number)
            else:
                to_userid = NumberTool.get_user_id_by_number_even_expire(to_number)

            if not to_userid:
                logger.error(f"[CustomerServiceToolAdmin] failed user_id not exists, to_number:{to_number}")
                return JsonResponse({"err_code": -1, "err_msg": "user_id invalid"})

            # 增加一些后门技巧
            if "用户" in content:
                res = self.get_user_ctx(to_userid, to_number)
                return JsonResponse({"err_code": 0, "err_msg": "success", "data": {"backend_content": res}})
            elif "后门短信(it=1)" in content:
                res = self.get_user_it_sms_list(to_userid, to_number)
                return JsonResponse({"err_code": 0, "err_msg": "success", "data": {"backend_content": res}})
            elif "后门短信(it=0)" in content:
                res = self.get_user_non_it_sms_list(to_userid, to_number)
                return JsonResponse({"err_code": 0, "err_msg": "success", "data": {"backend_content": res}})
            elif "恢复订阅" in content:
                res = self.recover_user_order(to_userid)
                return JsonResponse({"err_code": 0, "err_msg": "success", "data": {"backend_content": res}})
            elif "订单" in content or "订阅" in content:
                res = self.get_user_order(to_userid, to_number)
                return JsonResponse({"err_code": 0, "err_msg": "success", "data": {"backend_content": res}})
            elif "后门换号" in content:
                res = self.change_number_submit(to_userid)
                return JsonResponse({"err_code": 0, "err_msg": "success", "data": {"backend_content": res}})
            elif "号码列表" in content:
                res = self.get_user_number_list(to_userid)
                return JsonResponse({"err_code": 0, "err_msg": "success", "data": {"backend_content": res}})
            elif "后门各状态号码数量" in content:
                res = self.get_all_numbers_group_by_status_cnt()
                return JsonResponse({"err_code": 0, "err_msg": "success", "data": {"backend_content": res}})
            elif "后门" in content or "后台" in content:
                return JsonResponse({"err_code": -1, "err_msg": f"未命中后门: {content}"})
            # 非英语不让发送
            if SmsEnglishFilter.is_chinese(content):
                logger.error(f"[CustomerServiceToolAdmin] content is Chinese：{content}")
                return JsonResponse({"err_code": -1, "err_msg": f"NOT Englis: {content}"})

            # 加到和 it-support的对话列表
            support_content = content
            SmsItSupportTool.add_support_sms_both_from_it(to_userid, to_number, support_content)

        return JsonResponse({"err_code": 0, "err_msg": "success"})

    @staticmethod
    def get_conversation_details(number) -> list:
        if "+999" in number:
            user_id = NumberTool.get_userid_from_mock_number(number)
        else:
            user_id = NumberTool.get_user_id_by_number_even_expire(number)

        logger.info(f"[CustomerServiceToolAdmin] user_id:{user_id}, number: {number}")

        # 构建查询条件
        before_date = datetime.datetime.now() - datetime.timedelta(days=15)
        records = SmsTool.get_it_by_both_number(user_id, number, before_date)
        logger.info(f"[CustomerServiceToolAdmin] user_id:{user_id}, size:{len(records)}")
        for index, i in enumerate(records):
            logger.info(f"[CustomerServiceToolAdmin] index:{index}, {i.from_number}:{i.to_number}:{i.content}")

        res = []
        for r in records:
            # 把两个 IT 号码归一化
            if r.from_number == settings.APP_IT_SUPPORT_PHONE:
                r.from_number = settings.APP_IT_SUPPORT_SHOW_PHONE
            if r.to_number == settings.APP_IT_SUPPORT_PHONE:
                r.to_number = settings.APP_IT_SUPPORT_SHOW_PHONE

            content = r.content
            notice_code = SmsNoticeTool.get_notice_code(content)
            if notice_code:
                content = SmsNoticeTool.change_system_notice_code_to_content(content, notice_code)

            if r.from_number == settings.APP_IT_SUPPORT_SHOW_PHONE:
                is_right = True
            else:
                is_right = False

            d = {
                "is_right": is_right,
                "from_number": r.from_number,
                "to_number": r.to_number,
                "created_at": r.created_at,
                "direction": r.direction,
                "content": f"[{TimeUtil.GetBeijingTimeStr(r.created_at)}]\n{content if content else r.images}"
            }

            res.append(d)

        return res

    @staticmethod
    def get_conversations() -> dict:
        # 最近的对话
        logger.info(f"[CustomerServiceToolAdmin] start...")
        before_date = datetime.datetime.now() - datetime.timedelta(days=10)
        records = SmsTool.get_records_by_customer_service(settings.APP_IT_SUPPORT_USERID, before_date)
        group_records = {}
        logger.info(f"[CustomerServiceToolAdmin] get_conversations before size: {len(records)}")

        for r in records:
            # 把两个 IT 号码归一化
            if r.from_number == settings.APP_IT_SUPPORT_PHONE:
                r.from_number = settings.APP_IT_SUPPORT_SHOW_PHONE
            if r.to_number == settings.APP_IT_SUPPORT_PHONE:
                r.to_number = settings.APP_IT_SUPPORT_SHOW_PHONE

            if r.from_number == "+1000009999":
                number = r.to_number
            else:
                number = r.from_number

            if number not in group_records:
                group_records[number] = [r]
            else:
                group_records[number].append(r)

        # 自己的号码就不用看了
        if '+1000009999' in group_records:
            del group_records['+1000009999']
        if '+99901013552' in group_records:
            del group_records['+99901013552']

        tt = {}
        un_read_size = 0
        for from_number, _records in group_records.items():
            logger.info(f"[CustomerServiceToolAdmin] {from_number}, size: {len(_records)}")

            count_rs_after_last_s = 0
            last_r_ts = -1
            last_content = ""

            # 排除掉纯系统通知的group
            user_send_cnt = len([v for v in _records if v.direction == settings.SMS_DIRECTION_RECEIVE])
            if user_send_cnt == 0:
                continue

            for item in reversed(_records):
                if item.direction == settings.SMS_DIRECTION_RECEIVE:
                    last_r_ts = max(item.latest_ts, last_r_ts)
                    count_rs_after_last_s += 1
                    if not last_content:
                        last_content = item.content
                elif item.direction == settings.SMS_DIRECTION_SEND:
                    if last_r_ts < 0:
                        last_r_ts = item.latest_ts
                    if not last_content:
                        last_content = "[客服回复] " + item.content
                    break

            # 计算未读数量
            if count_rs_after_last_s > 0:
                un_read_size += 1

            tt[from_number] = {
                "no_read_cnt": count_rs_after_last_s,
                "latest_ts": last_r_ts,
                "last_content": last_content,
            }
        logger.info(f"[CustomerServiceToolAdmin] beforeConversationSize: {len(group_records)}, "
                    f"afterConversationSize: {len(tt)},  noReadSize: {un_read_size}")
        sorted_res = dict(sorted(tt.items(), key=CustomerServiceToolAdmin.custom_sort))

        messages = []
        for k, v in sorted_res.items():
            messages.append({
                "head": k[0:3],
                "number": k,
                "time": TimeUtil.GetBeijingTimeStrFromTs(v['latest_ts']),
                "message": v["last_content"],
                "tips": v["no_read_cnt"] if "no_read_cnt" in v else 0
            })

        context = {
            "conversation_start_from": TimeUtil.GetBeijingTimeStr(before_date),
            "conversation_list": messages,
            "conversation_size": len(tt),
            "conversation_unread_size": un_read_size,
        }
        return context

    @staticmethod
    def custom_sort(item):
        # 根据 "no_read_cnt" 的值来排序，no_read_cnt 大的排在前面
        if item[1]["no_read_cnt"] > 0:
            return 0, -item[1]["latest_ts"]
        else:
            return 1, -item[1]["latest_ts"]

    @staticmethod
    def get_user_ctx(to_userid: int, to_number: str) -> str:
        logger.info(f"[CustomerServiceToolAdmin] backend door get context: {to_userid}:{to_number}")
        user_profile_ctx_str = UserContextTool.get_user_context_str("", "", to_userid)
        return user_profile_ctx_str

    @staticmethod
    def get_user_it_sms_list(to_userid: int, to_number: str) -> str:
        logger.info(f"[CustomerServiceToolAdmin] backend door get sms list: {to_userid}:{to_number}")
        records = SmsTool.get_latest_top_n_it_sms(to_userid, 50)

        res = []
        for v in records:
            time_ts = v.created_at.strftime('%Y-%m-%d %H:%M:%S')
            _content = v.content if v.content else v.images
            res.append(f"[{time_ts}][{v.from_number}]->[{v.to_number}]:{_content}")
        if len(res) == 0:
            res.append("该用户没有短信!")
        return json.dumps(res, indent=4, default=str, ensure_ascii=False)

    @staticmethod
    def get_user_non_it_sms_list(to_userid: int, to_number: str) -> str:
        logger.info(f"[CustomerServiceToolAdmin] backend door get sms list: {to_userid}:{to_number}")
        records = SmsTool.get_latest_top_n_non_it_sms(to_userid, 50)

        res = []
        for v in records:
            time_ts = v.created_at.strftime('%Y-%m-%d %H:%M:%S')
            _content = v.content if v.content else v.images
            res.append(f"[{time_ts}][{v.from_number}]->[{v.to_number}]:{_content}")
        if len(res) == 0:
            res.append("该用户没有短信!")
        return json.dumps(res, indent=4, default=str, ensure_ascii=False)

    @staticmethod
    def get_user_order(to_userid: int, to_number: str) -> str:
        logger.info(f"[CustomerServiceToolAdmin] backend door update order: {to_userid}:{to_number}")
        ret = RefreshOrderTool.refresh_user_vip(to_userid)
        return json.dumps(ret, indent=4, default=str, ensure_ascii=False)

    @staticmethod
    def recover_user_order(to_userid: int) -> str:
        done, err_msg = RefreshOrderTool.recover_order_subscription(to_userid)
        if not done:
            return "恢复订阅失败，原因：" + err_msg

        to_number = NumberTool.get_mock_number_by_userid(to_userid)
        content = SmsNoticeTool.recover_subscription()
        SmsItSupportTool.add_support_sms_both_from_it(to_userid, to_number, content)
        user_profile_ctx_str = UserContextTool.get_user_context_str("", "", to_userid)
        return user_profile_ctx_str

    @staticmethod
    def change_number_submit(to_userid: int) -> str:
        to_number_previous = NumberTool.get_number_by_userid(to_userid)

        res, err_msg = NumberTool.unbind_user_phone_number(to_userid)

        # 推送
        system_content, db_content, push_content = SmsNoticeTool.go_and_pick_number()
        to_number = NumberTool.get_mock_number_by_userid(to_userid)
        SmsItSupportTool.add_support_sms_both_from_it_v2(to_userid, to_number_previous, db_content, push_content)
        SmsItSupportTool.add_support_sms_both_from_it_v2(to_userid, to_number, db_content, push_content)

        # 放开挑选号码权限
        cache.set(RedisKey.GenIsAllowSearchVendorDirectly(to_userid), 1, 3600)

        return f"{to_userid} {res} {err_msg}"

    @staticmethod
    def get_user_number_list(user_id: int) -> str:
        records = NumberTool.get_numbers_by_userid(user_id)
        res = [{
            "id": v.id,
            "number": v.number,
            "user_id": v.user_id,
            "expired_at": v.expired_at,
            "platform": v.platform,
            "status": v.status,
            "created_at": v.created_at
        } for v in records]

        return json.dumps(res, indent=4, default=str, ensure_ascii=False)

    @staticmethod
    def get_all_numbers_group_by_status_cnt() -> str:
        res = NumberTool.get_all_status_number_cnt()
        return json.dumps(res, indent=4, default=str, ensure_ascii=False)


from django.contrib.admin import SimpleListFilter


class HoursFilter(SimpleListFilter):
    title = '时间范围（小时）'
    parameter_name = 'hours'

    def lookups(self, request, model_admin):
        return [
            ('1', '过去1小时'),
            ('2', '过去2小时'),
            ('4', '过去4小时'),
            ('6', '过去6小时'),
            ('8', '过去8小时'),
            ('10', '过去10小时'),
            ('12', '过去12小时'),
            ('18', '过去18小时'),
            ('24', '过去24小时'),
        ]

    def queryset(self, request, queryset):
        hours = self.value()
        if hours:
            try:
                hours = int(hours)
                time_threshold = now() - datetime.timedelta(hours=hours)
                return queryset.filter(created_at__gte=time_threshold)
            except ValueError:
                pass
        return queryset


@admin.register(SmsFakeRecordProxy)
class CallSmsRecordProxyAdmin(admin.ModelAdmin):
    change_list_template = "admin/resent_fake_sms.html"

    list_display = (
        "id",
        'action_buttons',  # 按钮列
        'combined_number_info',
    )
    list_filter = (HoursFilter,)

    def action_buttons(self, obj):
        # 生成按钮的 HTML
        return format_html(
            '''
            <div style="display: flex; flex-direction: column; gap: 4px;">
                <button type="button" class="button" onclick="resendFakeSms('{}')">重发过滤</button>
                <button type="button" class="button" style="background-color: yellow; color: black;" 
                onclick="resendOriginalSms('{}')">重发原始</button>
                <button type="button" class="button" onclick="showConversation('{}')">查看会话</button>
                <button type="button" class="button" style="background-color: red; color: black;" 
                onclick="reviewFakeSms('{}')">杀掉</button>
            </div>
            ''',
            obj.id, obj.id, obj.id, obj.id
        )

    action_buttons.short_description = "操作"

    def combined_number_info(self, obj):
        try:
            # 缓存数据以减少重复调用
            contact_data = {
                'contact_count': SmsTool.get_contact_cnt(obj.user_id),
                'real_contact_count': SmsTool.get_contact_cnt_no_it_real_delivery(obj.user_id),
                'sms_count': SmsTool.get_total_send_cnt(obj.user_id),
                'real_delivered_count': SmsTool.get_total_send_cnt_no_it_real_delivery(obj.user_id),
            }
            delivered_cnt, received_cnt = SmsTool.get_number_delivered_and_received(obj.user_id, obj.to_number)
            sent_cnt, get_cnt = SmsTool.get_number_sent_and_get_cnt(obj.user_id, obj.to_number)
            sent_gt_id_cnt, get_gt_id_cnt = SmsTool.get_number_sent_and_get_cnt_gt_id(obj.user_id, obj.to_number,
                                                                                      obj.id)
            this_number_data = {
                'delivered': delivered_cnt,
                'received': received_cnt,
                'sent_cnt': sent_cnt,
                'get_cnt': get_cnt,
                'sent_gt_id_cnt': sent_gt_id_cnt,
                'get_gt_id_cnt': get_gt_id_cnt,
            }

            beijing_tz = pytz.timezone('Asia/Shanghai')
            created_time = localtime(obj.created_at, timezone=beijing_tz)
            time_difference = (now() - obj.created_at).total_seconds()

            # 参数化时间差值
            one_hour = 3600
            color = 'red' if time_difference <= one_hour else 'black'

            beijing_time_colored = format_html(
                '<span style="color: {};">{}</span>',
                color,
                created_time.strftime('%Y-%m-%d %H:%M:%S')
            )

            first_check_reason_colored = format_html(
                '<span style="font-weight: bold;">{}</span>',
                obj.first_check_reason
            )

            if obj.images:
                # 添加按钮和缩略图
                image_url = obj.images  # 假设 obj.images 是图片的 URL
                button_html = format_html(
                    '<button type="button" class="thumbnail-button" onclick="showImage(\'{}\')">'
                    '<img src="{}" alt="Thumbnail" style="width: 60px; height: 60px;"></button>',
                    image_url,
                    image_url
                )
                return self._format_with_images(obj, contact_data, this_number_data, beijing_time_colored,
                                                first_check_reason_colored, button_html)

            return self._format_with_text(obj, contact_data, this_number_data, beijing_time_colored,
                                          first_check_reason_colored)

        except Exception as e:
            # 添加异常处理
            logger.error(f"Error in combined_number_info: {e}", exc_info=True)
            return "Error occurred while processing data."

    @staticmethod
    def _format_with_images(obj, contact_data, this_number, beijing_time_colored, first_check_reason_colored,
                            button_html):
        return format_html("user_id: {}<br>"
                           "from: {}<br>"
                           "to: {}<br><br>"
                           "sid: {}<br><br>"
                           "contact_count:{}/{}<br>"
                           "sms_count:{}/{}<br>"
                           "delivered: {}/{}/{}<br>"
                           "received: {}/{}/{}<br>"
                           "time: {}<br><br><br>"
                           "images: {}<br><br>"
                           "why_bad: {}"
                           "{}",
                           obj.user_id,
                           obj.from_number,
                           obj.to_number,
                           obj.sid,
                           contact_data['real_contact_count'], contact_data['contact_count'],
                           contact_data['real_delivered_count'], contact_data['sms_count'],
                           this_number['delivered'], this_number['sent_cnt'], this_number['sent_gt_id_cnt'],
                           this_number['received'], this_number['get_cnt'], this_number['get_gt_id_cnt'],
                           beijing_time_colored,
                           obj.images,
                           first_check_reason_colored,
                           button_html)

    @staticmethod
    def _format_with_text(obj, contact_data, this_number, beijing_time_colored,
                          first_check_reason_colored):
        return format_html("user_id: {}<br>"
                           "from: {}<br>"
                           "to: {}<br><br>"
                           "sid: {}<br><br>"
                           "contact_count:{}/{}<br>"
                           "sms_count:{}/{}<br>"
                           "delivered: {}/{}/{}<br>"
                           "received: {}/{}/{}<br>"
                           "time: {}<br><br>"
                           "content: {}<br><br><br>"
                           "filtered_content: {}<br><br><br>"
                           "why_bad: {}",
                           obj.user_id,
                           obj.from_number,
                           obj.to_number,
                           obj.sid,
                           contact_data['real_contact_count'], contact_data['contact_count'],
                           contact_data['real_delivered_count'], contact_data['sms_count'],
                           this_number['delivered'], this_number['sent_cnt'], this_number['sent_gt_id_cnt'],
                           this_number['received'], this_number['get_cnt'], this_number['get_gt_id_cnt'],
                           beijing_time_colored,
                           obj.content,
                           obj.filtered_content,
                           first_check_reason_colored)

    combined_number_info.short_description = 'Number Info'

    def changelist_view(self, request, extra_context=None):
        response = super().changelist_view(request, extra_context=extra_context)
        if hasattr(response, 'context_data'):
            # 计算总和
            queryset = response.context_data['cl'].queryset
            total = queryset.aggregate(total_value=Count('id'))['total_value'] or 0
            # 将 total 传递给模板
            response.context_data['total'] = total
        return response

    def get_queryset(self, request):

        hours = request.GET.get('hours')
        if not hours:
            hours = 2
        is_reviewed = request.GET.get('is_reviewed')
        user_id = request.GET.get('user_id', 0)
        logger.info(f"[SendSms] re-send start listing, user_id:{user_id}, hours:{hours} ...")
        time_ago = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(hours=int(hours))
        queryset = SMSRecord.objects.filter(status='send_fake', is_fatpo_resend=0, created_at__gte=time_ago)
        if is_reviewed:
            queryset = queryset.filter(is_reviewed=is_reviewed)
        else:
            queryset = queryset.filter(is_reviewed=0)
        if user_id:
            queryset = queryset.filter(user_id=int(user_id))
        logger.info(f"[SendSms] re-send list done, hours:{hours}, user_id:{user_id}, size: {len(queryset)}")
        return queryset.order_by('-id')

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('resend_fake_sms/<int:record_id>/', self.admin_site.admin_view(self.resend_fake_sms),
                 name='resend_fake_sms'),
            path('resend_fake_sms_original/<int:record_id>/', self.admin_site.admin_view(self.resend_fake_sms_original),
                 name='resend_fake_sms_original'),
            path('review_fake_sms/<int:record_id>/', self.admin_site.admin_view(self.review_fake_sms),
                 name='review_fake_sms'),
            path('show_conversation_action/<int:record_id>/', self.admin_site.admin_view(self.show_conversation_action),
                 name='show_conversation_action'),
            path('filter_by_replies/<int:min_replies_cnt>/', self.admin_site.admin_view(self.filter_by_replies),
                 name='filter_by_replies'),
            path('filter_replies_by_userid/<int:user_id>/', self.admin_site.admin_view(self.filter_replies_by_userid),
                 name='filter_replies_by_userid'),
        ]
        return custom_urls + urls

    @staticmethod
    def resend_fake_sms(_, record_id):
        sms_record = SmsTool.get_by_id(record_id)
        logger.info(
            f"[SendSms] Tool.ReSend fake sms: {record_id}, sid:{sms_record.sid}, user_id:{sms_record.user_id}, "
            f"content:{sms_record.content}, status:{sms_record.status}, reason:{sms_record.first_check_reason}")

        try:
            if sms_record.status != "send_fake":
                return JsonResponse({
                    "status": "error",
                    "message": "This SMS record is not in the 'send_fake' status."
                })

            if OrderTool.is_user_vip_expire(sms_record.user_id) != ErrInfo.SUCCESS:
                logger.warning(f"[SendSms] resend sid:{sms_record.sid}, user: {sms_record.user_id} is expired")
                return JsonResponse({
                    "status": "error",
                    "message": "The user is expired."
                })

            if UserKillTool.is_tmp_kill(sms_record.user_id):
                logger.warning(f"[SendSms] resend sid:{sms_record.sid}, user: {sms_record.user_id} killed")
                sms_record.is_reviewed = 1
                sms_record.save()
                return JsonResponse({
                    "status": "error",
                    "message": f"currently kill, ttl: {cache.ttl(RedisKey.gen_tmp_kill_user(sms_record.user_id))}s"
                })

            if UserKillTool.is_tmp_mute(sms_record.user_id):
                logger.warning(f"[SendSms] resend sid:{sms_record.sid}, user: {sms_record.user_id} muted")
                sms_record.is_reviewed = 1
                sms_record.save()
                return JsonResponse({
                    "status": "error",
                    "message": f"currently mute, ttl: {cache.ttl(RedisKey.gen_tmp_mute_user(sms_record.user_id))}s"
                })

            if sms_record.images:
                sid = TelnyxUtil.send_mms(sms_record.from_number, sms_record.to_number, sms_record.images)
            else:
                sid = TelnyxUtil.send_sms(sms_record.from_number, sms_record.to_number,
                                          sms_record.filtered_content[0:700])
            logger.info(f"[SendSms] resend sid:{sid}")
            sms_record.is_fatpo_resend = 1
            sms_record.sid = sid
            sms_record.first_check_reason = sms_record.first_check_reason + ",fatpo_resend"
            sms_record.save()

            # 成功发送短信后，返回成功的响应
            return JsonResponse({
                "status": "success",
                "message": "Fake SMS has been successfully resent!",
                "data": {
                    "sid": sid,
                    "user_id": sms_record.user_id,
                    "content": sms_record.content
                }
            })

        except Exception as ex:
            # details: [{'code': '40300', 'title': 'Blocked due to STOP message',
            # 'detail': "Messages cannot be sent from  如果禁止发送短信就把所有的短信审核了
            if "40300" in str(ex):
                one_day_ago = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(hours=2)
                sms_list = SMSRecord.objects.filter(user_id=sms_record.user_id,
                                                    status='send_fake', is_reviewed=0, is_fatpo_resend=0,
                                                    created_at__gte=one_day_ago).order_by('-id')
                for s in sms_list:
                    s.is_reviewed = 1
                    s.save()

            error_stack = traceback.format_exc()  # 获取完整的错误堆栈

            # 只打印含有 'telnyx' 的行
            telnyx_lines = [line for line in error_stack.splitlines() if 'telnyx' in line.lower()]

            # 保留原有的日志记录
            logger.warning(f"[SendSms] re-send fake sms failed, sid:{sms_record.sid}, user_id:{sms_record.user_id}, "
                           f"content:{sms_record.content}, status:{sms_record.status}, "
                           f"reason:{sms_record.first_check_reason}", exc_info=False)

            # 如果发生异常，返回错误响应
            return JsonResponse({
                "status": "error",
                "message": "\n".join(telnyx_lines)
            })

    @staticmethod
    def resend_fake_sms_original(_, record_id):
        sms_record = SmsTool.get_by_id(record_id)
        logger.info(
            f"[SendSms] Tool.ReSend fake sms original: {record_id}, sid:{sms_record.sid}, user_id:{sms_record.user_id},"
            f"content:{sms_record.content}, status:{sms_record.status}, reason:{sms_record.first_check_reason}")

        try:
            if sms_record.status != "send_fake":
                return JsonResponse({
                    "status": "error",
                    "message": "This SMS record is not in the 'send_fake' status."
                })

            if OrderTool.is_user_vip_expire(sms_record.user_id) != ErrInfo.SUCCESS:
                logger.warning(f"[SendSms] resend sid:{sms_record.sid}, user: {sms_record.user_id} is expired")
                return JsonResponse({
                    "status": "error",
                    "message": "The user is expired."
                })

            if UserKillTool.is_tmp_kill(sms_record.user_id):
                logger.warning(f"[SendSms] resend sid:{sms_record.sid}, user: {sms_record.user_id} killed")
                sms_record.is_reviewed = 1
                sms_record.save()
                return JsonResponse({
                    "status": "error",
                    "message": f"currently kill, ttl: {cache.ttl(RedisKey.gen_tmp_kill_user(sms_record.user_id))}s"
                })

            if UserKillTool.is_tmp_mute(sms_record.user_id):
                logger.warning(f"[SendSms] resend sid:{sms_record.sid}, user: {sms_record.user_id} muted")
                sms_record.is_reviewed = 1
                sms_record.save()
                return JsonResponse({
                    "status": "error",
                    "message": f"currently mute, ttl: {cache.ttl(RedisKey.gen_tmp_mute_user(sms_record.user_id))}s"
                })

            if sms_record.images:
                sid = TelnyxUtil.send_mms(sms_record.from_number, sms_record.to_number, sms_record.images)
            else:
                content = sms_filter_simple.replace_non_gsm7(sms_record.content)  # 移除非gsm7
                remove_emojis_content = sms_filter_simple.remove_emojis(content)  # 移除表情
                if len(content) > 0:
                    content = remove_emojis_content
                content = util.Util.remove_accents(content)  # 替换带符号的字母比如 corazón perdó
                sid = TelnyxUtil.send_sms(sms_record.from_number, sms_record.to_number, content[0:700])

            logger.info(f"[SendSms] resend sid:{sid}")
            sms_record.is_fatpo_resend = 1
            sms_record.sid = sid
            sms_record.first_check_reason = sms_record.first_check_reason + ",fatpo_resend"
            sms_record.save()

            return JsonResponse({
                "status": "success",
                "message": "Fake SMS has been successfully resent!",
                "data": {
                    "sid": sid,
                    "user_id": sms_record.user_id,
                    "content": sms_record.content
                }
            })

        except Exception as ex:
            # details: [{'code': '40300', 'title': 'Blocked due to STOP message',
            # 'detail': "Messages cannot be sent from  如果禁止发送短信就把所有的短信审核了
            if "40300" in str(ex):
                one_day_ago = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(hours=2)
                sms_list = SMSRecord.objects.filter(user_id=sms_record.user_id,
                                                    status='send_fake', is_reviewed=0, is_fatpo_resend=0,
                                                    created_at__gte=one_day_ago).order_by('-id')
                for s in sms_list:
                    s.is_reviewed = 1
                    s.save()

            error_stack = traceback.format_exc()  # 获取完整的错误堆栈

            # 只打印含有 'telnyx' 的行
            telnyx_lines = [line for line in error_stack.splitlines() if 'telnyx' in line.lower()]

            # 保留原有的日志记录
            logger.warning(f"[SendSms] re-send fake sms failed, sid:{sms_record.sid}, user_id:{sms_record.user_id}, "
                           f"content:{sms_record.content}, status:{sms_record.status}, "
                           f"reason:{sms_record.first_check_reason}", exc_info=False)

            # 如果发生异常，返回错误响应
            return JsonResponse({
                "status": "error",
                "message": "\n".join(telnyx_lines)
            })

    @staticmethod
    def review_fake_sms(_, record_id):
        sms_record = SmsTool.get_by_id(record_id)
        logger.info(
            f"[SendSms] Tool.review fake sms: {record_id}, sid:{sms_record.sid}, user_id:{sms_record.user_id}, "
            f"content:{sms_record.content}, status:{sms_record.status}, reason:{sms_record.first_check_reason}")

        try:
            sms_record.is_reviewed = 1
            sms_record.save()

            return JsonResponse({
                "status": "success",
                "message": "Fake SMS has been successfully reviewed.",
                "data": {
                    "sid": sms_record.sid,
                    "user_id": sms_record.user_id
                }
            })

        except Exception:
            logger.error(f"[SendSms] review fake sms failed, sid:{sms_record.sid}, user_id:{sms_record.user_id}, "
                         f"content:{sms_record.content}, status:{sms_record.status}, "
                         f"reason:{sms_record.first_check_reason}", exc_info=True)

            return JsonResponse({
                "status": "error",
                "message": "Failed to review fake SMS. Please try again later."
            })

    @staticmethod
    def show_conversation_action(request, record_id):
        logger.info(f"[show_conversation_action] meta: {request.META}")
        record = SmsTool.get_by_id(record_id)
        user_id = record.user_id
        to_number = record.to_number

        sms_records = SmsTool.get_conversation_sms(user_id, to_number)
        sms_records_json = []

        # 只要最后50条
        for s in sms_records:
            if s.id == record_id:
                is_hl = True
            else:
                is_hl = False
            content = s.content.replace('\n', ' ')
            if not content:
                content = s.images

            sms_records_json.append({
                "direction": s.direction,
                "status": s.status + "  " + s.err_reason,
                "content": content,
                "filtered_content": s.filtered_content,
                "ts": timeutil.TimeUtil.GetBeijingTimeStr(s.created_at),
                "is_hl": is_hl
            })
        sms_size = len(sms_records_json)
        sms_records_json = sms_records_json[-50:]
        return_size = len(sms_records_json)
        logger.info(f"[show_conversation_action] user_id:{user_id}, to_number:{to_number}, sms size:{sms_size}, "
                    f"return size:{return_size}")
        return JsonResponse({'status': 'success', 'data': sms_records_json})

    @staticmethod
    def filter_by_replies(request, min_replies_cnt):
        try:
            hours = int(request.GET.get('hours', 2))

            logger.info(f"[SendSms][filter_by_replies] min_replies_cnt:{min_replies_cnt} ...")
            one_day_ago = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(hours=hours)
            ans = SMSRecord.objects.filter(status='send_fake', is_reviewed=0, is_fatpo_resend=0,
                                           created_at__gte=one_day_ago).order_by('-id')

            logger.info(f"[SendSms][filter_by_replies] min_replies_cnt:{min_replies_cnt}, total:{len(ans)} ")
            for s in ans:
                this_number_gt_send_cnt, this_number_gt_get_cnt = SmsTool.get_number_sent_and_get_cnt_gt_id(s.user_id,
                                                                                                            s.to_number,
                                                                                                            s.id)
                if this_number_gt_get_cnt >= min_replies_cnt:
                    logger.info(f"[SendSms][filter_by_replies] min_replies_cnt:{min_replies_cnt}, id:{s.id}, "
                                f"user:{s.user_id}, to_number:{s.to_number}...")
                    sms_record = SmsTool.get_by_id(s.id)
                    sms_record.is_reviewed = 1
                    sms_record.save()

            return JsonResponse({'status': 'success'})
        except Exception as e:
            logger.error(f"[SendSms][filter_by_replies] failed, min_replies_cnt:{min_replies_cnt}", exc_info=True)
            return JsonResponse({'status': 'error', "message": str(e)})

    @staticmethod
    def filter_replies_by_userid(request, user_id: int):
        try:
            logger.info(f"[SendSms][filter_replies_by_userid] user_id:{user_id} ...")
            ans = SMSRecord.objects.filter(status='send_fake', is_reviewed=0, is_fatpo_resend=0).order_by('-id')

            logger.info(f"[SendSms][filter_replies_by_userid] user_id:{user_id}, total:{len(ans)} ")
            for s in ans:
                if s.user_id == user_id:
                    logger.info(f"[SendSms][filter_replies_by_userid] id:{s.id} reviewed...")
                    s = SmsTool.get_by_id(s.id)
                    s.is_reviewed = 1
                    s.save()

            return JsonResponse({'status': 'success'})
        except Exception as e:
            logger.error(f"[SendSms][filter_replies_by_userid] failed, user_id:{user_id}", exc_info=True)
            return JsonResponse({'status': 'error', "message": str(e)})
