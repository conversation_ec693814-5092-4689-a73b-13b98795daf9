import os
import random
import string
import threading
import uuid
from datetime import datetime

from captcha.image import ImageCaptcha
from django.contrib.admin.views.decorators import staff_member_required
from django.core.cache import cache
from django.shortcuts import get_object_or_404, redirect
from django.utils.decorators import method_decorator

from Call.utils import CallUtils
from Common.err import ErrInfo
from Common.ratelimit.decorators import ratelimit
from Common.rediskey import RedisKey
from Common.views import SecPhoneView
from Feedback.feedback_auto_ex_mail import send_feedback_mail
from Feedback.models import Feedback
from Number.tools.number_tool import NumberTool
from SecPhone import settings
from SecPhone.settings import logger
from Sms.tools.tool_it_support_sms import SmsItSupportTool
from Sms.tools.tool_notice import SmsNoticeTool
from User.tools.user_context_tool import UserContextTool
from User.tools.user_login_tool import UserLoginTool
from User.tools.user_tool import UserTool
from .models import UserTicket


class CommitFeedback(SecPhoneView):

    @staticmethod
    def _send_sms(user_id: int, content: str, feedback_ctx_str: str):
        try:
            # 获取用户的号码，如果有的话
            feedback_number = NumberTool.GetNumberByUserId(user_id)
            if not feedback_number:
                feedback_number = UserTool.create_mock_number(user_id)

            # 插入双方的短信记录
            _content = "User feedback: " + content
            SmsItSupportTool.add_support_sms_both_from_feedback(user_id, feedback_number, _content)

            _content = SmsNoticeTool.feedback_auto_reply(_content)
            SmsItSupportTool.add_support_sms_both_from_it(user_id, feedback_number, _content)

            # 把用户的属性也反馈给support本人
            SmsItSupportTool.add_support_sms(settings.APP_IT_SUPPORT_USERID, settings.SMS_DIRECTION_RECEIVE,
                                             feedback_number, settings.APP_IT_SUPPORT_SHOW_PHONE, feedback_ctx_str)
        except Exception:
            logger.error(f"[CommitFeedback] failed, user:{user_id}, content:{content}", exc_info=True)

    @SecPhoneView.VerifySign
    @SecPhoneView.VerifyToken
    def post(self, request):
        data, err_code, err_msg = self.ReadPostJson(request, check_list=['content', 'email', ])
        if err_code != 0:
            logger.error(f"[CommitFeedback] invalid param, headers: {self.GetHeaderInRequest(request)}, "
                         f"request:{request.body}")
            return self.ReturnError(err_code, err_msg)

        header = self.GetHeaderInRequest(request)
        user_id = int(header['userid'])
        appid = int(header['appid'])
        content = data.get("content", "")
        email = data.get("email", "")

        # 自动包装上下文
        feedback_ctx_str = UserContextTool.get_user_context_str(email, content, user_id)
        logger.error(f"[CommitFeedback] user feedback：{feedback_ctx_str}, headers：{header}")

        # 落库
        feedback = Feedback(
            user_id=header['userid'],
            email=email,
            content=content,
            image_url=data.get("image_url", ""),
            user_profile=feedback_ctx_str
        )
        feedback.save()

        # 发送短信
        self._send_sms(user_id, content, feedback_ctx_str)

        # 发送一封邮件
        t = threading.Thread(target=send_feedback_mail, args=(user_id, appid, email, feedback.id, content))
        t.start()

        return self.ReturnSuccess({})


class CreateCaptcha(SecPhoneView):
    @staticmethod
    def generate_random_string(length=4):
        random_string = ''.join(random.choice(string.digits) for _ in range(length))
        return random_string

    @staticmethod
    def generate_captcha_image(captcha_text, save_path):
        image = ImageCaptcha().generate_image(captcha_text)
        image.save(save_path)

    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='3/60s', block=True))
    def get(self, request):
        # 主逻辑
        captcha_text = self.generate_random_string()  # 生成随机字符串作为验证码
        captcha_uuid = str(uuid.uuid4())  # 生成UUID，虽然在这个例子中可能不需要，但保留以供参考
        captcha_image_path = f'/tmp/captcha/{captcha_uuid}.png'  # 使用UUID作为文件名的一部分来避免冲突

        if not os.path.exists('/tmp/captcha/'):
            os.makedirs(f'/tmp/captcha/')

        # 生成验证码图片并保存
        self.generate_captcha_image(captcha_text, captcha_image_path)

        # 输出生成的验证码和图片路径（可选）
        logger.info(f"Captcha Text: {captcha_text}")
        logger.info(f"Captcha Image Path: {captcha_image_path}")

        # 验证码存到redis，5分钟
        key = RedisKey.gen_captcha(captcha_uuid)
        cache.set(key, captcha_text, 300)

        url = settings.STATIC_URL_BASE + f"/captcha/{captcha_uuid}.png"
        logger.info(f"Captcha Text: {captcha_text}, Captcha Image Path: {captcha_image_path}, url:{url}")
        return self.ReturnSuccess({
            "url": url
        })


@staff_member_required
def mark_ticket_done(request, ticket_id):
    ticket = get_object_or_404(UserTicket, id=ticket_id)
    ticket.status = 'done'
    ticket.save()
    return redirect(request.META.get('HTTP_REFERER', 'admin:index'))


class SubmitUserTicket(SecPhoneView):

    @method_decorator(ratelimit(key='header:X_REAL_IP', rate='20/60s', block=True))
    @method_decorator(ratelimit(key='post:wxid', rate='20/60s', block=True))
    def post(self, request):
        logger.info(f"[SubmitUserTicket] data: {request.POST}")
        ticket_type = request.POST.get('ticket_type')
        username = request.POST.get('username')
        email = request.POST.get('email')
        current_number = request.POST.get('current_number')
        ticket = request.POST.get('ticket')
        wxid = request.POST.get('wxid')

        # 参数校验
        if not ticket_type or len(ticket_type) > 128:
            logger.error(f"[SubmitUserTicket] invalid param, ticket_type, {ticket_type}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")
        if not username or len(username) > 128:
            logger.error(f"[SubmitUserTicket] invalid param, username, {username}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")
        if not email or len(email) > 128:
            logger.error(f"[SubmitUserTicket] invalid param, email, {email}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")
        if not current_number or len(current_number) > 32:
            logger.error(f"[SubmitUserTicket] invalid param, current_number, {current_number}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")
        if not ticket or len(ticket) > 10086:
            logger.error(f"[SubmitUserTicket] invalid param, ticket, {ticket}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")
        if not wxid or len(wxid) > 64:
            logger.error(f"[SubmitUserTicket] invalid param, device_id, {wxid}")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")

        # save pic
        today = datetime.today().strftime("%Y-%m-%d")
        file_dir = settings.BASE_DIR + "/%s/uploads/%s" % ('static', today)
        if not os.path.exists(file_dir):
            os.makedirs(file_dir)

        image_urls = []
        images = request.FILES.getlist('images')
        for image in images:
            filename = f"ticket_{CallUtils.gen_random_filename()}.png"
            filename_abs = "%s/%s" % (file_dir, filename)
            logger.info(f"save image: {filename_abs}")

            with open(filename_abs, 'wb') as file:
                for chunk in image.chunks():
                    file.write(chunk)

            filename_base = "/uploads/%s/%s" % (today, filename)
            image_urls.append(settings.STATIC_URL_BASE + filename_base)

        # 保存
        ud = UserLoginTool.get_userid_by_device(wxid)
        if not ud:
            logger.warning(f"[SubmitUserTicket] invalid wxid: {wxid}, not in db.")
            return self.ReturnError(ErrInfo.UNKNOWN_SERVER_ERROR, "invalid param")

        user_id = ud.user_id

        # 获取用户当前DB的号码
        current_db_number = NumberTool.get_number_by_userid(user_id)

        image_url = ",".join(image_urls)
        user_ticket = UserTicket(ticket_type=ticket_type,
                                 user_id=user_id,
                                 device_id=wxid,
                                 email=email,
                                 current_number=current_number,
                                 current_db_number=current_db_number,
                                 ticket=ticket,
                                 username=username,
                                 image_url=image_url
                                 )
        user_ticket.save()

        logger.warning(f"[SubmitUserTicket] user submit ticket, user_id:{user_id}, device_id:{wxid}, name:{username}, "
                       f"email: {email}, current_number:{current_number}, current_db_number:{current_db_number}，"
                       f"ticket:{ticket}, image_url:{image_url}.")
        return self.ReturnSuccess()
