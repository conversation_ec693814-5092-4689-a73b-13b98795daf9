from django.contrib import admin
from django.db import models
from django.http import HttpResponseRedirect
from django.urls import path, reverse
from django.utils.html import format_html

from Sms.tools.tool_it_support_sms import SmsItSupportTool


class Feedback(models.Model):
    user_id = models.IntegerField('user_id')
    email = models.CharField('email', max_length=128, null=False)
    user_profile = models.TextField('user_profile', null=True)
    content = models.TextField('content', max_length=2048, null=False)
    image_url = models.CharField('image_url', max_length=512, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


class UserTicket(models.Model):
    ticket_type = models.CharField('ticket_type', max_length=64, null=False)
    user_id = models.IntegerField('user_id', default=0, null=True)
    device_id = models.Char<PERSON>ield('device_id', max_length=64, null=False)
    username = models.CharField('username', max_length=128, default='')
    current_number = models.CharField('current_number', max_length=32, default='')
    current_db_number = models.CharField('current_db_number', max_length=32, default='')
    email = models.CharField('email', max_length=128, default='')
    ticket = models.TextField('ticket', default='')
    image_url = models.CharField('image_url', max_length=2048, default='', null=True)
    STATUS_CHOICES = (
        ('todo', 'ToDo'),
        ('done', 'Done'),
    )
    status = models.CharField('status', max_length=32, choices=STATUS_CHOICES, default='todo')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager()


@admin.register(UserTicket)
class UserTicketAdmin(admin.ModelAdmin):
    # 在这里定义 UserTicketAdmin 的其他属性和方法
    list_display = ('id', 'created_at', 'ticket_type', 'combined_user_info', 'ticket', 'combined_numbers',
                    'image_thumbnails', 'status', 'done_button')
    list_filter = ('ticket_type', 'status')
    actions = ['mark_as_done']
    # 在详情页中显示的字段的顺序
    readonly_fields = (
        'id', 'user_id', 'device_id', 'created_at', 'ticket_type', 'username', 'email', 'ticket', 'current_number',
        'current_db_number', 'image_url')

    class Media:
        js = ('admin/js/custom_user_ticket_admin.js',)
        css = {
            'all': ('admin/css/custom_user_ticket_admin.css',)  # 这里引入你的 CSS 文件
        }

    def combined_user_info(self, obj):

        submit_ticket_cnt = UserTicket.objects.filter(user_id=obj.user_id).count()

        return format_html("user_id: {}<br><br>submit_ticket_cnt:{}<br><br>device_id: {}<br><br>username: {}<br>email: {}",
                           obj.user_id, submit_ticket_cnt, obj.device_id, obj.username, obj.email)

    combined_user_info.short_description = 'User Info'

    def combined_numbers(self, obj):
        return format_html("{}<br>{}", obj.current_number, obj.current_db_number)

    combined_numbers.short_description = 'Current Numbers'

    def image_thumbnails(self, obj):
        if obj.image_url:
            image_urls = obj.image_url.split(',')
            thumbnails = []
            for url in image_urls:
                thumbnails.append(
                    format_html(
                        '<img src="{}" width="50" height="50" class="thumbnail" style="cursor: pointer;" '
                        'onclick="toggleImageSize(this);" />',
                        url.strip()
                    )
                )
            # 返回缩略图，并且插入模态框和相关的JavaScript代码
            return format_html(''.join(thumbnails)) + self._get_modal_html()
        else:
            return '-'

    image_thumbnails.short_description = 'Image Thumbnail'

    @staticmethod
    def _get_modal_html():
        # 生成模态框 HTML 和 JavaScript 代码
        return format_html("""
            <div id="modal" style="display:none;">
                <div id="modal-content">
                    <img id="modal-image" src="" alt="Large Image" />
                </div>
            </div>
        """)

    def mark_as_done(self, request, queryset):
        queryset.update(status='done')

    mark_as_done.short_description = 'Mark selected tickets as done'

    def done_button(self, obj):
        if obj.status != 'done':
            return format_html('<a class="button" href="{}">Done</a>',
                               reverse('admin:mark_ticket_done', args=[obj.id]))
        else:
            return '-'

    done_button.short_description = 'Done'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('mark_ticket_done/<int:ticket_id>/', self.admin_site.admin_view(self.mark_ticket_done),
                 name='mark_ticket_done'),
        ]
        return custom_urls + urls

    def mark_ticket_done(self, request, ticket_id):
        ticket = UserTicket.objects.get(id=ticket_id)
        ticket.status = 'done'
        ticket.save()

        # push ticket done
        content = "Hello, we have received your ticket request and have processed it. Thank you for your patience."
        SmsItSupportTool.add_support_sms_both_from_it_with_link(ticket.user_id, ticket.current_db_number, content,
                                                                link="", is_ai_answer=0)

        self.message_user(request, "Ticket marked as done.")
        return HttpResponseRedirect(request.META.get('HTTP_REFERER', '/fatpoadmin'))
