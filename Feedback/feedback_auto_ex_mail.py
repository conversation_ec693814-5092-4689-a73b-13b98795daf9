# coding=utf-8
import smtplib
from email.header import <PERSON><PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import parseaddr, formataddr

from SecPhone.settings import logger


def _format_addr(s):
    name, addr = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), addr))


def send_feedback_mail(user_id: int, appid: int, feedback_mail: str, feedback_id, content: str):
    try:
        # 邮箱定义
        smtp_server = 'smtp.exmail.qq.com'
        smtp_port = 587
        from_addr = '<EMAIL>'
        password = 'cptbtptp123ASD'
        to_addr = [feedback_mail]
        bcc_addr = ['<EMAIL>', '<EMAIL>', '<EMAIL>']

        # id 稍微伪造下
        feedback_id_mask = "000" + str(feedback_id + 10000)

        # 邮件对象
        email_appid_map = {
            0:  "TextApp",
            1:  "TxtNow",
        }

        msg = MIMEMultipart()
        msg['From'] = _format_addr(f'{email_appid_map[appid]} Group Team <%s>' % from_addr)
        msg['To'] = _format_addr('<%s>' % to_addr)
        msg['Bcc'] = _format_addr('<%s>' % bcc_addr)
        msg['Subject'] = Header(f'{email_appid_map[appid]} Group Feedback[ticket:{feedback_id_mask}]', 'utf-8').encode()

        html = f'<html></h2>'
        html += f"""
        </body>
        <p>
        Hi there,<br>
        <br>
        Glad to hear the feedback from you.<br>
        ==================================<br>
        <table border="1" >
        <tr><td>feedback mail</td><td>{to_addr[0]}</td></tr>
        <tr><td>feedback ticket</td><td>{feedback_id_mask}</td></tr>
        <tr><td>feedback content</td><td>{content}</td></tr>
        <tr><td>feedback uid</td><td>{'21' + str(user_id) + '8'}</td></tr>
        </table>
        ==================================<br>
        So glad to receive your feedback, and we will reply to you as soon as possible.<br>
        <br>
        Before our customer service staff replies, you can view the self-help question guide:<br>

        <strong>(1) Be suspended</strong><br>
                &emsp;&emsp;1. Have you sent too many violations recently？<br>
                &emsp;&emsp;2. It may be AI misjudgment, which requires manual unsuspended by our customer service staff.<br>
        <strong>(2) I can't send or receive messages</strong><br>
                &emsp;&emsp;1. Please ensure that your subscription is valid and your points are sufficient.<br>
                &emsp;&emsp;2. Because we have introduced AI audit mechanism, you may have sent some sensitive words, or AI judgment is wrong. If the former, you need to correct the message before sending it. If it is the latter, please wait for the reply from the customer service.<br>
        <strong>(3) I can't make or answer calls</strong><br>
                &emsp;&emsp;1. Please ensure that your subscription is valid and your points are sufficient.<br>
                &emsp;&emsp;2. Make sure that the number you dialed is correct and the target phone is not turned off.<br>
        <strong>(4) SMS contact exceeds the threshold</strong><br>
                &emsp;&emsp;1. The AI program suspects that your behavior is in RISK, please wait for a reply from the customer service.<br>
        <strong>(5) My subscription hasn’t been activated</strong><br>
                &emsp;&emsp;1. We support 3 types of accounts: facebook, Google, and Apple.<br>
                &emsp;&emsp;2. You can try switching accounts to see if the subscription is still there.<br>
        <br>
        You can reply to me in this email and I will answer you as soon as possible.<br>
        Sincerely,<br>
        {email_appid_map[appid]} Group Team<br>
        </p>
         """

        html += '</body></html>'
        msg.attach(MIMEText(html, 'html', 'utf-8'))

        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_addr, password)
        server.sendmail(from_addr, to_addr + bcc_addr, msg.as_string())
        server.quit()
    except Exception:
        logger.error(f"send_feedback_mail failed, user: {user_id}, content: {content}, mail: {feedback_mail}",
                     exc_info=True)


if __name__ == '__main__':
    send_feedback_mail(21, 1, '<EMAIL>', 0, "ada")
