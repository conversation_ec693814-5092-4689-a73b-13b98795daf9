# DB
DB_HOST_KEY=*********
DB_USER_KEY=root
DB_PASSWORD_KEY=123qweQWE
DB_PASSWORD_DEV_KEY=123qweQWE!@#
DB_DATABASE_NAME_KEY=secphone
DB_PORT_KEY=3306

# REDIS
REDIS_HOST_KEY=*********

# biz
APP_DOMAIN=tw.zehougroup.xyz
APP_IT_SUPPORT_PHONE=+13152271933
APP_IT_SUPPORT_SHOW_PHONE=+1000009999
APP_IT_SUPPORT_USERID=1013552
APP_IT_SUPPORT_NO_NUMBER_PREFIX=+999

# apple
APPLE_VERIFY_PASSWORD=1e92fd6e7e5f40678fd25c8a8b99f71e
APPLE_BUNDLE_ID=xyz.virtualsim.app
APPLE_VERIFY_2_PASSWORD=e76da754616a40c3a254f2acd8401136
APPLE_BUNDLE_2_ID=com.zehoutech.secondphone

# self
SALT_SECRET_KEY=x6*2nelmcubpzIKuK85p*vGrdltLjuTPhO9I4zIrVq
SIGN_SECRET_KEY=BVWTMiat$vJdQCgzCJBYnzuWhnVn#W2A8ZIP#*ZbR&Se
SIGN_SECRET_2_KEY=hUUprldW6oYPp8KzzUt@74Kv=puYbHK00xgdrt90@=Nv
SECRET_KEY=7pm*ct123098s8sOpL()9fd-OUYANG-BIG-MAC-s88_53mdzrx
AUTOGEN_PASSWORD=378d20480713fb84a75d91e7c3dfb420fe535aea0529aab40ae4604fff4efeb5
AES_KEY=2KT7zKxG2nL2TYeKXQRcRGVSpym30RY8
AES_IV=wDPPbv76VUDYKZBf


# old-tw
TWILIO_ACCOUNT_SID=**********************************
TWILIO_ACCOUNT_TOKEN=d703a14afd6342ce8699d3ed8607bfc2
TWILIO_API_KEY=**********************************
TWILIO_API_KEY_SECRET=aMhongf1klvadlAkGQeHxvs91OLWdTU4
TWILIO_VOIP_PUSH_CREDENTIAL_SID=CRc982cf2eb3671220562bc3168169a26d
TWILIO_VOIP_PUSH_CREDENTIAL_2_SID=CRc982cf2eb3671220562bc3168169a26d
TWILIO_PUSH_CREDENTIAL_SANDBOX_SID=CR88fb30d74996a84a905232690a024240
TWILIO_APP_SID=AP4cf73651580c587627999c92ac180561
TWILIO_SMS_NOTIFY_SERVICE_SID=IS461e06db3608b735ac673a2633fa6411
#TWILIO_SMS_MESSAGE_SERVICE_SID=MGdd082ba25c1602029b1b0a94da712f83
TWILIO_SMS_MESSAGE_SERVICE_SID2=
#TWILIO_SMS_MESSAGE_SERVICE_SID=MG1364926db36d3fb434dc1d9af5bb661c
TWILIO_SMS_MESSAGE_SERVICE_SID=MG6cc38b1f321fa15ff0de251b2f7b1a8f
TWILIO_SMS_NOTIFY_2_SERVICE_SID=IS43846b60ed12b94b39e59defaf240f13

# tw-majia1
#TWILIO_ACCOUNT_SID=**********************************
#TWILIO_ACCOUNT_TOKEN=7901ab6886b148460b16edc337d1343a

# tw api key :主要用于access_token, API keys are revokable credentials for the Twilio API. You can use API keys to authenticate to the REST API using basic auth with “user=KeySid” and “password=KeySecret”. You can also use API keys to sign access tokens which are also used by our Real-Time Communications SDKs. Access tokens are short-lived credentials that can be distributed safely to client-side applications
#TWILIO_API_KEY=**********************************
#TWILIO_API_KEY_SECRET=V0Jzgy3aXp9hqQt5TL90XyM6k1514qKS

# twiML
#TWILIO_APP_SID=AP7e37575c05f125004581e6db82caa198

# tw-电话 voip
#TWILIO_VOIP_PUSH_CREDENTIAL_SID=
#TWILIO_VOIP_PUSH_CREDENTIAL_2_SID=CR0eab7e05d171a3cb70235fa82ee8dfec

# tw-短信相关
#TWILIO_SMS_MESSAGE_SERVICE_SID=MG3a0587087dff18c2c1c115d032c8b881
#TWILIO_SMS_MESSAGE_SERVICE_SID2=MG2318ec5c51a6eb85d10d8e4cf9540314
TWILIO_SMS_NOTIFY_SERVICE_SID=
TWILIO_SMS_NOTIFY_2_SERVICE_SID=

# mail
EMAIL_PREFIX=TxtNowTelnyx
EMAIL_FROM_USER_KEY=<EMAIL>
DEFAULT_FROM_EMAIL_KEY=<EMAIL>
SERVER_EMAIL_KEY=<EMAIL>
EMAIL_HOST_PASSWORD_KEY=geodowcfakrsbhhi
EMAIL_FATPO_KEY=<EMAIL>
EMAIL_CHONG_KEY=<EMAIL>
EMAIL_SJJ_KEY=<EMAIL>


# FCM
FCM_API_KEY=AAAA06zjhuo:APA91bGAflNLqLjwhYBQIYvvzasMsToAxpBazcC4w07bFny79pIQZibAGkgPUT422nIkihQqJ9hb5682M61lLRC2NjRjZXcRVBDpGx59uRK5Fx0cThQWWbgeiiiSGEMc0eHZLl8fY1EK
FCM_TITLE=TxtNow
FCM_CERT_FILEPATH=/SecPhone/fcm/secondphone-271d4-f13e9590aba7.json
FCM_FATPO_PUSHID=fHPhH-z1h0FurOvdBFWGMQ:APA91bEJd_QI1mJ-4xK6bp4iquHmN6QJ9gmEJiUcHv2zo83AaBHQ8715ph-Oy9voMOZ-howxwlgfdVpQxj_NhAh9GE7sIA2Q29sQFYkHBAJUCC0bzZS9aPipBeC9QnQGGRJ00_iydMAl



# telnyx
TELNYX_API_KEY=**********************************************************
TELNYX_PUBLIC_KEY=84W+FlInraGv7d1K2SvcMMJhPwfOIwpnFIgcU9njNHQ=
TELNYX_CONNECTION_ID=2201611210929996890
TELNYX_MESSAGING_PROFILE_ID=4001891c-3745-4477-bf69-90749dd81d0d
TELNYX_VOICE_API_APPLICATION_ID=2151324095860442440
TELNYX_TEXML_APPLICATION_ID=2201610292201260090
TELNYX_OUTBOUND_VOICE_PROFILE=2201593832611513488
TELNYX_IOS_PUSH_CREDENTIAL_ID=0303fa44-0bcd-484f-bbdb-b0a98a279ccc
TELNYX_BILLING_GROUP_ID=72c8856f-0a46-4c18-8236-5e2f070087ef
TELNYX_NUMBER_TAGS=v2number
TELNYX_SID_CONNECTION_PREFIX=txtnow

