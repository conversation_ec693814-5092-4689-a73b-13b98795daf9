"""SecPhone URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf.urls import url, include
from django.contrib import admin
from django.urls import path
from django.views.static import serve

from SecPhone import settings

urlpatterns = [
    path('fatpoadmin/', admin.site.urls),
    url(r'^static/(?P<path>.*)$', serve, {'document_root': settings.STATIC_ROOT}),
    url(r'', include('User.urls')),
    url(r'', include('Number.urls')),
    url(r'', include('Point.urls')),
    url(r'', include('Call.urls')),
    url(r'', include('Sms.urls')),
    url(r'', include('Order.urls')),
    url(r'', include('Config.urls')),
    url(r'', include('Report.urls')),
    url(r'', include('Feedback.urls')),
]
