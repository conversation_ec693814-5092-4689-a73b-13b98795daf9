import inspect
import sys
import threading
import traceback

from django.conf import settings
from django.core.mail.message import EmailMultiAlternatives
from django.utils.log import AdminEmailHandler

from SecPhone.settings import logger


def send_slack_message(message):
    import json
    import requests
    channel = '#diss'
    payload = {"text": message, "channel": channel, "link_names": True}
    data = json.dumps(payload).encode("utf8")
    Token = "*********************************************************"
    url = 'https://slack.com/api/chat.postMessage'
    header = {"Content-Type": "application/json; charset=utf-8", "Authorization": "Bearer " + Token}
    response = requests.post(url, data=data, headers=header)
    print(response.text)


class MyEmailHandler(AdminEmailHandler):

    def do_send(self, subject, message):
        try:
            mail = EmailMultiAlternatives(
                u'%s%s' % (settings.EMAIL_SUBJECT_PREFIX, subject),
                message,
                settings.SERVER_EMAIL,
                [a[1] for a in settings.ADMINS]
            )
            mail.send(fail_silently=False)
        except Exception:
            traceback.print_exc()
            send_slack_message(f"V2 Error Log Mail Failed: {message}")

    def emit(self, record):
        try:
            if not getattr(settings, "ADMINS", None):
                return

            # 获取 logid，如果没有则使用默认值
            logid = getattr(record, 'logid', 'MISSING_LOGID')

            # 构建邮件内容，添加 logid 信息
            subject = self.format_subject(record.getMessage())
            origin_message = getattr(record, "email_body", record.getMessage())
            exc_info = sys.exc_info()
            message = f"Logid: {logid}\n\n{origin_message}\n\n{''.join(traceback.format_exception(*exc_info))}"

            # 带上堆栈信息，更好地观察错误情况
            message += "\n当前堆栈：\n"
            frames = inspect.stack()
            for f in frames:
                if 'VirtualSIM_BackEnd' in f.filename:
                    message += f"{f.filename}\t{f.lineno}\t{f.function}\n"

            # 特殊情况不带上下文
            if 'ratelimit.exceptions.Ratelimited' in message:
                message = origin_message
            if '贵人加点' in message:
                message = origin_message

            # 异步发送邮件
            t = threading.Thread(target=self.do_send, args=(subject, message))
            t.start()
        except Exception as e:
            logger.info(traceback.format_exc())
