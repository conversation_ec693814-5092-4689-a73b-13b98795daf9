import json
import logging
import os

import environ

import SecPhone.logid_middleware

# 环境变量
env = environ.Env()
environ.Env.read_env()

# **************** base ****************
# ***************************************
# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

LOCAL_DEV = False

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

ALLOWED_HOSTS = ['*']

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'User',
    'Number',
    'Point',
    'Order',
    'Call',
    'Sms',
    'Config',
    'Feedback',
    'Report',
    'ToolsForAAA',
    'ToolsForOrder',
    'ToolsForNumber',
    'ToolsForSms',
    'ToolsForBenefit',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'SecPhone.logid_middleware.RequestLogMiddleware',
    'SecPhone.profile_middleware.ProfileMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'SecPhone.error_handler.ErrorHandlerMiddleware',
]

ROOT_URLCONF = 'SecPhone.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'SecPhone.wsgi.application'

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_L10N = True
USE_TZ = True

STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
print("STATIC_ROOT:", STATIC_ROOT)
STATIC_URL = '/static/'
STATICFILES_DIRS = (
    os.path.join(BASE_DIR, "static"),
)

appid = 1

# 设置上传最大的文件size
DATA_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5MB

# **************** logger ****************
# ***************************************

LOG_DIR = os.path.join(BASE_DIR, "logs")
if not os.path.exists(LOG_DIR):
    os.mkdir(LOG_DIR)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '[%(levelname)s][%(asctime)s][%(logid)s][%(filename)s][%(funcName)s][%(lineno)d] > %(message)s' \
                .replace("\n", " ").replace("\r", " ")
        },
        'simple': {
            'format': '[%(levelname)s][%(logid)s] > %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
    },
    'handlers': {
        'console': {
            'level': 'WARNING',
            'filters': ['logid_filter'],
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
        },
        'error_file_handler': {
            'level': 'WARNING',
            'filters': ['logid_filter'],
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': '%s/django.err' % LOG_DIR,
            'formatter': 'standard',
            'encoding': 'utf-8',
        },
        'file_handler': {
            'level': 'INFO',
            'filters': ['logid_filter'],
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': '%s/django.log' % LOG_DIR,
            'formatter': 'standard',
            'encoding': 'utf-8',
        },  # 用于文件输出
        'mail_admins_handler': {
            'level': 'ERROR',
            'filters': ['logid_filter'],
            'class': 'SecPhone.mail.MyEmailHandler',
            'formatter': 'standard',
            'include_html': True,
        },
    },
    'loggers': {
        'mdjango': {
            # 一个记录器中可以使用多个处理器
            'handlers': ['file_handler', 'mail_admins_handler', 'error_file_handler'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'django.request': {
            'handlers': ['mail_admins_handler'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
    'filters': {
        'logid_filter': {
            '()': 'SecPhone.logid_middleware.LogidFilter',  # 指定你的过滤器
        },
    },
}

logger = logging.getLogger("mdjango")
if not any(isinstance(f, SecPhone.logid_middleware.LogidFilter) for f in logger.filters):
    logger.addFilter(SecPhone.logid_middleware.LogidFilter())

# **************** Database ****************
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases
# ***************************************

if LOCAL_DEV:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': env('DB_DATABASE_NAME_KEY'),
            'USER': env('DB_USER_KEY'),
            'PASSWORD': env('DB_PASSWORD_DEV_KEY'),
            'HOST': '127.0.0.1',
            'PORT': env('DB_PORT_KEY'),
            'OPTIONS': {'charset': 'utf8mb4'}
        }
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': env('DB_DATABASE_NAME_KEY'),
            'USER': env('DB_USER_KEY'),
            'PASSWORD': env('DB_PASSWORD_KEY'),
            'HOST': env('DB_HOST_KEY'),
            'PORT': env('DB_PORT_KEY'),
            'OPTIONS': {'charset': 'utf8mb4'},
            "POOL_OPTIONS": {
                "POOL_SIZE": 15,
                "MAX_OVERFLOW": 5,
                "RECYCLE": 10 * 60  # 10 min,
            },
        }
    }

# **************** cache ****************
# ***************************************
if LOCAL_DEV:
    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": f"redis://127.0.0.1:6379/0",
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
                "CONNECTION_POOL_KWARGS": {"max_connections": 100},
                "SERIALIZER": "django_redis.serializers.json.JSONSerializer"
            },
            "KEY_FUNCTION": "SecPhone.settings.make_key",
            "REVERSE_KEY_FUNCTION": "SecPhone.settings.reverse_key",
        }
    }
else:
    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": f"redis://{env('REDIS_HOST_KEY')}:6379/0",
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
                "CONNECTION_POOL_KWARGS": {"max_connections": 100},
                "SERIALIZER": "django_redis.serializers.json.JSONSerializer"
            },
            "KEY_FUNCTION": "SecPhone.settings.make_key",
            "REVERSE_KEY_FUNCTION": "SecPhone.settings.reverse_key",
        }
    }


def make_key(key, key_prefix, version):
    return '%s' % key


def reverse_key(key):
    return key


# **************** mail ****************
# ***************************************

# 管理员邮箱
TOTAL_ADMINS = (
    ('fatpo', env('EMAIL_FATPO_KEY')),
    ('chong', env('EMAIL_CHONG_KEY')),
    ('sjj', env('EMAIL_SJJ_KEY')),
)

# 开发环境就不要邮件报警
if LOCAL_DEV:
    ADMINS = ()
else:
    ADMINS = (
        ('fatpo', env('EMAIL_FATPO_KEY')),
    )

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.qq.com'  # QQ邮箱SMTP服务器(邮箱需要开通SMTP服务)
EMAIL_PORT = 25  # QQ邮箱SMTP服务端口
EMAIL_HOST_USER = env('EMAIL_FROM_USER_KEY')  # 我的邮箱帐号
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD_KEY')  # 授权码  不是你的QQ密码
EMAIL_SUBJECT_PREFIX = env('EMAIL_PREFIX')  # 为邮件标题的前缀
EMAIL_USE_TLS = True  # 开启安全链接
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL_KEY')
SERVER_EMAIL = env('SERVER_EMAIL_KEY')  # 设置发件人

# **************** push ****************
# ***************************************


FCM_CERT_FILEPATH_APPID_MAP = {
    0: BASE_DIR + "/SecPhone/fcm/call-me-323812-47c031c42a01.json",
    1: BASE_DIR + "/SecPhone/fcm/secondphone-271d4-f13e9590aba7.json"
}

from firebase_admin import initialize_app, credentials

with open(FCM_CERT_FILEPATH_APPID_MAP[0]) as f:
    credential1 = credentials.Certificate(json.load(f))

with open(FCM_CERT_FILEPATH_APPID_MAP[1]) as f:
    credential2 = credentials.Certificate(json.load(f))

FCM_APP_NAME_MAP = {
    0: 'text app',
    1: '2nd number',
}

FIREBASE_APP_0 = initialize_app(credential=credential1, name=FCM_APP_NAME_MAP[0])
FIREBASE_APP_1 = initialize_app(credential=credential2, name=FCM_APP_NAME_MAP[1])

# 是否推送的开关
PUSH_SWITCH = True

# **************** apple ****************
# ***************************************


## -------------------apple verify---------------
APPLE_VERIFY_URL = 'https://buy.itunes.apple.com/verifyReceipt'
APPLE_VERIFY_URL_SANDBOX = 'https://sandbox.itunes.apple.com/verifyReceipt'

APPLE_VERIFY_PASSWORD = {
    0: env('APPLE_VERIFY_PASSWORD'),
    1: env('APPLE_VERIFY_2_PASSWORD'),
}

# app的包名，用于订单验证
APPLE_BUNDLE_ID = {
    0: env('APPLE_BUNDLE_ID'),
    1: env('APPLE_BUNDLE_2_ID'),
}


def extract_product_ids(json_data: dict) -> set:
    product_ids = set()

    for key, value in json_data.items():
        if isinstance(value, dict):
            # 如果值是字典，则递归调用函数
            product_ids.update(extract_product_ids(value))
        elif isinstance(value, list):
            for i in value:
                product_ids.update(extract_product_ids(i))
        elif key == "product_id":
            # 找到目标 product_id，添加到集合中
            product_ids.add(value)

    return product_ids


vip_file = BASE_DIR + '/Config/vip_origin.json'
with open(vip_file) as f:
    vip_data = json.loads(f.read())

points_file = BASE_DIR + '/Config/points_origin.json'
with open(points_file) as f:
    points_data = json.loads(f.read())

total_product_ids = extract_product_ids(vip_data)
total_product_ids.update(extract_product_ids(points_data))

# **************** biz ****************
# ***************************************
SMS_WANGYI_MANUAL_CALLBACK = "https://phone2.zehougroup.xyz/zhphone/phone/sms/review/manual/callback/"

APP_IT_SUPPORT_PHONE = env('APP_IT_SUPPORT_PHONE')
APP_IT_SUPPORT_SHOW_PHONE = env('APP_IT_SUPPORT_SHOW_PHONE')
APP_IT_SUPPORT_USERID = int(env('APP_IT_SUPPORT_USERID'))
APP_IT_SUPPORT_NO_NUMBER_PREFIX = env('APP_IT_SUPPORT_NO_NUMBER_PREFIX')
EMAIL_PREFIX = env('EMAIL_PREFIX')

# release 相关
NUMBER_NEED_RELEASE_ALERT_CNT = 300  # 待释放号码数达到阈值后，可以告警出来
NUMBER_CHECK_RELEASE_BY_CREATED_DELTA_DAYS = 2  # 如果用户是3.5号订阅的，今天是4.18号，那么我们找到一个比今天大的订阅日期，5.3号，5.3 - 4.18 > 2天就可以释放
NUMBER_CHECK_RELEASE_BY_DURATION_GAP_DAYS = 20  # 如果用户3.5号订阅，它过期时间是4.5，那么4.5-3.5=30days > 20 ，可以释放了
NUMBER_CHECK_RELEASE_BY_USED_TIMES = 15  # 如果这个号码被太多人使用，也不大好，赶紧释放掉
SKIP_RELEASE_WHEN_EXPIRE_NUMBER_CNT = 200  # 如果只有<=200个expire number。不需要触发释放逻辑
STOP_RELEASE_WHEN_EXPIRE_NUMBER_CNT_AT = 100  # 至少要保证池子里面要有100个

# event type
EVENT_TYPE_CALL = "CALL"
EVENT_TYPE_SMS = "SMS"
EVENT_TYPE_MMS = "MMS"
NOT_FINISHED_SEEKING_BEFORE_USER = 1  # 这个from number来找前任的
NOT_FINISHED_INCOMING_REQ_INVALID_JSON = 2  # 请求入参json不对劲
NOT_FINISHED_INCOMING_NUMBER_FORBIDDEN = 3  # 这个from number被禁止的
NOT_FINISHED_TO_NUMBER_CANNOT_LOCATE_USER = 4  # 目标 to number无法定位到userid
NOT_FINISHED_CARRIER_FAILED = 5  # 运营商发送失败

# system notice
SYSTEM_NOTICE_LOCK_NUMBER_SUCCESS = "{{CODE_0001}}"
SYSTEM_NOTICE_LOCK_NUMBER_HALF_SUCCESS = "{{CODE_0002}}"
SYSTEM_NOTICE_MISS_INCOMING_CALL = "{{CODE_0003}}"
SYSTEM_NOTICE_GO_AND_PICK_NUMBER = "{{CODE_0004}}"
SYSTEM_NOTICE_FOLLOW_CORRECT_NUMBER_FORMAT = "{{CODE_0005}}"
SYSTEM_NOTICE_REQUEST_TOO_FAST = "{{CODE_0006}}"
SYSTEM_NOTICE_ADD_POINTS = "{{CODE_0007}}"
SYSTEM_NOTICE_ADD_VIP = "{{CODE_0008}}"
SYSTEM_NOTICE_GET_BACK_NUMBER = "{{CODE_0009}}"
SYSTEM_NOTICE_GET_BACK_ACCOUNT = "{{CODE_0010}}"

# --------------------consume point unit----------------------
POINT_PER_UNIT = {
    "CALL": 2,
    "SMS": 1,
    "MMS": 2
}

# --------------------charge point product----------------------
CHARGE_POINT_PRODUCT_CONFIG = {
    0: {
        "100_credits": 100,
        "500_credits": 500,
        "1000_credits": 1000,
        "2000_credits": 2000,
    },
    1: {
        "100_points": 100,
        "500_points": 500,
        "1000_points": 1000,
        "2000_points": 2000,
    }
}

# --------------------phone number ---------------
LOCK_NUMBER_EXPIRE_SECONDS = 600

STATIC_URL_BASE = 'http://phone.zehougroup.xyz'
STATIC_URL_BASE2 = 'http://phone2.zehougroup.xyz'

# 短信相关
SMS_DIRECTION_SEND = "SEND"
SMS_DIRECTION_RECEIVE = "RECIEVE"  # 没办法一开始写错了
VALID_SMS_CONTENT_MAX_LENGTH = 2000  # 单词长度
ILLEGAL_SMS_SUSPEND_THRESHOLD = 50  # 违规短信发送次数被封号的threshold
CONTACT_LIMIT_THRESHOLD = 50  # 只能联系 N 个人
CONTACT_LIMIT_TIME_DAYS = 3  # N 天内只能联系 M 个人

# 某些分类的标签虽然命中但也允许发送，比如骂别人
# suggestion 0 = 通过， 1  = 怀疑， 2 = 不通过
SMS_SUGGESTION_OK = 0
SMS_SUGGESTION_SUSPEND = 1
SMS_SUGGESTION_INVALID = 2

# 具体标签：https://support.dun.163.com/documents/588434200783982592?docId=******************
# 100：色情，200：广告，260：广告法，300：暴恐，400：违禁，500：涉政，600：谩骂，700：灌水，900：其他，1100：涉价值观
# 400238	暴力血腥（海外版）, 400242	自杀自残（海外版）,400243	剥削劳动力（海外版）
# 400241 欺诈（海外版）, 400446 欺诈, 400274 违禁赌博, 400277 违禁毒品药品
SMS_NOT_ALLOW_LABELS_WHEN_FILTER = [100, 200, 260, 400]

# Vendor
VENDOR_TWILIO = 0
VENDOR_TELNYX = 1

# call 方向
CALL_DIRECTION_OUTGOING = "outgoing"
CALL_DIRECTION_INCOMING = "incoming"

# LOGIN TYPE
LOGIN_TYPE_GOOGLE = 1
LOGIN_TYPE_APPLE = 2
LOGIN_TYPE_FACEBOOK = 3

# toll-free
TOLL_FREE_AREA_CODES = ['800', '811', '822', '833', '844', '855', '866', '877', '888']

# canada
canada_area_codes = [
    "204", "226", "236", "249", "250", "289", "306", "343", "354", "365",
    "367", "368", "382", "403", "416", "418", "428", "431", "437", "438",
    "450", "468", "474", "506", "514", "519", "548", "579", "581", "584",
    "587", "604", "613", "639", "647", "672", "683", "705", "709", "742",
    "753", "778", "780", "807", "819", "825", "833", "844", "855", "866",
    "867", "873", "877", "879", "888", "902", "905"
]

# 客服敷衍话术
CUSTOMER_REPLY_FINISH = "[End of this round of manual customer service, please feel free to contact us if needed]"

# **************** security ****************
# ***************************************


TOKEN_EXPIRE_TIMEOUT = 3600 * 24 * 30
UUID_EXPIRE_TIMEOUT = 3600 * 24 * 30

##---------------request sign ---------------------

SIGN_TS_ALLOW_DIFF = 30

SALT_SECRET_KEY = env('SALT_SECRET_KEY')
SIGN_SECRET_KEY = {
    0: env('SIGN_SECRET_KEY'),
    1: env('SIGN_SECRET_2_KEY')
}

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY')

##--------------autogen password-------------
AUTOGEN_PASSWORD = env('AUTOGEN_PASSWORD')

## 用户白名单
with open(BASE_DIR + "/Config/user_control.json") as f:
    user_config = json.load(f)

WHITE_USER_LIST = user_config.get("white_user_list", [])  # 用户白名单
WHITE_DEVICE_LIST = user_config.get("white_device_list", [])  # 用户设备白名单
GREY_USER_LIST = user_config.get("grey_user_list", [])  # 用户灰名单，还能救回来...

############################
AES_KEY = env('AES_KEY')
AES_IV = env('AES_IV')

######################
SESSION_COOKIE_AGE = 3600 * 24 * 30  # admin登录 30 天
