import traceback

from django.http import JsonResponse

from Common import ratelimit
from SecPhone.settings import logger


class ErrorHandlerMiddleware:

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_exception(self, request, exception):
        try:
            if exception:

                # 接口频率limit
                if isinstance(exception, ratelimit.exceptions.Ratelimited):
                    return JsonResponse({"err_code": 60001, "err_msg": "server error: request too frequently"},
                                        safe=False, status=200)

                # Format your message here
                message = "**{url}**\n\n{error}\n\n````{tb}````".format(
                    url=request.build_absolute_uri(),
                    error=repr(exception),
                    tb=traceback.format_exc()
                )
                logger.error(message)
                # Do now whatever with this message
                # e.g. requests.post(<slack channel/teams channel>, data=message)

            return JsonResponse({"err_code": 60000, "err_msg": "server error"}, safe=False, status=200)
        except Exception:
            logger.error(f"error_handler fallback failed: {request.path}", exc_info=True)
            return JsonResponse({"err_code": 60000, "err_msg": "server error"}, safe=False, status=200)
