"""
@author: jon
"""


class RedisKey:
    NEW_USER_FLAG_EXPIRE_SECONDS = 24 * 3600 * 5  # 5 day
    NEW_USER_SEND_PHONE_EXPIRE_SECONDS = 24 * 3600 * 1  # 1 day
    NEW_USER_FLAG_VALUE = "1"
    RETRY_SMS_EXPIRE_SECONDS = 3600  # 1 小时
    SMS_LATEST_TIMESTAMP_EXPIRE_SECONDS = 24 * 3600 * 30  # 1个月
    INCOMING_CALL_ALLOW_SECONDS = 86400 * 3  # 3day

    @staticmethod
    def GenTokenKey(user_id):
        return 'token_' + str(user_id)

    @staticmethod
    def GenUuidKey(user_id):
        return 'uuid_' + str(user_id)

    @staticmethod
    def GenAppIdKey(user_id):
        return 'app_id_' + str(user_id)

    @staticmethod
    def GenLockNumberKey(number):
        return 'lock_number_' + str(number)

    @staticmethod
    def GenLockNumberUserKey(user_id):
        return 'lock_user_number_' + str(user_id)

    @staticmethod
    def GenAllLockNumberPrefix():
        return 'lock_number_'

    @staticmethod
    def GetTwCacheNumbers() -> str:
        """
        tw的每分钟缓存
        :return:
        """
        return "tw:cache:numbers"

    @staticmethod
    def GetTwCacheNumbersExpireSeconds() -> int:
        """
        tw的每分钟缓存的过期时间
        :return:
        """
        return 3600

    @staticmethod
    def GenNewUserFlag(user_id):
        return 'newuser:' + str(user_id)

    @staticmethod
    def GenRetrySmsKey(sid: str):
        return f'sms:retry:{sid}'

    @staticmethod
    def GenSmsLatestTimestamp(user_id: int):
        return f'sms:latest_ts:{user_id}'

    @staticmethod
    def GenPhoneTwVersion(phone_number: str):
        return "tw_version_" + phone_number

    @staticmethod
    def get_number_platform_key(phone_number: str):
        return f"platform_{phone_number}"

    @staticmethod
    def get_number_platform_expire_seconds():
        return 3600

    @staticmethod
    def GenSmsBlockToKey(from_number: str):
        return f'sms:block:{from_number}'

    @staticmethod
    def GenIsAllowSearchVendorDirectly(user_id: int):
        return f"is_allow_vendor_search_{user_id}"

    @staticmethod
    def GenCloseSearchVendor():
        return f"is_close_vendor_search"

    @staticmethod
    def GenBaiduFilterAccessTokenCache():
        return "baidu_filter_access_token"

    @staticmethod
    def BackendDogGetLatestsSmsSyncSendingPartByFatpo():
        return 'get_latest_sms_sync_sending_part_by_fatpo'

    @staticmethod
    def is_prepare_change_number_done(user_id: int):
        return f"is_prepare_change_number_done_{user_id}"

    @staticmethod
    def newest_customer_service_message():
        return f"newest_customer_service_message"

    @staticmethod
    def newest_customer_service_message_details():
        return f"newest_customer_service_message_details"

    @staticmethod
    def notice_duplicate_order(new_user_id: int, old_user_id: int):
        return f"notice_duplicate_order_{new_user_id}_{old_user_id}"

    @staticmethod
    def gen_captcha(text: str) -> str:
        return f"captcha:{text}"

    @staticmethod
    def gen_incoming_call_allow(user_id: int) -> str:
        return f"incoming_call_allow_{user_id}"

    @staticmethod
    def gen_check_bad_sms_every_day(day: str):
        return f"check_bad_sms_{day}"

    @staticmethod
    def gen_tmp_kill_user(user_id: int):
        return f"tmp_kill_user_{user_id}"

    @staticmethod
    def gen_tmp_mute_user(user_id: int):
        return f"tmp_mute_user_{user_id}"

    @staticmethod
    def gen_check_event_allow(user_id: int) -> str:
        return f"check_event_allow_{user_id}"

    @staticmethod
    def gen_user_first_init(user_id: int):
        return f"first_init_{user_id}"
