import re
import unicodedata
from itertools import product


class Util:

    @staticmethod
    def remove_accents(input_str):
        nfkd_form = unicodedata.normalize('NFKD', input_str)
        only_ascii = ''.join([c for c in nfkd_form if not unicodedata.combining(c)])
        return only_ascii

    @staticmethod
    def is_all_asterisks_count(s: str) -> bool:
        return s.count('*') == len(s)

    @staticmethod
    def only_digital(text: str) -> bool:
        filtered = re.sub(r'[^a-zA-Z0-9]', '', text)

        # 检查字符串是否只包含数字
        if filtered.isdigit():
            return True

        return False

    @staticmethod
    def check_list_less_than_max_distance(my_list: list) -> bool:

        # 使用 itertools.product 获取所有可能的组合
        for combo in product(*my_list):
            # 检查组合的最大值和最小值差是否小于等于10
            if max(combo) - min(combo) <= 10:
                print(combo)
                return True
        return False

    @staticmethod
    def find_indices(lst, text):
        indices = [index for index, value in enumerate(lst) if value == text]
        return indices

    @staticmethod
    def seconds_to_str(seconds):
        days = seconds // 86400
        remaining_seconds = seconds % 86400
        hours = remaining_seconds // 3600
        remaining_seconds %= 3600
        minutes = remaining_seconds // 60
        remaining_seconds %= 60
        result = ""
        if days > 0:
            result += f"{days}d"
        if hours > 0:
            result += f"{hours}h"
        if minutes > 0:
            result += f"{minutes}m"
        if remaining_seconds > 0 or (days == 0 and hours == 0 and minutes == 0):
            result += f"{remaining_seconds}s"
        return result


if __name__ == '__main__':
    # print(Util.only_digital("100/120/140"))
    # print(Util.only_digital("100/120/140 abc"))
    # print(Util.check_list_less_than_max_distance([[1, 2], [50], [3, 3, 4]]))
    print(Util.check_list_less_than_max_distance([[1, 2], [1], [3, 3, 4]]))
    print(Util.find_indices(["apple", "banana", "apple", "orange", "apple"], "apple"))
    print(Util.is_all_asterisks_count("****"))
    print(Util.is_all_asterisks_count("🫶*****"))
    print(Util.seconds_to_str(86500))
    print(Util.seconds_to_str(86400))
    print(Util.seconds_to_str(86439))
    print(Util.seconds_to_str(3599))
    print(Util.remove_accents("3599 !! how are you !@#!@#gmail.com !! corazón perdó"))
