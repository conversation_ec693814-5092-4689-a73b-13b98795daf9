import json
import time

from django.conf import settings
from django.core.cache import cache
from django.http import HttpResponse, HttpResponsePermanentRedirect, JsonResponse
from django.views.generic.base import View

from Common.err import ErrInfo
from Common.rediskey import <PERSON>isKey
from Common.util import Util
from SecPhone.settings import logger
from User.tools.user_block_tool import UserBlockTool
from User.tools.user_login_tool import UserLoginTool
from User.tools.user_tool import UserTool


class SecPhoneView(View):

    def GetOrNone(self, model, **kwargs):
        try:
            return model.objects.get(**kwargs)
        except model.DoesNotExist:
            return None

    def ReturnError(self, err_code, err_msg=None):
        if err_msg is None:
            msg_dic = ErrInfo.ErrMsg_EN
            err_msg = msg_dic.get(err_code, "err msg undefine")
        return self.ReturnJson(
            {"err_code": err_code, "err_msg": err_msg, "server_ts": int(time.time() * 1000 * 1000), "data": {}})

    def ReturnErrorToApple(self, err_code, err_msg=None):
        if err_msg is None:
            msg_dic = ErrInfo.ErrMsg_EN
            err_msg = msg_dic.get(err_code, "err msg undefine")
        return self.ReturnJsonWithStatusCode(
            {"err_code": err_code, "err_msg": err_msg, "server_ts": int(time.time() * 1000 * 1000), "data": {}},
            status_code=400)

    def ReturnSuccess(self, data=None):
        data = {} if data is None else data
        return self.ReturnJson(
            {"err_code": 0, "err_msg": "success", "server_ts": int(time.time() * 1000 * 1000), "data": data})

    def ReturnSuccessToWangyi(self, data=None):
        # wangyi 只支持： 这种格式
        data = {} if data is None else data
        return self.ReturnJson(
            {"code": 200, "msg": "success", "server_ts": int(time.time() * 1000 * 1000), "data": data})

    def ReturnErrorToWangyi(self, err_code, err_msg=None):
        if err_msg is None:
            msg_dic = ErrInfo.ErrMsg_EN
            err_msg = msg_dic.get(err_code, "err msg undefine")
        return self.ReturnJson(
            {"code": err_code, "msg": err_msg, "server_ts": int(time.time() * 1000 * 1000), "data": {}})

    def ReturnJson(self, args):
        return JsonResponse(args, safe=False)

    def ReturnJsonWithStatusCode(self, args, status_code=400):
        return JsonResponse(args, safe=False, status=status_code)

    def ReadPostJson(self, request, check_list=()):
        try:
            data = json.loads(request.body)
        except Exception as e:
            logger.error(f"{request.path} parse request json failed: {request.body}", exc_info=True)
            return None, ErrInfo.JSON_FORMAT_ERROR, str(e)

        for item in check_list:
            if item not in data:
                logger.error(f"{request.path} post request without needed param：{item}, body:{data}")
                return None, ErrInfo.JSON_REQUEST_PARA_MISS, "miss parameter: " + item
        return data, 0, None

    def ReturnStr(self, content):
        return HttpResponse(content)

    def RedirectPage(self, url):
        return HttpResponsePermanentRedirect(url)

    def GetHeaderInRequest(self, request):
        h = request.META
        headers = {
            "userid": h.get('HTTP_X_USERID', None),
            "token": h.get('HTTP_X_TOKEN', None),
            "timestamp": h.get('HTTP_X_TIMESTAMP', None),
            "sign": h.get('HTTP_X_SIGN', None),
            "appid": int(h.get('HTTP_X_APPID', 0)),
            "os": h.get('HTTP_X_OS', None),
            "uuid": h.get('HTTP_X_UUID', None),
            "asaid": h.get('HTTP_X_ASAID', None),
            "source": h.get('HTTP_X_SOURCE', None),
            "app_version": int(str(h.get('HTTP_X_VERSION', 0)).replace(".", "")),
        }

        if headers['timestamp'] is not None:
            headers['timestamp'] = Util.FormatInt(headers['timestamp'])

        headers['userid'] = Util.FormatInt(headers['userid'])

        if headers['source'] is not None:
            logger.info(f"GetHeaderInRequest headers:{headers}, source: {headers['source']}")
            UserTool.update_app_source(headers['userid'], headers['source'])
            if headers['source'] == 'my_official_website':
                logger.error(f"GetHeaderInRequest headers:{headers}, website source: {headers['source']}")

        if headers['asaid'] is not None:
            logger.info(f"GetHeaderInRequest headers:{headers}, asaid: {headers['asaid']}")
            UserTool.update_asaid(headers['userid'], headers['asaid'])

        return headers

    def __verify_sign(self, path: str, headers: dict):
        try:
            userid = headers.get('userid', -1)
            for x in ('token', 'timestamp', 'sign'):
                if headers[x] is None:
                    logger.error(f"[VerifySign] userid:{userid}, path:{path}, request header without {x}, give up！")
                    return False, ErrInfo.SIGN_PARAMETER_MISS

            time1 = int(headers['timestamp'] / 1000000)
            time2 = int(time.time())
            diff_ts = abs(time1 - time2)
            gap_seconds = settings.SIGN_TS_ALLOW_DIFF

            # 用户第一次启动不检查gap时间
            if not cache.get(RedisKey.gen_user_first_init(userid)):
                gap_seconds += 3600 * 24 * 30
                cache.set(RedisKey.gen_user_first_init(userid), 1, 3600)
                logger.info(f"[VerifySign] userid:{userid}, path:{path}, request first init, not check ts"
                            f"headers: {headers}！ abs({time1} - {time2}) = {diff_ts}")
            # push 接口稍微宽容一些，怕影响推送
            if "push" in path.lower():
                gap_seconds += 3600
            # profile 接口稍微宽容一些，怕影响后续流程
            if "profile" in path.lower():
                gap_seconds += 3600

            if diff_ts > gap_seconds:
                logger.warning(
                    f"[VerifySign] userid:{userid}, path:{path}, request header ts error，"
                    f"abs({time1} - {time2}) = {diff_ts} > {settings.SIGN_TS_ALLOW_DIFF}, give up, headers: {headers}！")
                return False, ErrInfo.SIGN_TIMESTAMP_EXPIRED

            appid = int(headers.get("appid", 0))
            sign = Util.MD5Sum(str(headers['timestamp']) + headers['token'] + settings.SIGN_SECRET_KEY[appid])

            # check if duplicated request
            is_dup_req = cache.get(headers['sign'])
            if is_dup_req is not None:
                logger.warning(f"[VerifySign] userid:{userid}, path:{path}, request with duplicate reqID，give up！")
                return False, ErrInfo.SIGN_INCORRECT

            cache.set(headers['sign'], 1, settings.SIGN_TS_ALLOW_DIFF)

            if sign == headers['sign']:
                return True, 0

            logger.warning(
                f"[VerifySign] userid:{userid}, path:{path}, sign error，needed sign = {sign}，"
                f"headers's sign={headers['sign']}！")
            return False, ErrInfo.AUTH_TOKEN_INVALID
        except:
            logger.error(f"[VerifySign] sign error, path:{path}, with header: {headers}！", exc_info=True)
            return False, ErrInfo.AUTH_TOKEN_INVALID

    @staticmethod
    def VerifySign(func):
        def wrapper(self, request):
            headers = self.GetHeaderInRequest(request)
            # logger.info(f"[VerifySign] path:{request.path}, headers: {headers}, body:{request.body}")
            status, err_code = self.__verify_sign(request.path, headers)
            if status is not True:
                return self.ReturnError(err_code)
            return func(self, request)

        return wrapper

    def __verify_token(self, request, headers):
        try:
            userid = headers.get("userid", 0)
            uuid = headers.get("uuid", "")
            app_version = headers.get('app_version', 0)

            if app_version >= 300000:
                logger.info(f"[VerifyToken] user:{userid}, path:{request.path}, app_version:{app_version} checking...")
                if userid == 0:
                    logger.info(f"[VerifyToken] user:{userid},{uuid},{app_version}, {request.path}, userid=0, pass..")
                    return False, ErrInfo.AUTH_DUPLICATE_REQUEST
                else:
                    if not UserLoginTool.is_device_user_match(uuid, userid):
                        logger.warning(f"[VerifyToken] user:{userid},{uuid},{app_version}, {request.path}, not match")
                        return False, ErrInfo.AUTH_DUPLICATE_REQUEST
                    else:
                        logger.info(f"[VerifyToken] user:{userid},{uuid},{app_version}, {request.path}, match")
                        return True, ErrInfo.SUCCESS

            if 'app_version' not in headers or headers['app_version'] < 200200:
                logger.warning(f"[VerifyToken] user:{userid}, path:{request.path}, 所有请求先屏蔽，让他们退出登录")
                return False, ErrInfo.AUTH_TOKEN_INVALID

            if 'userid' not in headers:
                logger.error(f"[VerifyToken] user:{userid}, path:{request.path}, headers's userid not exist! "
                             f"headers: {headers}")
                return False, ErrInfo.AUTH_TOKEN_INVALID

            if userid > 0 and UserBlockTool.is_black_user(userid, headers['uuid']) != ErrInfo.SUCCESS:
                logger.error(f"[VerifyToken] user:{userid}, path:{request.path}, black user: {headers['userid']}")
                return False, ErrInfo.AUTH_TOKEN_INVALID

            token = UserTool.get_token_by_userid(headers['userid'])
            if not token or token != headers['token']:
                if not token and headers['token'] == 'ViRTualSIM-TOKEN-DEFAULT-(sdfdkjwQ1':
                    logger.info(
                        f"[VerifyToken] userid: {userid}, path:{request.path}, nice token={token}, "
                        f"but header's token={headers['token']}, give up request.")
                elif request.path in ['/user/registerPushId/', '/zhphone/user/register/pushid/']:
                    logger.info(
                        f"[VerifyToken] userid: {userid}, path:{request.path}, nice token={token}, "
                        f"but header's token={headers['token']}, give up request.")
                else:
                    logger.warning(
                        f"[VerifyToken] userid: {userid}, path:{request.path}, nice token={token}, "
                        f"but header's token={headers['token']}, give up request.")
                return False, ErrInfo.AUTH_TOKEN_INVALID

            return True, 0
        except:
            userid = headers.get("userid", 0)
            logger.error(f"[VerifyToken] user:{userid}, path:{request.path}, check token except header={headers}",
                         exc_info=True)
            return False, ErrInfo.AUTH_TOKEN_INVALID

    @staticmethod
    def VerifyToken(func):
        def wrapper(self, request):
            headers = self.GetHeaderInRequest(request)
            status, err_code = self.__verify_token(request, headers)
            if status is not True:
                return self.ReturnError(err_code)
            return func(self, request)

        return wrapper

    def GetDataInGet(self, request, check_list=()):
        data = request.GET
        for item in check_list:
            if item not in data:
                logger.warning(f"{request.path} get request without needed param: {item}, headers: {request.META}")
                return None, ErrInfo.JSON_REQUEST_PARA_MISS, "miss parameter: " + item

        return data, 0, None

    def __verify_sign_v4(self, path: str, headers: dict):
        try:
            userid = headers.get('userid', -1)
            for x in ('token', 'timestamp', 'sign'):
                if headers[x] is None:
                    logger.error(f"[VerifySignV4] userid:{userid}, path:{path}, request header without {x}, give up！")
                    return False, ErrInfo.SIGN_PARAMETER_MISS

            time1 = int(headers['timestamp'] / 1000000)
            time2 = int(time.time())
            diff_ts = abs(time1 - time2)
            if diff_ts > settings.SIGN_TS_ALLOW_DIFF:
                logger.warning(
                    f"[VerifySignV4] userid:{userid}, path:{path}, request header ts error，"
                    f"abs({time1} - {time2}) = {diff_ts} > {settings.SIGN_TS_ALLOW_DIFF}, give up, headers: {headers}！")
                return False, ErrInfo.SIGN_TIMESTAMP_EXPIRED

            sign = Util.MD5Sum(str(headers['timestamp']) + "~p!BZhlevI^Zjx^1E_~ODPk1za6TT$yyj)@IJf)F!A(v")

            # check if duplicated request
            is_dup_req = cache.get(headers['sign'])
            if is_dup_req is not None:
                logger.warning(f"[VerifySignV4] userid:{userid}, path:{path}, request with duplicate reqID，give up！")
                return False, ErrInfo.SIGN_INCORRECT

            cache.set(headers['sign'], 1, settings.SIGN_TS_ALLOW_DIFF)

            if sign == headers['sign']:
                return True, 0

            logger.warning(
                f"[VerifySignV4] userid:{userid}, path:{path}, sign error，needed sign = {sign}，"
                f"headers's sign={headers['sign']}！")
            return False, ErrInfo.AUTH_TOKEN_INVALID
        except:
            logger.error(f"[VerifySignV4] sign error, path:{path}, with header: {headers}！", exc_info=True)
            return False, ErrInfo.AUTH_TOKEN_INVALID

    @staticmethod
    def VerifySignV4(func):
        def wrapper(self, request):
            headers = self.GetHeaderInRequest(request)
            # logger.info(f"[VerifySignV4] path:{request.path}, headers: {headers}, body:{request.body}")
            status, err_code = self.__verify_sign_v4(request.path, headers)
            if status is not True:
                return self.ReturnError(err_code)
            return func(self, request)

        return wrapper
