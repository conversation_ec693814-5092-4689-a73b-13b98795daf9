"""
@author: jon
"""


### 1: error level
#   1: sign error, 2: auth error, 3: parameter error,
#   4:business error  5: service error, 6: unknown error

### 2-3: error module or function
#   00: common module, 01: sign check, 02: token check, 03: signup,
#   04: signin, 05: make call, 06: charge/consume, 07: number
#   08: sms, 09: point

### 4-5: detail code


class ErrInfo:
    def __init__(self, err_code, err_msg):
        self.err_code = err_code
        self.err_msg = err_msg

    SUCCESS = 0
    SIGN_PARAMETER_MISS = 10100
    SIGN_TIMESTAMP_EXPIRED = 10101
    SIGN_INCORRECT = 10102

    AUTH_PARAMETER_MISS = 20200
    AUTH_TOKEN_INVALID = 20201
    AUTH_DUPLICATE_REQUEST = 20202
    AUTH_USER_NOT_EXIST = 20203
    AUTH_REQUEST_INVALID_HEADER = 20204

    JSON_FORMAT_ERROR = 30000  ## no specific err_msg
    JSON_REQUEST_PARA_MISS = 30001

    SIGNUP_PARAMETER_MISS = 30300
    SIGNUP_SERVER_ERROR = 30301
    SIGNIN_PARAMETER_MISS = 30400
    SIGNIN_SERVER_ERROR = 30401
    SIGNUP_USERNAME_EXISTS = 40301

    SIGNIN_PASSWORD_INCORRECT = 40400
    USER_NOT_EXIST = 40401
    NO_PHONE_NUMBER_AVAILABLE = 40500

    CHARGE_ERROR = 40600
    CONSUME_EVENT_UNKNOW = 40601
    CONSUME_ORDER_CERT_INVALID = 40602
    CONSUME_ORDER_SERVER_ERROR = 40603
    GET_CERT_SERVER_ERROR = 40604
    CERT_HAS_NO_EXPIRE_ERROR = 40605
    DUPLICATE_CERT = 40606
    DUPLICATE_CERT_FOR_IOS = 50001

    NUMBER_SEARCH_ERROR = 40700
    USER_ALREADY_HAS_PHONE_NUMBER = 40701
    NUMBER_ALREADY_BEING_USED = 40702
    NUMBER_BUY_PARA_MISS = 40703
    NUMBER_BUY_SERVER_ERROR = 40704
    NUMBER_LOCK_BY_OTHER_USER = 40705
    NUMBER_BUY_PARA_ERROR = 40706
    NUMBER_INVALID = 40708
    NUMBER_INVALID_SAME = 40709

    SEND_SMS_ERROR = 40800
    SEND_MMS_IMAGE_ERROR = 40801
    SEND_SMS_CONTENT_EMPTY = 40802
    SEND_SMS_CONTENT_TOO_LONG = 40803
    SEND_SMS_CONTENT_CONTAINS_BAN_WORD = 40804
    SEND_SMS_CONTENT_CONTAINS_LINK = 40805
    SEND_SMS_CONTENT_CONTAINS_PHONE_NUMBER = 40806
    SEND_SMS_SERVER_INVALID = 40807
    SEND_SMS_CONTACT_EXCEEDS_THRESHOLD = 40808
    SEND_SMS_CONTACT_BLOCK_BY_US = 40809
    SEND_SMS_CONTACT_BE_BLOCKED = 40810
    SEND_SMS_INVALID_COUNT_EXCEEDS = 40811
    SEND_SMS_CONTENT_ILLEGAL_WANGYI = 40812
    SEND_SMS_CONTENT_ILLEGAL_CHATGPT = 40813
    SEND_SMS_CONTENT_ILLEGAL_ES = 40814
    SEND_SMS_CONTENT_CONTAINS_TOO_MUCH_UPPER_WORDS = 40815
    SEND_SMS_CONTENT_CONTAINS_MONEY = 40816
    SEND_SMS_CONTENT_AI_UNKNOWN = 40817
    SEND_SMS_CONTENT_LONG_FOR_AI = 40818
    SEND_SMS_CONTENT_NON_ENGLISH = 40819

    POINT_NOT_SUFFICIENT = 40900

    # app遇到6000X会弹窗
    UNKNOWN_SERVER_ERROR = 60000
    IP_RATE_SERVER_ERROR = 60001
    CONTACTED_ACCOUNT_UNAVAILABLE = 60002
    YOUR_ACCOUNT_UNAVAILABLE = 60003
    NUMBER_SERVER_ERROR = 60004
    CONTACTED_ACCOUNT_UNAVAILABLE_NO_POINT = 60005
    CONTACTED_ACCOUNT_UNAVAILABLE_NO_VIP = 60006
    CONTACTED_ACCOUNT_UNAVAILABLE_NO_SUPPORT_NUMBER = 60007
    CONTACTED_ACCOUNT_NUMBER_NOT_VALID = 60008
    ONLY_NUMBERS_IN_THE_US_AND_CANADA_ARE_SUPPORTED = 60009
    NEW_USER_CAN_ONLY_SEND_10_PHONES = 60010
    SUSPECTED_VIOLATION = 60011
    SUSPECTED_VIOLATION_TEMP = 60012
    SEND_SMS_CONTENT_ILLEGAL = 60013
    ORDER_BELONGS_TO_OTHER_ACCOUNT = 60014
    NEED_TO_UPDATE_APP = 60015
    NUMBER_DONT_WANT_TO_BEEN_CONTACTED = 60016
    SIGNUP_ERROR_DELETE_BEFORE = 60501
    SIGNUP_ERROR_INVALID_ACCOUNT = 60502
    SIGNUP_ERROR_ALREADY_BIND_USER = 60503
    REQUESTS_TOO_FREQUENTLY = 60103
    REQUESTS_TOO_FREQUENTLY_WHEN_TRAIL = 60104
    TEMP_MUTE_ONE_HOUR = 60105
    TEMP_MUTE_24_HOUR = 60106

    # app遇到7000x会跳转
    JUMP_VIEW = 70000
    JUMP_CHARGE_POINT_VIEW = 70001
    JUMP_VIP_VIEW = 70002
    JUMP_BUY_NUMBER_VIEW = 70003

    ErrMsg_EN = {
        0: "success",
        10100: "sign parameter miss",
        10101: "sign timestamp expired",
        10102: "sign incorrect",

        20200: "auth parameter miss",
        20201: "auth token invalid",
        20202: "duplicate request",
        20203: "auth user not exist",
        20204: "auth request invalid headers",

        30300: "signup parameter miss",
        30301: "signup server error",
        30400: "signIn parameter miss",
        30401: "signIn server error",

        40401: "account invalid",
        40301: "signup email already exists",
        40400: "signIn password or username incorrect",

        40500: "no phone number available, not purchase or expired",

        40600: "charge error",
        40601: "event unknown",
        40602: "order verify invalid",
        40603: "order verify server error",
        40604: "get order cert server error",
        40605: "cert has no expire time",
        40606: "duplicate certificate",
        50001: "Your subscription is duplicated with others. Please contact customer service for assistance.",

        40700: "number search error",
        40701: "user already has one number",
        40702: "number already being used",
        40703: "number buy para miss",
        40704: "number buy error",
        40705: "number is locked by other user",
        40706: "number buy para error",
        40708: "number invalid",
        40709: "number invalid: The destination number needs to be checked",

        40801: "send mms, image parse error",
        40900: "point insufficient, please charge",
        40802: "The concatenated message body is empty",
        40803: "The concatenated message body exceeds the 1000 character limit",
        40804: "Message contains prohibited word",
        40805: "Not allowed to carry link due to policy requirements",
        40806: "Not allowed to carry phone number due to policy requirements",
        40807: "SMS provider in your region interruption, emergency repairing. It is temporarily unable to send SMS, but can receive it.",
        40808: "SMS contact exceeds the threshold, please contact customer service through feedback",
        40809: "SMS contact blocked, please contact customer service through feedback",
        40810: "Sorry, the recipient has blocked you",
        40811: "You have sent too many inappropriate text messages. Please contact customer service.",
        40812: "illegal, unsafe or unwanted message detected, please change your message. Notice: Continue sending illegal traffic will be blocked",
        40813: "illegal, unsafe or unwanted message detected, please change your message. Notice: Continue sending illegal traffic will be blocked",
        40814: "illegal, unsafe or unwanted message detected, please change your message. Notice: Continue sending illegal traffic will be blocked",
        40815: "illegal, unsafe or unwanted message detected, please change your message. Notice: Continue sending illegal traffic will be blocked",
        40816: "illegal, unsafe or unwanted message detected, please change your message. Notice: Continue sending illegal traffic will be blocked",
        40817: "AI Unknown",
        40818: "The content is a bit long and requires AI assistance to check",
        40819: "Content non-English",

        60000: "unknown server error",
        60001: "server error: rate limit",
        60002: "The account you contacted is unavailable",
        60003: "Your account is unavailable",
        60004: "Some accidents happened with the purchase number",
        60005: "The account you contacted has no points",
        60006: "The account you contacted is not vip",
        60007: "The number you contacted is not supported",
        60008: "The number is not valid",
        60009: "Only numbers in the US and Canada are supported. Please add number prefix +1, total number like +***********",
        60010: "The sending number exceeds the daily limit, please contact customer service",
        60011: "Sorry, cause suspicious activity and be vetted many times, your account is suspended",
        60012: "Sorry, the outbound SMS messaging capabilities for this your account have been disabled due to illegal traffic being sent. Please contact us on the Me->Feedback page to recover it.",
        60013: "illegal, unsafe or unwanted message detected, please change your message. Notice: Continue sending illegal traffic will be blocked",
        60014: "The subscription order belongs to another account. Please log in with that account",
        60015: "Sorry, the app needs to be updated to the latest version to work properly",
        60016: "This number has indicated that it doesn't like to be disturbed or contacted.",
        60103: "We apologize for the inconvenience. Your recent requests have been too frequent, which may be flagged as abnormal traffic by the telecommunications provider. Please wait a moment and try again. For further assistance, feel free to contact our customer service center.",
        60104: "Hello, since you are in the trial period, you can only make one call per hour and contact only one person. If you have more questions, please contact our customer service center.",
        60105: "Hello, due to a violation, you have been temporarily muted for 1 hour. If you have any questions, please contact our customer service center.",
        60106: "Hello, due to a violation, you have been temporarily muted for hours. If you have any questions, please contact our customer service center.",
        60501: "The account has been deleted as per your request. For any needs, please reach out to <NAME_EMAIL>",
        60502: "Something wrong with the server. Please contact customer support for assistance: <EMAIL>",
        60503: "Your device has already been bound to an account. Please use that account to login this device. Your ticketId is ",

        70000: "jump view",
        70001: "jump charge point view",
        70002: "jump vip view",
        70003: "jump buy number view",
    }
