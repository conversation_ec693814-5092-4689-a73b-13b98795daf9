import base64
import hashlib
import random
import re
import string
import time
import unicodedata
from itertools import product

from Crypto.Cipher import AES

from SecPhone import settings
from SecPhone.settings import logger


class Util:
    @staticmethod
    def remove_accents(input_str):
        nfkd_form = unicodedata.normalize('NFKD', input_str)
        only_ascii = ''.join([c for c in nfkd_form if not unicodedata.combining(c)])
        return only_ascii

    @staticmethod
    def left_only_one_digit(text: str):
        # 把 150 变成 1**
        words = text.split()
        new_words = []
        for w in words:
            first_digit = True
            new_w = ""
            for c in w:
                if c.isdigit():
                    if first_digit:
                        new_w += c
                        first_digit = False
                    else:
                        new_w += "*"
                else:
                    new_w += c
            new_words.append(new_w)

        return " ".join(new_words)

    @staticmethod
    def is_most_asterisks_count(s: str) -> bool:
        if not s:  # 如果字符串为空，返回 False
            return False

        # 全 ***
        if s.count('*') == len(s):
            return True

        # 百分之70%是 ***
        return s.count('*') / len(s) > 0.7

    @staticmethod
    def generate_random_password(length=16):
        characters = string.ascii_letters + string.digits
        random_password = ''.join(random.choice(characters) for _ in range(length))
        return random_password

    @staticmethod
    def GetFriendlyNameByPhoneNumber(number: str):
        region = number[2:5]
        friendly_name = f"({region}){number[5:8]}-{number[8:]}"
        return friendly_name

    @staticmethod
    def GenFakeSId():
        fake_sid = str(time.time() * 1000) + "_" + str(random.randint(10000, 99999))
        return fake_sid

    @staticmethod
    def FormatInt(number):

        try:
            return int(number)
        except Exception:
            return 0

    @staticmethod
    def MD5Sum(s):

        m = hashlib.md5()
        m.update(s.encode('utf-8'))
        return m.hexdigest()

    @staticmethod
    def FormatNumber(number):

        # +12056240841 --> 12056240841
        return number[number.find('+') + 1:]

    @staticmethod
    def FormatNumberV2(number: str) -> str:
        # 不处理 it support 号码
        if number == "+1000009999":
            return number

        valid_number = ''
        for index, s in enumerate(number):
            if s.isdigit():
                valid_number += s

        if len(valid_number) == 10:
            valid_number = "+1" + valid_number
        elif len(valid_number) == 11:
            valid_number = "+" + valid_number
        return valid_number

    @staticmethod
    def has_alphanumeric_chars(text: str):
        pattern = r'[a-zA-Z]'
        match = re.search(pattern, text)
        return match is not None

    @staticmethod
    def mask_email(email: str):
        try:
            if not email:
                logger.info(f"[Util.mask_email] email is empty")
                return ""

            if '@' not in email:
                logger.error(f"[Util.mask_email] invalid email without @: {email}")
                return email

            splits = email.split("@")

            if len(splits[0]) < 4:
                t1 = splits[0][0:1] + '*' * (len(splits[0]) - 2) + splits[0][-1:]
                t2 = splits[1]
                return t1 + '@' + t2

            t1 = splits[0][0:2] + '*' * (len(splits[0]) - 4) + splits[0][-2:]
            t2 = splits[1]
            return t1 + '@' + t2
        except Exception:
            logger.error(f"[Util.mask_email] mask email failed, {email}", exc_info=True)
            return email

    @staticmethod
    def AESEncode(content: str) -> str:
        cipher = AES.new(
            settings.AES_KEY.encode(encoding='utf8'),
            AES.MODE_CFB,
            iv=settings.AES_IV.encode(encoding='utf8'),
            segment_size=128
        )

        encrypt_data = cipher.encrypt(content.encode(encoding='utf8'))
        return base64.b64encode(encrypt_data).decode(encoding='utf8')

    @staticmethod
    def round_dict_values(dictionary, decimal_places=2):
        for key, value in dictionary.items():
            if isinstance(value, dict):
                Util.round_dict_values(value, decimal_places)  # 递归处理内部字典
            else:
                dictionary[key] = round(value, decimal_places)

    @staticmethod
    def calc_letter_cnt(text: str) -> int:
        text = (text.replace("\n", "").replace(" ", "")
                .replace("'", "").replace("\t", "").replace("’", ""))
        dic = {}
        for c in text:
            if c in dic:
                dic[c] += 1
            else:
                dic[c] = 1

        return len(dic)

    @staticmethod
    def is_only_digit(text: str) -> bool:
        filtered = re.sub(r'[^a-zA-Z0-9]', '', text)

        # 检查字符串是否只包含数字
        if filtered.isdigit():
            return True

        return False

    @staticmethod
    def check_list_less_than_max_distance(my_list: list, max_dis: int) -> bool:

        # 使用 itertools.product 获取所有可能的组合
        for combo in product(*my_list):
            # 检查组合的最大值和最小值差是否小于等于 max_dis
            if max(combo) - min(combo) <= max_dis:
                print(combo)
                return True
        return False

    @staticmethod
    def find_indices(lst, text):
        indices = [index for index, value in enumerate(lst) if value == text]
        return indices

    @staticmethod
    def seconds_to_str(seconds):
        days = seconds // 86400
        remaining_seconds = seconds % 86400
        hours = remaining_seconds // 3600
        remaining_seconds %= 3600
        minutes = remaining_seconds // 60
        remaining_seconds %= 60
        result = ""
        if days > 0:
            result += f"{days}d"
        if hours > 0:
            if result:
                result += " "
            result += f"{hours}h"
        if minutes > 0:
            if result:
                result += " "
            result += f"{minutes}m"
        if remaining_seconds > 0 or (days == 0 and hours == 0 and minutes == 0):
            if result:
                result += " "
            result += f"{remaining_seconds}s"
        return result

    @staticmethod
    def mask_emails_correctly(text: str) -> str:
        def mask_email(match):
            email = match.group(0)  # 匹配到的完整邮箱
            username, domain = email.split("@", 1)  # 分割用户名和域名
            masked_username = username[0] + "*" * (len(username) - 1)  # 保留首字母
            return f"{masked_username}@{domain}"  # 组合回邮箱格式

        # 匹配邮箱的正则表达式
        email_regex = r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
        return re.sub(email_regex, mask_email, text)


if __name__ == '__main__':
    print(Util.has_alphanumeric_chars("txtnow1123213"))
    print(Util.mask_email("<EMAIL>"))
    print(Util.mask_email("<EMAIL>"))
    print(Util.mask_email("<EMAIL>"))
    # print(Util.AESEncode("<EMAIL>"))
    print(Util.calc_letter_cnt("?????"))
    print(Util.calc_letter_cnt("how are you"))
    print(Util.calc_letter_cnt("fuck u"))
    print(Util.is_only_digit("100/120/140"))
    print(Util.seconds_to_str(313543))
    print(Util.seconds_to_str(123))
    print(Util.seconds_to_str(33555))
